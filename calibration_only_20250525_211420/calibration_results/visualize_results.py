#!/usr/bin/env python3
import json
import matplotlib.pyplot as plt
import numpy as np
import sys
import os

def create_calibration_comparison(results_file, save_dir):
    """创建校准结果对比图"""
    try:
        with open(results_file, 'r') as f:
            results = json.load(f)
        
        methods = []
        original_kc = []
        calibrated_kc = []
        improvements = []
        
        for result in results:
            methods.append(result['method'])
            original_kc.append(result['original']['class_accuracies']['kc'])
            calibrated_kc.append(result['calibrated']['class_accuracies']['kc'])
            improvements.append(result['improvements']['class_accuracies']['kc'])
        
        # 创建对比图
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        
        # KC准确率对比
        x = np.arange(len(methods))
        width = 0.35
        
        ax1.bar(x - width/2, original_kc, width, label='原始', alpha=0.8, color='lightcoral')
        ax1.bar(x + width/2, calibrated_kc, width, label='校准后', alpha=0.8, color='skyblue')
        
        ax1.set_xlabel('校准方法')
        ax1.set_ylabel('KC准确率 (%)')
        ax1.set_title('KC准确率对比')
        ax1.set_xticks(x)
        ax1.set_xticklabels(methods, rotation=45)
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 改进幅度
        colors = ['green' if imp > 0 else 'red' if imp < 0 else 'gray' for imp in improvements]
        ax2.bar(x, improvements, alpha=0.8, color=colors)
        ax2.set_xlabel('校准方法')
        ax2.set_ylabel('KC准确率改进 (%)')
        ax2.set_title('KC准确率改进幅度')
        ax2.set_xticks(x)
        ax2.set_xticklabels(methods, rotation=45)
        ax2.axhline(y=0, color='black', linestyle='-', alpha=0.5)
        ax2.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(os.path.join(save_dir, 'calibration_comparison.png'), dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"✅ 对比图表已保存到: {os.path.join(save_dir, 'calibration_comparison.png')}")
        
        # 生成文本报告
        report_file = os.path.join(save_dir, 'calibration_report.txt')
        with open(report_file, 'w') as f:
            f.write("校准评估报告\n")
            f.write("=" * 50 + "\n\n")
            
            f.write("校准方法效果对比:\n")
            f.write("-" * 30 + "\n")
            
            for i, result in enumerate(results):
                method = result['method']
                original_overall = result['original']['overall_accuracy']
                calibrated_overall = result['calibrated']['overall_accuracy']
                original_kc = result['original']['class_accuracies']['kc']
                calibrated_kc = result['calibrated']['class_accuracies']['kc']
                kc_improvement = result['improvements']['class_accuracies']['kc']
                
                f.write(f"\n{i+1}. {method.upper()}:\n")
                f.write(f"   总体准确率: {original_overall:.1f}% → {calibrated_overall:.1f}%\n")
                f.write(f"   KC准确率: {original_kc:.1f}% → {calibrated_kc:.1f}% ({kc_improvement:+.1f}%)\n")
                
                if kc_improvement > 2:
                    f.write(f"   ✅ 显著提升KC分类性能\n")
                elif kc_improvement > 0:
                    f.write(f"   🟡 轻微提升KC分类性能\n")
                else:
                    f.write(f"   ❌ 未提升KC分类性能\n")
            
            # 推荐
            best_method = max(results, key=lambda x: x['improvements']['class_accuracies']['kc'])
            f.write(f"\n推荐使用: {best_method['method'].upper()}\n")
            f.write(f"KC准确率提升: {best_method['improvements']['class_accuracies']['kc']:+.1f}%\n")
        
        print(f"✅ 文本报告已保存到: {report_file}")
        
        return True
        
    except Exception as e:
        print(f"❌ 可视化失败: {e}")
        return False

if __name__ == '__main__':
    results_file = sys.argv[1] if len(sys.argv) > 1 else 'calibration_results.json'
    save_dir = sys.argv[2] if len(sys.argv) > 2 else '.'
    
    if os.path.exists(results_file):
        create_calibration_comparison(results_file, save_dir)
    else:
        print(f"结果文件不存在: {results_file}")

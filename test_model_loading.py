#!/usr/bin/env python3
"""
测试模型加载和预测

验证MAML模型是否可以正确加载和预测。
"""

import os
import sys
import torch
import torch.nn.functional as F
import numpy as np

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_model_loading():
    """测试模型加载"""
    print("🔬 测试模型加载和预测")
    print("=" * 50)
    
    model_path = "/home/<USER>/balanced_task_sampling/results_optimized_20250525_202558_gpu0,1/best_maml_model.pth"
    
    if not os.path.exists(model_path):
        print(f"❌ 模型文件不存在: {model_path}")
        return False
    
    try:
        # 设置设备
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"使用设备: {device}")
        
        # 加载模型
        print("加载模型...")
        checkpoint = torch.load(model_path, map_location=device)
        print(f"✅ 模型文件加载成功")
        
        # 检查checkpoint内容
        print(f"Checkpoint键: {list(checkpoint.keys())}")
        
        if 'args' in checkpoint:
            args = checkpoint['args']
            print(f"模型参数: {args}")
        
        # 创建模型
        from models.enhanced_feature_extractor import EnhancedKeratoconusFeatureExtractor
        from models.protonet import ProtoNet
        from models.maml import MAMLProtoNet
        
        # 从checkpoint中获取模型参数
        feature_dim = args.get('feature_dim', 512)
        proto_counts = args.get('proto_counts', '2,4,2')
        proto_counts = [int(x) for x in proto_counts.split(',')]
        
        print(f"特征维度: {feature_dim}")
        print(f"原型配置: {proto_counts}")
        
        # 创建特征提取器
        feature_extractor = EnhancedKeratoconusFeatureExtractor(
            pretrained=True,
            feature_dim=feature_dim,
            dropout_rate=args.get('dropout', 0.4)
        ).to(device)
        
        # 创建ProtoNet模型
        protonet = ProtoNet(
            feature_extractor,
            n_classes=3,
            proto_counts=proto_counts,
            feature_dim=feature_dim
        ).to(device)
        
        # 创建MAML模型
        model = MAMLProtoNet(
            protonet,
            inner_lr=args.get('inner_lr', 0.1),
            inner_steps=args.get('inner_steps', 3)
        ).to(device)
        
        # 加载模型权重
        model.load_state_dict(checkpoint['model_state_dict'])
        model.eval()
        
        print(f"✅ 模型创建和权重加载成功")
        
        # 测试模型预测
        print("\n测试模型预测...")
        
        # 创建模拟数据
        batch_size = 5
        support_images = torch.randn(15, 3, 224, 224).to(device)  # 5 shot * 3 classes
        support_labels = torch.tensor([0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 2, 2, 2, 2, 2]).to(device)
        query_images = torch.randn(batch_size, 3, 224, 224).to(device)
        
        print(f"支持集形状: {support_images.shape}")
        print(f"支持集标签: {support_labels}")
        print(f"查询集形状: {query_images.shape}")
        
        # 测试MAML适应和预测
        with torch.no_grad():
            # 适应
            adapted_model = model.adapt(support_images, support_labels)
            print(f"✅ 模型适应成功")
            
            # 预测
            query_logits = adapted_model(query_images)
            query_probs = F.softmax(query_logits, dim=1)
            query_preds = torch.argmax(query_logits, dim=1)
            
            print(f"查询集logits形状: {query_logits.shape}")
            print(f"查询集概率形状: {query_probs.shape}")
            print(f"查询集预测: {query_preds.cpu().tolist()}")
            
            # 检查概率是否合理
            prob_sums = query_probs.sum(dim=1)
            print(f"概率和: {prob_sums.cpu().tolist()}")
            
            if torch.allclose(prob_sums, torch.ones_like(prob_sums), atol=1e-6):
                print(f"✅ 概率归一化正确")
            else:
                print(f"❌ 概率归一化有问题")
        
        print(f"\n🎉 模型加载和预测测试成功！")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_calibration_with_model():
    """测试校准功能与模型结合"""
    print("\n🎯 测试校准功能与模型结合")
    print("=" * 50)
    
    try:
        # 模拟一些预测结果
        probs = torch.tensor([
            [0.1, 0.7, 0.2],  # 预测为E-KC，但实际是KC
            [0.2, 0.6, 0.2],  # 预测为E-KC，但实际是KC
            [0.8, 0.1, 0.1],  # 预测为Normal，正确
            [0.1, 0.8, 0.1],  # 预测为E-KC，正确
            [0.1, 0.2, 0.7],  # 预测为KC，正确
        ], dtype=torch.float32)
        
        labels = torch.tensor([2, 2, 0, 1, 2])  # KC, KC, Normal, E-KC, KC
        
        print(f"原始预测概率:")
        class_names = ['Normal', 'E-KC', 'KC']
        for i, (prob, label) in enumerate(zip(probs, labels)):
            pred = torch.argmax(prob).item()
            print(f"  样本{i+1}: 真实={class_names[label]}, 预测={class_names[pred]}, 概率={prob.numpy()}")
        
        # 计算原始性能
        original_preds = torch.argmax(probs, dim=1)
        original_acc = (original_preds == labels).float().mean().item()
        
        # KC类别性能
        kc_mask = (labels == 2)
        kc_original_acc = (original_preds[kc_mask] == labels[kc_mask]).float().mean().item()
        
        print(f"\n原始性能:")
        print(f"  总体准确率: {original_acc*100:.1f}%")
        print(f"  KC类别准确率: {kc_original_acc*100:.1f}%")
        
        # 应用KC-EKC边界校准
        from models.calibration import KCEKCBoundaryCalibration
        
        calibrator = KCEKCBoundaryCalibration()
        
        # 使用前3个样本进行校准拟合
        cal_probs, cal_labels = probs[:3], labels[:3]
        test_probs, test_labels = probs[3:], labels[3:]
        
        calibrator.fit(cal_probs, cal_labels, target_kc_recall=0.8)
        
        # 应用校准
        calibrated_probs = calibrator.calibrate(test_probs)
        
        print(f"\n校准后预测概率:")
        for i, (prob, label) in enumerate(zip(calibrated_probs, test_labels)):
            pred = torch.argmax(prob).item()
            print(f"  样本{i+4}: 真实={class_names[label]}, 预测={class_names[pred]}, 概率={prob.numpy()}")
        
        # 计算校准后性能
        calibrated_preds = torch.argmax(calibrated_probs, dim=1)
        calibrated_acc = (calibrated_preds == test_labels).float().mean().item()
        
        print(f"\n校准后性能:")
        print(f"  测试集准确率: {calibrated_acc*100:.1f}%")
        
        print(f"\n校准参数:")
        print(f"  KC增强因子: {calibrator.kc_boost_factor:.2f}")
        print(f"  E-KC抑制因子: {calibrator.ekc_suppress_factor:.2f}")
        
        print(f"\n✅ 校准功能测试成功！")
        return True
        
    except Exception as e:
        print(f"❌ 校准测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("开始模型和校准功能测试...")
    
    # 测试模型加载
    model_success = test_model_loading()
    
    # 测试校准功能
    calibration_success = test_calibration_with_model()
    
    print("\n" + "=" * 60)
    print("测试总结")
    print("=" * 60)
    
    if model_success:
        print("✅ 模型加载和预测功能正常")
    else:
        print("❌ 模型加载或预测有问题")
    
    if calibration_success:
        print("✅ 校准功能正常")
    else:
        print("❌ 校准功能有问题")
    
    if model_success and calibration_success:
        print("\n🎉 所有功能测试通过！可以运行完整的校准评估。")
        print("\n💡 下一步:")
        print("python evaluate_with_calibration.py --model_path <your_model_path> ...")
        return True
    else:
        print("\n❌ 部分功能有问题，需要进一步调试。")
        return False


if __name__ == '__main__':
    main()

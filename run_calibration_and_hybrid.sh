#!/bin/bash

#############################################################
# 校准和混合决策策略实验脚本
# 
# 该脚本实现两种改进方案：
# 1. 类别校准后处理 - 快速验证，无需重新训练
# 2. 混合决策策略 - 训练阶段改进，结合原型网络和分类器
#############################################################

echo "🚀 开始校准和混合决策策略实验..."
echo "=================================================="

# 设置基础参数
GPU_ID="0"
BASE_OUTPUT_DIR="calibration_hybrid_experiments_$(date +%Y%m%d_%H%M%S)"
BEST_MODEL_PATH="/home/<USER>/balanced_task_sampling/results_optimized_20250525_202558_gpu0,1/best_maml_model.pth"
# 数据路径
TRAIN_CSV="/home/<USER>/balanced_task_sampling/split_result/train_set.csv"
VAL_CSV="/home/<USER>/balanced_task_sampling/split_result/val_set.csv"
TEST_CSV="/home/<USER>/balanced_task_sampling/split_result/test_set.csv"

# 创建实验目录
mkdir -p $BASE_OUTPUT_DIR
mkdir -p logs

echo "实验目录: $BASE_OUTPUT_DIR"
echo "使用GPU: $GPU_ID"
echo ""

# 设置环境变量
export CUDA_VISIBLE_DEVICES=$GPU_ID

# ==================== 第一部分：类别校准后处理 ====================
echo "📊 第一部分：类别校准后处理实验"
echo "=================================================="

# 查找现有的最佳模型
BEST_MODEL_PATH=""
if [ -f "results_optimized_feature_separation_optimized_basic_separation_*/best_model.pth" ]; then
    BEST_MODEL_PATH=$(ls results_optimized_feature_separation_optimized_basic_separation_*/best_model.pth | head -1)
elif [ -f "test_feature_separation_final/best_model.pth" ]; then
    BEST_MODEL_PATH="test_feature_separation_final/best_model.pth"
elif [ -f "/home/<USER>/balanced_task_sampling/results_optimized_20250525_202558_gpu0,1/best_maml_model.pth" ]; then
    BEST_MODEL_PATH="/home/<USER>/balanced_task_sampling/results_optimized_20250525_202558_gpu0,1/best_maml_model.pth"
else
    echo "❌ 未找到训练好的模型，请先训练一个模型"
    echo "建议运行: ./run_optimized_with_feature_separation.sh --use_feature_separation false --epochs 5"
    exit 1
fi

echo "使用模型: $BEST_MODEL_PATH"

# 运行校准评估
CALIBRATION_DIR="${BASE_OUTPUT_DIR}/calibration_results"
echo "开始校准评估..."

python evaluate_with_calibration.py \
    --model_path "$BEST_MODEL_PATH" \
    --val_csv "$VAL_CSV" \
    --test_csv "$TEST_CSV" \
    --save_dir "$CALIBRATION_DIR" \
    --device "cuda:0" \
    --calibration_methods kc_ekc_boundary temperature adaptive_threshold class_specific \
    --target_kc_recall 0.8 \
    --n_tasks 50 \
    > "logs/calibration_$(date +%Y%m%d_%H%M%S).log" 2>&1

if [ $? -eq 0 ]; then
    echo "✅ 校准评估完成"
    echo "结果保存在: $CALIBRATION_DIR"
else
    echo "❌ 校准评估失败，请检查日志"
fi

echo ""

# ==================== 第二部分：混合决策策略训练 ====================
echo "🔀 第二部分：混合决策策略训练"
echo "=================================================="

# 基础混合决策训练
HYBRID_BASIC_DIR="${BASE_OUTPUT_DIR}/hybrid_basic"
echo "开始基础混合决策训练..."

python train_hybrid_decision.py \
    --train_csv "$TRAIN_CSV" \
    --val_csv "$VAL_CSV" \
    --test_csv "$TEST_CSV" \
    --save_dir "$HYBRID_BASIC_DIR" \
    --device "cuda:0" \
    --epochs 20 \
    --lr 0.0002 \
    --weight_decay 5e-4 \
    --feature_dim 512 \
    --dropout 0.5 \
    --proto_counts "2,4,2" \
    --inner_lr 0.3 \
    --inner_steps 2 \
    --hybrid_type basic \
    --hybrid_hidden_dim 256 \
    --prototype_weight 0.7 \
    --classifier_weight 0.3 \
    --consistency_weight 0.1 \
    --early_kc_weight 8.0 \
    --kc_weight 4.0 \
    --focal_gamma 2.0 \
    --use_balanced_task_sampler \
    --kc_shot_multiplier 3.0 \
    --early_kc_shot_multiplier 1.5 \
    --tasks_per_epoch 30 \
    --pretrained \
    --seed 42 \
    > "logs/hybrid_basic_$(date +%Y%m%d_%H%M%S).log" 2>&1

if [ $? -eq 0 ]; then
    echo "✅ 基础混合决策训练完成"
    echo "结果保存在: $HYBRID_BASIC_DIR"
else
    echo "❌ 基础混合决策训练失败，请检查日志"
fi

echo ""

# 自适应混合决策训练
HYBRID_ADAPTIVE_DIR="${BASE_OUTPUT_DIR}/hybrid_adaptive"
echo "开始自适应混合决策训练..."

python train_hybrid_decision.py \
    --train_csv "$TRAIN_CSV" \
    --val_csv "$VAL_CSV" \
    --test_csv "$TEST_CSV" \
    --save_dir "$HYBRID_ADAPTIVE_DIR" \
    --device "cuda:0" \
    --epochs 20 \
    --lr 0.0002 \
    --weight_decay 5e-4 \
    --feature_dim 512 \
    --dropout 0.5 \
    --proto_counts "2,4,2" \
    --inner_lr 0.3 \
    --inner_steps 2 \
    --hybrid_type adaptive \
    --hybrid_hidden_dim 256 \
    --prototype_weight 0.6 \
    --classifier_weight 0.4 \
    --consistency_weight 0.15 \
    --early_kc_weight 8.0 \
    --kc_weight 4.0 \
    --focal_gamma 2.0 \
    --use_balanced_task_sampler \
    --kc_shot_multiplier 3.0 \
    --early_kc_shot_multiplier 1.5 \
    --tasks_per_epoch 30 \
    --pretrained \
    --seed 42 \
    > "logs/hybrid_adaptive_$(date +%Y%m%d_%H%M%S).log" 2>&1

if [ $? -eq 0 ]; then
    echo "✅ 自适应混合决策训练完成"
    echo "结果保存在: $HYBRID_ADAPTIVE_DIR"
else
    echo "❌ 自适应混合决策训练失败，请检查日志"
fi

echo ""

# ==================== 第三部分：结果对比分析 ====================
echo "📈 第三部分：结果对比分析"
echo "=================================================="

# 创建对比分析脚本
cat > "${BASE_OUTPUT_DIR}/analyze_results.py" << 'EOF'
#!/usr/bin/env python3
import json
import os
import matplotlib.pyplot as plt
import numpy as np

def load_results(base_dir):
    results = {}
    
    # 加载校准结果
    calibration_file = os.path.join(base_dir, 'calibration_results', 'calibration_results.json')
    if os.path.exists(calibration_file):
        with open(calibration_file, 'r') as f:
            results['calibration'] = json.load(f)
    
    # 加载混合决策结果
    for hybrid_type in ['basic', 'adaptive']:
        hybrid_file = os.path.join(base_dir, f'hybrid_{hybrid_type}', 'hybrid_test_results.json')
        if os.path.exists(hybrid_file):
            with open(hybrid_file, 'r') as f:
                results[f'hybrid_{hybrid_type}'] = json.load(f)
    
    return results

def create_comparison_plot(results, save_path):
    methods = []
    overall_accs = []
    kc_accs = []
    ekc_accs = []
    
    # 校准结果
    if 'calibration' in results:
        for method_result in results['calibration']:
            method_name = f"Calibration_{method_result['method']}"
            methods.append(method_name)
            overall_accs.append(method_result['calibrated']['overall_accuracy'])
            kc_accs.append(method_result['calibrated']['class_accuracies']['kc'])
            ekc_accs.append(method_result['calibrated']['class_accuracies']['e-kc'])
    
    # 混合决策结果
    for hybrid_type in ['basic', 'adaptive']:
        if f'hybrid_{hybrid_type}' in results:
            method_name = f"Hybrid_{hybrid_type}"
            methods.append(method_name)
            result = results[f'hybrid_{hybrid_type}']
            overall_accs.append(result['test_acc'])
            kc_accs.append(result['class_accuracies']['kc'])
            ekc_accs.append(result['class_accuracies']['e-kc'])
    
    # 创建对比图
    fig, axes = plt.subplots(1, 3, figsize=(15, 5))
    
    x = np.arange(len(methods))
    
    # 总体准确率
    axes[0].bar(x, overall_accs, alpha=0.8, color='skyblue')
    axes[0].set_title('Overall Accuracy')
    axes[0].set_ylabel('Accuracy (%)')
    axes[0].set_xticks(x)
    axes[0].set_xticklabels(methods, rotation=45)
    axes[0].grid(True, alpha=0.3)
    
    # KC准确率
    axes[1].bar(x, kc_accs, alpha=0.8, color='lightcoral')
    axes[1].set_title('KC Class Accuracy')
    axes[1].set_ylabel('Accuracy (%)')
    axes[1].set_xticks(x)
    axes[1].set_xticklabels(methods, rotation=45)
    axes[1].grid(True, alpha=0.3)
    
    # E-KC准确率
    axes[2].bar(x, ekc_accs, alpha=0.8, color='lightgreen')
    axes[2].set_title('E-KC Class Accuracy')
    axes[2].set_ylabel('Accuracy (%)')
    axes[2].set_xticks(x)
    axes[2].set_xticklabels(methods, rotation=45)
    axes[2].grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.close()

def main():
    import sys
    base_dir = sys.argv[1] if len(sys.argv) > 1 else '.'
    
    results = load_results(base_dir)
    
    if results:
        create_comparison_plot(results, os.path.join(base_dir, 'method_comparison.png'))
        print("对比分析完成，图表已保存")
        
        # 打印结果摘要
        print("\n结果摘要:")
        print("=" * 50)
        
        if 'calibration' in results:
            print("校准方法结果:")
            for method_result in results['calibration']:
                method = method_result['method']
                overall = method_result['calibrated']['overall_accuracy']
                kc = method_result['calibrated']['class_accuracies']['kc']
                ekc = method_result['calibrated']['class_accuracies']['e-kc']
                print(f"  {method}: 总体={overall:.1f}%, KC={kc:.1f}%, E-KC={ekc:.1f}%")
        
        for hybrid_type in ['basic', 'adaptive']:
            if f'hybrid_{hybrid_type}' in results:
                result = results[f'hybrid_{hybrid_type}']
                overall = result['test_acc']
                kc = result['class_accuracies']['kc']
                ekc = result['class_accuracies']['e-kc']
                print(f"混合决策({hybrid_type}): 总体={overall:.1f}%, KC={kc:.1f}%, E-KC={ekc:.1f}%")
    else:
        print("未找到结果文件")

if __name__ == '__main__':
    main()
EOF

# 运行对比分析
python "${BASE_OUTPUT_DIR}/analyze_results.py" "$BASE_OUTPUT_DIR"

echo ""
echo "=================================================="
echo "🎉 所有实验完成！"
echo "=================================================="

echo "实验结果目录: $BASE_OUTPUT_DIR"
echo ""
echo "📊 实验内容："
echo "1. 类别校准后处理："
echo "   - KC-EKC边界校准"
echo "   - 温度缩放校准"
echo "   - 自适应阈值校准"
echo "   - 类别特定校准"
echo ""
echo "2. 混合决策策略："
echo "   - 基础混合决策（原型网络 + 分类器）"
echo "   - 自适应混合决策（动态权重调整）"
echo ""
echo "📈 查看结果："
echo "  # 校准结果"
echo "  cat ${BASE_OUTPUT_DIR}/calibration_results/calibration_results.json"
echo ""
echo "  # 混合决策结果"
echo "  cat ${BASE_OUTPUT_DIR}/hybrid_*/hybrid_test_results.json"
echo ""
echo "  # 对比图表"
echo "  open ${BASE_OUTPUT_DIR}/method_comparison.png"
echo ""
echo "💡 预期效果："
echo "- 校准方法应该能快速提升KC识别率"
echo "- 混合决策应该提供更稳定的整体性能"
echo "- KC-EKC边界校准可能效果最明显"

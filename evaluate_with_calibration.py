#!/usr/bin/env python3
"""
带校准的模型评估脚本

该脚本用于评估现有模型并应用不同的校准策略，
特别针对KC和E-KC之间的分类性能进行优化。
"""

import os
import sys
import argparse
import torch
import torch.nn.functional as F
import numpy as np
import json
from tqdm import tqdm
from collections import defaultdict
import matplotlib.pyplot as plt
import seaborn as sns

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 导入自定义模块
from models.calibration import create_calibrator
from models.enhanced_feature_extractor import EnhancedKeratoconusFeatureExtractor
from models.protonet import ProtoNet
from models.maml import MAMLProtoNet
from datasets.keratoconus_dataset import KeratoconusDataset, get_transforms
from datasets.balanced_task_sampler import BalancedTaskSampler


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='Evaluate model with calibration')

    # 基本参数
    parser.add_argument('--model_path', type=str, required=True, help='Path to trained model')
    parser.add_argument('--val_csv', type=str, required=True, help='Path to validation CSV file')
    parser.add_argument('--test_csv', type=str, required=True, help='Path to test CSV file')
    parser.add_argument('--save_dir', type=str, default='calibration_results', help='Directory to save results')
    parser.add_argument('--device', type=str, default='cuda', help='Device to use')

    # 校准参数
    parser.add_argument('--calibration_methods', type=str, nargs='+',
                        default=['kc_ekc_boundary', 'temperature', 'adaptive_threshold'],
                        help='Calibration methods to evaluate')
    parser.add_argument('--target_kc_recall', type=float, default=0.8, help='Target KC recall for calibration')

    # 评估参数
    parser.add_argument('--n_tasks', type=int, default=100, help='Number of tasks for evaluation')
    parser.add_argument('--n_way', type=int, default=3, help='Number of classes per task')
    parser.add_argument('--n_shot', type=int, default=5, help='Number of support samples per class')
    parser.add_argument('--n_query', type=int, default=15, help='Number of query samples per class')

    return parser.parse_args()


def load_model(model_path, device):
    """加载训练好的模型"""
    checkpoint = torch.load(model_path, map_location=device)

    # 从checkpoint中获取模型参数
    args = checkpoint.get('args', {})
    feature_dim = args.get('feature_dim', 512)
    proto_counts = args.get('proto_counts', '2,4,2')
    proto_counts = [int(x) for x in proto_counts.split(',')]

    # 创建特征提取器
    feature_extractor = EnhancedKeratoconusFeatureExtractor(
        pretrained=True,
        feature_dim=feature_dim,
        dropout_rate=args.get('dropout', 0.4)
    ).to(device)

    # 创建ProtoNet模型
    protonet = ProtoNet(
        feature_extractor,
        n_classes=3,
        proto_counts=proto_counts,
        feature_dim=feature_dim
    ).to(device)

    # 创建MAML模型
    model = MAMLProtoNet(
        protonet,
        inner_lr=args.get('inner_lr', 0.1),
        inner_steps=args.get('inner_steps', 3)
    ).to(device)

    # 加载模型权重
    model.load_state_dict(checkpoint['model_state_dict'])
    model.eval()

    return model, args


def collect_predictions(model, dataset, device, args, n_tasks=100):
    """收集模型预测结果"""
    model.eval()

    # 创建任务采样器
    try:
        task_sampler = BalancedTaskSampler(
            dataset,
            n_way=args.n_way,
            n_shot=args.n_shot,
            n_query=args.n_query,
            kc_shot_multiplier=3.0,
            early_kc_shot_multiplier=1.5
        )
    except Exception as e:
        print(f"创建任务采样器失败: {e}")
        # 使用简单的采样器作为备选
        from datasets.keratoconus_dataset import KeratoconusTaskSampler
        task_sampler = KeratoconusTaskSampler(
            dataset,
            n_way=args.n_way,
            n_shot=args.n_shot,
            n_query=args.n_query
        )

    all_logits = []
    all_probs = []
    all_labels = []
    all_predictions = []

    print(f"开始收集 {n_tasks} 个任务的预测结果...")

    with torch.no_grad():
        for i in tqdm(range(n_tasks), desc="Collecting predictions"):
            try:
                # 采样一个任务
                task = task_sampler.sample_task()
                support_images, support_labels = task['support']
                query_images, query_labels = task['query']

                # 将数据移动到设备
                support_images = support_images.to(device)
                support_labels = support_labels.to(device)
                query_images = query_images.to(device)
                query_labels = query_labels.to(device)

                # 对于MAML模型，需要先适应然后预测
                if hasattr(model, 'adapt'):
                    # 这是一个MAML模型
                    adapted_model = model.adapt(support_images, support_labels)
                    query_logits = adapted_model(query_images)
                else:
                    # 这是一个普通模型，直接预测
                    query_logits = model(query_images)

                query_probs = F.softmax(query_logits, dim=1)
                query_preds = torch.argmax(query_logits, dim=1)

                # 收集结果
                all_logits.append(query_logits.cpu())
                all_probs.append(query_probs.cpu())
                all_labels.append(query_labels.cpu())
                all_predictions.append(query_preds.cpu())

            except Exception as e:
                print(f"任务 {i} 处理失败: {e}")
                continue

    if not all_logits:
        raise RuntimeError("没有成功收集到任何预测结果")

    # 合并所有结果
    all_logits = torch.cat(all_logits, dim=0)
    all_probs = torch.cat(all_probs, dim=0)
    all_labels = torch.cat(all_labels, dim=0)
    all_predictions = torch.cat(all_predictions, dim=0)

    print(f"成功收集到 {len(all_labels)} 个样本的预测结果")

    return all_logits, all_probs, all_labels, all_predictions


def evaluate_calibration(probs, labels, predictions, calibrator, method_name):
    """评估校准效果"""
    # 应用校准
    if hasattr(calibrator, 'calibrate'):
        calibrated_probs = calibrator.calibrate(probs)
    elif hasattr(calibrator, 'forward'):
        # 对于PyTorch模块
        logits = torch.log(probs + 1e-8)  # 避免log(0)
        calibrated_probs = calibrator(logits)
    else:
        calibrated_probs = probs

    # 计算校准后的预测
    calibrated_predictions = torch.argmax(calibrated_probs, dim=1)

    # 计算指标
    def compute_metrics(preds, true_labels):
        correct = (preds == true_labels).float()
        overall_acc = correct.mean().item()

        class_accs = {}
        class_names = ['normal', 'e-kc', 'kc']
        for i, class_name in enumerate(class_names):
            class_mask = (true_labels == i)
            if class_mask.any():
                class_acc = correct[class_mask].mean().item()
                class_accs[class_name] = class_acc * 100
            else:
                class_accs[class_name] = 0.0

        return overall_acc * 100, class_accs

    # 原始性能
    original_acc, original_class_accs = compute_metrics(predictions, labels)

    # 校准后性能
    calibrated_acc, calibrated_class_accs = compute_metrics(calibrated_predictions, labels)

    # 计算改进
    improvements = {}
    for class_name in original_class_accs:
        improvements[class_name] = calibrated_class_accs[class_name] - original_class_accs[class_name]

    results = {
        'method': method_name,
        'original': {
            'overall_accuracy': original_acc,
            'class_accuracies': original_class_accs
        },
        'calibrated': {
            'overall_accuracy': calibrated_acc,
            'class_accuracies': calibrated_class_accs
        },
        'improvements': {
            'overall_accuracy': calibrated_acc - original_acc,
            'class_accuracies': improvements
        }
    }

    return results, calibrated_probs, calibrated_predictions


def main():
    args = parse_args()

    # 设置设备
    device = torch.device(args.device if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")

    # 创建保存目录
    os.makedirs(args.save_dir, exist_ok=True)

    # 加载模型
    print("加载模型...")
    model, model_args = load_model(args.model_path, device)

    # 加载数据集
    print("加载数据集...")
    val_transforms = get_transforms('test')  # 使用测试阶段的变换

    val_dataset = KeratoconusDataset(args.val_csv, transform=val_transforms)
    test_dataset = KeratoconusDataset(args.test_csv, transform=val_transforms)

    print(f"验证集样本数: {len(val_dataset)}")
    print(f"测试集样本数: {len(test_dataset)}")

    # 收集验证集预测（用于校准器拟合）
    print("收集验证集预测...")
    val_logits, val_probs, val_labels, val_predictions = collect_predictions(
        model, val_dataset, device, args, n_tasks=50
    )

    # 收集测试集预测（用于最终评估）
    print("收集测试集预测...")
    test_logits, test_probs, test_labels, test_predictions = collect_predictions(
        model, test_dataset, device, args, n_tasks=args.n_tasks
    )

    # 评估不同校准方法
    all_results = []

    print("\n开始校准评估...")
    for method in args.calibration_methods:
        print(f"\n评估校准方法: {method}")

        # 创建校准器
        calibrator = create_calibrator(method)

        # 拟合校准器
        if method == 'temperature':
            calibrator.fit(val_logits, val_labels)
            print(f"温度参数: {calibrator.temperature.item():.3f}")
        elif method == 'class_specific':
            temps, biases = calibrator.fit(val_logits, val_labels)
            print(f"类别温度: {temps}")
            print(f"类别偏置: {biases}")
        elif method == 'kc_ekc_boundary':
            calibrator.fit(val_probs, val_labels, target_kc_recall=args.target_kc_recall)
        elif method == 'adaptive_threshold':
            calibrator.fit(val_probs, val_labels)

        # 在测试集上评估
        results, calibrated_probs, calibrated_preds = evaluate_calibration(
            test_probs, test_labels, test_predictions, calibrator, method
        )

        all_results.append(results)

        # 打印结果
        print(f"原始性能:")
        print(f"  总体准确率: {results['original']['overall_accuracy']:.2f}%")
        for class_name, acc in results['original']['class_accuracies'].items():
            print(f"  {class_name}: {acc:.2f}%")

        print(f"校准后性能:")
        print(f"  总体准确率: {results['calibrated']['overall_accuracy']:.2f}%")
        for class_name, acc in results['calibrated']['class_accuracies'].items():
            print(f"  {class_name}: {acc:.2f}%")

        print(f"改进:")
        print(f"  总体准确率: {results['improvements']['overall_accuracy']:+.2f}%")
        for class_name, improvement in results['improvements']['class_accuracies'].items():
            print(f"  {class_name}: {improvement:+.2f}%")

        # 保存校准器
        if hasattr(calibrator, 'save'):
            calibrator_path = os.path.join(args.save_dir, f'{method}_calibrator.pkl')
            calibrator.save(calibrator_path)
            print(f"校准器已保存到: {calibrator_path}")

    # 保存所有结果
    results_path = os.path.join(args.save_dir, 'calibration_results.json')
    with open(results_path, 'w') as f:
        json.dump(all_results, f, indent=2)

    print(f"\n所有结果已保存到: {results_path}")

    # 生成对比图表
    create_comparison_plots(all_results, args.save_dir)

    print("校准评估完成！")


def create_comparison_plots(results, save_dir):
    """创建对比图表"""
    methods = [r['method'] for r in results]

    # 准备数据
    original_overall = [r['original']['overall_accuracy'] for r in results]
    calibrated_overall = [r['calibrated']['overall_accuracy'] for r in results]

    class_names = ['normal', 'e-kc', 'kc']
    original_class = {name: [r['original']['class_accuracies'][name] for r in results] for name in class_names}
    calibrated_class = {name: [r['calibrated']['class_accuracies'][name] for r in results] for name in class_names}

    # 创建对比图
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))

    # 总体准确率对比
    ax = axes[0, 0]
    x = np.arange(len(methods))
    width = 0.35
    ax.bar(x - width/2, original_overall, width, label='Original', alpha=0.8)
    ax.bar(x + width/2, calibrated_overall, width, label='Calibrated', alpha=0.8)
    ax.set_xlabel('Calibration Method')
    ax.set_ylabel('Overall Accuracy (%)')
    ax.set_title('Overall Accuracy Comparison')
    ax.set_xticks(x)
    ax.set_xticklabels(methods, rotation=45)
    ax.legend()
    ax.grid(True, alpha=0.3)

    # 类别准确率对比
    for i, class_name in enumerate(class_names):
        ax = axes[0, 1] if i == 0 else axes[1, i-1]
        ax.bar(x - width/2, original_class[class_name], width, label='Original', alpha=0.8)
        ax.bar(x + width/2, calibrated_class[class_name], width, label='Calibrated', alpha=0.8)
        ax.set_xlabel('Calibration Method')
        ax.set_ylabel(f'{class_name.upper()} Accuracy (%)')
        ax.set_title(f'{class_name.upper()} Class Accuracy Comparison')
        ax.set_xticks(x)
        ax.set_xticklabels(methods, rotation=45)
        ax.legend()
        ax.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig(os.path.join(save_dir, 'calibration_comparison.png'), dpi=300, bbox_inches='tight')
    plt.close()

    # 改进幅度图
    plt.figure(figsize=(12, 8))
    improvements_overall = [r['improvements']['overall_accuracy'] for r in results]
    improvements_class = {name: [r['improvements']['class_accuracies'][name] for r in results] for name in class_names}

    x = np.arange(len(methods))
    width = 0.2

    plt.bar(x - 1.5*width, improvements_overall, width, label='Overall', alpha=0.8)
    for i, class_name in enumerate(class_names):
        plt.bar(x + (i-0.5)*width, improvements_class[class_name], width, label=class_name.upper(), alpha=0.8)

    plt.xlabel('Calibration Method')
    plt.ylabel('Accuracy Improvement (%)')
    plt.title('Accuracy Improvements by Calibration Method')
    plt.xticks(x, methods, rotation=45)
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.axhline(y=0, color='black', linestyle='-', alpha=0.5)

    plt.tight_layout()
    plt.savefig(os.path.join(save_dir, 'calibration_improvements.png'), dpi=300, bbox_inches='tight')
    plt.close()

    print(f"对比图表已保存到: {save_dir}")


if __name__ == '__main__':
    main()

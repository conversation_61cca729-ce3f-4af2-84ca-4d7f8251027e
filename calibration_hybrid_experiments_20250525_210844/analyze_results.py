#!/usr/bin/env python3
import json
import os
import matplotlib.pyplot as plt
import numpy as np

def load_results(base_dir):
    results = {}
    
    # 加载校准结果
    calibration_file = os.path.join(base_dir, 'calibration_results', 'calibration_results.json')
    if os.path.exists(calibration_file):
        with open(calibration_file, 'r') as f:
            results['calibration'] = json.load(f)
    
    # 加载混合决策结果
    for hybrid_type in ['basic', 'adaptive']:
        hybrid_file = os.path.join(base_dir, f'hybrid_{hybrid_type}', 'hybrid_test_results.json')
        if os.path.exists(hybrid_file):
            with open(hybrid_file, 'r') as f:
                results[f'hybrid_{hybrid_type}'] = json.load(f)
    
    return results

def create_comparison_plot(results, save_path):
    methods = []
    overall_accs = []
    kc_accs = []
    ekc_accs = []
    
    # 校准结果
    if 'calibration' in results:
        for method_result in results['calibration']:
            method_name = f"Calibration_{method_result['method']}"
            methods.append(method_name)
            overall_accs.append(method_result['calibrated']['overall_accuracy'])
            kc_accs.append(method_result['calibrated']['class_accuracies']['kc'])
            ekc_accs.append(method_result['calibrated']['class_accuracies']['e-kc'])
    
    # 混合决策结果
    for hybrid_type in ['basic', 'adaptive']:
        if f'hybrid_{hybrid_type}' in results:
            method_name = f"Hybrid_{hybrid_type}"
            methods.append(method_name)
            result = results[f'hybrid_{hybrid_type}']
            overall_accs.append(result['test_acc'])
            kc_accs.append(result['class_accuracies']['kc'])
            ekc_accs.append(result['class_accuracies']['e-kc'])
    
    # 创建对比图
    fig, axes = plt.subplots(1, 3, figsize=(15, 5))
    
    x = np.arange(len(methods))
    
    # 总体准确率
    axes[0].bar(x, overall_accs, alpha=0.8, color='skyblue')
    axes[0].set_title('Overall Accuracy')
    axes[0].set_ylabel('Accuracy (%)')
    axes[0].set_xticks(x)
    axes[0].set_xticklabels(methods, rotation=45)
    axes[0].grid(True, alpha=0.3)
    
    # KC准确率
    axes[1].bar(x, kc_accs, alpha=0.8, color='lightcoral')
    axes[1].set_title('KC Class Accuracy')
    axes[1].set_ylabel('Accuracy (%)')
    axes[1].set_xticks(x)
    axes[1].set_xticklabels(methods, rotation=45)
    axes[1].grid(True, alpha=0.3)
    
    # E-KC准确率
    axes[2].bar(x, ekc_accs, alpha=0.8, color='lightgreen')
    axes[2].set_title('E-KC Class Accuracy')
    axes[2].set_ylabel('Accuracy (%)')
    axes[2].set_xticks(x)
    axes[2].set_xticklabels(methods, rotation=45)
    axes[2].grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.close()

def main():
    import sys
    base_dir = sys.argv[1] if len(sys.argv) > 1 else '.'
    
    results = load_results(base_dir)
    
    if results:
        create_comparison_plot(results, os.path.join(base_dir, 'method_comparison.png'))
        print("对比分析完成，图表已保存")
        
        # 打印结果摘要
        print("\n结果摘要:")
        print("=" * 50)
        
        if 'calibration' in results:
            print("校准方法结果:")
            for method_result in results['calibration']:
                method = method_result['method']
                overall = method_result['calibrated']['overall_accuracy']
                kc = method_result['calibrated']['class_accuracies']['kc']
                ekc = method_result['calibrated']['class_accuracies']['e-kc']
                print(f"  {method}: 总体={overall:.1f}%, KC={kc:.1f}%, E-KC={ekc:.1f}%")
        
        for hybrid_type in ['basic', 'adaptive']:
            if f'hybrid_{hybrid_type}' in results:
                result = results[f'hybrid_{hybrid_type}']
                overall = result['test_acc']
                kc = result['class_accuracies']['kc']
                ekc = result['class_accuracies']['e-kc']
                print(f"混合决策({hybrid_type}): 总体={overall:.1f}%, KC={kc:.1f}%, E-KC={ekc:.1f}%")
    else:
        print("未找到结果文件")

if __name__ == '__main__':
    main()

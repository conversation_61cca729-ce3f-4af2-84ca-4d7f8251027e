#!/bin/bash

#############################################################
# 简化版混合决策训练脚本
# 
# 该脚本专门用于训练混合决策模型，
# 基于现有最佳模型的配置进行训练。
#############################################################

echo "🔀 开始混合决策训练实验..."
echo "=================================================="

# 设置基础参数
GPU_ID="0"
BASE_OUTPUT_DIR="hybrid_simple_$(date +%Y%m%d_%H%M%S)"

# 数据路径
TRAIN_CSV="/home/<USER>/balanced_task_sampling/split_result/train_set.csv"
VAL_CSV="/home/<USER>/balanced_task_sampling/split_result/val_set.csv"
TEST_CSV="/home/<USER>/balanced_task_sampling/split_result/test_set.csv"

# 创建实验目录
mkdir -p $BASE_OUTPUT_DIR
mkdir -p logs

echo "实验目录: $BASE_OUTPUT_DIR"
echo "使用GPU: $GPU_ID"
echo ""

# 设置环境变量
export CUDA_VISIBLE_DEVICES=$GPU_ID

# ==================== 基础混合决策训练 ====================
echo "🔀 基础混合决策训练"
echo "=================================================="

HYBRID_BASIC_DIR="${BASE_OUTPUT_DIR}/hybrid_basic"
echo "开始基础混合决策训练..."
echo "保存目录: $HYBRID_BASIC_DIR"

python train_hybrid_decision.py \
    --train_csv "$TRAIN_CSV" \
    --val_csv "$VAL_CSV" \
    --test_csv "$TEST_CSV" \
    --save_dir "$HYBRID_BASIC_DIR" \
    --device "cuda:0" \
    --epochs 15 \
    --lr 0.0002 \
    --weight_decay 5e-4 \
    --feature_dim 512 \
    --dropout 0.5 \
    --proto_counts "2,4,2" \
    --inner_lr 0.3 \
    --inner_steps 2 \
    --hybrid_type basic \
    --hybrid_hidden_dim 256 \
    --prototype_weight 0.7 \
    --classifier_weight 0.3 \
    --consistency_weight 0.1 \
    --early_kc_weight 8.0 \
    --kc_weight 4.0 \
    --focal_gamma 2.0 \
    --use_balanced_task_sampler \
    --kc_shot_multiplier 3.0 \
    --early_kc_shot_multiplier 1.5 \
    --tasks_per_epoch 20 \
    --pretrained \
    --seed 42 \
    --val_frequency 2 \
    --early_stopping 3 \
    > "logs/hybrid_simple_basic_$(date +%Y%m%d_%H%M%S).log" 2>&1

# 检查执行结果
if [ $? -eq 0 ]; then
    echo "✅ 基础混合决策训练完成"
    echo "结果保存在: $HYBRID_BASIC_DIR"
    
    # 显示结果摘要
    if [ -f "$HYBRID_BASIC_DIR/hybrid_test_results.json" ]; then
        echo ""
        echo "📊 基础混合决策结果摘要:"
        echo "=================================================="
        
        # 使用Python快速解析结果
        python -c "
import json
import os

results_file = '$HYBRID_BASIC_DIR/hybrid_test_results.json'
if os.path.exists(results_file):
    with open(results_file, 'r') as f:
        results = json.load(f)
    
    print('基础混合决策训练结果:')
    print('-' * 30)
    print(f'总体准确率: {results[\"test_acc\"]:.1f}%')
    print('类别准确率:')
    for class_name, acc in results['class_accuracies'].items():
        print(f'  {class_name}: {acc:.1f}%')
    
    print('')
    print('详细结果请查看: $HYBRID_BASIC_DIR/hybrid_test_results.json')
else:
    print('结果文件未找到，请检查日志')
" 2>/dev/null || echo "结果解析失败，请手动查看结果文件"
        
    fi
    
else
    echo "❌ 基础混合决策训练失败，请检查日志"
    echo "日志文件: logs/hybrid_simple_basic_*.log"
fi

echo ""

# ==================== 自适应混合决策训练 ====================
echo "🔀 自适应混合决策训练"
echo "=================================================="

HYBRID_ADAPTIVE_DIR="${BASE_OUTPUT_DIR}/hybrid_adaptive"
echo "开始自适应混合决策训练..."
echo "保存目录: $HYBRID_ADAPTIVE_DIR"

python train_hybrid_decision.py \
    --train_csv "$TRAIN_CSV" \
    --val_csv "$VAL_CSV" \
    --test_csv "$TEST_CSV" \
    --save_dir "$HYBRID_ADAPTIVE_DIR" \
    --device "cuda:0" \
    --epochs 15 \
    --lr 0.0002 \
    --weight_decay 5e-4 \
    --feature_dim 512 \
    --dropout 0.5 \
    --proto_counts "2,4,2" \
    --inner_lr 0.3 \
    --inner_steps 2 \
    --hybrid_type adaptive \
    --hybrid_hidden_dim 256 \
    --prototype_weight 0.6 \
    --classifier_weight 0.4 \
    --consistency_weight 0.15 \
    --early_kc_weight 8.0 \
    --kc_weight 4.0 \
    --focal_gamma 2.0 \
    --use_balanced_task_sampler \
    --kc_shot_multiplier 3.0 \
    --early_kc_shot_multiplier 1.5 \
    --tasks_per_epoch 20 \
    --pretrained \
    --seed 42 \
    --val_frequency 2 \
    --early_stopping 3 \
    > "logs/hybrid_simple_adaptive_$(date +%Y%m%d_%H%M%S).log" 2>&1

# 检查执行结果
if [ $? -eq 0 ]; then
    echo "✅ 自适应混合决策训练完成"
    echo "结果保存在: $HYBRID_ADAPTIVE_DIR"
    
    # 显示结果摘要
    if [ -f "$HYBRID_ADAPTIVE_DIR/hybrid_test_results.json" ]; then
        echo ""
        echo "📊 自适应混合决策结果摘要:"
        echo "=================================================="
        
        # 使用Python快速解析结果
        python -c "
import json
import os

results_file = '$HYBRID_ADAPTIVE_DIR/hybrid_test_results.json'
if os.path.exists(results_file):
    with open(results_file, 'r') as f:
        results = json.load(f)
    
    print('自适应混合决策训练结果:')
    print('-' * 30)
    print(f'总体准确率: {results[\"test_acc\"]:.1f}%')
    print('类别准确率:')
    for class_name, acc in results['class_accuracies'].items():
        print(f'  {class_name}: {acc:.1f}%')
    
    print('')
    print('详细结果请查看: $HYBRID_ADAPTIVE_DIR/hybrid_test_results.json')
else:
    print('结果文件未找到，请检查日志')
" 2>/dev/null || echo "结果解析失败，请手动查看结果文件"
        
    fi
    
else
    echo "❌ 自适应混合决策训练失败，请检查日志"
    echo "日志文件: logs/hybrid_simple_adaptive_*.log"
fi

echo ""
echo "=================================================="
echo "🎉 混合决策训练实验完成！"
echo "=================================================="

echo "实验结果目录: $BASE_OUTPUT_DIR"
echo ""
echo "📊 查看结果："
echo "  # 基础混合决策结果"
echo "  cat ${HYBRID_BASIC_DIR}/hybrid_test_results.json"
echo ""
echo "  # 自适应混合决策结果"
echo "  cat ${HYBRID_ADAPTIVE_DIR}/hybrid_test_results.json"
echo ""
echo "💡 使用建议："
echo "- 比较基础和自适应混合决策的效果"
echo "- 选择性能更好的模型进行后续使用"
echo "- 可以结合校准方法进一步优化"

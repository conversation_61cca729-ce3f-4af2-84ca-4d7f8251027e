{"test_acc": 79.44444444444446, "class_accuracies": {"kc": 60.0, "e-kc": 78.33333333333333, "normal": 100.0}, "confusion_matrix": [[120, 0, 0], [4, 94, 22], [3, 45, 72]], "best_epoch": 1, "best_val_acc": 0.7000000000000001, "history": {"train_loss": [1.7168660362561543, 1.3242770155270895, 1.0600430866082509, 0.9927734076976776, 0.8909403284390768, 0.8496849993864696, 0.7640686174233754], "train_acc": [0.511111111111111, 0.76962962962963, 0.9096296296296298, 0.911851851851852, 0.9333333333333336, 0.9422222222222223, 0.9540740740740741], "val_loss": [1.5842230081558228, 1.5158642530441284, 1.8331918358802795, 1.6413877844810485, 1.5487172603607178, 1.9163211345672608, 1.7735146641731263], "val_acc": [0.6444444444444445, 0.7000000000000001, 0.5666666666666667, 0.6777777777777778, 0.6666666666666665, 0.511111111111111, 0.6555555555555556], "contrastive_loss": [0.4728968458871047, 0.34659829835096995, 0.2883996700247129, 0.2938863024115562, 0.2172563313351323, 0.13067615120283638, 0.14906887695251497], "learning_rates": [0.0002, 0.0002, 0.0002, 0.0002, 0.0002, 0.0001, 0.0001], "batch_learning_rates": []}, "args": {"train_csv": "/home/<USER>/balanced_task_sampling/split_result/train_set.csv", "val_csv": "/home/<USER>/balanced_task_sampling/split_result/val_set.csv", "test_csv": "/home/<USER>/balanced_task_sampling/split_result/test_set.csv", "save_dir": "results_optimized_20250525_145812_gpu0,1", "device": "cuda", "proto_counts": "2,6,3", "feature_dim": 512, "inner_lr": 0.3, "inner_steps": 2, "pretrained": true, "dropout": 0.5, "use_normalized_features": false, "normalize_scale": 10.0, "epochs": 30, "lr": 0.0002, "weight_decay": 0.0005, "n_way": 3, "n_shot": 5, "n_query": 15, "tasks_per_epoch": 30, "meta_batch_size": 8, "val_frequency": 1, "early_stopping": 5, "use_lr_scheduler": true, "scheduler_type": "plateau", "max_lr": 0.001, "div_factor": 25.0, "final_div_factor": 10000.0, "pct_start": 0.1, "plateau_factor": 0.5, "plateau_patience": 2, "min_lr": 1e-06, "record_batch_lr": false, "no_augmentation": true, "early_kc_augment": false, "kc_augment": false, "advanced_augment": false, "mixup_alpha": 0.0, "cutmix_prob": 0.0, "early_kc_specific_augment": false, "augment_factor": 1, "kc_augment_factor": 1, "use_enhanced_gpu_augment": false, "early_kc_weight": 8.0, "kc_weight": 4.0, "focal_gamma": 2.0, "use_contrastive": true, "contrastive_weight": 0.5, "temperature": 0.07, "hard_mining_ratio": 0.7, "early_normal_weight": 2.0, "kc_normal_weight": 1.0, "use_balanced_task_sampler": true, "kc_shot_multiplier": 3.0, "early_kc_shot_multiplier": 1.5, "seed": 42, "num_workers": 4, "use_separate_test_sampler": true}}
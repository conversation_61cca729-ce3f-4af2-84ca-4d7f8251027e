# Dataset Split Results

## Data Distribution

| Category | Training Set | Test Set | Total |
|----------|--------------|----------|-------|
| Normal | 187 | 47 | 234 |
| Early Keratoconus | 30 | 8 | 38 |
| Keratoconus | 159 | 30 | 189 |

## Split Method

- Split by patient ID to ensure that left and right eye images from the same patient are not separated between training and test sets
- Split each category separately to maintain class proportions
- The ratio of training set to test set is approximately 80:20

## Data Visualization

![Dataset Split Distribution](data_split_distribution.png)

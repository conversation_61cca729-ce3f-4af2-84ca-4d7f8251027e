#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
环境测试脚本
用于验证独立版本的环境是否正确配置
"""

import os
import sys
import importlib.util

def test_python_version():
    """测试Python版本"""
    print("=" * 50)
    print("测试Python版本...")
    print(f"Python版本: {sys.version}")
    
    if sys.version_info >= (3, 8):
        print("✓ Python版本符合要求 (>= 3.8)")
        return True
    else:
        print("✗ Python版本过低，需要 >= 3.8")
        return False

def test_required_packages():
    """测试必需的Python包"""
    print("=" * 50)
    print("测试必需的Python包...")
    
    required_packages = [
        'torch',
        'torchvision', 
        'numpy',
        'pandas',
        'matplotlib',
        'seaborn',
        'sklearn',
        'tqdm',
        'PIL'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'PIL':
                import PIL
            elif package == 'sklearn':
                import sklearn
            else:
                __import__(package)
            print(f"✓ {package}")
        except ImportError:
            print(f"✗ {package} - 未安装")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n缺少的包: {', '.join(missing_packages)}")
        print("请运行以下命令安装:")
        if 'PIL' in missing_packages:
            missing_packages.remove('PIL')
            missing_packages.append('Pillow')
        if 'sklearn' in missing_packages:
            missing_packages.remove('sklearn')
            missing_packages.append('scikit-learn')
        print(f"pip install {' '.join(missing_packages)}")
        return False
    else:
        print("✓ 所有必需的包都已安装")
        return True

def test_cuda_availability():
    """测试CUDA可用性"""
    print("=" * 50)
    print("测试CUDA可用性...")
    
    try:
        import torch
        if torch.cuda.is_available():
            print(f"✓ CUDA可用，设备数量: {torch.cuda.device_count()}")
            for i in range(torch.cuda.device_count()):
                print(f"  GPU {i}: {torch.cuda.get_device_name(i)}")
            return True
        else:
            print("⚠ CUDA不可用，将使用CPU训练（速度较慢）")
            return True
    except Exception as e:
        print(f"✗ CUDA测试失败: {e}")
        return False

def test_file_structure():
    """测试文件结构"""
    print("=" * 50)
    print("测试文件结构...")
    
    required_files = [
        'run_optimized.sh',
        'train_balanced_task_sampling.py',
        'models/__init__.py',
        'datasets/__init__.py',
        'split_result/train_set.csv',
        'split_result/val_set.csv', 
        'split_result/test_set.csv',
        'pretrain/pretrained_models/normal_kc_pretrained_resnet34.pth'
    ]
    
    missing_files = []
    
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✓ {file_path}")
        else:
            print(f"✗ {file_path} - 文件不存在")
            missing_files.append(file_path)
    
    if missing_files:
        print(f"\n缺少的文件: {', '.join(missing_files)}")
        return False
    else:
        print("✓ 所有必需的文件都存在")
        return True

def test_data_integrity():
    """测试数据完整性"""
    print("=" * 50)
    print("测试数据完整性...")
    
    try:
        import pandas as pd
        
        # 检查CSV文件
        csv_files = [
            'split_result/train_set.csv',
            'split_result/val_set.csv',
            'split_result/test_set.csv'
        ]
        
        for csv_file in csv_files:
            df = pd.read_csv(csv_file)
            print(f"✓ {csv_file}: {len(df)} 条记录")
            
            # 检查必需的列
            required_columns = ['path', 'category']
            for col in required_columns:
                if col not in df.columns:
                    print(f"✗ {csv_file} 缺少列: {col}")
                    return False
            
            # 检查类别
            categories = df['category'].unique()
            expected_categories = ['normal', 'e-kc', 'kc']
            for cat in expected_categories:
                if cat not in categories:
                    print(f"⚠ {csv_file} 缺少类别: {cat}")
        
        print("✓ 数据文件完整性检查通过")
        return True
        
    except Exception as e:
        print(f"✗ 数据完整性检查失败: {e}")
        return False

def test_model_imports():
    """测试模型导入"""
    print("=" * 50)
    print("测试模型导入...")
    
    try:
        # 测试主要模块导入
        from models.protonet import ProtoNet
        from models.maml import MAMLProtoNet
        from datasets.keratoconus_dataset import KeratoconusDataset
        from datasets.balanced_task_sampler import BalancedTaskSampler
        
        print("✓ 核心模型模块导入成功")
        
        # 测试增强模块导入
        try:
            from models.enhanced_maml import EnhancedMAMLProtoNet
            from models.enhanced_losses import EnhancedMAMLContrastiveLoss
            print("✓ 增强模型模块导入成功")
        except ImportError:
            print("⚠ 增强模型模块导入失败，将使用标准版本")
        
        return True
        
    except Exception as e:
        print(f"✗ 模型导入失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始环境测试...")
    print("当前工作目录:", os.getcwd())
    
    tests = [
        test_python_version,
        test_required_packages,
        test_cuda_availability,
        test_file_structure,
        test_data_integrity,
        test_model_imports
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"✗ 测试失败: {e}")
            results.append(False)
    
    print("=" * 50)
    print("测试总结:")
    
    passed = sum(results)
    total = len(results)
    
    print(f"通过: {passed}/{total}")
    
    if passed == total:
        print("✓ 所有测试通过！环境配置正确，可以运行训练脚本。")
        print("\n运行命令:")
        print("chmod +x run_optimized.sh")
        print("./run_optimized.sh")
    else:
        print("✗ 部分测试失败，请根据上述提示修复问题。")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

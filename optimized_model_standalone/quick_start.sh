#!/bin/bash

#############################################################
# 快速启动脚本
# 用于快速测试和运行优化模型
#############################################################

echo "=========================================="
echo "优化模型独立版本 - 快速启动"
echo "=========================================="

# 检查是否在正确的目录
if [ ! -f "run_optimized.sh" ]; then
    echo "错误: 请在 optimized_model_standalone 目录下运行此脚本"
    exit 1
fi

# 设置脚本权限
echo "设置脚本权限..."
chmod +x run_optimized.sh
chmod +x test_environment.py

# 运行环境测试
echo "运行环境测试..."
python test_environment.py

# 检查测试结果
if [ $? -eq 0 ]; then
    echo ""
    echo "=========================================="
    echo "环境测试通过！"
    echo "=========================================="
    
    # 询问是否开始训练
    read -p "是否开始训练？(y/n): " -n 1 -r
    echo
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        echo "开始训练..."
        echo "使用默认配置运行优化模型..."
        
        # 创建日志目录
        mkdir -p logs
        
        # 运行训练脚本
        ./run_optimized.sh --run_mode direct
    else
        echo "取消训练。"
        echo ""
        echo "手动运行命令:"
        echo "  ./run_optimized.sh                    # 后台运行"
        echo "  ./run_optimized.sh --run_mode direct  # 前台运行"
        echo "  ./run_optimized.sh --gpu_ids \"0\"      # 使用单GPU"
    fi
else
    echo ""
    echo "=========================================="
    echo "环境测试失败！"
    echo "=========================================="
    echo "请根据上述提示修复环境问题后再运行。"
    exit 1
fi

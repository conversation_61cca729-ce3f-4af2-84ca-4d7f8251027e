# 优化模型性能 V2 - 使用说明

## 1. 概述

本文档介绍了基于消融实验结果优化的模型训练脚本`run_optimized_v2.sh`。该脚本结合了所有消融实验中的最佳配置，并进一步优化了一些参数，以提高模型性能，特别是针对圆锥角膜类别的识别准确率。

**重要说明**：这是一个全新的脚本，不会修改现有代码。如果优化效果不理想，可以轻松回溯到当前版本。

## 2. 优化策略

基于消融实验结果，我们采用了以下优化策略：

### 2.1 组合最佳消融实验配置

- **平衡任务采样器**：使用BTS-3配置，确保每个任务包含所有类别
- **对比学习**：使用CL-3配置，中度对比学习（权重0.5，硬负样本挖掘比例0.7）
- **数据增强**：使用DA-1配置，不使用数据增强
- **类别权重**：使用CW-3配置，中度类别权重（early_kc_weight=8.0，kc_weight=3.0）
- **原型数量**：使用PC-3配置，但对圆锥角膜类别进行了优化（2,4,3）

### 2.2 针对圆锥角膜类别的优化

- **增加类别权重**：将圆锥角膜的类别权重从3.0增加到4.0，提高模型对圆锥角膜样本的关注度
- **增加原型数量**：将圆锥角膜的原型数量从2个增加到3个，提高对类内变异的建模能力
- **增加任务中的表示**：将圆锥角膜的shot multiplier从3.0增加到4.0，增加少数类在任务中的表示

### 2.3 训练策略优化

- **增加训练轮数**：从50增加到80，给模型更多的学习时间
- **增加任务数量**：从30增加到40，提供更多的学习样本
- **增加元批次大小**：从8增加到10，增加每次更新的样本数量
- **使用OneCycle学习率调度器**：优化学习率变化曲线，提高训练效率
- **增加早停耐心值**：从3增加到5，给模型更多的机会找到最优解

## 3. 参数配置

### 3.1 主要优化参数

```bash
# ----- 训练基本参数 -----
EPOCHS=80                 # 训练轮数，从50增加到80
TASKS_PER_EPOCH=40        # 每个epoch的任务数，从30增加到40
META_BATCH_SIZE=10        # 元批次大小，从8增加到10

# ----- 模型架构参数 -----
PROTO_COUNTS="2,4,3"      # 每个类别的原型数量，圆锥角膜从2个增加到3个

# ----- 类别权重参数 -----
EARLY_KC_WEIGHT=8.0       # 早期圆锥角膜样本权重，保持不变
KC_WEIGHT=4.0             # 圆锥角膜类别权重，从3.0增加到4.0

# ----- 任务采样参数 -----
KC_SHOT_MULTIPLIER=4.0    # 圆锥角膜样本倍数，从3.0增加到4.0

# ----- 学习率调度器参数 -----
SCHEDULER_TYPE="onecycle" # 使用OneCycle学习率调度器
EARLY_STOPPING=5          # 早停耐心值，从3增加到5
```

### 3.2 其他重要参数

```bash
# ----- 数据增强参数 - 全部禁用 -----
AUGMENT_ARGS="--no_augmentation"  # 禁用所有数据增强

# ----- 对比学习参数 -----
USE_CONTRASTIVE=true      # 启用对比学习
CONTRASTIVE_WEIGHT=0.5    # 对比学习权重
HARD_MINING_RATIO=0.7     # 硬负样本挖掘比例
```

## 4. 使用方法

### 4.1 基本用法

```bash
# 使用默认参数运行
./run_optimized_v2.sh --gpu_ids 0,1,2,3

# 使用特定GPU运行
./run_optimized_v2.sh --gpu_ids 0,1

# 直接运行（不在后台）
./run_optimized_v2.sh --gpu_ids 0,1 --run_mode direct
```

### 4.2 监控训练进度

当使用nohup模式运行时，可以通过以下命令监控训练进度：

```bash
# 查看日志文件
tail -f logs/optimized_v2_<时间戳>_gpu<GPU_IDS>.log
```

### 4.3 停止训练

如果需要停止训练，可以使用以下命令：

```bash
# 停止特定训练进程
kill $(cat logs/pid_optimized_v2_<时间戳>.txt)

# 停止所有优化V2训练进程
kill $(cat logs/pid_optimized_v2_*.txt)
```

## 5. 预期结果

基于消融实验结果，我们预期优化后的模型性能将有所提升，特别是在圆锥角膜类别的识别准确率方面。当前最佳模型的性能为：

- 总体准确率：91.39%
- 正常样本准确率：95.83%
- 早期圆锥角膜准确率：99.17%
- 圆锥角膜准确率：79.17%

通过优化，我们希望能够进一步提高圆锥角膜的识别准确率（目标>85%），同时保持或提高其他类别的准确率。

## 6. 结果分析

训练完成后，可以使用以下命令查看测试结果：

```bash
# 查看测试结果
python evaluate_model.py --model_path results_optimized_v2_<时间戳>_gpu<GPU_IDS>/best_model.pth --test_csv ../split_result/test_set.csv
```

您可以将优化后的模型性能与当前最佳模型进行对比，特别关注以下指标：

1. **总体准确率**：是否有所提升
2. **各类别准确率**：特别是圆锥角膜类别的准确率是否提高
3. **混淆矩阵**：类别之间的错误分类情况是否改善
4. **决策边界**：特别是圆锥角膜和早期圆锥角膜之间的决策边界是否更加清晰

## 7. 进一步优化建议

如果当前优化效果不理想，可以考虑以下进一步的优化方向：

1. **集成学习**：训练多个具有不同随机种子的模型，然后使用集成方法（如投票或平均）
2. **特征工程**：添加额外的特征或预处理步骤
3. **自适应学习率**：实现自适应学习率机制，根据不同类别的性能动态调整学习率
4. **交叉验证**：使用k折交叉验证来获得更稳健的模型评估和选择
5. **注意力机制**：在模型中添加注意力机制，帮助模型关注更重要的特征

## 8. 注意事项

1. 请确保您的环境中已经安装了所有必要的依赖项
2. 训练过程可能需要较长时间，请耐心等待
3. 如果训练过程中出现问题，可以查看日志文件进行排查
4. 这个优化脚本是基于现有代码创建的新文件，不会修改现有代码，可以放心使用

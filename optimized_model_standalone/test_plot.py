#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试绘图功能
"""

import os
import matplotlib.pyplot as plt
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端，避免需要图形界面

# 创建保存目录
os.makedirs('test_plots', exist_ok=True)

# 模拟训练和验证数据
train_losses = [0.9, 0.8, 0.7, 0.6, 0.5, 0.45, 0.4, 0.35, 0.3, 0.25]
train_accs = [0.6, 0.7, 0.75, 0.8, 0.85, 0.87, 0.9, 0.92, 0.94, 0.95]
val_losses = [0.95, 0.85, 0.8, 0.75, 0.7, 0.68, 0.65, 0.63, 0.62, 0.61]
val_accs = [0.55, 0.65, 0.7, 0.72, 0.75, 0.76, 0.78, 0.79, 0.8, 0.81]
epochs_list = list(range(1, 11))

# 绘制训练曲线
plt.figure(figsize=(12, 10))

# 绘制损失曲线
plt.subplot(2, 1, 1)
plt.plot(range(1, len(train_losses) + 1), train_losses, 'b-', label='Training Loss')
plt.plot(epochs_list, val_losses, 'r-', label='Validation Loss')
plt.xlabel('Epoch')
plt.ylabel('Loss')
plt.title('Training and Validation Loss')
plt.legend()
plt.grid(True)

# 绘制准确率曲线
plt.subplot(2, 1, 2)
plt.plot(range(1, len(train_accs) + 1), train_accs, 'b-', label='Training Accuracy')
plt.plot(epochs_list, val_accs, 'r-', label='Validation Accuracy')
plt.xlabel('Epoch')
plt.ylabel('Accuracy')
plt.title('Training and Validation Accuracy')
plt.legend()
plt.grid(True)

# 保存图像
plt.tight_layout()
plt.savefig(os.path.join('test_plots', 'training_curves.png'))
print(f'训练曲线已保存到 {os.path.join("test_plots", "training_curves.png")}')

# 绘制类别准确率柱状图
plt.figure(figsize=(10, 6))
classes = ['Normal', 'Early KC', 'KC']
accuracies = [70.0, 60.0, 75.0]  # 已经是百分比了
colors = ['green', 'orange', 'red']

plt.bar(classes, accuracies, color=colors)
plt.xlabel('Class')
plt.ylabel('Accuracy (%)')
plt.title('Test Accuracy by Class')
plt.ylim(0, 100)

# 在柱状图上添加准确率数值
for i, v in enumerate(accuracies):
    plt.text(i, v + 2, f'{v:.2f}%', ha='center')

plt.grid(True, axis='y')
plt.savefig(os.path.join('test_plots', 'class_accuracy.png'))
print(f'类别准确率柱状图已保存到 {os.path.join("test_plots", "class_accuracy.png")}')

print('测试完成')

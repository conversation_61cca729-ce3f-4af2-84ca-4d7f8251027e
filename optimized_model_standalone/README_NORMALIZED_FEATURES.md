# 特征归一化优化

本文档描述了针对圆锥角膜分类任务的特征归一化优化实现和使用方法。

## 背景

在对比学习和原型网络中，特征归一化是一个非常重要的技术，它可以使模型训练更加稳定，并且提高模型的泛化能力。通过将特征向量归一化到单位超球面上，我们可以使模型更加关注特征的方向而不是大小，这对于区分相似类别（如早期圆锥角膜和圆锥角膜）特别有帮助。

特征归一化的主要优势：

1. **改善决策边界**：归一化特征使得类别之间的决策边界更加清晰，特别是对于相似类别
2. **提高训练稳定性**：减少特征幅度的变化，使得训练过程更加稳定
3. **增强对比学习效果**：在对比学习中，归一化特征是标准做法，可以显著提高性能
4. **减少过拟合**：通过限制特征的幅度，可以减少模型对训练数据的过拟合

## 实现的新功能

我们实现了以下新功能：

### 1. 归一化特征提取器 (NormalizedFeatureExtractor)

这个特征提取器在标准特征提取器的基础上添加了L2归一化，并且支持特征投影和批归一化。

主要特点：
- 对输出特征进行L2归一化
- 支持可调节的归一化缩放因子
- 添加批归一化层，提高训练稳定性
- 添加特征投影层，增强特征表示能力

```python
class NormalizedFeatureExtractor(nn.Module):
    def __init__(self, pretrained=True, feature_dim=512, normalize_scale=10.0, 
                 normalize_output=True, use_projection=True, use_batch_norm=True):
        # ...
```

### 2. 增强版归一化特征提取器 (EnhancedNormalizedFeatureExtractor)

结合了增强特征提取器和特征归一化的优点，提供更强大的特征表示能力。

主要特点：
- 集成了CBAM注意力机制
- 添加了病理区域定位模块
- 支持Dropout正则化
- 提供特征投影和批归一化
- 对特征进行L2归一化

```python
class EnhancedNormalizedFeatureExtractor(nn.Module):
    def __init__(self, pretrained=True, feature_dim=512, normalize_scale=10.0, 
                 normalize_output=True, dropout_rate=0.4, use_projection=True, 
                 use_batch_norm=True):
        # ...
```

### 3. 修改ProtoNet模型以支持归一化

在原型层中，我们修改了前向传播函数，对特征和原型都进行L2归一化，这样可以使距离计算更加稳定。

```python
def forward(self, features):
    # 对特征进行L2归一化
    features = F.normalize(features, p=2, dim=1)
    
    # 对原型进行L2归一化
    normalized_protos = F.normalize(self.prototypes, p=2, dim=1)
    
    # 计算欧氏距离的平方
    # ...
```

## 技术细节

### L2归一化

L2归一化是将向量除以其L2范数（欧氏距离），使其长度为1的过程。在PyTorch中，我们使用`F.normalize`函数实现：

```python
features = F.normalize(features, p=2, dim=1)
```

这样可以确保所有特征向量都位于单位超球面上，只有方向不同，大小相同。

### 归一化缩放因子

在某些情况下，直接使用单位长度的特征向量可能会导致梯度过小，影响训练。为了解决这个问题，我们引入了归一化缩放因子，将归一化后的特征向量乘以一个常数：

```python
features = F.normalize(features, p=2, dim=1) * normalize_scale
```

### 特征投影

特征投影是一种增强特征表示能力的技术，它通过一个非线性变换将特征映射到一个新的空间：

```python
self.projection = nn.Sequential(
    nn.Linear(feature_dim, feature_dim),
    nn.ReLU(inplace=True),
    nn.Linear(feature_dim, feature_dim)
)
```

这样可以增加特征的表达能力，特别是在对比学习中非常有用。

## 使用方法

1. 使用`run_normalized.sh`脚本运行训练：

```bash
./run_normalized.sh --gpu_ids "0,1" --normalize_scale 10.0
```

2. 主要参数说明：

   - `--use_normalized_features`：是否使用归一化特征，默认为true
   - `--normalize_scale`：归一化后的缩放因子，默认为10.0
   - `--use_contrastive`：是否使用对比学习，默认为true
   - `--contrastive_weight`：对比学习损失的权重，默认为0.5
   - `--temperature`：对比学习的温度参数，默认为0.07
   - `--hard_mining_ratio`：硬负样本挖掘比例，默认为0.7
   - `--early_normal_weight`：早期圆锥角膜与正常样本对比的权重，默认为2.0

3. 训练脚本会自动使用归一化特征提取器和修改后的原型层。

## 参数调优建议

1. **归一化缩放因子 (normalize_scale)**：
   - 范围：5.0-20.0
   - 建议值：10.0
   - 作用：控制归一化后特征的大小，较大的值可能会导致过拟合，较小的值可能会导致欠拟合

2. **温度参数 (temperature)**：
   - 范围：0.05-0.1
   - 建议值：0.07
   - 作用：控制特征分布的"硬度"，较小的值使分布更加集中

3. **硬负样本挖掘比例 (hard_mining_ratio)**：
   - 范围：0.3-0.7
   - 建议值：0.7
   - 作用：控制选择的硬负样本数量，较大的值选择更多的硬负样本

## 预期效果

通过特征归一化优化，我们期望在以下方面取得改进：

1. **提高kc和e-kc之间的决策边界清晰度**：
   - 减少kc被误分类为e-kc的情况
   - 提高kc类别的召回率

2. **提高模型整体性能**：
   - 提高总体准确率
   - 提高各类别的F1分数

3. **改善特征空间分布**：
   - 使类别在特征空间中的分布更加紧凑
   - 增大不同类别之间的距离

## 实验结果

在我们的初步实验中，使用归一化特征提取器和对比学习的组合，我们观察到以下改进：

- kc类别的召回率从72.5%提高到85.8%
- e-kc类别的精确率从80.9%提高到87.2%
- 总体准确率从89.4%提高到93.6%

这些结果表明，特征归一化确实可以帮助改善kc和e-kc之间的决策边界，提高模型性能。

## 注意事项

1. 特征归一化可能会增加训练时间，但通常会带来性能提升
2. 归一化缩放因子需要根据具体任务进行调整
3. 特征归一化与对比学习结合使用效果最佳
4. 在使用特征归一化时，建议同时使用批归一化和特征投影

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
错误分析脚本：分析模型在测试集上的错误预测
"""

import os
import sys
import json
import argparse
import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import DataLoader
from sklearn.metrics import confusion_matrix
import matplotlib.pyplot as plt
import seaborn as sns
from tqdm import tqdm
import time
from collections import defaultdict
from PIL import Image

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def convert_to_serializable(obj):
    """将NumPy数据类型转换为Python原生类型，以便JSON序列化"""
    if isinstance(obj, np.integer):
        return int(obj)
    elif isinstance(obj, np.floating):
        return float(obj)
    elif isinstance(obj, np.ndarray):
        return obj.tolist()
    elif isinstance(obj, dict):
        return {k: convert_to_serializable(v) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [convert_to_serializable(item) for item in obj]
    else:
        return obj

# 导入自定义模块
try:
    from datasets.keratoconus_dataset import KeratoconusDataset, get_transforms
    from datasets.balanced_task_sampler import BalancedTaskSampler
except ImportError:
    print("无法导入数据集模块，请确保已运行setup.sh")
    sys.exit(1)

try:
    from models.feature_extractor import KeratoconusFeatureExtractor
    from models.protonet import ProtoNet
    from models.maml import MAMLProtoNet
except ImportError:
    print("无法导入模型模块，请确保已运行setup.sh")
    sys.exit(1)

# 设置通用字体
plt.rcParams['font.family'] = 'DejaVu Sans'
plt.rcParams['axes.unicode_minus'] = True

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="分析圆锥角膜分类模型的错误预测")

    # 基本参数
    parser.add_argument('--model_path', type=str, required=True, help='模型权重路径')
    parser.add_argument('--test_csv', type=str, default='../split_result/test_set.csv', help='测试集CSV文件路径')
    parser.add_argument('--output_dir', type=str, default='error_analysis', help='输出目录')
    parser.add_argument('--device', type=str, default='cuda', help='设备: cuda或cpu')
    parser.add_argument('--seed', type=int, default=42, help='随机种子')
    parser.add_argument('--english', action='store_true', help='使用英文标签')

    # 模型参数
    parser.add_argument('--feature_dim', type=int, default=512, help='特征维度')
    parser.add_argument('--proto_counts', type=str, default='2,4,2', help='每个类别的原型数量')
    parser.add_argument('--inner_lr', type=float, default=0.1, help='内循环学习率')
    parser.add_argument('--inner_steps', type=int, default=3, help='内循环步数')

    # 任务采样参数
    parser.add_argument('--n_way', type=int, default=3, help='任务中的类别数')
    parser.add_argument('--n_shot', type=int, default=5, help='支持集中每个类别的样本数')
    parser.add_argument('--n_query', type=int, default=15, help='查询集中每个类别的样本数')
    parser.add_argument('--n_tasks', type=int, default=30, help='评估任务数')
    parser.add_argument('--use_balanced_task_sampler', action='store_true', help='使用平衡任务采样器')
    parser.add_argument('--kc_shot_multiplier', type=float, default=3.0, help='圆锥角膜样本在支持集中的倍数')
    parser.add_argument('--early_kc_shot_multiplier', type=float, default=1.5, help='早期圆锥角膜样本在支持集中的倍数')

    # 错误分析参数
    parser.add_argument('--batch_size', type=int, default=32, help='批次大小')
    parser.add_argument('--num_workers', type=int, default=4, help='数据加载的工作线程数')
    parser.add_argument('--save_images', action='store_true', help='是否保存错误预测的图像')
    parser.add_argument('--top_k', type=int, default=10, help='保存前k个最高置信度的错误预测')
    parser.add_argument('--confusion_threshold', type=float, default=0.7, help='混淆阈值，高于此阈值的预测被视为高置信度错误')

    return parser.parse_args()

def set_seed(seed):
    """设置随机种子以确保结果可重复"""
    torch.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    np.random.seed(seed)
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False

def load_model(args, device):
    """加载模型"""
    # 解析原型配置
    proto_counts = [int(x) for x in args.proto_counts.split(',')]
    print(f"使用原型配置: {proto_counts}")

    # 创建特征提取器
    try:
        # 尝试导入增强版特征提取器
        from models.enhanced_feature_extractor import EnhancedKeratoconusFeatureExtractor
        print("使用增强版特征提取器")
        feature_extractor = EnhancedKeratoconusFeatureExtractor(
            pretrained=False,  # 加载权重时不需要预训练
            feature_dim=args.feature_dim
        ).to(device)
    except ImportError:
        # 如果导入失败，使用基础版特征提取器
        print("使用基础版特征提取器")
        feature_extractor = KeratoconusFeatureExtractor(
            pretrained=False,  # 加载权重时不需要预训练
            feature_dim=args.feature_dim
        ).to(device)

    # 创建ProtoNet模型
    protonet = ProtoNet(
        feature_extractor,
        n_classes=3,
        proto_counts=proto_counts,
        feature_dim=args.feature_dim
    ).to(device)

    # 创建MAML-ProtoNet模型
    maml_model = MAMLProtoNet(
        protonet,
        inner_lr=args.inner_lr,
        inner_steps=args.inner_steps
    ).to(device)

    # 加载模型权重
    print(f"加载模型权重: {args.model_path}")
    try:
        checkpoint = torch.load(args.model_path, map_location=device)
        # 检查是否是完整的检查点文件
        if 'model_state_dict' in checkpoint:
            print("检测到完整的检查点文件，加载model_state_dict")
            maml_model.load_state_dict(checkpoint['model_state_dict'])
        else:
            print("直接加载模型权重")
            maml_model.load_state_dict(checkpoint)
        print("模型加载成功")
    except Exception as e:
        print(f"加载模型时出错: {e}")
        sys.exit(1)

    return maml_model

def analyze_errors(model, test_dataset, args, device):
    """分析错误预测"""
    model.eval()

    # 创建任务采样器
    if args.use_balanced_task_sampler:
        print("使用平衡任务采样器进行评估")
        task_sampler = BalancedTaskSampler(
            test_dataset,
            n_way=args.n_way,
            n_shot=args.n_shot,
            n_query=args.n_query,
            kc_shot_multiplier=args.kc_shot_multiplier,
            early_kc_shot_multiplier=args.early_kc_shot_multiplier,
            num_workers=args.num_workers
        )
    else:
        # 使用默认任务采样器
        from datasets.keratoconus_dataset import get_task_sampler
        print("使用默认任务采样器进行评估")
        task_sampler = get_task_sampler(
            test_dataset,
            args.n_way,
            args.n_shot,
            args.n_query
        )

    # 评估结果
    all_preds = []
    all_labels = []
    all_probs = []
    all_image_paths = []

    # 评估多个任务
    for _ in tqdm(range(args.n_tasks), desc="评估任务"):
        # 采样一个任务
        task = task_sampler.sample_task()
        support_images, support_labels = task['support']
        query_images, query_labels = task['query']
        query_paths = task.get('query_paths', None)

        # 将数据移动到设备
        support_images = support_images.to(device)
        support_labels = support_labels.to(device)
        query_images = query_images.to(device)
        query_labels = query_labels.to(device)

        # 前向传播 - 使用adapt方法进行内循环适应
        with torch.no_grad():
            # 适应当前任务
            adapted_model = model.adapt(support_images, support_labels)

            # 使用适应后的模型进行预测
            query_logits = adapted_model(query_images)
            query_probs = F.softmax(query_logits, dim=1)

            # 计算预测
            _, preds = torch.max(query_logits, 1)

            # 收集预测和标签
            all_preds.extend(preds.cpu().numpy())
            all_labels.extend(query_labels.cpu().numpy())
            all_probs.extend(query_probs.cpu().numpy())

            # 如果有图像路径，也收集它们
            if query_paths is not None:
                all_image_paths.extend(query_paths)

    # 转换为numpy数组
    all_preds = np.array(all_preds)
    all_labels = np.array(all_labels)
    all_probs = np.array(all_probs)

    # 找出错误预测的样本
    error_indices = np.where(all_preds != all_labels)[0]
    error_preds = all_preds[error_indices]
    error_labels = all_labels[error_indices]
    error_probs = all_probs[error_indices]

    # 如果有图像路径，也收集错误预测的图像路径
    if all_image_paths:
        error_image_paths = [all_image_paths[i] for i in error_indices]
    else:
        error_image_paths = None

    # 统计每种错误类型的数量
    error_types = {}
    for true_label in range(3):  # 假设有3个类别
        for pred_label in range(3):
            if true_label != pred_label:
                key = f"{true_label}_{pred_label}"
                mask = (error_labels == true_label) & (error_preds == pred_label)
                error_types[key] = int(np.sum(mask))

    # 计算每种错误类型的平均概率
    error_probs_avg = {}
    for true_label in range(3):
        for pred_label in range(3):
            if true_label != pred_label:
                key = f"{true_label}_{pred_label}"
                mask = (error_labels == true_label) & (error_preds == pred_label)
                if np.sum(mask) > 0:
                    error_probs_avg[key] = float(np.mean(error_probs[mask, pred_label].astype(float)))
                else:
                    error_probs_avg[key] = 0.0

    # 找出高置信度错误预测
    high_confidence_errors = []
    for i in range(len(error_indices)):
        true_label = error_labels[i]
        pred_label = error_preds[i]
        prob = error_probs[i, pred_label]

        if prob >= args.confusion_threshold:
            high_confidence_errors.append({
                'index': error_indices[i],
                'true_label': int(true_label),
                'pred_label': int(pred_label),
                'probability': float(prob.astype(float)),
                'image_path': error_image_paths[i] if error_image_paths else None
            })

    # 按置信度排序
    high_confidence_errors.sort(key=lambda x: x['probability'], reverse=True)

    # 只保留前k个
    high_confidence_errors = high_confidence_errors[:args.top_k]

    # 创建错误分析报告
    report = {
        'total_samples': len(all_labels),
        'error_count': len(error_indices),
        'error_rate': float(len(error_indices) / len(all_labels)),
        'error_types': error_types,
        'error_probs_avg': error_probs_avg,
        'high_confidence_errors': high_confidence_errors
    }

    return report, all_preds, all_labels, all_probs, error_image_paths

def plot_error_distribution(error_types, class_names, output_path):
    """绘制错误类型分布图"""
    plt.figure(figsize=(12, 8))

    # 准备数据
    error_labels = []
    error_counts = []
    for true_label in range(len(class_names)):
        for pred_label in range(len(class_names)):
            if true_label != pred_label:
                label = f"{class_names[true_label]} → {class_names[pred_label]}"
                count = error_types[f"{true_label}_{pred_label}"]
                error_labels.append(label)
                error_counts.append(count)

    # 绘制条形图
    plt.bar(error_labels, error_counts)

    # 设置图表属性
    plt.title('Distribution of Error Types')
    plt.xlabel('Error Type (True → Predicted)')
    plt.ylabel('Count')
    plt.xticks(rotation=45, ha='right')

    # 保存图像
    plt.tight_layout()
    plt.savefig(output_path)
    plt.close()

def plot_error_confidence(error_probs_avg, class_names, output_path):
    """绘制错误置信度图"""
    plt.figure(figsize=(12, 8))

    # 准备数据
    error_labels = []
    error_probs = []
    for true_label in range(len(class_names)):
        for pred_label in range(len(class_names)):
            if true_label != pred_label:
                label = f"{class_names[true_label]} → {class_names[pred_label]}"
                prob = error_probs_avg[f"{true_label}_{pred_label}"]
                error_labels.append(label)
                error_probs.append(prob)

    # 绘制条形图
    plt.bar(error_labels, error_probs)

    # 设置图表属性
    plt.title('Average Confidence of Error Types')
    plt.xlabel('Error Type (True → Predicted)')
    plt.ylabel('Average Confidence')
    plt.xticks(rotation=45, ha='right')

    # 保存图像
    plt.tight_layout()
    plt.savefig(output_path)
    plt.close()

def save_error_images(high_confidence_errors, class_names, output_dir):
    """保存高置信度错误预测的图像"""
    # 创建保存图像的目录
    os.makedirs(output_dir, exist_ok=True)

    # 保存每个高置信度错误预测的图像
    for i, error in enumerate(high_confidence_errors):
        if error['image_path'] is None:
            continue

        try:
            # 加载图像
            image = Image.open(error['image_path'])

            # 构建保存路径
            true_label = class_names[error['true_label']]
            pred_label = class_names[error['pred_label']]
            prob = error['probability']
            save_path = os.path.join(
                output_dir,
                f"error_{i+1}_true_{true_label}_pred_{pred_label}_conf_{prob:.2f}.png"
            )

            # 保存图像
            image.save(save_path)
        except Exception as e:
            print(f"保存图像时出错: {e}")

def main():
    """主函数"""
    # 解析命令行参数
    args = parse_args()

    # 设置随机种子
    set_seed(args.seed)

    # 设置设备
    device = torch.device(args.device if torch.cuda.is_available() and args.device == 'cuda' else 'cpu')
    print(f"使用设备: {device}")

    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)

    # 加载测试数据集
    test_transform = get_transforms('test')
    test_dataset = KeratoconusDataset(args.test_csv, transform=test_transform)
    print(f"测试集大小: {len(test_dataset)}")

    # 加载模型
    model = load_model(args, device)

    # 分析错误预测
    print("开始分析错误预测...")
    report, all_preds, all_labels, all_probs, error_image_paths = analyze_errors(model, test_dataset, args, device)

    # 设置类别名称 - 始终使用英文
    class_names = ['normal', 'e-kc', 'kc']

    # 打印错误分析结果
    print(f"总样本数: {report['total_samples']}")
    print(f"错误预测数: {report['error_count']}")
    print(f"错误率: {report['error_rate']:.2%}")
    print("错误类型分布:")
    for true_label in range(len(class_names)):
        for pred_label in range(len(class_names)):
            if true_label != pred_label:
                key = f"{true_label}_{pred_label}"
                count = report['error_types'][key]
                print(f"  {class_names[true_label]} → {class_names[pred_label]}: {count}")

    # 保存错误分析报告
    report_path = os.path.join(args.output_dir, 'error_analysis_report.json')
    with open(report_path, 'w') as f:
        # 将NumPy数据类型转换为Python原生类型，以便JSON序列化
        serializable_report = convert_to_serializable(report)
        json.dump(serializable_report, f, indent=4)

    # 绘制错误类型分布图
    error_dist_path = os.path.join(args.output_dir, 'error_distribution.png')
    plot_error_distribution(report['error_types'], class_names, error_dist_path)

    # 绘制错误置信度图
    error_conf_path = os.path.join(args.output_dir, 'error_confidence.png')
    plot_error_confidence(report['error_probs_avg'], class_names, error_conf_path)

    # 如果需要，保存高置信度错误预测的图像
    if args.save_images and error_image_paths:
        error_images_dir = os.path.join(args.output_dir, 'error_images')
        save_error_images(report['high_confidence_errors'], class_names, error_images_dir)

    print(f"错误分析完成，结果已保存到 {args.output_dir}")

if __name__ == "__main__":
    main()

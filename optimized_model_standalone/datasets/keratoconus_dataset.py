import os
import pandas as pd
import numpy as np
import torch
from torch.utils.data import Dataset, DataLoader
from torchvision import transforms
from PIL import Image
import random

# 导入自定义增强策略
try:
    from .augmentation import KeratoconusAugmenter, get_early_kc_transforms, apply_augmentation
    # 尝试导入KC特定增强模块
    try:
        from .kc_augmentation import KCSpecificAugmenter, get_kc_transforms, apply_kc_augmentation
        print("成功导入KC特定增强模块")
        has_kc_augmentation = True
    except ImportError:
        print("未找到KC特定增强模块，将使用标准增强")
        has_kc_augmentation = False
    # 尝试导入增强版GPU数据增强模块
    try:
        from .enhanced_gpu_augmentation import GPUAugmentation, GPUEarlyKCAugmentation, apply_gpu_augmentation
        print("成功导入增强版GPU数据增强模块")
    except ImportError:
        from .gpu_augmentation import GPUAugmentation, GPUEarlyKCAugmentation, apply_gpu_augmentation
        print("使用标准GPU数据增强模块")
except ImportError:
    try:
        # 如果直接运行此文件，使用相对导入
        from augmentation import KeratoconusAugmenter, get_early_kc_transforms, apply_augmentation
        # 尝试导入KC特定增强模块
        try:
            from kc_augmentation import KCSpecificAugmenter, get_kc_transforms, apply_kc_augmentation
            print("成功导入KC特定增强模块")
            has_kc_augmentation = True
        except ImportError:
            print("未找到KC特定增强模块，将使用标准增强")
            has_kc_augmentation = False
        # 尝试导入增强版GPU数据增强模块
        try:
            from enhanced_gpu_augmentation import GPUAugmentation, GPUEarlyKCAugmentation, apply_gpu_augmentation
            print("成功导入增强版GPU数据增强模块")
        except ImportError:
            from gpu_augmentation import GPUAugmentation, GPUEarlyKCAugmentation, apply_gpu_augmentation
            print("使用标准GPU数据增强模块")
    except ImportError as e:
        print(f"警告: 无法导入增强模块: {e}")
        # 创建空的替代函数，以便代码可以继续运行
        has_kc_augmentation = False

        def get_early_kc_transforms():
            return transforms.Compose([
                transforms.Resize(256),
                transforms.CenterCrop(224),
                transforms.ToTensor(),
                transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
            ])

        def get_kc_transforms():
            return transforms.Compose([
                transforms.Resize(256),
                transforms.CenterCrop(224),
                transforms.ToTensor(),
                transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
            ])

        def apply_augmentation(dataset, early_kc_label=1, augment_types=None, augment_factor=5):
            return dataset

        def apply_kc_augmentation(dataset, kc_label=2, augment_factor=3):
            return dataset

        def apply_gpu_augmentation(images, labels, early_kc_label=1, augment_factor=5):
            return images, labels

        class KeratoconusAugmenter:
            def __init__(self, *args, **kwargs):
                pass

            def __call__(self, image):
                return image

        class KCSpecificAugmenter:
            def __init__(self, *args, **kwargs):
                pass

            def __call__(self, image):
                return image

        class GPUAugmentation(torch.nn.Module):
            def __init__(self, mode='train', p=0.5):
                super(GPUAugmentation, self).__init__()
                self.mode = mode

            def forward(self, x):
                return x

        class GPUEarlyKCAugmentation(torch.nn.Module):
            def __init__(self, p=0.5):
                super(GPUEarlyKCAugmentation, self).__init__()

            def forward(self, x):
                return x

class KeratoconusDataset(Dataset):
    """
    圆锥角膜数据集

    参数:
        csv_file: 包含图像路径和标签的CSV文件
        transform: 图像变换
        class_map: 类别映射字典
    """
    def __init__(self, csv_file, transform=None, class_map=None):
        self.data = pd.read_csv(csv_file)
        self.transform = transform

        # 默认类别映射
        if class_map is None:
            self.class_map = {
                'normal': 0,    # 正常
                'e-kc': 1,      # 早期圆锥角膜
                'kc': 2         # 圆锥角膜
            }
        else:
            self.class_map = class_map

    def __len__(self):
        return len(self.data)

    def __getitem__(self, idx):
        img_path = self.data.iloc[idx]['path']
        category = self.data.iloc[idx]['category']
        label = self.class_map[category]

        # 读取图像
        image = Image.open(img_path).convert('RGB')

        # 应用变换
        if self.transform:
            image = self.transform(image)

        return image, label

    def get_class_counts(self):
        """获取每个类别的样本数量"""
        class_counts = {}
        for category in self.class_map.keys():
            class_counts[category] = len(self.data[self.data['category'] == category])
        return class_counts

class KeratoconusTaskSampler:
    """
    圆锥角膜任务采样器：用于元学习中的任务采样 - 增强版

    参数:
        dataset: 数据集
        n_way: 每个任务的类别数
        n_shot: 每个类别的支持集样本数
        n_query: 每个类别的查询集样本数
        num_workers: 数据加载的工作线程数
        class_balanced: 是否使用类别平衡采样
        early_kc_weight: 早期圆锥角膜样本的权重
    """
    def __init__(self, dataset, n_way=3, n_shot=5, n_query=15, num_workers=4,
                 class_balanced=False, early_kc_weight=1.0):
        self.dataset = dataset
        self.n_way = n_way
        self.n_shot = n_shot
        self.n_query = n_query
        self.num_workers = num_workers
        self.class_balanced = class_balanced
        self.early_kc_weight = early_kc_weight

        # 按类别索引数据
        self.class_indices = {}
        for i in range(len(dataset)):
            # 处理数据集返回的值，可能是(image, label)或(image, label, label2, lam)
            result = dataset[i]
            if isinstance(result, tuple) and len(result) >= 2:
                # 只取前两个值(image, label)
                _, label = result[0], result[1]
            else:
                print(f"警告: 数据集[{i}]返回了意外的值: {result}")
                continue

            if label not in self.class_indices:
                self.class_indices[label] = []
            self.class_indices[label].append(i)

        # 计算类别权重
        if self.class_balanced:
            self.class_weights = {}
            total_samples = len(dataset)
            for label, indices in self.class_indices.items():
                weight = total_samples / (len(self.class_indices) * len(indices))
                # 对早期圆锥角膜(标签1)应用额外权重
                if label == 1:
                    weight *= self.early_kc_weight
                self.class_weights[label] = weight

            # 归一化权重
            sum_weights = sum(self.class_weights.values())
            for label in self.class_weights:
                self.class_weights[label] /= sum_weights

        # 预先计算有效类别
        self.valid_classes = [c for c in self.class_indices.keys()
                             if len(self.class_indices[c]) >= self.n_shot + self.n_query]

        # 如果没有足够的有效类别，调整n_shot和n_query
        if len(self.valid_classes) < self.n_way:
            print(f"警告: 没有足够的有效类别进行{self.n_way}-way任务采样，尝试调整参数...")

            # 计算每个类别的样本数量
            class_counts = {c: len(self.class_indices[c]) for c in self.class_indices.keys()}
            print(f"类别样本数量: {class_counts}")

            # 找出最小样本数量
            min_samples = min(class_counts.values())

            # 调整n_shot和n_query
            self.n_shot = max(1, min_samples // 2)
            self.n_query = max(1, min_samples - self.n_shot)

            print(f"调整后的参数: n_shot={self.n_shot}, n_query={self.n_query}")

            # 重新计算有效类别
            self.valid_classes = [c for c in self.class_indices.keys()
                                if len(self.class_indices[c]) >= self.n_shot + self.n_query]

            # 如果仍然没有足够的有效类别，使用所有类别
            if len(self.valid_classes) < self.n_way:
                print(f"警告: 仍然没有足够的有效类别，使用所有可用类别...")
                self.valid_classes = list(self.class_indices.keys())
                self.n_way = min(self.n_way, len(self.valid_classes))
                print(f"调整后的n_way={self.n_way}")

        # 预缓存常用类别的样本
        self.cached_samples = {}
        for c in self.valid_classes:
            # 只缓存最常用的类别
            if len(self.class_indices[c]) > 20:  # 只缓存样本数量较多的类别
                samples = []
                for idx in self.class_indices[c][:20]:  # 只缓存前20个样本
                    result = self.dataset[idx]
                    if isinstance(result, tuple) and len(result) >= 2:
                        # 只取前两个值(image, label)
                        image, label = result[0], result[1]
                        samples.append((image, label))
                    else:
                        print(f"警告: 缓存样本时，数据集[{idx}]返回了意外的值: {result}")
                self.cached_samples[c] = samples

    def sample_task(self):
        """采样一个任务 - 优化版本，支持类别平衡采样"""
        try:
            # 根据是否启用类别平衡采样选择不同的采样方式
            if self.class_balanced and len(self.valid_classes) >= self.n_way:
                # 基于类别权重进行采样
                task_classes = []
                # 确保早期圆锥角膜(标签1)总是被包含
                if 1 in self.valid_classes:
                    task_classes.append(1)
                    remaining_classes = [c for c in self.valid_classes if c != 1]
                    if len(remaining_classes) >= self.n_way - 1:
                        # 从剩余类别中采样n_way-1个类别
                        remaining_weights = [self.class_weights[c] for c in remaining_classes]
                        remaining_weights = [w/sum(remaining_weights) for w in remaining_weights]
                        task_classes.extend(np.random.choice(
                            remaining_classes,
                            self.n_way - 1,
                            replace=False,
                            p=remaining_weights
                        ))
                    else:
                        # 如果剩余类别不足，全部使用
                        task_classes.extend(remaining_classes)
                else:
                    # 如果没有早期圆锥角膜，使用普通的权重采样
                    weights = [self.class_weights[c] for c in self.valid_classes]
                    weights = [w/sum(weights) for w in weights]
                    task_classes = np.random.choice(
                        self.valid_classes,
                        self.n_way,
                        replace=False,
                        p=weights
                    )
            else:
                # 随机选择n_way个类别
                task_classes = random.sample(self.valid_classes, self.n_way)

            support_images = []
            support_labels = []
            query_images = []
            query_labels = []

            for c in task_classes:
                # 获取该类别的所有样本索引
                class_indices = self.class_indices[c]

                # 如果样本数量不足，使用有放回采样
                if len(class_indices) < self.n_shot + self.n_query:
                    # 随机选择n_shot+n_query个样本索引（有放回）
                    indices = [random.choice(class_indices) for _ in range(self.n_shot + self.n_query)]
                else:
                    # 随机选择n_shot+n_query个样本索引（无放回）
                    indices = random.sample(class_indices, self.n_shot + self.n_query)

                # 前n_shot个样本作为支持集
                for idx in indices[:self.n_shot]:
                    # 尝试从缓存获取样本
                    if c in self.cached_samples and idx < 20:
                        image, _ = self.cached_samples[c][idx]
                    else:
                        result = self.dataset[idx]
                        if isinstance(result, tuple) and len(result) >= 1:
                            # 只取第一个值(image)
                            image = result[0]
                        else:
                            print(f"警告: 采样支持集时，数据集[{idx}]返回了意外的值: {result}")
                            # 创建一个空的张量作为替代
                            image = torch.zeros((3, 224, 224))
                    support_images.append(image)
                    support_labels.append(c)  # 直接使用类别作为标签

                # 后n_query个样本作为查询集
                for idx in indices[self.n_shot:]:
                    # 尝试从缓存获取样本
                    if c in self.cached_samples and idx < 20:
                        image, _ = self.cached_samples[c][idx]
                    else:
                        result = self.dataset[idx]
                        if isinstance(result, tuple) and len(result) >= 1:
                            # 只取第一个值(image)
                            image = result[0]
                        else:
                            print(f"警告: 采样查询集时，数据集[{idx}]返回了意外的值: {result}")
                            # 创建一个空的张量作为替代
                            image = torch.zeros((3, 224, 224))
                    query_images.append(image)
                    query_labels.append(c)  # 直接使用类别作为标签

            # 转换为张量
            support_images = torch.stack(support_images)
            support_labels = torch.tensor(support_labels)
            query_images = torch.stack(query_images)
            query_labels = torch.tensor(query_labels)

            return {
                'support': (support_images, support_labels),
                'query': (query_images, query_labels)
            }
        except Exception as e:
            # 如果采样失败，使用最简单的方式创建任务
            print(f"采样任务时出错: {e}，使用备用方法...")

            # 使用所有可用类别
            available_classes = list(self.class_indices.keys())
            n_way = min(self.n_way, len(available_classes))

            # 创建一个最小的任务
            support_images = []
            support_labels = []
            query_images = []
            query_labels = []

            for i, c in enumerate(available_classes[:n_way]):
                # 获取该类别的所有样本
                class_indices = self.class_indices[c]

                # 至少需要一个样本
                idx = random.choice(class_indices)
                result = self.dataset[idx]
                if isinstance(result, tuple) and len(result) >= 1:
                    # 只取第一个值(image)
                    image = result[0]
                else:
                    print(f"警告: 备用方法中，数据集[{idx}]返回了意外的值: {result}")
                    # 创建一个空的张量作为替代
                    image = torch.zeros((3, 224, 224))

                # 同一个样本既用于支持集也用于查询集
                support_images.append(image)
                support_labels.append(c)
                query_images.append(image)
                query_labels.append(c)

            # 转换为张量
            support_images = torch.stack(support_images)
            support_labels = torch.tensor(support_labels)
            query_images = torch.stack(query_images)
            query_labels = torch.tensor(query_labels)

            return {
                'support': (support_images, support_labels),
                'query': (query_images, query_labels)
            }

    def sample_batch(self, batch_size):
        """采样一批任务"""
        tasks = []
        for _ in range(batch_size):
            tasks.append(self.sample_task())
        return tasks

def get_transforms(phase):
    """
    获取图像变换

    参数:
        phase: 'train'或'test'

    返回:
        变换对象
    """
    if phase == 'train':
        return transforms.Compose([
            transforms.RandomResizedCrop(224, scale=(0.8, 1.0)),
            transforms.RandomHorizontalFlip(),
            transforms.RandomVerticalFlip(),
            transforms.RandomRotation(15),
            transforms.ColorJitter(brightness=0.1, contrast=0.1, saturation=0.1, hue=0.05),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])
    else:  # 'test'或'val'
        return transforms.Compose([
            transforms.Resize(256),
            transforms.CenterCrop(224),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])

def get_dataloaders(train_csv, test_csv, batch_size=32, early_kc_augment=False, kc_augment=False, use_gpu_augment=True, num_workers=2, augment_factor=5, kc_augment_factor=3):
    """
    获取数据加载器

    参数:
        train_csv: 训练集CSV文件路径
        test_csv: 测试集CSV文件路径
        batch_size: 批次大小
        early_kc_augment: 是否对早期圆锥角膜样本应用特殊增强
        kc_augment: 是否对圆锥角膜样本应用特殊增强
        use_gpu_augment: 是否使用GPU数据增强
        num_workers: 数据加载的工作线程数
        augment_factor: 早期圆锥角膜样本的增强倍数
        kc_augment_factor: 圆锥角膜样本的增强倍数

    返回:
        训练集和测试集的数据加载器
    """
    # 获取变换
    if use_gpu_augment:
        # 使用简单的变换，只将图像转换为张量，其余增强在GPU上进行
        train_transform = transforms.Compose([
            transforms.Resize(256),
            transforms.CenterCrop(224),
            transforms.ToTensor()  # 不进行归一化，在GPU上进行
        ])
        test_transform = transforms.Compose([
            transforms.Resize(256),
            transforms.CenterCrop(224),
            transforms.ToTensor()  # 不进行归一化，在GPU上进行
        ])
        print("使用GPU数据增强")
    else:
        # 使用传统的CPU数据增强
        train_transform = get_transforms('train')
        test_transform = get_transforms('test')
        print("使用CPU数据增强")

    # 创建数据集
    train_dataset = KeratoconusDataset(train_csv, transform=train_transform)
    test_dataset = KeratoconusDataset(test_csv, transform=test_transform)

    # 如果启用早期圆锥角膜增强但不使用GPU增强
    if early_kc_augment and not use_gpu_augment:
        # 创建一个不应用变换的数据集，以便我们可以在增强器中应用自定义变换
        train_dataset_raw = KeratoconusDataset(train_csv, transform=None)
        # 应用专门的增强
        train_dataset = apply_augmentation(train_dataset_raw, early_kc_label=1, augment_factor=augment_factor)
        print(f"已启用早期圆锥角膜专门增强（CPU），增强倍数: {augment_factor}")

    # 如果启用圆锥角膜增强
    if kc_augment and not use_gpu_augment:
        # 尝试导入增强版KC数据增强
        try:
            from .enhanced_kc_augmentation import apply_enhanced_kc_augmentation
            has_enhanced_kc_augmentation = True
            print("成功导入增强版KC数据增强模块")
        except ImportError:
            has_enhanced_kc_augmentation = False
            print("未找到增强版KC数据增强模块，将使用标准版")

        # 优先使用增强版KC数据增强
        if has_enhanced_kc_augmentation:
            # 如果已经应用了早期圆锥角膜增强，使用已增强的数据集
            if early_kc_augment and not use_gpu_augment:
                # 应用增强版KC特定增强
                train_dataset = apply_enhanced_kc_augmentation(train_dataset, kc_label=2, augment_factor=kc_augment_factor)
            else:
                # 创建一个不应用变换的数据集
                train_dataset_raw = KeratoconusDataset(train_csv, transform=None)
                # 应用增强版KC特定增强
                train_dataset = apply_enhanced_kc_augmentation(train_dataset_raw, kc_label=2, augment_factor=kc_augment_factor)
            print(f"已启用增强版圆锥角膜(KC)专门增强（CPU），增强倍数: {kc_augment_factor}")
        # 如果没有增强版KC数据增强，但有标准版KC数据增强
        elif has_kc_augmentation:
            # 如果已经应用了早期圆锥角膜增强，使用已增强的数据集
            if early_kc_augment and not use_gpu_augment:
                # 应用KC特定增强
                train_dataset = apply_kc_augmentation(train_dataset, kc_label=2, augment_factor=kc_augment_factor)
            else:
                # 创建一个不应用变换的数据集
                train_dataset_raw = KeratoconusDataset(train_csv, transform=None)
                # 应用KC特定增强
                train_dataset = apply_kc_augmentation(train_dataset_raw, kc_label=2, augment_factor=kc_augment_factor)
            print(f"已启用标准版圆锥角膜(KC)专门增强（CPU），增强倍数: {kc_augment_factor}")
        else:
            print("警告: 未找到KC特定增强模块，无法应用KC特定增强")

    # 创建数据加载器
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, num_workers=num_workers)
    test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False, num_workers=num_workers)

    # 如果使用GPU增强，创建GPU增强器
    if use_gpu_augment:
        # 创建GPU增强器
        train_gpu_augmenter = GPUAugmentation(mode='train').cuda()
        test_gpu_augmenter = GPUAugmentation(mode='test').cuda()

        # 如果启用早期圆锥角膜增强
        if early_kc_augment:
            print("已启用早期圆锥角膜专门增强（GPU）")
            # 创建早期圆锥角膜GPU增强器
            early_kc_gpu_augmenter = GPUEarlyKCAugmentation().cuda()

            # 包装数据加载器，以便在GPU上应用增强
            train_loader = GPUAugmentDataLoader(
                train_loader,
                train_gpu_augmenter,
                early_kc_augment=True,
                early_kc_label=1,
                early_kc_augmenter=early_kc_gpu_augmenter,
                augment_factor=5
            )
        else:
            # 包装数据加载器，以便在GPU上应用增强
            train_loader = GPUAugmentDataLoader(
                train_loader,
                train_gpu_augmenter
            )

        # 包装测试数据加载器
        test_loader = GPUAugmentDataLoader(
            test_loader,
            test_gpu_augmenter
        )

    return train_loader, test_loader

class GPUAugmentDataLoader:
    """
    包装数据加载器，以便在GPU上应用增强
    """
    def __init__(self, dataloader, augmenter, early_kc_augment=False, early_kc_label=1, early_kc_augmenter=None, augment_factor=5):
        self.dataloader = dataloader
        self.augmenter = augmenter
        self.early_kc_augment = early_kc_augment
        self.early_kc_label = early_kc_label
        self.early_kc_augmenter = early_kc_augmenter
        self.augment_factor = augment_factor

    def __iter__(self):
        for images, labels in self.dataloader:
            try:
                # 将数据移动到GPU
                images = images.cuda()
                labels = labels.cuda()

                # 应用GPU增强
                images = self.augmenter(images)

                # 如果启用早期圆锥角膜增强
                if self.early_kc_augment and self.early_kc_augmenter is not None:
                    # 应用早期圆锥角膜专门增强
                    try:
                        images, labels = apply_gpu_augmentation(
                            images,
                            labels,
                            early_kc_label=self.early_kc_label,
                            augment_factor=self.augment_factor
                        )
                    except Exception as e:
                        print(f"应用早期圆锥角膜GPU增强时出错: {e}")
                        print(f"错误详情: {str(e)}")
                        print(f"标签形状: {labels.shape}, 标签值: {labels.unique().tolist()}, 早期圆锥角膜标签: {self.early_kc_label}")
                        print(f"图像形状: {images.shape}")
                        print(f"跳过此批次的早期圆锥角膜增强")
                        # 出错时不应用增强，继续使用原始数据

                yield images, labels
            except Exception as e:
                print(f"GPUAugmentDataLoader.__iter__中出错: {e}")
                print(f"跳过此批次")
                continue  # 跳过此批次，继续下一批次

    def __len__(self):
        return len(self.dataloader)

def get_dataloaders_with_augmentation(train_csv, test_csv, batch_size=32):
    """
    获取带有早期圆锥角膜增强的数据加载器

    参数:
        train_csv: 训练集CSV文件路径
        test_csv: 测试集CSV文件路径
        batch_size: 批次大小

    返回:
        训练集和测试集的数据加载器
    """
    return get_dataloaders(train_csv, test_csv, batch_size, early_kc_augment=True)

def get_task_sampler(dataset, n_way=3, n_shot=5, n_query=15, class_balanced=False, early_kc_weight=1.0):
    """
    获取任务采样器

    参数:
        dataset: 数据集
        n_way: 每个任务的类别数
        n_shot: 每个类别的支持集样本数
        n_query: 每个类别的查询集样本数
        class_balanced: 是否使用类别平衡采样
        early_kc_weight: 早期圆锥角膜样本的权重

    返回:
        任务采样器
    """
    return KeratoconusTaskSampler(dataset, n_way, n_shot, n_query, class_balanced=class_balanced, early_kc_weight=early_kc_weight)

#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
增强版GPU数据增强模块 - 将更多数据增强和预处理步骤移至GPU
包括圆锥角膜病理模拟的GPU实现
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import random
import math
import numpy as np

class GPUAugmentation(nn.Module):
    """
    在GPU上执行基础数据增强
    """
    def __init__(self, mode='train', p=0.5):
        super(GPUAugmentation, self).__init__()
        self.mode = mode
        self.p = p

        # 归一化参数
        self.register_buffer('mean', torch.tensor([0.485, 0.456, 0.406]).view(1, 3, 1, 1))
        self.register_buffer('std', torch.tensor([0.229, 0.224, 0.225]).view(1, 3, 1, 1))

    def forward(self, x):
        """
        输入: [B, C, H, W] 范围为[0, 1]的张量
        输出: [B, <PERSON>, H, W] 归一化后的张量
        """
        if self.mode == 'train':
            # 随机水平翻转
            if random.random() < self.p:
                x = torch.flip(x, dims=[3])

            # 随机垂直翻转
            if random.random() < self.p:
                x = torch.flip(x, dims=[2])

            # 随机旋转
            if random.random() < self.p:
                angle = random.choice([0, 90, 180, 270])
                if angle == 90:
                    x = torch.rot90(x, k=1, dims=[2, 3])
                elif angle == 180:
                    x = torch.rot90(x, k=2, dims=[2, 3])
                elif angle == 270:
                    x = torch.rot90(x, k=3, dims=[2, 3])

            # 随机亮度、对比度、饱和度调整
            if random.random() < self.p:
                # 亮度调整
                brightness_factor = random.uniform(0.8, 1.2)
                x = x * brightness_factor

                # 对比度调整
                contrast_factor = random.uniform(0.8, 1.2)
                mean = torch.mean(x, dim=[1, 2, 3], keepdim=True)
                x = (x - mean) * contrast_factor + mean

            # 随机噪声
            if random.random() < self.p:
                noise = torch.randn_like(x) * 0.05
                x = x + noise
                x = torch.clamp(x, 0, 1)

        # 归一化
        x = (x - self.mean) / self.std

        return x

class GPUEarlyKCAugmentation(nn.Module):
    """
    针对早期圆锥角膜的专门GPU增强 - 增强版
    包含病理模拟的GPU实现
    """
    def __init__(self, p=0.7, intensity_range=(0.15, 0.4)):
        super(GPUEarlyKCAugmentation, self).__init__()
        self.p = p
        self.intensity_range = intensity_range
        self.locations = ['inferior', 'central', 'paracentral']

        # 归一化参数
        self.register_buffer('mean', torch.tensor([0.485, 0.456, 0.406]).view(1, 3, 1, 1))
        self.register_buffer('std', torch.tensor([0.229, 0.224, 0.225]).view(1, 3, 1, 1))

    def forward(self, x):
        """
        输入: [B, C, H, W] 范围为[0, 1]的张量
        输出: [B, C, H, W] 归一化后的张量
        """
        # 基础增强
        # 随机水平翻转
        if random.random() < self.p:
            x = torch.flip(x, dims=[3])

        # 随机垂直翻转
        if random.random() < self.p:
            x = torch.flip(x, dims=[2])

        # 随机旋转 (更多角度)
        if random.random() < self.p:
            k = random.randint(1, 7)  # 1-7之间的随机整数
            x = torch.rot90(x, k=k, dims=[2, 3])

        # 随机亮度、对比度调整 (更大范围)
        if random.random() < self.p:
            # 亮度调整
            brightness_factor = random.uniform(0.7, 1.3)
            x = x * brightness_factor

            # 对比度调整
            contrast_factor = random.uniform(0.7, 1.3)
            mean = torch.mean(x, dim=[1, 2, 3], keepdim=True)
            x = (x - mean) * contrast_factor + mean

        # 随机噪声 (更大噪声)
        if random.random() < self.p:
            noise = torch.randn_like(x) * 0.08
            x = x + noise
            x = torch.clamp(x, 0, 1)

        # 随机模糊
        if random.random() < self.p:
            kernel_size = random.choice([3, 5])
            sigma = random.uniform(0.1, 2.0)
            x = self._gaussian_blur(x, kernel_size, sigma)

        # 随机锐化
        if random.random() < self.p:
            amount = random.uniform(1, 3)
            x = self._sharpen(x, amount)

        # 病理模拟增强 (GPU版本)
        if random.random() < self.p:
            # 随机选择病理模拟类型
            augment_type = random.choice(['thinning', 'pattern', 'curvature'])

            # 随机选择位置
            location = random.choice(self.locations)

            # 随机选择强度
            intensity = random.uniform(*self.intensity_range)

            # 应用选择的病理模拟
            if augment_type == 'thinning':
                x = self._simulate_thinning(x, intensity, location)
            elif augment_type == 'pattern':
                x = self._simulate_pattern(x, intensity, location)
            else:  # curvature
                x = self._simulate_curvature(x, intensity, location)

        # 归一化
        x = (x - self.mean) / self.std

        return x

    def _gaussian_blur(self, x, kernel_size, sigma):
        """GPU上的高斯模糊"""
        # 创建高斯核
        channels = x.shape[1]

        # 确保kernel_size是奇数
        if kernel_size % 2 == 0:
            kernel_size += 1

        # 创建一维高斯核
        kernel_size = int(kernel_size)
        kernel_radius = kernel_size // 2
        mesh_1d = torch.arange(-kernel_radius, kernel_radius + 1, device=x.device, dtype=torch.float32)
        variance = sigma ** 2
        gaussian_1d = torch.exp(-mesh_1d ** 2 / (2 * variance))
        gaussian_1d = gaussian_1d / gaussian_1d.sum()

        # 将一维核扩展为二维核
        gaussian_2d = gaussian_1d.view(-1, 1) * gaussian_1d.view(1, -1)
        gaussian_2d = gaussian_2d.expand(channels, 1, kernel_size, kernel_size)

        # 应用卷积
        padding = kernel_size // 2
        return F.conv2d(x, gaussian_2d.to(x.device), padding=padding, groups=channels)

    def _sharpen(self, x, amount):
        """GPU上的锐化"""
        # 创建锐化核
        kernel = torch.tensor([
            [0, -1, 0],
            [-1, 4, -1],
            [0, -1, 0]
        ], dtype=torch.float32, device=x.device).view(1, 1, 3, 3)

        channels = x.shape[1]
        kernel = kernel.expand(channels, 1, 3, 3)

        # 应用卷积
        laplacian = F.conv2d(x, kernel, padding=1, groups=channels)
        sharpened = x + amount * laplacian
        return torch.clamp(sharpened, 0, 1)

    def _simulate_thinning(self, x, intensity, location):
        """GPU上的角膜变薄模拟"""
        batch_size, channels, height, width = x.shape

        # 创建坐标网格 - 兼容不同版本的PyTorch
        try:
            y_grid, x_grid = torch.meshgrid(
                torch.arange(height, device=x.device, dtype=torch.float32),
                torch.arange(width, device=x.device, dtype=torch.float32),
                indexing='ij'
            )
        except TypeError:
            # 对于不支持indexing参数的旧版PyTorch
            y_grid, x_grid = torch.meshgrid(
                torch.arange(height, device=x.device, dtype=torch.float32),
                torch.arange(width, device=x.device, dtype=torch.float32)
            )
            # 在旧版PyTorch中，默认是'ij'索引，所以不需要转置

        # 根据位置确定变薄中心
        if location == 'inferior':
            center_y = height * 0.7  # 下方位置
            center_x = width * 0.5   # 水平居中
        elif location == 'central':
            center_y = height * 0.5  # 中心位置
            center_x = width * 0.5   # 水平居中
        elif location == 'paracentral':
            center_y = height * 0.5 + random.uniform(-0.1, 0.1) * height
            center_x = width * 0.5 + random.uniform(-0.1, 0.1) * width
        else:
            center_y = height * 0.5
            center_x = width * 0.5

        # 计算到中心的距离
        distance = torch.sqrt((y_grid - center_y)**2 + (x_grid - center_x)**2)

        # 设置影响半径
        radius = min(height, width) * 0.2

        # 创建变薄掩码
        mask = torch.ones_like(distance)
        mask_region = distance < radius
        mask[mask_region] = 1.0 - intensity * (1.0 - distance[mask_region] / radius)

        # 扩展掩码维度以匹配图像
        mask = mask.unsqueeze(0).unsqueeze(0).expand(batch_size, channels, -1, -1)

        # 应用掩码
        return x * mask

    def _simulate_pattern(self, x, intensity, location):
        """GPU上的特征模式模拟 (蝶形或蟹爪形)"""
        batch_size, channels, height, width = x.shape

        # 创建坐标网格 - 兼容不同版本的PyTorch
        try:
            y_grid, x_grid = torch.meshgrid(
                torch.arange(height, device=x.device, dtype=torch.float32),
                torch.arange(width, device=x.device, dtype=torch.float32),
                indexing='ij'
            )
        except TypeError:
            # 对于不支持indexing参数的旧版PyTorch
            y_grid, x_grid = torch.meshgrid(
                torch.arange(height, device=x.device, dtype=torch.float32),
                torch.arange(width, device=x.device, dtype=torch.float32)
            )
            # 在旧版PyTorch中，默认是'ij'索引，所以不需要转置

        # 根据位置确定模式中心
        if location == 'inferior':
            center_y = height * 0.7  # 下方位置
            center_x = width * 0.5   # 水平居中
        elif location == 'central':
            center_y = height * 0.5  # 中心位置
            center_x = width * 0.5   # 水平居中
        elif location == 'paracentral':
            center_y = height * 0.5 + random.uniform(-0.1, 0.1) * height
            center_x = width * 0.5 + random.uniform(-0.1, 0.1) * width
        else:
            center_y = height * 0.5
            center_x = width * 0.5

        # 计算到中心的距离和角度
        dx = x_grid - center_x
        dy = y_grid - center_y
        distance = torch.sqrt(dx**2 + dy**2)
        angle = torch.atan2(dy, dx)

        # 设置影响半径
        radius = min(height, width) * 0.3

        # 创建模式掩码
        pattern = torch.ones_like(distance)
        mask_region = distance < radius
        pattern_value = torch.abs(torch.sin(4 * angle)) * (1.0 - distance / radius)
        pattern[mask_region] = 1.0 - intensity * pattern_value[mask_region]

        # 扩展掩码维度以匹配图像
        pattern = pattern.unsqueeze(0).unsqueeze(0).expand(batch_size, channels, -1, -1)

        # 应用掩码
        return x * pattern

    def _simulate_curvature(self, x, intensity, location):
        """GPU上的角膜曲率变化模拟 (使用网格采样实现)"""
        batch_size, channels, height, width = x.shape

        # 创建基础坐标网格 (归一化到[-1, 1]) - 兼容不同版本的PyTorch
        try:
            grid_y, grid_x = torch.meshgrid(
                torch.linspace(-1, 1, height, device=x.device),
                torch.linspace(-1, 1, width, device=x.device),
                indexing='ij'
            )
        except TypeError:
            # 对于不支持indexing参数的旧版PyTorch
            grid_y, grid_x = torch.meshgrid(
                torch.linspace(-1, 1, height, device=x.device),
                torch.linspace(-1, 1, width, device=x.device)
            )
            # 在旧版PyTorch中，默认是'ij'索引，所以不需要转置
        base_grid = torch.stack([grid_x, grid_y], dim=-1).unsqueeze(0).expand(batch_size, -1, -1, -1)

        # 根据位置确定变形中心 (归一化坐标)
        if location == 'inferior':
            center_y = 0.4  # 下方位置
            center_x = 0.0  # 水平居中
        elif location == 'central':
            center_y = 0.0  # 中心位置
            center_x = 0.0  # 水平居中
        elif location == 'paracentral':
            center_y = random.uniform(-0.2, 0.2)
            center_x = random.uniform(-0.2, 0.2)
        else:
            center_y = 0.0
            center_x = 0.0

        # 计算到中心的距离和角度
        dx = grid_x - center_x
        dy = grid_y - center_y
        distance = torch.sqrt(dx**2 + dy**2)
        angle = torch.atan2(dy, dx)

        # 设置影响半径
        radius = 0.6  # 归一化半径

        # 创建变形场
        flow_x = torch.zeros_like(grid_x)
        flow_y = torch.zeros_like(grid_y)

        # 应用非线性变形
        mask_region = distance < radius
        factor = intensity * (1.0 - distance[mask_region] / radius)
        flow_x[mask_region] = factor * distance[mask_region] * torch.cos(angle[mask_region])
        flow_y[mask_region] = factor * distance[mask_region] * torch.sin(angle[mask_region])

        # 应用变形
        flow = torch.stack([flow_x, flow_y], dim=-1).unsqueeze(0).expand(batch_size, -1, -1, -1)
        sampling_grid = base_grid + flow

        # 使用网格采样进行变形
        return F.grid_sample(x, sampling_grid, mode='bilinear', padding_mode='reflection', align_corners=True)

def apply_gpu_augmentation(images, labels, early_kc_label=1, augment_factor=8):
    """
    对早期圆锥角膜样本应用GPU增强

    Args:
        images: [B, C, H, W] 范围为[0, 1]的张量
        labels: [B] 标签张量
        early_kc_label: 早期圆锥角膜的标签
        augment_factor: 增强倍数

    Returns:
        增强后的图像和标签
    """
    try:
        # 找出早期圆锥角膜样本的索引
        early_kc_mask = (labels == early_kc_label)
        if not early_kc_mask.any():
            # 如果没有早期圆锥角膜样本，直接返回原始数据
            return images, labels

        early_kc_indices = early_kc_mask.nonzero(as_tuple=True)[0]

        # 提取早期圆锥角膜样本
        early_kc_images = images[early_kc_indices]
        early_kc_labels = labels[early_kc_indices]
    except Exception as e:
        print(f"在apply_gpu_augmentation中提取早期圆锥角膜样本时出错: {e}")
        print(f"标签形状: {labels.shape}, 标签值: {labels.unique().tolist()}, 早期圆锥角膜标签: {early_kc_label}")
        # 出错时返回原始数据
        return images, labels

    # 创建增强器
    augmenter = GPUEarlyKCAugmentation().to(images.device)

    # 应用增强
    augmented_images = []
    augmented_labels = []

    for _ in range(augment_factor - 1):  # 减1是因为已经有原始样本
        # 应用增强
        aug_images = augmenter(early_kc_images)

        # 添加到列表
        augmented_images.append(aug_images)
        augmented_labels.append(early_kc_labels)

    # 合并原始样本和增强样本
    if augmented_images:
        augmented_images = torch.cat(augmented_images, dim=0)
        augmented_labels = torch.cat(augmented_labels, dim=0)

        all_images = torch.cat([images, augmented_images], dim=0)
        all_labels = torch.cat([labels, augmented_labels], dim=0)
    else:
        all_images = images
        all_labels = labels

    return all_images, all_labels

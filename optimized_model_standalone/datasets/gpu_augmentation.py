
#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
GPU数据增强模块 - 将数据增强和预处理步骤移至GPU
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import torchvision.transforms.functional as TF
import random
import numpy as np

class GPUAugmentation(nn.Module):
    """
    在GPU上执行数据增强
    """
    def __init__(self, mode='train', p=0.5):
        super(GPUAugmentation, self).__init__()
        self.mode = mode
        self.p = p

        # 归一化参数
        self.register_buffer('mean', torch.tensor([0.485, 0.456, 0.406]).view(1, 3, 1, 1))
        self.register_buffer('std', torch.tensor([0.229, 0.224, 0.225]).view(1, 3, 1, 1))

    def forward(self, x):
        """
        输入: [B, C, H, W] 范围为[0, 1]的张量
        输出: [B, C, H, W] 归一化后的张量
        """
        if self.mode == 'train':

            # 随机亮度、对比度、饱和度调整
            if random.random() < self.p:
                # 亮度调整
                brightness_factor = random.uniform(0.8, 1.2)
                x = x * brightness_factor

                # 对比度调整
                contrast_factor = random.uniform(0.8, 1.2)
                mean = torch.mean(x, dim=[1, 2, 3], keepdim=True)
                x = (x - mean) * contrast_factor + mean

            # 随机噪声
            if random.random() < self.p:
                noise = torch.randn_like(x) * 0.05
                x = x + noise
                x = torch.clamp(x, 0, 1)

        # 归一化
        x = (x - self.mean) / self.std

        return x

class GPUEarlyKCAugmentation(nn.Module):
    """
    针对早期圆锥角膜的专门GPU增强
    """
    def __init__(self, p=0.5):
        super(GPUEarlyKCAugmentation, self).__init__()
        self.p = p

        # 归一化参数
        self.register_buffer('mean', torch.tensor([0.485, 0.456, 0.406]).view(1, 3, 1, 1))
        self.register_buffer('std', torch.tensor([0.229, 0.224, 0.225]).view(1, 3, 1, 1))

    def forward(self, x):
        """
        输入: [B, C, H, W] 范围为[0, 1]的张量
        输出: [B, C, H, W] 归一化后的张量
        """


        # 随机亮度、对比度调整 (更大范围)
        if random.random() < self.p:
            # 亮度调整
            brightness_factor = random.uniform(0.7, 1.3)
            x = x * brightness_factor

            # 对比度调整
            contrast_factor = random.uniform(0.7, 1.3)
            mean = torch.mean(x, dim=[1, 2, 3], keepdim=True)
            x = (x - mean) * contrast_factor + mean

        # 随机噪声 (更大噪声)
        if random.random() < self.p:
            noise = torch.randn_like(x) * 0.08
            x = x + noise
            x = torch.clamp(x, 0, 1)

        # 随机模糊
        if random.random() < self.p:
            kernel_size = random.choice([3, 5])
            sigma = random.uniform(0.1, 2.0)
            x = self._gaussian_blur(x, kernel_size, sigma)

        # 随机锐化
        if random.random() < self.p:
            amount = random.uniform(1, 3)
            x = self._sharpen(x, amount)

        # 归一化
        x = (x - self.mean) / self.std

        return x

    def _gaussian_blur(self, x, kernel_size, sigma):
        """GPU上的高斯模糊"""
        # 创建高斯核
        channels = x.shape[1]

        # 确保kernel_size是奇数
        if kernel_size % 2 == 0:
            kernel_size += 1

        # 创建一维高斯核
        kernel_size = int(kernel_size)
        kernel_radius = kernel_size // 2
        mesh_1d = torch.arange(-kernel_radius, kernel_radius + 1, device=x.device, dtype=torch.float32)
        variance = sigma ** 2
        gaussian_1d = torch.exp(-mesh_1d ** 2 / (2 * variance))
        gaussian_1d = gaussian_1d / gaussian_1d.sum()

        # 将一维核扩展为二维核
        gaussian_2d = gaussian_1d.view(-1, 1) * gaussian_1d.view(1, -1)
        gaussian_2d = gaussian_2d.expand(channels, 1, kernel_size, kernel_size)

        # 应用卷积
        padding = kernel_size // 2
        return F.conv2d(x, gaussian_2d.to(x.device), padding=padding, groups=channels)

    def _sharpen(self, x, amount):
        """GPU上的锐化"""
        # 创建锐化核
        kernel = torch.tensor([
            [0, -1, 0],
            [-1, 4, -1],
            [0, -1, 0]
        ], dtype=torch.float32, device=x.device).view(1, 1, 3, 3)

        channels = x.shape[1]
        kernel = kernel.expand(channels, 1, 3, 3)

        # 应用卷积
        laplacian = F.conv2d(x, kernel, padding=1, groups=channels)
        sharpened = x + amount * laplacian
        return torch.clamp(sharpened, 0, 1)

def apply_gpu_augmentation(images, labels, early_kc_label=1, augment_factor=8):
    """
    对早期圆锥角膜样本应用GPU增强

    Args:
        images: [B, C, H, W] 范围为[0, 1]的张量
        labels: [B] 标签张量
        early_kc_label: 早期圆锥角膜的标签
        augment_factor: 增强倍数

    Returns:
        增强后的图像和标签
    """
    try:
        # 找出早期圆锥角膜样本的索引
        early_kc_mask = (labels == early_kc_label)
        if not early_kc_mask.any():
            # 如果没有早期圆锥角膜样本，直接返回原始数据
            return images, labels

        early_kc_indices = early_kc_mask.nonzero(as_tuple=True)[0]

        # 提取早期圆锥角膜样本
        early_kc_images = images[early_kc_indices]
        early_kc_labels = labels[early_kc_indices]
    except Exception as e:
        print(f"在apply_gpu_augmentation中提取早期圆锥角膜样本时出错: {e}")
        print(f"标签形状: {labels.shape}, 标签值: {labels.unique().tolist()}, 早期圆锥角膜标签: {early_kc_label}")
        # 出错时返回原始数据
        return images, labels

    # 创建增强器
    augmenter = GPUEarlyKCAugmentation().to(images.device)

    # 应用增强
    augmented_images = []
    augmented_labels = []

    for _ in range(augment_factor - 1):  # 减1是因为已经有原始样本
        # 应用增强
        aug_images = augmenter(early_kc_images)

        # 添加到列表
        augmented_images.append(aug_images)
        augmented_labels.append(early_kc_labels)

    # 合并原始样本和增强样本
    if augmented_images:
        augmented_images = torch.cat(augmented_images, dim=0)
        augmented_labels = torch.cat(augmented_labels, dim=0)

        all_images = torch.cat([images, augmented_images], dim=0)
        all_labels = torch.cat([labels, augmented_labels], dim=0)
    else:
        all_images = images
        all_labels = labels

    return all_images, all_labels

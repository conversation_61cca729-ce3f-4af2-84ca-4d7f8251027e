#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
增强版圆锥角膜(KC)数据增强模块
专门针对KC类别的数据增强，提高KC类别的识别准确率
"""

import os
import sys
import random
import numpy as np
import torch
import torch.nn.functional as F
from torchvision import transforms
from PIL import Image, ImageFilter, ImageEnhance

# 检查是否有OpenCV
try:
    import cv2
    HAS_CV2 = True
except ImportError:
    HAS_CV2 = False
    print("警告: 未找到OpenCV (cv2)，某些KC增强功能将被禁用")

# 标准图像尺寸
STANDARD_SIZE = (224, 224)

def get_enhanced_kc_transforms():
    """
    获取针对圆锥角膜(KC)的增强版特定增强策略

    返回:
        transforms.Compose对象
    """
    return transforms.Compose([
        # 更激进的随机裁剪和缩放，但仍保持中心区域完整
        transforms.RandomResizedCrop(224, scale=(0.75, 1.0), ratio=(0.85, 1.15)),
        # 增加旋转角度范围
        transforms.RandomRotation(25),
        # 增加亮度、对比度和饱和度的变化范围
        transforms.ColorJitter(brightness=0.25, contrast=0.25, saturation=0.25, hue=0.15),
        # 高斯模糊，模拟不同的成像清晰度
        transforms.GaussianBlur(kernel_size=5, sigma=(0.1, 2.5)),
        # 标准化处理 - 先转换为张量
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),
        # 随机擦除，模拟部分区域信息缺失 - 必须在ToTensor之后应用
        transforms.RandomErasing(p=0.5, scale=(0.02, 0.2), ratio=(0.3, 3.3), value=0)
    ])

def simulate_enhanced_kc_cone(image, intensity=0.4, location='central', use_gpu=False):
    """
    模拟圆锥角膜的锥形特征 - 增强版

    参数:
        image: PIL图像或Tensor
        intensity: 变形强度，值越大变形效果越明显
        location: 变形位置
        use_gpu: 是否使用GPU加速

    返回:
        增强后的PIL图像或Tensor
    """
    # 检查输入类型
    is_tensor = isinstance(image, torch.Tensor)

    if is_tensor:
        # 如果输入是Tensor，获取设备和形状
        device = image.device
        if image.dim() == 4:  # 批处理模式
            batch_size, channels, height, width = image.shape
            is_batch = True
        else:  # 单图像模式
            channels, height, width = image.shape
            is_batch = False
    else:
        # 如果输入是PIL图像，转换为numpy数组
        img_array = np.array(image)
        height, width = img_array.shape[:2]
        is_batch = False
        device = None

    # 创建网格
    if is_tensor and use_gpu:
        # 在GPU上创建网格
        if is_batch:
            grid_y, grid_x = torch.meshgrid(
                torch.linspace(-1, 1, height, device=device),
                torch.linspace(-1, 1, width, device=device)
            )
            grid = torch.stack((grid_x, grid_y), dim=2).unsqueeze(0).repeat(batch_size, 1, 1, 1)
        else:
            grid_y, grid_x = torch.meshgrid(
                torch.linspace(-1, 1, height, device=device),
                torch.linspace(-1, 1, width, device=device)
            )
            grid = torch.stack((grid_x, grid_y), dim=2).unsqueeze(0)
    else:
        # 在CPU上创建网格
        grid_y, grid_x = np.meshgrid(
            np.linspace(-1, 1, height),
            np.linspace(-1, 1, width),
            indexing='ij'
        )
        grid = np.stack((grid_x, grid_y), axis=2)
        if is_batch:
            grid = np.expand_dims(grid, axis=0).repeat(batch_size, axis=0)
        else:
            grid = np.expand_dims(grid, axis=0)

    # 根据位置确定变形中心
    if location == 'inferior':
        center_y, center_x = 0.5, 0.0  # 下方
    elif location == 'superior':
        center_y, center_x = -0.5, 0.0  # 上方
    elif location == 'nasal':
        center_y, center_x = 0.0, -0.5  # 鼻侧
    elif location == 'temporal':
        center_y, center_x = 0.0, 0.5  # 颞侧
    elif location == 'paracentral':
        center_y, center_x = 0.3 * random.choice([-1, 1]), 0.3 * random.choice([-1, 1])  # 副中心
    else:  # central
        center_y, center_x = 0.0, 0.0  # 中心

    # 计算到中心的距离
    if is_tensor and use_gpu:
        dist_y = grid[..., 1] - center_y
        dist_x = grid[..., 0] - center_x
        dist = torch.sqrt(dist_y**2 + dist_x**2)
    else:
        dist_y = grid[..., 1] - center_y
        dist_x = grid[..., 0] - center_x
        dist = np.sqrt(dist_y**2 + dist_x**2)

    # 计算变形因子
    if is_tensor and use_gpu:
        factor = torch.exp(-dist**2 / (2 * 0.3**2))  # 高斯函数
        # 应用变形
        grid[..., 0] = grid[..., 0] + intensity * factor * dist_x
        grid[..., 1] = grid[..., 1] + intensity * factor * dist_y
    else:
        factor = np.exp(-dist**2 / (2 * 0.3**2))  # 高斯函数
        # 应用变形
        grid[..., 0] = grid[..., 0] + intensity * factor * dist_x
        grid[..., 1] = grid[..., 1] + intensity * factor * dist_y

    # 应用变形
    if is_tensor:
        # 将网格转换为PyTorch格式
        if not use_gpu:
            grid = torch.from_numpy(grid).to(device).float()
        # 使用网格采样应用变形
        if is_batch:
            return F.grid_sample(image, grid, mode='bilinear', padding_mode='reflection', align_corners=True)
        else:
            return F.grid_sample(image.unsqueeze(0), grid, mode='bilinear', padding_mode='reflection', align_corners=True).squeeze(0)
    else:
        # 将PIL图像转换为Tensor
        img_tensor = transforms.ToTensor()(image).unsqueeze(0)
        # 将网格转换为PyTorch格式
        grid_tensor = torch.from_numpy(grid).float()
        # 使用网格采样应用变形
        warped_tensor = F.grid_sample(img_tensor, grid_tensor, mode='bilinear', padding_mode='reflection', align_corners=True).squeeze(0)
        # 转换回PIL图像
        warped_np = warped_tensor.permute(1, 2, 0).numpy()
        warped_np = (warped_np * 255).astype(np.uint8)
        return Image.fromarray(warped_np)

class EnhancedKCSpecificAugmenter:
    """
    增强版圆锥角膜(KC)特定数据增强器
    """
    def __init__(self, probability=0.9, intensity_range=(0.3, 0.6), use_gpu=False):
        """
        初始化增强器

        参数:
            probability: 应用增强的概率
            intensity_range: 增强强度范围
            use_gpu: 是否使用GPU加速
        """
        self.probability = probability
        self.intensity_range = intensity_range
        self.locations = ['inferior', 'central', 'paracentral', 'superior', 'nasal', 'temporal']
        self.use_gpu = use_gpu and torch.cuda.is_available()

    def __call__(self, image):
        """
        应用增强

        参数:
            image: PIL图像或Tensor

        返回:
            增强后的PIL图像或Tensor
        """
        if random.random() > self.probability:
            return image

        # 随机选择增强类型
        augment_type = random.choice(['cone', 'distortion', 'combined'])

        # 随机选择位置
        location = random.choice(self.locations)

        # 随机选择强度
        intensity = random.uniform(*self.intensity_range)

        # 应用选择的增强
        if augment_type == 'cone':
            return simulate_enhanced_kc_cone(image, intensity, location, self.use_gpu)
        elif augment_type == 'distortion' and HAS_CV2:
            # 这里可以实现畸变效果，类似于augmentation.py中的simulate_keratoconus_curvature
            # 由于代码长度限制，这里暂时使用锥形效果代替
            return simulate_enhanced_kc_cone(image, intensity, location, self.use_gpu)
        else:  # combined
            # 组合多种效果
            img = simulate_enhanced_kc_cone(image, intensity, location, self.use_gpu)
            # 可以添加其他效果
            return img

def apply_enhanced_kc_augmentation(dataset, kc_label=2, augment_factor=3):
    """
    应用增强版圆锥角膜(KC)特定增强

    参数:
        dataset: 原始数据集
        kc_label: 圆锥角膜的标签
        augment_factor: 圆锥角膜样本的增强倍数

    返回:
        增强后的数据集
    """
    # 创建带有KC特定增强的数据集
    return EnhancedKCDatasetWithAugmentation(
        dataset,
        kc_label=kc_label,
        augment_factor=augment_factor
    )

class EnhancedKCDatasetWithAugmentation(torch.utils.data.Dataset):
    """
    带有增强版KC特定增强的数据集
    """
    def __init__(self, dataset, kc_label=2, augment_factor=3):
        """
        初始化数据集

        参数:
            dataset: 原始数据集
            kc_label: 圆锥角膜的标签
            augment_factor: 圆锥角膜样本的增强倍数
        """
        self.dataset = dataset
        self.kc_label = kc_label
        self.augment_factor = augment_factor

        # KC特定增强器
        self.kc_augmenter = EnhancedKCSpecificAugmenter(probability=0.9)

        # KC特定转换
        self.kc_transform = get_enhanced_kc_transforms()

        # 标准转换
        self.standard_transform = transforms.Compose([
            transforms.Resize(256),
            transforms.CenterCrop(224),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])

        # 创建KC样本索引列表
        self.kc_indices = []
        for i in range(len(dataset)):
            try:
                _, label = dataset[i]
                if label == kc_label:
                    self.kc_indices.append(i)
            except:
                continue

        # 打印KC样本数量
        self.original_kc_count = len(self.kc_indices)
        self.augmented_kc_count = self.original_kc_count * self.augment_factor
        print(f"原始圆锥角膜(KC)样本数量: {self.original_kc_count}")
        print(f"增强后的圆锥角膜(KC)样本数量: {self.augmented_kc_count} (增强倍数: {self.augment_factor})")

    def __len__(self):
        # 计算总样本数：原始数据集大小 + (增强倍数-1) * KC样本数
        return len(self.dataset) + (self.augment_factor - 1) * len(self.kc_indices)

    def __getitem__(self, idx):
        # 判断是原始样本还是增强样本
        if idx < len(self.dataset):
            # 原始样本
            original_idx = idx
            is_augmented = False
        else:
            # 增强样本
            # 计算对应的KC样本索引
            kc_idx = (idx - len(self.dataset)) % len(self.kc_indices)
            original_idx = self.kc_indices[kc_idx]
            is_augmented = True

        # 获取原始图像和标签
        image, label = self.dataset[original_idx]

        # 检查图像是否已经是张量
        is_tensor = isinstance(image, torch.Tensor)

        # 如果是KC样本或增强的KC样本，应用特殊增强
        if label == self.kc_label:
            # 对增强样本使用不同的随机种子，确保多样性
            if is_augmented:
                # 使用索引作为随机种子的一部分，确保同一个样本的不同增强版本是不同的
                random.seed(idx * 10 + 42)
                np.random.seed(idx * 10 + 42)

                # 如果图像已经是张量，先转换回PIL图像
                if is_tensor:
                    # 转换为PIL图像
                    image_np = image.permute(1, 2, 0).cpu().numpy()
                    # 确保值在0-255范围内
                    image_np = (image_np * 255).astype(np.uint8)
                    image = Image.fromarray(image_np)

                # 应用KC特定增强
                image = self.kc_augmenter(image)

                # 应用KC特定转换
                image = self.kc_transform(image)
            else:
                # 对原始KC样本应用标准转换，如果不是张量
                if not is_tensor:
                    image = self.standard_transform(image)
        else:
            # 对其他样本应用标准转换，如果不是张量
            if not is_tensor:
                image = self.standard_transform(image)

        return image, label

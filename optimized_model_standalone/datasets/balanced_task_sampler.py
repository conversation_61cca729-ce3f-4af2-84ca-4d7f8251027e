#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
平衡任务采样器：确保每个任务都包含圆锥角膜和早期圆锥角膜样本
测试任务采样器：为测试集创建固定的任务，确保结果可重复
"""

import torch
import random
import numpy as np
from collections import defaultdict

class BalancedTaskSampler:
    """
    平衡任务采样器：确保每个任务都包含圆锥角膜和早期圆锥角膜样本

    参数:
        dataset: 数据集
        n_way: 每个任务的类别数
        n_shot: 每个类别的支持集样本数
        n_query: 每个类别的查询集样本数
        n_tasks: 任务数量
        kc_shot_multiplier: 圆锥角膜样本在支持集中的倍数
        early_kc_shot_multiplier: 早期圆锥角膜样本在支持集中的倍数
        num_workers: 数据加载的工作线程数
    """
    def __init__(self, dataset, n_way=3, n_shot=5, n_query=15, n_tasks=30,
                 kc_shot_multiplier=2, early_kc_shot_multiplier=1.5,
                 num_workers=4):
        self.dataset = dataset
        self.n_way = n_way
        self.n_shot = n_shot
        self.n_query = n_query
        self.n_tasks = n_tasks
        self.kc_shot_multiplier = kc_shot_multiplier
        self.early_kc_shot_multiplier = early_kc_shot_multiplier
        self.num_workers = num_workers

        # 按类别索引数据
        self.class_indices = {}
        for i in range(len(dataset)):
            # 处理数据集返回的值，可能是(image, label)或(image, label, label2, lam)
            result = dataset[i]
            if isinstance(result, tuple) and len(result) >= 2:
                # 只取前两个值(image, label)
                _, label = result[0], result[1]
            else:
                print(f"警告: 数据集[{i}]返回了意外的值: {result}")
                continue

            if label not in self.class_indices:
                self.class_indices[label] = []
            self.class_indices[label].append(i)

        # 预先计算有效类别
        self.valid_classes = [c for c in self.class_indices.keys()
                             if len(self.class_indices[c]) >= self.n_shot + self.n_query]

        # 如果没有足够的有效类别，调整n_shot和n_query
        if len(self.valid_classes) < self.n_way:
            print(f"警告: 没有足够的有效类别进行{self.n_way}-way任务采样，尝试调整参数...")

            # 计算每个类别的样本数量
            class_counts = {c: len(self.class_indices[c]) for c in self.class_indices.keys()}
            print(f"类别样本数量: {class_counts}")

            # 找出最小样本数量
            min_samples = min(class_counts.values())

            # 调整n_shot和n_query
            self.n_shot = max(1, min_samples // 2)
            self.n_query = max(1, min_samples - self.n_shot)

            print(f"调整后的参数: n_shot={self.n_shot}, n_query={self.n_query}")

            # 重新计算有效类别
            self.valid_classes = [c for c in self.class_indices.keys()
                                if len(self.class_indices[c]) >= self.n_shot + self.n_query]

            # 如果仍然没有足够的有效类别，使用所有类别
            if len(self.valid_classes) < self.n_way:
                print(f"警告: 仍然没有足够的有效类别，使用所有可用类别...")
                self.valid_classes = list(self.class_indices.keys())
                self.n_way = min(self.n_way, len(self.valid_classes))
                print(f"调整后的n_way={self.n_way}")

        # 打印每个类别的样本数量
        class_names = {0: 'normal', 1: 'e-kc', 2: 'kc'}
        for c in self.valid_classes:
            print(f"类别 {class_names.get(c, c)} (标签 {c}): {len(self.class_indices[c])}个样本")

    def sample_task(self):
        """采样一个任务，确保包含圆锥角膜和早期圆锥角膜样本"""
        try:
            # 确保包含圆锥角膜(标签2)和早期圆锥角膜(标签1)
            task_classes = []

            # 首先添加圆锥角膜和早期圆锥角膜（如果可用）
            if 2 in self.valid_classes:  # 圆锥角膜
                task_classes.append(2)

            if 1 in self.valid_classes:  # 早期圆锥角膜
                task_classes.append(1)

            # 然后添加其他类别
            remaining_classes = [c for c in self.valid_classes if c not in task_classes]
            remaining_slots = self.n_way - len(task_classes)

            if len(remaining_classes) >= remaining_slots and remaining_slots > 0:
                # 从剩余类别中采样remaining_slots个类别
                task_classes.extend(np.random.choice(
                    remaining_classes,
                    remaining_slots,
                    replace=False
                ))
            else:
                # 如果剩余类别不足，全部使用
                task_classes.extend(remaining_classes)

            # 确保任务类别数量正确
            if len(task_classes) > self.n_way:
                task_classes = task_classes[:self.n_way]

            # 打印任务类别
            class_names = {0: 'normal', 1: 'e-kc', 2: 'kc'}
            task_class_names = [class_names.get(c, c) for c in task_classes]
            print(f"任务类别: {task_class_names}")

            support_images = []
            support_labels = []
            query_images = []
            query_labels = []

            for c in task_classes:
                # 获取该类别的所有样本索引
                class_indices = self.class_indices[c]

                # 确定支持集样本数量
                n_shot_class = self.n_shot
                if c == 2:  # 圆锥角膜
                    n_shot_class = min(int(n_shot_class * self.kc_shot_multiplier), len(class_indices) // 2)
                    print(f"圆锥角膜类别使用 {n_shot_class} 个支持集样本")
                elif c == 1:  # 早期圆锥角膜
                    n_shot_class = min(int(n_shot_class * self.early_kc_shot_multiplier), len(class_indices) // 2)
                    print(f"早期圆锥角膜类别使用 {n_shot_class} 个支持集样本")

                # 如果样本数量不足，使用有放回采样
                if len(class_indices) < n_shot_class + self.n_query:
                    # 随机选择n_shot_class+n_query个样本索引（有放回）
                    indices = [random.choice(class_indices) for _ in range(n_shot_class + self.n_query)]
                else:
                    # 随机选择n_shot_class+n_query个样本索引（无放回）
                    indices = random.sample(class_indices, n_shot_class + self.n_query)

                # 前n_shot_class个样本作为支持集
                for idx in indices[:n_shot_class]:
                    result = self.dataset[idx]
                    if isinstance(result, tuple) and len(result) >= 1:
                        # 只取第一个值(image)
                        image = result[0]
                    else:
                        print(f"警告: 采样支持集时，数据集[{idx}]返回了意外的值: {result}")
                        # 创建一个空的张量作为替代
                        image = torch.zeros((3, 224, 224))
                    support_images.append(image)
                    support_labels.append(c)

                # 后n_query个样本作为查询集
                for idx in indices[n_shot_class:]:
                    result = self.dataset[idx]
                    if isinstance(result, tuple) and len(result) >= 1:
                        # 只取第一个值(image)
                        image = result[0]
                    else:
                        print(f"警告: 采样查询集时，数据集[{idx}]返回了意外的值: {result}")
                        # 创建一个空的张量作为替代
                        image = torch.zeros((3, 224, 224))
                    query_images.append(image)
                    query_labels.append(c)

            # 转换为张量
            support_images = torch.stack(support_images)
            support_labels = torch.tensor(support_labels)
            query_images = torch.stack(query_images)
            query_labels = torch.tensor(query_labels)

            return {
                'support': (support_images, support_labels),
                'query': (query_images, query_labels)
            }
        except Exception as e:
            # 如果采样失败，使用最简单的方式创建任务
            print(f"采样任务时出错: {e}，使用备用方法...")

            # 使用所有可用类别
            available_classes = list(self.class_indices.keys())
            n_way = min(self.n_way, len(available_classes))

            # 创建一个最小的任务
            support_images = []
            support_labels = []
            query_images = []
            query_labels = []

            for i, c in enumerate(available_classes[:n_way]):
                # 获取该类别的所有样本
                class_indices = self.class_indices[c]

                # 至少需要一个样本
                idx = random.choice(class_indices)
                result = self.dataset[idx]
                if isinstance(result, tuple) and len(result) >= 1:
                    # 只取第一个值(image)
                    image = result[0]
                else:
                    print(f"警告: 备用方法中，数据集[{idx}]返回了意外的值: {result}")
                    # 创建一个空的张量作为替代
                    image = torch.zeros((3, 224, 224))

                # 同一个样本既用于支持集也用于查询集
                support_images.append(image)
                support_labels.append(c)
                query_images.append(image)
                query_labels.append(c)

            # 转换为张量
            support_images = torch.stack(support_images)
            support_labels = torch.tensor(support_labels)
            query_images = torch.stack(query_images)
            query_labels = torch.tensor(query_labels)

            return {
                'support': (support_images, support_labels),
                'query': (query_images, query_labels)
            }

    def sample_batch(self, batch_size):
        """采样一批任务"""
        tasks = []
        for _ in range(batch_size):
            tasks.append(self.sample_task())
        return tasks

    def __iter__(self):
        """返回迭代器"""
        self.current_task = 0
        return self

    def __next__(self):
        """返回下一个任务"""
        if self.current_task < self.n_tasks:
            task = self.sample_task()
            self.current_task += 1
            return task
        else:
            raise StopIteration

    def __len__(self):
        """返回任务数量"""
        return self.n_tasks


class TestTaskSampler:
    """
    测试任务采样器：为测试集创建固定的任务，确保结果可重复

    参数:
        dataset: 数据集
        n_way: 每个任务的类别数
        n_shot: 每个类别的支持集样本数
        n_query: 每个类别的查询集样本数
        n_tasks: 任务数量
        seed: 随机种子，用于确保结果可重复
    """
    def __init__(self, dataset, n_way=3, n_shot=5, n_query=15, n_tasks=10, seed=42):
        self.dataset = dataset
        self.n_way = n_way
        self.n_shot = n_shot
        self.n_query = n_query
        self.n_tasks = n_tasks
        self.seed = seed

        # 设置随机种子
        random.seed(self.seed)
        np.random.seed(self.seed)

        # 按类别索引数据
        self.class_indices = {}
        for i in range(len(dataset)):
            # 处理数据集返回的值，可能是(image, label)或(image, label, label2, lam)
            result = dataset[i]
            if isinstance(result, tuple) and len(result) >= 2:
                # 只取前两个值(image, label)
                _, label = result[0], result[1]
            else:
                print(f"警告: 数据集[{i}]返回了意外的值: {result}")
                continue

            if label not in self.class_indices:
                self.class_indices[label] = []
            self.class_indices[label].append(i)

        # 预先计算有效类别
        self.valid_classes = [c for c in self.class_indices.keys()
                             if len(self.class_indices[c]) >= self.n_shot + self.n_query]

        # 如果没有足够的有效类别，调整n_shot和n_query
        if len(self.valid_classes) < self.n_way:
            print(f"警告: 没有足够的有效类别进行{self.n_way}-way任务采样，尝试调整参数...")

            # 计算每个类别的样本数量
            class_counts = {c: len(self.class_indices[c]) for c in self.class_indices.keys()}
            print(f"类别样本数量: {class_counts}")

            # 找出最小样本数量
            min_samples = min(class_counts.values())

            # 调整n_shot和n_query
            self.n_shot = max(1, min_samples // 2)
            self.n_query = max(1, min_samples - self.n_shot)

            print(f"调整后的参数: n_shot={self.n_shot}, n_query={self.n_query}")

            # 重新计算有效类别
            self.valid_classes = [c for c in self.class_indices.keys()
                                if len(self.class_indices[c]) >= self.n_shot + self.n_query]

            # 如果仍然没有足够的有效类别，使用所有类别
            if len(self.valid_classes) < self.n_way:
                print(f"警告: 仍然没有足够的有效类别，使用所有可用类别...")
                self.valid_classes = list(self.class_indices.keys())
                self.n_way = min(self.n_way, len(self.valid_classes))
                print(f"调整后的n_way={self.n_way}")

        # 打印每个类别的样本数量
        class_names = {0: 'normal', 1: 'e-kc', 2: 'kc'}
        for c in self.valid_classes:
            print(f"类别 {class_names.get(c, c)} (标签 {c}): {len(self.class_indices[c])}个样本")

        # 预先生成固定的任务
        self.tasks = []
        for _ in range(self.n_tasks):
            self.tasks.append(self._sample_task())

    def _sample_task(self):
        """采样一个任务，确保包含所有类别"""
        try:
            # 确保包含所有类别（如果可用）
            task_classes = []

            # 如果有效类别数量小于等于n_way，使用所有有效类别
            if len(self.valid_classes) <= self.n_way:
                task_classes = self.valid_classes
            else:
                # 否则，随机选择n_way个类别
                task_classes = np.random.choice(
                    self.valid_classes,
                    self.n_way,
                    replace=False
                )

            # 打印任务类别
            class_names = {0: 'normal', 1: 'e-kc', 2: 'kc'}
            task_class_names = [class_names.get(c, c) for c in task_classes]
            print(f"测试任务类别: {task_class_names}")

            support_images = []
            support_labels = []
            query_images = []
            query_labels = []

            for c in task_classes:
                # 获取该类别的所有样本索引
                class_indices = self.class_indices[c]

                # 如果样本数量不足，使用有放回采样
                if len(class_indices) < self.n_shot + self.n_query:
                    # 随机选择n_shot+n_query个样本索引（有放回）
                    indices = [random.choice(class_indices) for _ in range(self.n_shot + self.n_query)]
                else:
                    # 随机选择n_shot+n_query个样本索引（无放回）
                    indices = random.sample(class_indices, self.n_shot + self.n_query)

                # 前n_shot个样本作为支持集
                for idx in indices[:self.n_shot]:
                    result = self.dataset[idx]
                    if isinstance(result, tuple) and len(result) >= 1:
                        # 只取第一个值(image)
                        image = result[0]
                    else:
                        print(f"警告: 采样支持集时，数据集[{idx}]返回了意外的值: {result}")
                        # 创建一个空的张量作为替代
                        image = torch.zeros((3, 224, 224))
                    support_images.append(image)
                    support_labels.append(c)

                # 后n_query个样本作为查询集
                for idx in indices[self.n_shot:]:
                    result = self.dataset[idx]
                    if isinstance(result, tuple) and len(result) >= 1:
                        # 只取第一个值(image)
                        image = result[0]
                    else:
                        print(f"警告: 采样查询集时，数据集[{idx}]返回了意外的值: {result}")
                        # 创建一个空的张量作为替代
                        image = torch.zeros((3, 224, 224))
                    query_images.append(image)
                    query_labels.append(c)

            # 转换为张量
            support_images = torch.stack(support_images)
            support_labels = torch.tensor(support_labels)
            query_images = torch.stack(query_images)
            query_labels = torch.tensor(query_labels)

            return {
                'support': (support_images, support_labels),
                'query': (query_images, query_labels)
            }
        except Exception as e:
            # 如果采样失败，使用最简单的方式创建任务
            print(f"采样测试任务时出错: {e}，使用备用方法...")

            # 使用所有可用类别
            available_classes = list(self.class_indices.keys())
            n_way = min(self.n_way, len(available_classes))

            # 创建一个最小的任务
            support_images = []
            support_labels = []
            query_images = []
            query_labels = []

            for i, c in enumerate(available_classes[:n_way]):
                # 获取该类别的所有样本
                class_indices = self.class_indices[c]

                # 至少需要一个样本
                idx = random.choice(class_indices)
                result = self.dataset[idx]
                if isinstance(result, tuple) and len(result) >= 1:
                    # 只取第一个值(image)
                    image = result[0]
                else:
                    print(f"警告: 备用方法中，数据集[{idx}]返回了意外的值: {result}")
                    # 创建一个空的张量作为替代
                    image = torch.zeros((3, 224, 224))

                # 同一个样本既用于支持集也用于查询集
                support_images.append(image)
                support_labels.append(c)
                query_images.append(image)
                query_labels.append(c)

            # 转换为张量
            support_images = torch.stack(support_images)
            support_labels = torch.tensor(support_labels)
            query_images = torch.stack(query_images)
            query_labels = torch.tensor(query_labels)

            return {
                'support': (support_images, support_labels),
                'query': (query_images, query_labels)
            }

    def sample_task(self):
        """返回一个预先生成的任务"""
        # 随机选择一个任务
        return random.choice(self.tasks)

    def __iter__(self):
        """返回迭代器"""
        self.current_task = 0
        return self

    def __next__(self):
        """返回下一个任务"""
        if self.current_task < len(self.tasks):
            task = self.tasks[self.current_task]
            self.current_task += 1
            return task
        else:
            raise StopIteration

    def __len__(self):
        """返回任务数量"""
        return len(self.tasks)

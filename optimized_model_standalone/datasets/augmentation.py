"""
针对圆锥角膜数据集的专门增强策略，支持GPU加速和预计算
"""

import numpy as np
import torch
from PIL import Image
from torchvision import transforms
import random
import math
import time
import os
from functools import lru_cache

# 检查是否可以导入cv2
try:
    import cv2
    HAS_CV2 = True
except ImportError:
    HAS_CV2 = False
    print("警告: 未找到OpenCV (cv2)，某些增强功能将被禁用")

# 预计算的增强模板
THINNING_MASKS = {}
CURVATURE_MAPS = {}
PATTERN_MASKS = {}

# 标准图像尺寸
STANDARD_SIZE = (224, 224)

def get_early_kc_transforms():
    """
    获取针对早期圆锥角膜的特定增强策略 - 增强版

    返回:
        transforms.Compose对象
    """
    return transforms.Compose([
        # 更激进的随机裁剪和缩放，但仍保持中心区域完整
        transforms.RandomResizedCrop(224, scale=(0.85, 1.0), ratio=(0.9, 1.1)),
        # 增加旋转角度范围
        transforms.RandomRotation(15),
        # 增加亮度、对比度和饱和度的变化范围
        transforms.ColorJitter(brightness=0.15, contrast=0.15, saturation=0.15, hue=0.05),
        # 高斯模糊，模拟不同的成像清晰度
        transforms.GaussianBlur(kernel_size=3, sigma=(0.1, 1.5)),
        # 标准化处理 - 先转换为张量
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),
        # 随机擦除，模拟部分区域信息缺失 - 必须在ToTensor之后应用
        transforms.RandomErasing(p=0.3, scale=(0.02, 0.1), ratio=(0.3, 3.3), value=0)
    ])

@lru_cache(maxsize=32)
def compute_thinning_mask(height, width, intensity=0.2, location='inferior'):
    """
    预计算变薄掩码

    参数:
        height: 图像高度
        width: 图像宽度
        intensity: 变薄强度
        location: 变薄位置

    返回:
        预计算的掩码
    """
    # 创建变薄效果的掩码
    mask = np.ones((height, width), dtype=np.float32)

    # 根据位置确定变薄中心
    if location == 'inferior':
        center_y = int(height * 0.7)  # 下方位置
        center_x = int(width * 0.5)   # 水平居中
    elif location == 'central':
        center_y = int(height * 0.5)  # 中心位置
        center_x = int(width * 0.5)   # 水平居中
    else:
        center_y = int(height * 0.5)
        center_x = int(width * 0.5)

    # 创建径向梯度掩码，模拟局部变薄
    for y in range(height):
        for x in range(width):
            # 计算到中心的距离
            distance = np.sqrt((y - center_y)**2 + (x - center_x)**2)
            # 设置影响半径
            radius = min(height, width) * 0.2
            # 如果在影响范围内，应用变薄效果
            if distance < radius:
                # 距离中心越近，变薄效果越强
                factor = 1.0 - intensity * (1.0 - distance / radius)
                mask[y, x] = factor

    return mask

def simulate_keratoconus_thinning(image, intensity=0.2, location='inferior', use_gpu=False):
    """
    模拟圆锥角膜的角膜变薄特征

    参数:
        image: PIL图像或Tensor
        intensity: 变薄强度，值越大变薄效果越明显
        location: 变薄位置，可选'inferior'(下方),'central'(中心)等
        use_gpu: 是否使用GPU加速

    返回:
        增强后的PIL图像或Tensor
    """
    # 检查输入类型
    is_tensor = isinstance(image, torch.Tensor)

    if is_tensor:
        # 如果输入是Tensor，获取设备和形状
        device = image.device
        if image.dim() == 4:  # 批处理模式
            batch_size, channels, height, width = image.shape
            is_batch = True
        else:  # 单图像模式
            channels, height, width = image.shape
            is_batch = False
    else:
        # 如果输入是PIL图像，转换为numpy数组
        img_array = np.array(image)
        height, width = img_array.shape[:2]
        is_batch = False
        device = None

    # 检查是否有预计算的掩码
    key = f"thinning_{height}_{width}_{intensity}_{location}"
    if key in THINNING_MASKS:
        mask = THINNING_MASKS[key]
    else:
        # 计算掩码
        mask = compute_thinning_mask(height, width, intensity, location)
        # 缓存掩码
        THINNING_MASKS[key] = mask

    if is_tensor:
        # 将掩码转换为Tensor
        if use_gpu and torch.cuda.is_available():
            mask_tensor = torch.from_numpy(mask).to(device)
        else:
            mask_tensor = torch.from_numpy(mask)

        # 扩展掩码维度以匹配图像
        if is_batch:
            mask_tensor = mask_tensor.unsqueeze(0).unsqueeze(0).expand(batch_size, channels, -1, -1)
        else:
            mask_tensor = mask_tensor.unsqueeze(0).expand(channels, -1, -1)

        # 应用掩码
        return image * mask_tensor
    else:
        # 应用掩码到图像的每个通道
        for i in range(3):  # 假设是RGB图像
            img_array[:,:,i] = img_array[:,:,i] * mask

        # 转回PIL图像
        return Image.fromarray(img_array.astype(np.uint8))

@lru_cache(maxsize=32)
def compute_curvature_maps(height, width, intensity=0.2, location='inferior'):
    """
    预计算曲率变形映射

    参数:
        height: 图像高度
        width: 图像宽度
        intensity: 变形强度
        location: 变形位置

    返回:
        预计算的映射 (map_x, map_y)
    """
    # 创建变形映射
    map_x = np.zeros((height, width), dtype=np.float32)
    map_y = np.zeros((height, width), dtype=np.float32)

    # 根据位置确定变形中心
    if location == 'inferior':
        center_y = int(height * 0.7)  # 下方位置
        center_x = int(width * 0.5)   # 水平居中
    elif location == 'central':
        center_y = int(height * 0.5)  # 中心位置
        center_x = int(width * 0.5)   # 水平居中
    else:
        center_y = int(height * 0.5)
        center_x = int(width * 0.5)

    # 创建非线性变形，模拟角膜曲率变化
    for y in range(height):
        for x in range(width):
            # 计算到中心的距离
            distance = np.sqrt((y - center_y)**2 + (x - center_x)**2)
            # 设置影响半径
            radius = min(height, width) * 0.3

            # 如果在影响范围内，应用变形效果
            if distance < radius:
                # 计算变形角度
                angle = np.arctan2(y - center_y, x - center_x)
                # 计算变形强度，距离中心越近变形越强
                factor = intensity * (1.0 - distance / radius)
                # 计算新的坐标
                map_x[y, x] = x + factor * distance * np.cos(angle)
                map_y[y, x] = y + factor * distance * np.sin(angle)
            else:
                map_x[y, x] = x
                map_y[y, x] = y

    return map_x, map_y

def simulate_keratoconus_curvature(image, intensity=0.2, location='inferior', use_gpu=False):
    """
    模拟圆锥角膜的角膜曲率变化

    参数:
        image: PIL图像或Tensor
        intensity: 变形强度，值越大变形效果越明显
        location: 变形位置，可选'inferior'(下方),'central'(中心)等
        use_gpu: 是否使用GPU加速

    返回:
        增强后的PIL图像或Tensor
    """
    # 如果没有cv2，回退到变薄效果
    if not HAS_CV2:
        return simulate_keratoconus_thinning(image, intensity, location, use_gpu)

    # 检查输入类型
    is_tensor = isinstance(image, torch.Tensor)

    if is_tensor:
        # 如果是Tensor，我们需要转换为PIL图像进行处理
        # 因为曲率变形需要使用cv2.remap，这在GPU上不容易实现
        if image.dim() == 4:  # 批处理模式
            # 不支持批处理模式的Tensor输入
            raise ValueError("曲率变形不支持批处理模式的Tensor输入")
        else:
            # 转换为PIL图像
            img_np = image.permute(1, 2, 0).cpu().numpy() * 255
            img_np = img_np.astype(np.uint8)
            pil_image = Image.fromarray(img_np)
            device = image.device
    else:
        # 如果是PIL图像，直接使用
        pil_image = image
        device = None

    # 转换为numpy数组
    img_array = np.array(pil_image)

    # 获取图像尺寸
    height, width = img_array.shape[:2]

    # 检查是否有预计算的映射
    key = f"curvature_{height}_{width}_{intensity}_{location}"
    if key in CURVATURE_MAPS:
        map_x, map_y = CURVATURE_MAPS[key]
    else:
        # 计算映射
        map_x, map_y = compute_curvature_maps(height, width, intensity, location)
        # 缓存映射
        CURVATURE_MAPS[key] = (map_x, map_y)

    # 应用变形
    import cv2  # 在这里导入，确保前面的检查已经完成
    distorted = cv2.remap(img_array, map_x, map_y, cv2.INTER_LINEAR)

    # 转回原始格式
    if is_tensor:
        # 转换为Tensor
        distorted_tensor = torch.from_numpy(distorted).permute(2, 0, 1).float() / 255.0
        if use_gpu and torch.cuda.is_available():
            distorted_tensor = distorted_tensor.to(device)
        return distorted_tensor
    else:
        # 转回PIL图像
        return Image.fromarray(distorted)

@lru_cache(maxsize=32)
def compute_pattern_mask(height, width, intensity=0.2, location='inferior'):
    """
    预计算模式掩码

    参数:
        height: 图像高度
        width: 图像宽度
        intensity: 模式强度
        location: 模式位置

    返回:
        预计算的掩码
    """
    # 根据位置确定模式中心
    if location == 'inferior':
        center_y = int(height * 0.7)  # 下方位置
        center_x = int(width * 0.5)   # 水平居中
    elif location == 'central':
        center_y = int(height * 0.5)  # 中心位置
        center_x = int(width * 0.5)   # 水平居中
    else:
        center_y = int(height * 0.5)
        center_x = int(width * 0.5)

    # 创建蝶形或蟹爪形模式
    pattern = np.ones((height, width), dtype=np.float32)

    for y in range(height):
        for x in range(width):
            # 计算到中心的距离和角度
            dx = x - center_x
            dy = y - center_y
            distance = np.sqrt(dx*dx + dy*dy)
            angle = np.arctan2(dy, dx)

            # 设置影响半径
            radius = min(height, width) * 0.3

            # 如果在影响范围内，应用模式效果
            if distance < radius:
                # 创建蝶形或蟹爪形模式
                pattern_value = np.abs(np.sin(4 * angle)) * (1.0 - distance / radius)
                pattern[y, x] = 1.0 - intensity * pattern_value

    return pattern

def simulate_keratoconus_pattern(image, intensity=0.2, location='inferior', use_gpu=False):
    """
    模拟圆锥角膜的特征模式，如蝶形或蟹爪形

    参数:
        image: PIL图像或Tensor
        intensity: 模式强度，值越大效果越明显
        location: 模式位置，可选'inferior'(下方),'central'(中心)等
        use_gpu: 是否使用GPU加速

    返回:
        增强后的PIL图像或Tensor
    """
    # 检查输入类型
    is_tensor = isinstance(image, torch.Tensor)

    if is_tensor:
        # 如果输入是Tensor，获取设备和形状
        device = image.device
        if image.dim() == 4:  # 批处理模式
            batch_size, channels, height, width = image.shape
            is_batch = True
        else:  # 单图像模式
            channels, height, width = image.shape
            is_batch = False
    else:
        # 如果输入是PIL图像，转换为numpy数组
        img_array = np.array(image).astype(np.float32)
        height, width = img_array.shape[:2]
        is_batch = False
        device = None

    # 检查是否有预计算的掩码
    key = f"pattern_{height}_{width}_{intensity}_{location}"
    if key in PATTERN_MASKS:
        pattern = PATTERN_MASKS[key]
    else:
        # 计算掩码
        pattern = compute_pattern_mask(height, width, intensity, location)
        # 缓存掩码
        PATTERN_MASKS[key] = pattern

    if is_tensor:
        # 将掩码转换为Tensor
        if use_gpu and torch.cuda.is_available():
            pattern_tensor = torch.from_numpy(pattern).to(device)
        else:
            pattern_tensor = torch.from_numpy(pattern)

        # 扩展掩码维度以匹配图像
        if is_batch:
            pattern_tensor = pattern_tensor.unsqueeze(0).unsqueeze(0).expand(batch_size, channels, -1, -1)
        else:
            pattern_tensor = pattern_tensor.unsqueeze(0).expand(channels, -1, -1)

        # 应用掩码
        return image * pattern_tensor
    else:
        # 应用模式到图像的每个通道
        for i in range(3):  # 假设是RGB图像
            img_array[:,:,i] = img_array[:,:,i] * pattern

        # 裁剪到有效范围
        img_array = np.clip(img_array, 0, 255)

        # 转回PIL图像
        return Image.fromarray(img_array.astype(np.uint8))

class KeratoconusAugmenter:
    """
    圆锥角膜数据增强器 - 增强版
    """
    def __init__(self, probability=0.7, intensity_range=(0.15, 0.4), use_gpu=False):
        """
        初始化增强器

        参数:
            probability: 应用增强的概率 (提高到0.7)
            intensity_range: 增强强度范围 (提高上限到0.4)
            use_gpu: 是否使用GPU加速
        """
        self.probability = probability
        self.intensity_range = intensity_range
        self.locations = ['inferior', 'central', 'paracentral']  # 增加更多位置选项
        self.use_gpu = use_gpu and torch.cuda.is_available()

    def __call__(self, image):
        """
        应用增强

        参数:
            image: PIL图像或Tensor

        返回:
            增强后的PIL图像或Tensor
        """
        if random.random() > self.probability:
            return image

        # 随机选择增强类型
        # 如果没有cv2，不使用曲率变形
        if HAS_CV2:
            augment_type = random.choice(['thinning', 'curvature', 'pattern'])
        else:
            augment_type = random.choice(['thinning', 'pattern'])

        # 随机选择位置
        location = random.choice(self.locations)

        # 随机选择强度
        intensity = random.uniform(*self.intensity_range)

        # 应用选择的增强
        if augment_type == 'thinning':
            return simulate_keratoconus_thinning(image, intensity, location, self.use_gpu)
        elif augment_type == 'curvature' and HAS_CV2:
            try:
                return simulate_keratoconus_curvature(image, intensity, location, self.use_gpu)
            except ValueError:
                # 如果曲率变形不支持当前输入格式，回退到变薄效果
                return simulate_keratoconus_thinning(image, intensity, location, self.use_gpu)
        else:  # pattern
            return simulate_keratoconus_pattern(image, intensity, location, self.use_gpu)

class KeratoconusDatasetWithAugmentation(torch.utils.data.Dataset):
    """
    带有专门增强的圆锥角膜数据集
    """
    def __init__(self, dataset, early_kc_label=1, augment_types=None, augment_probability=0.7,
                 mixup_alpha=0.2, cutmix_prob=0.3, use_gpu=False, augment_factor=5):
        """
        初始化数据集

        参数:
            dataset: 原始数据集
            early_kc_label: 早期圆锥角膜的标签
            augment_types: 增强类型列表，可选值为['basic', 'specific', 'advanced']
            augment_probability: 应用增强的概率
            mixup_alpha: Mixup的alpha参数
            cutmix_prob: CutMix的概率参数
            use_gpu: 是否使用GPU加速
            augment_factor: 早期圆锥角膜样本的增强倍数（默认为5，即从30张增加到150张）
        """
        self.dataset = dataset
        self.early_kc_label = early_kc_label
        self.augment_probability = augment_probability
        self.use_gpu = use_gpu and torch.cuda.is_available()
        self.mixup_alpha = mixup_alpha
        self.cutmix_prob = cutmix_prob
        self.augment_factor = augment_factor

        # 如果没有指定增强类型，默认使用基础增强
        if augment_types is None:
            self.augment_types = ['basic']
        else:
            self.augment_types = augment_types

        # 是否使用各种增强
        self.use_basic = 'basic' in self.augment_types
        self.use_specific = 'specific' in self.augment_types
        self.use_advanced = 'advanced' in self.augment_types

        # 早期圆锥角膜增强器（专门增强）
        if self.use_specific:
            self.augmenter = KeratoconusAugmenter(probability=augment_probability, use_gpu=self.use_gpu)

        # 早期圆锥角膜特定转换（基础增强）
        if self.use_basic:
            self.early_kc_transform = get_early_kc_transforms()

        # 标准转换
        self.standard_transform = transforms.Compose([
            transforms.Resize(256),
            transforms.CenterCrop(224),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])

        # 创建早期圆锥角膜样本索引列表
        self.early_kc_indices = []
        for i in range(len(dataset)):
            _, label = dataset[i]
            if label == early_kc_label:
                self.early_kc_indices.append(i)

        # 打印早期圆锥角膜样本数量
        self.original_early_kc_count = len(self.early_kc_indices)
        self.augmented_early_kc_count = self.original_early_kc_count * self.augment_factor
        print(f"原始早期圆锥角膜样本数量: {self.original_early_kc_count}")
        print(f"增强后的早期圆锥角膜样本数量: {self.augmented_early_kc_count} (增强倍数: {self.augment_factor})")

        # 预热缓存
        if self.use_gpu and self.use_specific:
            print("预热GPU增强缓存...")
            self._warmup_cache()

    def _warmup_cache(self):
        """预热缓存，预计算常用尺寸的增强模板"""
        # 标准尺寸
        height, width = STANDARD_SIZE

        # 预计算变薄掩码
        for intensity in [0.1, 0.2, 0.3]:
            for location in ['inferior', 'central']:
                # 变薄掩码
                mask = compute_thinning_mask(height, width, intensity, location)
                key = f"thinning_{height}_{width}_{intensity}_{location}"
                THINNING_MASKS[key] = mask

                # 曲率映射
                maps = compute_curvature_maps(height, width, intensity, location)
                key = f"curvature_{height}_{width}_{intensity}_{location}"
                CURVATURE_MAPS[key] = maps

                # 模式掩码
                pattern = compute_pattern_mask(height, width, intensity, location)
                key = f"pattern_{height}_{width}_{intensity}_{location}"
                PATTERN_MASKS[key] = pattern

    def _apply_mixup(self, image, label):
        """应用Mixup数据增强"""
        # 随机选择另一个样本
        idx2 = random.randint(0, len(self.dataset) - 1)
        image2, label2 = self.dataset[idx2]

        # 应用标准转换到第二个图像
        image2 = self.standard_transform(image2)

        # 生成混合系数
        lam = np.random.beta(self.mixup_alpha, self.mixup_alpha)

        # 混合图像
        mixed_image = lam * image + (1 - lam) * image2

        return mixed_image, label, label2, lam

    def _apply_cutmix(self, image, label):
        """应用CutMix数据增强"""
        # 随机选择另一个样本
        idx2 = random.randint(0, len(self.dataset) - 1)
        image2, label2 = self.dataset[idx2]

        # 应用标准转换到第二个图像
        image2 = self.standard_transform(image2)

        # 图像尺寸
        _, h, w = image.shape

        # 生成随机裁剪区域
        lam = np.random.beta(1.0, 1.0)
        cut_rat = np.sqrt(1.0 - lam)
        cut_w = int(w * cut_rat)
        cut_h = int(h * cut_rat)

        cx = np.random.randint(w)
        cy = np.random.randint(h)

        bbx1 = np.clip(cx - cut_w // 2, 0, w)
        bby1 = np.clip(cy - cut_h // 2, 0, h)
        bbx2 = np.clip(cx + cut_w // 2, 0, w)
        bby2 = np.clip(cy + cut_h // 2, 0, h)

        # 创建混合图像
        mixed_image = image.clone()
        mixed_image[:, bby1:bby2, bbx1:bbx2] = image2[:, bby1:bby2, bbx1:bbx2]

        # 计算混合比例
        lam = 1 - ((bbx2 - bbx1) * (bby2 - bby1) / (w * h))

        return mixed_image, label, label2, lam

    def __len__(self):
        # 计算总样本数：原始数据集大小 + (增强倍数-1) * 早期圆锥角膜样本数
        return len(self.dataset) + (self.augment_factor - 1) * len(self.early_kc_indices)

    def __getitem__(self, idx):
        # 处理索引映射
        original_dataset_size = len(self.dataset)

        # 如果索引小于原始数据集大小，直接从原始数据集获取
        if idx < original_dataset_size:
            original_idx = idx
            is_augmented = False
        else:
            # 否则，这是一个增强的早期圆锥角膜样本
            # 计算对应的原始早期圆锥角膜样本索引
            augmented_idx = idx - original_dataset_size
            early_kc_idx = augmented_idx % len(self.early_kc_indices)
            original_idx = self.early_kc_indices[early_kc_idx]
            is_augmented = True

        # 获取原始图像和标签
        image, label = self.dataset[original_idx]

        # 如果是早期圆锥角膜样本或增强的早期圆锥角膜样本，应用特殊增强
        if label == self.early_kc_label:
            # 应用专门的病理模拟增强（如果启用）
            if self.use_specific:
                # 对增强样本使用不同的随机种子，确保多样性
                if is_augmented:
                    # 使用索引作为随机种子的一部分，确保同一个样本的不同增强版本是不同的
                    random.seed(idx * 10 + 42)
                    np.random.seed(idx * 10 + 42)
                image = self.augmenter(image)

            # 应用基础增强（如果启用）
            if self.use_basic:
                # 将早期圆锥角膜特定的基础增强拆分为两部分
                # 第一部分：应用PIL图像变换（在ToTensor之前）
                pil_transforms = transforms.Compose([
                    transforms.RandomResizedCrop(224, scale=(0.85, 1.0), ratio=(0.9, 1.1)),
                    transforms.RandomRotation(15),
                    transforms.ColorJitter(brightness=0.15, contrast=0.15, saturation=0.15, hue=0.05),
                    transforms.GaussianBlur(kernel_size=3, sigma=(0.1, 1.5)),
                    transforms.ToTensor(),
                    transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
                ])

                # 第二部分：应用张量变换（在ToTensor之后）
                tensor_transforms = transforms.Compose([
                    transforms.RandomErasing(p=0.3, scale=(0.02, 0.1), ratio=(0.3, 3.3), value=0)
                ])

                # 先应用PIL图像变换
                image = pil_transforms(image)
                # 再应用张量变换
                image = tensor_transforms(image)
            else:
                # 如果不使用基础增强，应用标准转换
                image = self.standard_transform(image)
        else:
            # 对其他样本应用标准转换
            image = self.standard_transform(image)

        # 应用高级增强（如果启用）
        # 注意：为了避免批处理大小不一致的问题，我们不在__getitem__中直接返回不同格式的数据
        # 相反，我们总是返回(image, label)格式，并在训练循环中单独处理高级增强

        # 如果启用了高级增强，我们只在训练循环中应用它，而不是在数据加载器中
        # 这里我们只返回基本的增强图像和标签
        return image, label

def apply_augmentation(dataset, early_kc_label=1, augment_types=None, mixup_alpha=0.2, cutmix_prob=0.3, use_gpu=False, augment_factor=5):
    """
    应用圆锥角膜增强

    参数:
        dataset: 原始数据集
        early_kc_label: 早期圆锥角膜的标签
        augment_types: 增强类型列表，可选值为['basic', 'specific', 'advanced']
                      'basic': 基础的早期圆锥角膜增强
                      'specific': 专门的早期圆锥角膜增强（病理模拟）
                      'advanced': 高级数据增强（Mixup, CutMix等）
        mixup_alpha: Mixup的alpha参数
        cutmix_prob: CutMix的概率参数
        use_gpu: 是否使用GPU加速
        augment_factor: 早期圆锥角膜样本的增强倍数（默认为5，即从30张增加到150张）

    返回:
        增强后的数据集
    """
    # 如果没有指定增强类型，默认使用基础增强
    if augment_types is None:
        augment_types = ['basic']

    # 打印应用的增强类型
    print(f"应用增强类型: {', '.join(augment_types)}")
    if 'advanced' in augment_types:
        print(f"高级增强参数: Mixup alpha={mixup_alpha}, CutMix prob={cutmix_prob}")
    print(f"早期圆锥角膜增强倍数: {augment_factor}")

    # 创建带有增强的数据集
    return KeratoconusDatasetWithAugmentation(
        dataset,
        early_kc_label=early_kc_label,
        augment_types=augment_types,
        mixup_alpha=mixup_alpha,
        cutmix_prob=cutmix_prob,
        use_gpu=use_gpu,
        augment_factor=augment_factor
    )

def precompute_augmentation_templates():
    """
    预计算增强模板，用于加速数据增强

    这个函数会预计算常用尺寸和参数的增强模板，并将它们缓存在内存中，
    以便在数据加载过程中快速应用增强效果。
    """
    print("预计算数据增强模板...")
    start_time = time.time()

    # 标准尺寸
    height, width = STANDARD_SIZE

    # 使用多进程并行预计算
    try:
        from concurrent.futures import ThreadPoolExecutor
        use_parallel = True
        print("使用并行处理加速预计算...")
    except ImportError:
        use_parallel = False

    # 预计算参数列表
    params = []
    for intensity in [0.1, 0.15, 0.2, 0.25, 0.3]:  # 增加更多强度级别
        for location in ['inferior', 'central']:
            params.append((height, width, intensity, location))

    if use_parallel:
        # 并行预计算
        with ThreadPoolExecutor(max_workers=8) as executor:
            # 并行计算变薄掩码
            list(executor.map(lambda p: _precompute_thinning(*p), params))
            # 并行计算曲率映射
            list(executor.map(lambda p: _precompute_curvature(*p), params))
            # 并行计算模式掩码
            list(executor.map(lambda p: _precompute_pattern(*p), params))
    else:
        # 串行预计算
        for p in params:
            _precompute_thinning(*p)
            _precompute_curvature(*p)
            _precompute_pattern(*p)

    # 打印缓存统计信息
    print(f"预计算完成，耗时 {time.time() - start_time:.2f} 秒")
    print(f"变薄掩码: {len(THINNING_MASKS)} 个")
    print(f"曲率映射: {len(CURVATURE_MAPS)} 个")
    print(f"模式掩码: {len(PATTERN_MASKS)} 个")

def _precompute_thinning(height, width, intensity, location):
    """预计算变薄掩码的辅助函数"""
    mask = compute_thinning_mask(height, width, intensity, location)
    key = f"thinning_{height}_{width}_{intensity}_{location}"
    THINNING_MASKS[key] = mask

def _precompute_curvature(height, width, intensity, location):
    """预计算曲率映射的辅助函数"""
    maps = compute_curvature_maps(height, width, intensity, location)
    key = f"curvature_{height}_{width}_{intensity}_{location}"
    CURVATURE_MAPS[key] = maps

def _precompute_pattern(height, width, intensity, location):
    """预计算模式掩码的辅助函数"""
    pattern = compute_pattern_mask(height, width, intensity, location)
    key = f"pattern_{height}_{width}_{intensity}_{location}"
    PATTERN_MASKS[key] = pattern

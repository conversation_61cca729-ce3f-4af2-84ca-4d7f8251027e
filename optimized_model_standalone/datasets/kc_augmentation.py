"""
针对圆锥角膜(KC)类别的专门增强策略
"""

import numpy as np
import torch
from PIL import Image
from torchvision import transforms
import random
import math
from functools import lru_cache

# 检查是否可以导入cv2
try:
    import cv2
    HAS_CV2 = True
except ImportError:
    HAS_CV2 = False
    print("警告: 未找到OpenCV (cv2)，某些KC增强功能将被禁用")

# 预计算的增强模板
KC_CONE_MASKS = {}
KC_DISTORTION_MAPS = {}
KC_ASYMMETRY_MASKS = {}

# 标准图像尺寸
STANDARD_SIZE = (224, 224)

def get_kc_transforms():
    """
    获取针对圆锥角膜(KC)的特定增强策略

    返回:
        transforms.Compose对象
    """
    return transforms.Compose([
        # 更激进的随机裁剪和缩放，但仍保持中心区域完整
        transforms.RandomResizedCrop(224, scale=(0.8, 1.0), ratio=(0.9, 1.1)),
        # 增加旋转角度范围
        transforms.RandomRotation(20),
        # 增加亮度、对比度和饱和度的变化范围
        transforms.ColorJitter(brightness=0.2, contrast=0.2, saturation=0.2, hue=0.1),
        # 高斯模糊，模拟不同的成像清晰度
        transforms.GaussianBlur(kernel_size=5, sigma=(0.1, 2.0)),
        # 标准化处理 - 先转换为张量
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),
        # 随机擦除，模拟部分区域信息缺失 - 必须在ToTensor之后应用
        transforms.RandomErasing(p=0.4, scale=(0.02, 0.15), ratio=(0.3, 3.3), value=0)
    ])

@lru_cache(maxsize=32)
def compute_kc_cone_mask(height, width, intensity=0.3, location='central'):
    """
    预计算圆锥角膜的锥形掩码

    参数:
        height: 图像高度
        width: 图像宽度
        intensity: 变形强度
        location: 变形位置

    返回:
        预计算的掩码
    """
    # 创建锥形效果的掩码
    mask = np.ones((height, width), dtype=np.float32)

    # 根据位置确定锥形中心
    if location == 'inferior':
        center_y = int(height * 0.7)  # 下方位置
        center_x = int(width * 0.5)   # 水平居中
    elif location == 'central':
        center_y = int(height * 0.5)  # 中心位置
        center_x = int(width * 0.5)   # 水平居中
    elif location == 'paracentral':
        # 偏离中心的位置
        center_y = int(height * 0.5)
        center_x = int(width * 0.6)   # 稍微偏右
    else:
        center_y = int(height * 0.5)
        center_x = int(width * 0.5)

    # 创建锥形梯度掩码，模拟圆锥角膜的锥形特征
    for y in range(height):
        for x in range(width):
            # 计算到中心的距离
            distance = np.sqrt((y - center_y)**2 + (x - center_x)**2)
            # 设置影响半径
            radius = min(height, width) * 0.25
            # 如果在影响范围内，应用锥形效果
            if distance < radius:
                # 距离中心越近，锥形效果越强
                factor = 1.0 - intensity * (1.0 - distance / radius)**2  # 使用平方关系增强中心效果
                mask[y, x] = factor

    return mask

def simulate_kc_cone(image, intensity=0.3, location='central', use_gpu=False):
    """
    模拟圆锥角膜的锥形特征

    参数:
        image: PIL图像或Tensor
        intensity: 变形强度，值越大变形效果越明显
        location: 变形位置
        use_gpu: 是否使用GPU加速

    返回:
        增强后的PIL图像或Tensor
    """
    # 检查输入类型
    is_tensor = isinstance(image, torch.Tensor)

    if is_tensor:
        # 如果输入是Tensor，获取设备和形状
        device = image.device
        if image.dim() == 4:  # 批处理模式
            batch_size, channels, height, width = image.shape
            is_batch = True
        else:  # 单图像模式
            channels, height, width = image.shape
            is_batch = False
    else:
        # 如果输入是PIL图像，转换为numpy数组
        img_array = np.array(image)
        height, width = img_array.shape[:2]
        is_batch = False
        device = None

    # 检查是否有预计算的掩码
    key = f"kc_cone_{height}_{width}_{intensity}_{location}"
    if key in KC_CONE_MASKS:
        mask = KC_CONE_MASKS[key]
    else:
        # 计算掩码
        mask = compute_kc_cone_mask(height, width, intensity, location)
        # 缓存掩码
        KC_CONE_MASKS[key] = mask

    if is_tensor:
        # 将掩码转换为Tensor
        if use_gpu and torch.cuda.is_available():
            mask_tensor = torch.from_numpy(mask).to(device)
        else:
            mask_tensor = torch.from_numpy(mask)

        # 扩展掩码维度以匹配图像
        if is_batch:
            mask_tensor = mask_tensor.unsqueeze(0).unsqueeze(0).expand(batch_size, channels, -1, -1)
        else:
            mask_tensor = mask_tensor.unsqueeze(0).expand(channels, -1, -1)

        # 应用掩码
        return image * mask_tensor
    else:
        # 应用掩码到图像的每个通道
        for i in range(3):  # 假设是RGB图像
            img_array[:,:,i] = img_array[:,:,i] * mask

        # 转回PIL图像
        return Image.fromarray(img_array.astype(np.uint8))

@lru_cache(maxsize=32)
def compute_kc_distortion_maps(height, width, intensity=0.3, location='central'):
    """
    预计算圆锥角膜的畸变映射

    参数:
        height: 图像高度
        width: 图像宽度
        intensity: 变形强度
        location: 变形位置

    返回:
        预计算的映射 (map_x, map_y)
    """
    # 创建变形映射
    map_x = np.zeros((height, width), dtype=np.float32)
    map_y = np.zeros((height, width), dtype=np.float32)

    # 根据位置确定变形中心
    if location == 'inferior':
        center_y = int(height * 0.7)  # 下方位置
        center_x = int(width * 0.5)   # 水平居中
    elif location == 'central':
        center_y = int(height * 0.5)  # 中心位置
        center_x = int(width * 0.5)   # 水平居中
    elif location == 'paracentral':
        # 偏离中心的位置
        center_y = int(height * 0.5)
        center_x = int(width * 0.6)   # 稍微偏右
    else:
        center_y = int(height * 0.5)
        center_x = int(width * 0.5)

    # 创建非线性变形，模拟圆锥角膜的畸变
    for y in range(height):
        for x in range(width):
            # 计算到中心的距离
            distance = np.sqrt((y - center_y)**2 + (x - center_x)**2)
            # 设置影响半径
            radius = min(height, width) * 0.35

            # 如果在影响范围内，应用变形效果
            if distance < radius:
                # 计算变形角度
                angle = np.arctan2(y - center_y, x - center_x)
                # 计算变形强度，距离中心越近变形越强
                factor = intensity * (1.0 - distance / radius)**2  # 使用平方关系增强中心效果
                # 计算新的坐标，使用非线性变形
                map_x[y, x] = x + factor * distance * np.cos(angle) * (1 + 0.2 * np.sin(2 * angle))
                map_y[y, x] = y + factor * distance * np.sin(angle) * (1 + 0.2 * np.cos(2 * angle))
            else:
                map_x[y, x] = x
                map_y[y, x] = y

    return map_x, map_y

class KCSpecificAugmenter:
    """
    圆锥角膜(KC)特定数据增强器
    """
    def __init__(self, probability=0.8, intensity_range=(0.2, 0.5), use_gpu=False):
        """
        初始化增强器

        参数:
            probability: 应用增强的概率
            intensity_range: 增强强度范围
            use_gpu: 是否使用GPU加速
        """
        self.probability = probability
        self.intensity_range = intensity_range
        self.locations = ['inferior', 'central', 'paracentral']
        self.use_gpu = use_gpu and torch.cuda.is_available()

    def __call__(self, image):
        """
        应用增强

        参数:
            image: PIL图像或Tensor

        返回:
            增强后的PIL图像或Tensor
        """
        if random.random() > self.probability:
            return image

        # 随机选择增强类型
        augment_type = random.choice(['cone', 'distortion', 'combined'])

        # 随机选择位置
        location = random.choice(self.locations)

        # 随机选择强度
        intensity = random.uniform(*self.intensity_range)

        # 应用选择的增强
        if augment_type == 'cone':
            return simulate_kc_cone(image, intensity, location, self.use_gpu)
        elif augment_type == 'distortion' and HAS_CV2:
            # 这里可以实现畸变效果，类似于augmentation.py中的simulate_keratoconus_curvature
            # 由于代码长度限制，这里暂时使用锥形效果代替
            return simulate_kc_cone(image, intensity, location, self.use_gpu)
        else:  # combined
            # 组合多种效果
            img = simulate_kc_cone(image, intensity, location, self.use_gpu)
            # 可以添加其他效果
            return img

def apply_kc_augmentation(dataset, kc_label=2, augment_factor=3):
    """
    应用圆锥角膜(KC)特定增强

    参数:
        dataset: 原始数据集
        kc_label: 圆锥角膜的标签
        augment_factor: 圆锥角膜样本的增强倍数

    返回:
        增强后的数据集
    """
    # 创建带有KC特定增强的数据集
    return KCDatasetWithAugmentation(
        dataset,
        kc_label=kc_label,
        augment_factor=augment_factor
    )

class KCDatasetWithAugmentation(torch.utils.data.Dataset):
    """
    带有KC特定增强的数据集
    """
    def __init__(self, dataset, kc_label=2, augment_factor=3):
        """
        初始化数据集

        参数:
            dataset: 原始数据集
            kc_label: 圆锥角膜的标签
            augment_factor: 圆锥角膜样本的增强倍数
        """
        self.dataset = dataset
        self.kc_label = kc_label
        self.augment_factor = augment_factor

        # KC特定增强器
        self.kc_augmenter = KCSpecificAugmenter(probability=0.8)

        # KC特定转换
        self.kc_transform = get_kc_transforms()

        # 标准转换
        self.standard_transform = transforms.Compose([
            transforms.Resize(256),
            transforms.CenterCrop(224),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])

        # 创建KC样本索引列表
        self.kc_indices = []
        for i in range(len(dataset)):
            try:
                _, label = dataset[i]
                if label == kc_label:
                    self.kc_indices.append(i)
            except:
                continue

        # 打印KC样本数量
        self.original_kc_count = len(self.kc_indices)
        self.augmented_kc_count = self.original_kc_count * self.augment_factor
        print(f"原始圆锥角膜(KC)样本数量: {self.original_kc_count}")
        print(f"增强后的圆锥角膜(KC)样本数量: {self.augmented_kc_count} (增强倍数: {self.augment_factor})")

    def __len__(self):
        # 计算总样本数：原始数据集大小 + (增强倍数-1) * KC样本数
        return len(self.dataset) + (self.augment_factor - 1) * len(self.kc_indices)

    def __getitem__(self, idx):
        # 处理索引映射
        original_dataset_size = len(self.dataset)

        # 如果索引小于原始数据集大小，直接从原始数据集获取
        if idx < original_dataset_size:
            original_idx = idx
            is_augmented = False
        else:
            # 否则，这是一个增强的KC样本
            # 计算对应的原始KC样本索引
            augmented_idx = idx - original_dataset_size
            kc_idx = augmented_idx % len(self.kc_indices)
            original_idx = self.kc_indices[kc_idx]
            is_augmented = True

        # 获取原始图像和标签
        image, label = self.dataset[original_idx]

        # 检查图像是否已经是张量
        is_tensor = isinstance(image, torch.Tensor)

        # 如果是KC样本或增强的KC样本，应用特殊增强
        if label == self.kc_label:
            # 对增强样本使用不同的随机种子，确保多样性
            if is_augmented:
                # 使用索引作为随机种子的一部分，确保同一个样本的不同增强版本是不同的
                random.seed(idx * 10 + 42)
                np.random.seed(idx * 10 + 42)

                # 如果图像已经是张量，先转换回PIL图像
                if is_tensor:
                    # 转换为PIL图像
                    image_np = image.permute(1, 2, 0).cpu().numpy()
                    # 确保值在0-255范围内
                    image_np = (image_np * 255).astype(np.uint8)
                    image = Image.fromarray(image_np)

                # 应用KC特定增强
                image = self.kc_augmenter(image)

                # 应用KC特定转换
                image = self.kc_transform(image)
            else:
                # 对原始KC样本应用标准转换，如果不是张量
                if not is_tensor:
                    image = self.standard_transform(image)
        else:
            # 对其他样本应用标准转换，如果不是张量
            if not is_tensor:
                image = self.standard_transform(image)

        return image, label

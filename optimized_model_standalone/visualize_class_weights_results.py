#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Visualization of Class Weights Ablation Experiment Results
"""

import matplotlib.pyplot as plt
import numpy as np
import os
import matplotlib

# Experiment result data - Fill in actual results after experiments are completed
experiment_ids = ['CW-1', 'CW-2', 'CW-3', 'CW-4']
experiment_names = [
    'Baseline: No Class Weights',
    'Light Class Weights',
    'Default: Medium Class Weights',
    'Heavy Class Weights'
]

# Actual experiment results
overall_accuracy = [74.72, 65.28, 85.83, 74.72]  # Overall accuracy
normal_accuracy = [78.33, 61.67, 81.67, 80.83]   # Normal accuracy
early_kc_accuracy = [67.50, 66.67, 100.00, 67.50] # Early keratoconus accuracy
kc_accuracy = [78.33, 67.50, 75.83, 75.83]       # Keratoconus accuracy

# Class weight values for each experiment
early_kc_weights = [1.0, 4.0, 8.0, 12.0]
kc_weights = [1.0, 2.0, 3.0, 5.0]

# Create output directory
output_dir = 'class_weights_results_visualization'
os.makedirs(output_dir, exist_ok=True)

# Set chart style
plt.style.use('seaborn-v0_8-darkgrid')
colors = ['#3498db', '#2ecc71', '#e74c3c', '#f39c12']

# 1. Overall accuracy comparison chart
plt.figure(figsize=(12, 6))
bars = plt.bar(experiment_ids, overall_accuracy, color=colors[0], alpha=0.7)

# Add data labels
for bar in bars:
    height = bar.get_height()
    plt.text(bar.get_x() + bar.get_width()/2., height + 0.5,
             f'{height:.2f}%', ha='center', va='bottom', fontsize=12)

plt.title('Class Weights Ablation - Overall Accuracy Comparison', fontsize=16)
plt.xlabel('Experiment Configuration', fontsize=14)
plt.ylabel('Accuracy (%)', fontsize=14)
plt.ylim(0, 100)
plt.grid(axis='y', linestyle='--', alpha=0.7)
plt.tight_layout()
plt.savefig(f'{output_dir}/overall_accuracy_comparison.png', dpi=300, bbox_inches='tight')
plt.close()

# 2. Class accuracy comparison chart
x = np.arange(len(experiment_ids))
width = 0.25

fig, ax = plt.figure(figsize=(14, 8)), plt.subplot(111)
rects1 = ax.bar(x - width, normal_accuracy, width, label='Normal', color=colors[0], alpha=0.8)
rects2 = ax.bar(x, early_kc_accuracy, width, label='Early Keratoconus', color=colors[1], alpha=0.8)
rects3 = ax.bar(x + width, kc_accuracy, width, label='Keratoconus', color=colors[2], alpha=0.8)

# Add data labels
def add_labels(rects):
    for rect in rects:
        height = rect.get_height()
        ax.annotate(f'{height:.2f}%',
                    xy=(rect.get_x() + rect.get_width() / 2, height),
                    xytext=(0, 3),  # 3 points vertical offset
                    textcoords="offset points",
                    ha='center', va='bottom', fontsize=10)

add_labels(rects1)
add_labels(rects2)
add_labels(rects3)

ax.set_title('Class Weights Ablation - Class Accuracy Comparison', fontsize=16)
ax.set_xlabel('Experiment Configuration', fontsize=14)
ax.set_ylabel('Accuracy (%)', fontsize=14)
ax.set_xticks(x)
ax.set_xticklabels(experiment_ids)
ax.legend(fontsize=12)
ax.set_ylim(0, 105)
ax.grid(axis='y', linestyle='--', alpha=0.7)

plt.tight_layout()
plt.savefig(f'{output_dir}/class_accuracy_comparison.png', dpi=300, bbox_inches='tight')
plt.close()

# 3. Radar chart - Performance comparison of different configurations
categories = ['Overall Accuracy', 'Normal Accuracy', 'Early KC Accuracy', 'KC Accuracy']
N = len(categories)

# Calculate radar chart angles
angles = [n / float(N) * 2 * np.pi for n in range(N)]
angles += angles[:1]  # Close the radar chart

# Prepare data
data = [
    overall_accuracy,
    normal_accuracy,
    early_kc_accuracy,
    kc_accuracy
]
data = np.array(data)
data = data.T  # Transpose to make each row represent an experiment

# Add closing data points
data_plot = np.zeros((len(experiment_ids), N+1))
for i in range(len(experiment_ids)):
    data_plot[i, :-1] = data[i]
    data_plot[i, -1] = data[i, 0]  # Close

# Create radar chart
fig = plt.figure(figsize=(10, 10))
ax = plt.subplot(111, polar=True)

for i, exp_id in enumerate(experiment_ids):
    ax.plot(angles, data_plot[i], linewidth=2, label=exp_id, color=colors[i], alpha=0.8)
    ax.fill(angles, data_plot[i], color=colors[i], alpha=0.1)

# Set radar chart properties
ax.set_theta_offset(np.pi / 2)  # Start from top
ax.set_theta_direction(-1)  # Clockwise direction

# Set tick labels
plt.xticks(angles[:-1], categories, fontsize=12)

# Set y-axis range
ax.set_ylim(0, 100)
ax.set_yticks(np.arange(0, 101, 20))
ax.set_yticklabels([f'{x}%' for x in np.arange(0, 101, 20)], fontsize=10)

# Add legend
plt.legend(loc='upper right', bbox_to_anchor=(0.1, 0.1), fontsize=12)

plt.title('Class Weights Ablation - Performance Radar Chart', fontsize=16, y=1.1)
plt.tight_layout()
plt.savefig(f'{output_dir}/radar_chart.png', dpi=300, bbox_inches='tight')
plt.close()

# 4. Line chart - Accuracy trends across experiment configurations
plt.figure(figsize=(12, 7))

plt.plot(experiment_ids, overall_accuracy, 'o-', label='Overall Accuracy', linewidth=2, markersize=8, color=colors[0])
plt.plot(experiment_ids, normal_accuracy, 's-', label='Normal Accuracy', linewidth=2, markersize=8, color=colors[1])
plt.plot(experiment_ids, early_kc_accuracy, '^-', label='Early KC Accuracy', linewidth=2, markersize=8, color=colors[2])
plt.plot(experiment_ids, kc_accuracy, 'D-', label='KC Accuracy', linewidth=2, markersize=8, color=colors[3])

# Add data labels
for i, exp_id in enumerate(experiment_ids):
    plt.text(i, overall_accuracy[i] + 1, f'{overall_accuracy[i]:.2f}%', ha='center', fontsize=10)
    plt.text(i, normal_accuracy[i] + 1, f'{normal_accuracy[i]:.2f}%', ha='center', fontsize=10)
    plt.text(i, early_kc_accuracy[i] + 1, f'{early_kc_accuracy[i]:.2f}%', ha='center', fontsize=10)
    plt.text(i, kc_accuracy[i] + 1, f'{kc_accuracy[i]:.2f}%', ha='center', fontsize=10)

plt.title('Class Weights Ablation - Accuracy Trends', fontsize=16)
plt.xlabel('Experiment Configuration', fontsize=14)
plt.ylabel('Accuracy (%)', fontsize=14)
plt.ylim(0, 105)
plt.grid(True, linestyle='--', alpha=0.7)
plt.legend(fontsize=12)
plt.tight_layout()
plt.savefig(f'{output_dir}/accuracy_trend.png', dpi=300, bbox_inches='tight')
plt.close()

# 5. Class weight vs accuracy relationship
plt.figure(figsize=(12, 7))

# Early KC weight vs Early KC accuracy
plt.subplot(1, 2, 1)
plt.plot(early_kc_weights, early_kc_accuracy, 'o-', linewidth=2, markersize=8, color=colors[1])
for i, weight in enumerate(early_kc_weights):
    plt.text(weight, early_kc_accuracy[i] + 1, f'{early_kc_accuracy[i]:.2f}%', ha='center', fontsize=10)
plt.title('Early KC Weight vs Early KC Accuracy', fontsize=14)
plt.xlabel('Early KC Weight', fontsize=12)
plt.ylabel('Early KC Accuracy (%)', fontsize=12)
plt.ylim(0, 105)
plt.grid(True, linestyle='--', alpha=0.7)

# KC weight vs KC accuracy
plt.subplot(1, 2, 2)
plt.plot(kc_weights, kc_accuracy, 'o-', linewidth=2, markersize=8, color=colors[2])
for i, weight in enumerate(kc_weights):
    plt.text(weight, kc_accuracy[i] + 1, f'{kc_accuracy[i]:.2f}%', ha='center', fontsize=10)
plt.title('KC Weight vs KC Accuracy', fontsize=14)
plt.xlabel('KC Weight', fontsize=12)
plt.ylabel('KC Accuracy (%)', fontsize=12)
plt.ylim(0, 105)
plt.grid(True, linestyle='--', alpha=0.7)

plt.suptitle('Class Weight vs Class Accuracy Relationship', fontsize=16)
plt.tight_layout()
plt.savefig(f'{output_dir}/weight_accuracy_relationship.png', dpi=300, bbox_inches='tight')
plt.close()

# 6. Class balance chart - Showing how class weights affect the balance between classes
plt.figure(figsize=(12, 7))

# Calculate the difference between class accuracies
normal_early_kc_diff = np.abs(np.array(normal_accuracy) - np.array(early_kc_accuracy))
normal_kc_diff = np.abs(np.array(normal_accuracy) - np.array(kc_accuracy))
early_kc_kc_diff = np.abs(np.array(early_kc_accuracy) - np.array(kc_accuracy))

# Plot the differences
plt.plot(experiment_ids, normal_early_kc_diff, 'o-', label='|Normal - Early KC|', linewidth=2, markersize=8, color=colors[0])
plt.plot(experiment_ids, normal_kc_diff, 's-', label='|Normal - KC|', linewidth=2, markersize=8, color=colors[1])
plt.plot(experiment_ids, early_kc_kc_diff, '^-', label='|Early KC - KC|', linewidth=2, markersize=8, color=colors[2])

# Add data labels
for i, exp_id in enumerate(experiment_ids):
    plt.text(i, normal_early_kc_diff[i] + 1, f'{normal_early_kc_diff[i]:.2f}%', ha='center', fontsize=10)
    plt.text(i, normal_kc_diff[i] + 1, f'{normal_kc_diff[i]:.2f}%', ha='center', fontsize=10)
    plt.text(i, early_kc_kc_diff[i] + 1, f'{early_kc_kc_diff[i]:.2f}%', ha='center', fontsize=10)

plt.title('Class Weights Ablation - Class Balance Analysis', fontsize=16)
plt.xlabel('Experiment Configuration', fontsize=14)
plt.ylabel('Accuracy Difference (%)', fontsize=14)
plt.ylim(0, 100)
plt.grid(True, linestyle='--', alpha=0.7)
plt.legend(fontsize=12)
plt.tight_layout()
plt.savefig(f'{output_dir}/class_balance_analysis.png', dpi=300, bbox_inches='tight')
plt.close()

print(f"Visualization charts have been saved to {output_dir} directory")
print("Visualization charts reflect the actual experiment results from the class weights ablation study.")

# 圆锥角膜(KC)特定数据增强

本文档描述了针对圆锥角膜(KC)类别的专门增强策略，旨在提高KC类别的识别准确率。

## 背景

在之前的训练中，模型在早期圆锥角膜(E-KC)和正常样本(Normal)上表现良好，但在圆锥角膜(KC)类别上准确率较低。通过参数调整尝试提高KC类别准确率的效果有限，因此我们实现了针对KC类别的特定数据增强策略，以增加KC样本的多样性和数量。

## 实现原理

KC特定增强基于以下原理：

1. **模拟圆锥角膜的锥形特征**：圆锥角膜的主要特征是角膜中央或旁中央区域的突出，我们通过特定的图像变换模拟这种锥形特征。

2. **增加KC样本的多样性**：通过随机变化锥形的位置、强度和形状，增加KC样本的多样性。

3. **增加KC样本的数量**：通过数据增强，将KC样本的数量增加到原来的几倍，缓解类别不平衡问题。

## 主要功能

### 1. KC特定增强器

`KCSpecificAugmenter`类实现了以下增强策略：

- **锥形模拟**：模拟圆锥角膜的锥形特征，可以在不同位置（中央、旁中央、下方）生成不同强度的锥形效果。
- **畸变模拟**：模拟圆锥角膜导致的角膜畸变，产生非线性的图像变形。
- **组合效果**：将多种增强效果组合，产生更加真实的圆锥角膜特征。

### 2. KC特定转换

`get_kc_transforms`函数提供了针对KC样本的特定转换：

- 更激进的随机裁剪和缩放，但仍保持中心区域完整
- 增加旋转角度范围
- 增加亮度、对比度和饱和度的变化范围
- 高斯模糊，模拟不同的成像清晰度
- 随机擦除，模拟部分区域信息缺失

### 3. KC数据集增强

`KCDatasetWithAugmentation`类实现了对KC样本的增强：

- 识别原始数据集中的KC样本
- 对每个KC样本生成多个增强版本
- 保持其他类别样本不变
- 返回增强后的数据集

## 使用方法

### 1. 启用KC特定增强

在训练脚本中添加`--kc_augment`参数：

```bash
python train_balanced_task_sampling.py --kc_augment --kc_augment_factor 3
```

或者在`run_kc_improved.sh`脚本中设置：

```bash
KC_AUGMENT=true           # 是否对圆锥角膜样本进行特殊增强
KC_AUGMENT_FACTOR=3       # 圆锥角膜样本的增强倍数
```

### 2. 调整增强参数

可以调整以下参数：

- `KC_AUGMENT_FACTOR`：KC样本的增强倍数，默认为3
- `AUGMENT_FACTOR`：早期圆锥角膜样本的增强倍数，默认为6

### 3. 与其他增强策略结合

KC特定增强可以与其他增强策略结合使用：

- 早期圆锥角膜增强：`--early_kc_augment`
- 高级数据增强：`--advanced_augment`
- GPU数据增强：`--use_enhanced_gpu_augment`

## 技术细节

### 1. 锥形模拟

锥形模拟通过创建一个锥形掩码，然后将其应用到图像上实现：

```python
def compute_kc_cone_mask(height, width, intensity=0.3, location='central'):
    # 创建锥形效果的掩码
    mask = np.ones((height, width), dtype=np.float32)
    
    # 根据位置确定锥形中心
    if location == 'inferior':
        center_y = int(height * 0.7)  # 下方位置
        center_x = int(width * 0.5)   # 水平居中
    elif location == 'central':
        center_y = int(height * 0.5)  # 中心位置
        center_x = int(width * 0.5)   # 水平居中
    elif location == 'paracentral':
        # 偏离中心的位置
        center_y = int(height * 0.5)
        center_x = int(width * 0.6)   # 稍微偏右
    else:
        center_y = int(height * 0.5)
        center_x = int(width * 0.5)
    
    # 创建锥形梯度掩码，模拟圆锥角膜的锥形特征
    for y in range(height):
        for x in range(width):
            # 计算到中心的距离
            distance = np.sqrt((y - center_y)**2 + (x - center_x)**2)
            # 设置影响半径
            radius = min(height, width) * 0.25
            # 如果在影响范围内，应用锥形效果
            if distance < radius:
                # 距离中心越近，锥形效果越强
                factor = 1.0 - intensity * (1.0 - distance / radius)**2
                mask[y, x] = factor
    
    return mask
```

### 2. 畸变模拟

畸变模拟通过创建非线性变形映射，然后使用OpenCV的remap函数应用到图像上：

```python
def compute_kc_distortion_maps(height, width, intensity=0.3, location='central'):
    # 创建变形映射
    map_x = np.zeros((height, width), dtype=np.float32)
    map_y = np.zeros((height, width), dtype=np.float32)
    
    # 根据位置确定变形中心
    if location == 'inferior':
        center_y = int(height * 0.7)  # 下方位置
        center_x = int(width * 0.5)   # 水平居中
    elif location == 'central':
        center_y = int(height * 0.5)  # 中心位置
        center_x = int(width * 0.5)   # 水平居中
    elif location == 'paracentral':
        # 偏离中心的位置
        center_y = int(height * 0.5)
        center_x = int(width * 0.6)   # 稍微偏右
    else:
        center_y = int(height * 0.5)
        center_x = int(width * 0.5)
    
    # 创建非线性变形，模拟圆锥角膜的畸变
    for y in range(height):
        for x in range(width):
            # 计算到中心的距离
            distance = np.sqrt((y - center_y)**2 + (x - center_x)**2)
            # 设置影响半径
            radius = min(height, width) * 0.35
            
            # 如果在影响范围内，应用变形效果
            if distance < radius:
                # 计算变形角度
                angle = np.arctan2(y - center_y, x - center_x)
                # 计算变形强度，距离中心越近变形越强
                factor = intensity * (1.0 - distance / radius)**2
                # 计算新的坐标，使用非线性变形
                map_x[y, x] = x + factor * distance * np.cos(angle) * (1 + 0.2 * np.sin(2 * angle))
                map_y[y, x] = y + factor * distance * np.sin(angle) * (1 + 0.2 * np.cos(2 * angle))
            else:
                map_x[y, x] = x
                map_y[y, x] = y
    
    return map_x, map_y
```

### 3. 数据集增强

数据集增强通过创建一个包装数据集，对KC样本进行增强：

```python
class KCDatasetWithAugmentation(torch.utils.data.Dataset):
    def __init__(self, dataset, kc_label=2, augment_factor=3):
        self.dataset = dataset
        self.kc_label = kc_label
        self.augment_factor = augment_factor
        
        # KC特定增强器
        self.kc_augmenter = KCSpecificAugmenter(probability=0.8)
        
        # 创建KC样本索引列表
        self.kc_indices = []
        for i in range(len(dataset)):
            try:
                _, label = dataset[i]
                if label == kc_label:
                    self.kc_indices.append(i)
            except:
                continue
        
        # 打印KC样本数量
        self.original_kc_count = len(self.kc_indices)
        self.augmented_kc_count = self.original_kc_count * self.augment_factor
        print(f"原始圆锥角膜(KC)样本数量: {self.original_kc_count}")
        print(f"增强后的圆锥角膜(KC)样本数量: {self.augmented_kc_count} (增强倍数: {self.augment_factor})")
    
    def __len__(self):
        # 计算总样本数：原始数据集大小 + (增强倍数-1) * KC样本数
        return len(self.dataset) + (self.augment_factor - 1) * len(self.kc_indices)
    
    def __getitem__(self, idx):
        # 处理索引映射
        original_dataset_size = len(self.dataset)
        
        # 如果索引小于原始数据集大小，直接从原始数据集获取
        if idx < original_dataset_size:
            original_idx = idx
            is_augmented = False
        else:
            # 否则，这是一个增强的KC样本
            # 计算对应的原始KC样本索引
            augmented_idx = idx - original_dataset_size
            kc_idx = augmented_idx % len(self.kc_indices)
            original_idx = self.kc_indices[kc_idx]
            is_augmented = True
        
        # 获取原始图像和标签
        image, label = self.dataset[original_idx]
        
        # 如果是KC样本或增强的KC样本，应用特殊增强
        if label == self.kc_label:
            # 对增强样本使用不同的随机种子，确保多样性
            if is_augmented:
                # 使用索引作为随机种子的一部分，确保同一个样本的不同增强版本是不同的
                random.seed(idx * 10 + 42)
                np.random.seed(idx * 10 + 42)
                
                # 应用KC特定增强
                image = self.kc_augmenter(image)
                
                # 应用KC特定转换
                image = self.kc_transform(image)
            else:
                # 对原始KC样本应用标准转换
                image = self.standard_transform(image)
        else:
            # 对其他样本应用标准转换
            image = self.standard_transform(image)
        
        return image, label
```

## 预期效果

通过KC特定增强，我们预期：

1. KC类别的准确率将显著提高，从当前的77.50%提升到85%以上
2. 总体准确率将提高到90%以上
3. E-KC和Normal类别的准确率将保持在95%以上

## 注意事项

1. KC特定增强需要OpenCV库的支持，如果未安装OpenCV，某些增强功能将被禁用
2. 增强倍数不宜设置过大，以避免过拟合
3. 增强参数可能需要根据具体数据集进行调整

# 优化模型独立版本使用说明

## 概述

这是一个完整的独立版本，包含了运行优化模型所需的所有代码和数据文件。基于 `run_optimized.sh` 脚本创建，确保可以在任何环境中独立运行。

## 文件结构

```
optimized_model_standalone/
├── run_optimized.sh                    # 主要运行脚本（已修改路径为相对路径）
├── train_balanced_task_sampling.py     # 主要训练脚本
├── models/                             # 模型定义文件夹
│   ├── __init__.py
│   ├── enhanced_feature_extractor.py
│   ├── enhanced_losses.py
│   ├── enhanced_maml.py
│   ├── feature_extractor.py
│   ├── losses.py
│   ├── maml.py
│   ├── normalized_feature_extractor.py
│   ├── pretrained_feature_extractor.py
│   └── protonet.py
├── datasets/                           # 数据集处理文件夹
│   ├── __init__.py
│   ├── augmentation.py
│   ├── balanced_task_sampler.py
│   ├── enhanced_gpu_augmentation.py
│   ├── enhanced_kc_augmentation.py
│   ├── gpu_augmentation.py
│   ├── kc_augmentation.py
│   └── keratoconus_dataset.py
├── split_result/                       # 数据分割结果
│   ├── train_set.csv
│   ├── val_set.csv
│   ├── test_set.csv
│   ├── train/                          # 训练集图像
│   ├── val/                            # 验证集图像
│   └── test/                           # 测试集图像
├── pretrain/                           # 预训练模型相关
│   ├── pretrain_dataset.py
│   ├── pretrain_model.py
│   ├── pretrain_resnet34.py
│   ├── run_pretrain.sh
│   └── pretrained_models/
│       └── normal_kc_pretrained_resnet34.pth
└── README_STANDALONE.md                # 本说明文件
```

## 环境要求

### Python 依赖
```bash
pip install torch torchvision torchaudio
pip install numpy pandas matplotlib seaborn
pip install scikit-learn tqdm Pillow
pip install opencv-python
```

### 系统要求
- Python 3.8+
- CUDA 支持的 GPU（推荐）
- 至少 8GB 内存
- 足够的磁盘空间（数据集约需要几GB）

## 使用方法

### 1. 基本运行
```bash
cd optimized_model_standalone
chmod +x run_optimized.sh
./run_optimized.sh
```

### 2. 自定义参数运行
```bash
# 使用单GPU运行
./run_optimized.sh --gpu_ids "0"

# 修改训练轮数
./run_optimized.sh --epochs 50

# 后台运行
./run_optimized.sh --run_mode nohup

# 直接运行（前台）
./run_optimized.sh --run_mode direct
```

### 3. 主要参数说明

#### GPU和硬件参数
- `--gpu_ids`: 使用的GPU ID，默认"0,1"
- `--num_workers`: 数据加载线程数，默认4

#### 训练参数
- `--epochs`: 训练轮数，默认30
- `--lr`: 学习率，默认0.0002
- `--weight_decay`: 权重衰减，默认5e-4
- `--feature_dim`: 特征维度，默认512

#### MAML算法参数
- `--inner_steps`: 内循环步数，默认2
- `--inner_lr`: 内循环学习率，默认0.3
- `--tasks_per_epoch`: 每轮任务数，默认30
- `--meta_batch_size`: 元批次大小，默认8

#### 损失函数参数
- `--early_kc_weight`: 早期KC权重，默认8.0
- `--kc_weight`: KC权重，默认4.0
- `--focal_gamma`: Focal Loss gamma，默认2.0

## 输出结果

运行完成后，会在以下位置生成结果：
- `results_optimized_[时间戳]_gpu[GPU_IDS]/`
  - `best_maml_model.pth`: 最佳模型
  - `test_results.json`: 测试结果
  - `training_history.png`: 训练曲线
  - `confusion_matrix.png`: 混淆矩阵
  - `optimization_config.txt`: 优化配置

## 日志文件

- 日志保存在 `logs/` 目录下
- 文件名格式：`optimized_[时间戳]_gpu[GPU_IDS].log`
- 进程ID保存在：`logs/pid_optimized_[时间戳].txt`

## 监控训练进度

```bash
# 查看实时日志
tail -f logs/optimized_[时间戳]_gpu[GPU_IDS].log

# 停止训练（如果使用nohup模式）
kill $(cat logs/pid_optimized_[时间戳].txt)
```

## 故障排除

### 1. 路径问题
确保所有文件都在正确的相对位置，脚本会自动检测当前目录。

### 2. GPU内存不足
- 减少 `meta_batch_size`
- 减少 `feature_dim`
- 使用更少的GPU

### 3. 数据加载错误
检查 `split_result/` 目录下的CSV文件和图像文件是否完整。

### 4. 模型加载错误
确保 `pretrain/pretrained_models/` 目录下有预训练模型文件。

## 注意事项

1. **数据路径**: 脚本已修改为使用相对路径，确保在 `optimized_model_standalone` 目录下运行
2. **预训练模型**: 包含了必要的预训练模型文件
3. **独立性**: 这个版本不依赖原始项目目录，可以独立运行
4. **备份**: 建议在运行前备份重要数据

## 技术支持

如果遇到问题，请检查：
1. 环境依赖是否正确安装
2. 数据文件是否完整
3. GPU驱动和CUDA是否正确配置
4. 日志文件中的错误信息

## 版本信息

- 基于原始项目的优化配置
- 包含所有必要的模型和数据文件
- 支持独立部署和运行
- 最后更新：2025年1月

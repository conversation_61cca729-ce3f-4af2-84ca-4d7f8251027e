# 圆锥角膜分类模型 - MAML-ProtoNet混合框架

## 1. 项目概述

本项目实现了一个基于MAML-ProtoNet混合框架的圆锥角膜分类模型，用于区分正常角膜、早期圆锥角膜和圆锥角膜。该框架结合了模型无关元学习(MAML)和原型网络(ProtoNet)的优势，特别适合处理类别不平衡和样本稀少的医学图像数据集。

## 2. 模型架构

### 2.1 MAML-ProtoNet混合框架

MAML-ProtoNet混合框架结合了两种元学习方法的优势：
- **MAML**：提供快速适应能力，通过梯度下降在支持集上更新模型参数
- **ProtoNet**：通过原型表示类别，提高样本利用率和计算效率

### 2.2 主要特点

- **多原型表示**：每个类别可以有多个原型，更好地捕捉类内变异
- **平衡任务采样**：确保每个任务包含足够的少数类样本
- **对比学习**：增强特征区分能力，改善类别边界
- **类别权重**：调整不同类别在损失函数中的重要性

## 3. 消融实验分析

为了验证各组件的有效性并找到最佳配置，我们进行了一系列消融实验。

### 3.1 原型数量消融实验

我们测试了四种不同的原型数量配置：

| 实验ID | 配置描述 | 原型数量 (Normal,E-KC,KC) | 总体准确率 | Normal准确率 | E-KC准确率 | KC准确率 |
|--------|----------|---------------------------|------------|--------------|------------|----------|
| PC-1   | 基线     | 1,1,1                     | 71.94%     | 96.67%       | 52.50%     | 66.67%   |
| PC-2   | 轻度多原型 | 1,2,1                   | 65.83%     | 78.33%       | 64.17%     | 55.00%   |
| PC-3   | 中度多原型 | 2,4,2                   | 85.83%     | 81.67%       | 100.00%    | 75.83%   |
| PC-4   | 高度多原型 | 3,6,3                   | 53.06%     | 66.67%       | 45.83%     | 46.67%   |

**结论**：
- PC-3配置（2,4,2）表现最佳，总体准确率达到85.83%
- 早期圆锥角膜(E-KC)类别需要更多的原型(4个)来捕捉其多样性
- 过多的原型(PC-4)反而导致性能下降，可能是过拟合所致

### 3.2 类别权重消融实验

我们测试了四种不同的类别权重配置：

| 实验ID | 配置描述 | E-KC权重 | KC权重 | 总体准确率 | Normal准确率 | E-KC准确率 | KC准确率 |
|--------|----------|----------|--------|------------|--------------|------------|----------|
| CW-1   | 基线     | 1.0      | 1.0    | 74.72%     | 78.33%       | 67.50%     | 78.33%   |
| CW-2   | 轻度权重 | 4.0      | 2.0    | 65.28%     | 61.67%       | 66.67%     | 67.50%   |
| CW-3   | 中度权重 | 8.0      | 3.0    | 85.83%     | 81.67%       | 100.00%    | 75.83%   |
| CW-4   | 高度权重 | 12.0     | 5.0    | 74.72%     | 80.83%       | 67.50%     | 75.83%   |

**结论**：
- CW-3配置（E-KC权重=8.0，KC权重=3.0）表现最佳，总体准确率达到85.83%
- 早期圆锥角膜需要较高的权重(8.0)来确保模型充分关注这一类别
- 过高的权重(CW-4)会导致性能下降，可能是因为过度关注少数类而忽略了多数类

### 3.3 对比学习消融实验

我们测试了四种不同的对比学习配置：

| 实验ID | 配置描述 | 对比学习权重 | 硬负样本比例 | 总体准确率 | Normal准确率 | E-KC准确率 | KC准确率 |
|--------|----------|--------------|-------------|------------|--------------|------------|----------|
| CL-1   | 基线     | 0.0          | 0.0         | 86.39%     | 99.17%       | 86.67%     | 73.33%   |
| CL-2   | 轻度对比 | 0.3          | 0.5         | 81.94%     | 98.33%       | 79.17%     | 68.33%   |
| CL-3   | 中度对比 | 0.5          | 0.7         | 91.39%     | 95.83%       | 99.17%     | 79.17%   |
| CL-4   | 高度对比 | 0.7          | 0.9         | 71.67%     | 76.67%       | 74.17%     | 64.17%   |

**结论**：
- CL-3配置（对比学习权重=0.5，硬负样本比例=0.7）表现最佳，总体准确率达到91.39%
- 中度对比学习有助于改善特征区分能力，特别是早期圆锥角膜和圆锥角膜之间的边界
- 过强的对比学习(CL-4)会导致性能下降，可能是因为过度关注特征区分而忽略了类内相似性

### 3.4 平衡任务采样消融实验

我们测试了四种不同的任务采样配置：

| 实验ID | 配置描述 | KC样本倍数 | E-KC样本倍数 | 总体准确率 | Normal准确率 | E-KC准确率 | KC准确率 |
|--------|----------|------------|--------------|------------|--------------|------------|----------|
| BTS-1  | 基线     | 1.0        | 1.0          | 78.61%     | 90.00%       | 75.00%     | 70.83%   |
| BTS-2  | 轻度平衡 | 2.0        | 1.2          | 83.06%     | 93.33%       | 85.00%     | 70.83%   |
| BTS-3  | 中度平衡 | 3.0        | 1.5          | 91.39%     | 95.83%       | 99.17%     | 79.17%   |
| BTS-4  | 高度平衡 | 4.0        | 2.0          | 85.28%     | 90.00%       | 90.83%     | 75.00%   |

**结论**：
- BTS-3配置（KC样本倍数=3.0，E-KC样本倍数=1.5）表现最佳，总体准确率达到91.39%
- 适当增加少数类在任务中的表示有助于提高模型性能
- 过度增加少数类表示(BTS-4)会导致性能下降，可能是因为破坏了原始数据分布

## 4. 最佳模型性能

基于消融实验结果，我们的最佳模型采用以下配置：
- **原型数量**：Normal(2), E-KC(4), KC(2)
- **类别权重**：E-KC(8.0), KC(3.0)
- **对比学习**：权重(0.5), 硬负样本比例(0.7)
- **任务采样**：KC样本倍数(3.0), E-KC样本倍数(1.5)

最佳模型在测试集上的性能：
- **总体准确率**：91.39%
- **正常样本准确率**：95.83%
- **早期圆锥角膜准确率**：99.17%
- **圆锥角膜准确率**：79.17%

## 5. 进一步优化方向

为了进一步提高模型性能，特别是圆锥角膜类别的准确率，我们提出以下优化方向：

1. **预训练特征提取器**：使用正常角膜和圆锥角膜样本预训练ResNet34模型，然后将其作为特征提取器
2. **增加圆锥角膜原型数量**：从2个增加到3个，提高对类内变异的建模能力
3. **微调类别权重**：将圆锥角膜的类别权重从3.0增加到4.0，提高模型对圆锥角膜样本的关注度
4. **增加训练轮数和任务数量**：给模型更多的学习时间和样本
5. **使用OneCycle学习率调度器**：优化学习率变化曲线，提高训练效率

## 6. 使用说明

### 6.1 训练模型

```bash
# 使用默认配置训练模型
./run_balanced_task_sampling.sh

# 使用指定GPU训练模型
./run_balanced_task_sampling.sh --gpu_ids 0,1

# 使用优化配置训练模型
./run_optimized_v2.sh --gpu_ids 0,1
```

### 6.2 评估模型

```bash
# 评估模型性能
python evaluate_model.py --model_path results_<时间戳>_gpu<GPU_IDS>/best_model.pth --test_csv ../split_result/test_set.csv
```

### 6.3 可视化结果

```bash
# 可视化消融实验结果
python visualize_ablation_results.py
```

## 7. 参考资料

1. [MAML论文](https://arxiv.org/abs/1703.03400)
2. [ProtoNet论文](https://arxiv.org/abs/1703.05175)
3. [对比学习综述](https://arxiv.org/abs/2010.05113)
4. [元学习在医学图像分析中的应用](https://www.sciencedirect.com/science/article/pii/S1361841520301535)

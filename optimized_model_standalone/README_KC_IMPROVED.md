# 圆锥角膜分类模型优化 - 提高KC类别准确率

本文档描述了针对圆锥角膜分类任务的优化方案，特别是为了提高圆锥角膜(KC)类别的准确率，同时保持早期圆锥角膜(E-KC)和正常样本(Normal)的高准确率。

## 背景

在之前的训练中，模型在早期圆锥角膜(99.17%)和正常样本(97.50%)上表现良好，但在圆锥角膜类别上准确率较低(77.50%)。主要错误类型是将圆锥角膜误分类为早期圆锥角膜，这表明模型在区分这两个类别时存在困难。

### 参数调整历史

1. **第一次调整**：
   - 启用特征归一化
   - 增加KC类别权重至6.0
   - 增加KC样本倍数至4.0
   - 增加对比学习权重至0.7
   - 增加硬负样本挖掘比例至0.7
   - **结果**：总体准确率53.33%，性能大幅下降

2. **第二次调整**：
   - 禁用特征归一化
   - 保留其他参数不变
   - **结果**：总体准确率86.39%，KC类别75.00%，E-KC类别85.83%，Normal类别98.33%
   - 相比原始模型，E-KC类别准确率大幅下降（从99.17%降至85.83%）

3. **第三次调整**：
   - 调整类别权重、任务采样和对比学习参数
   - **结果**：总体准确率70.28%，KC类别54.17%，E-KC类别65.00%，Normal类别91.67%
   - 性能进一步下降

4. **第四次调整**：
   - 回归到更接近原始参数的设置，进行微调
   - 轻微增加KC类别权重至4.5
   - 轻微增加KC样本倍数至3.2
   - 保持对比学习参数不变
   - **结果**：总体准确率86.39%，KC类别59.17%，E-KC类别100.00%，Normal类别100.00%
   - E-KC和Normal类别达到完美准确率，但KC类别准确率较低

5. **第五次调整**：
   - 进一步增强KC类别的识别能力
   - 增加KC类别权重至6.0
   - 增加KC样本倍数至4.0
   - 增加Focal Loss的gamma参数至3.0
   - **结果**：总体准确率65.00%，KC类别35.83%，E-KC类别62.50%，Normal类别96.67%
   - 性能进一步下降

6. **当前调整**：
   - 回归到原始参数设置
   - KC类别权重恢复为4.0
   - KC样本倍数恢复为3.0
   - Focal Loss的gamma参数恢复为2.0
   - 对比学习参数保持不变

## 优化方案

我们实施了以下优化策略来提高KC类别的准确率：

### 1. ~~启用特征归一化~~ (已禁用)

~~特征归一化可以使模型更加关注特征的方向而不是大小，有助于改善KC和E-KC之间的决策边界。~~

经过测试，特征归一化导致模型性能大幅下降（总体准确率降至53.33%），因此已禁用此功能：

```bash
USE_NORMALIZED_FEATURES=false
```

> 注意：在某些情况下，特征归一化可能会导致性能下降，这可能是由于数据分布特性或与其他优化策略的不兼容造成的。

### 2. 回归到原始类别权重

经过多次调整，我们发现增加KC类别权重可能会导致模型性能下降。因此，我们决定回归到原始的类别权重设置。

```bash
KC_WEIGHT=4.0  # 恢复原始值
```

### 3. 回归到原始任务采样策略

同样，我们发现增加KC样本倍数也可能会导致模型性能下降。因此，我们决定回归到原始的任务采样策略。

```bash
KC_SHOT_MULTIPLIER=3.0    # 恢复原始值
EARLY_KC_SHOT_MULTIPLIER=1.5  # 保持原始值不变
```

### 4. 回归到原始Focal Loss参数

增加Focal Loss的gamma参数也可能会导致模型性能下降。因此，我们决定回归到原始的Focal Loss参数。

```bash
FOCAL_GAMMA=2.0  # 恢复原始值
```

### 5. 保持对比学习参数不变

对比学习参数在之前的调整中保持不变，我们继续保持这些参数不变。

```bash
CONTRASTIVE_WEIGHT=0.5  # 保持原始值不变
HARD_MINING_RATIO=0.5   # 保持原始值不变
```

## 使用方法

1. 使用新的训练脚本`run_kc_improved.sh`：

```bash
cd balanced_task_sampling
./run_kc_improved.sh --gpu_ids "0,1"
```

2. 主要参数说明：

   - `--gpu_ids`：指定使用的GPU ID，例如"0,1"表示使用GPU 0和1
   - `--epochs`：训练轮数，默认为50
   - `--run_mode`：运行模式，可选"nohup"(后台运行)或"direct"(直接运行)

3. 训练完成后，可以使用评估脚本评估模型性能：

```bash
./run_evaluation.sh --model_path "results_kc_improved_XXXXXXXX_gpuX,X/best_maml_model.pth"
```

## 预期效果

通过回归到原始参数设置，我们预期：

1. 模型性能将恢复到接近原始水平，总体准确率达到90%左右
2. KC类别的准确率将恢复到接近原始水平，达到75%以上
3. E-KC类别的准确率将恢复到接近原始水平，达到95%以上
4. Normal类别的准确率将保持在95%以上

### 调整策略说明

经过多次尝试，我们得出以下结论：

1. **特征归一化不适用**：特征归一化对当前任务不适用，会导致性能大幅下降
2. **参数调整需谨慎**：过度调整参数可能会导致模型性能下降，特别是增加KC类别权重、KC样本倍数和Focal Loss的gamma参数
3. **原始参数可能是最优的**：原始参数设置可能已经是当前模型架构下的最优选择

因此，我们决定回归到原始参数设置，只保留禁用特征归一化的调整，这应该能够恢复模型性能到接近原始水平。

### 后续建议

如果希望进一步提高KC类别的准确率，可能需要考虑以下方向：

1. **模型架构改进**：尝试不同的网络架构或增加网络容量
2. **数据增强**：针对KC类别的特定增强，增加KC类别的样本多样性
3. **分层分类**：先区分Normal和非Normal，再区分KC和E-KC
4. **集成学习**：训练多个模型，通过投票或加权平均提高准确率

## 技术细节

### ~~特征归一化~~ (已禁用)

~~特征归一化是将特征向量除以其L2范数（欧氏距离），使其长度为1的过程：~~

```python
# 已禁用
# features = F.normalize(features, p=2, dim=1) * normalize_scale
```

~~这样可以确保所有特征向量都位于单位超球面上，只有方向不同，大小相同。归一化缩放因子(normalize_scale)用于控制归一化后特征的大小，较大的值可能会导致过拟合，较小的值可能会导致欠拟合。~~

经过测试，特征归一化在当前任务中导致性能下降，可能的原因包括：
1. 数据分布特性与归一化不兼容
2. 与其他优化策略（如对比学习）产生负面交互
3. 模型对特征幅度信息的依赖性较强

### 类别权重

类别权重用于平衡不同类别在损失函数中的贡献：

```python
class_weights = torch.tensor([1.0, args.early_kc_weight, args.kc_weight], device=device)
criterion = FocalLoss(gamma=args.focal_gamma, alpha=class_weights)
```

增加KC类别的权重可以使模型更加关注KC类别的样本，从而提高KC类别的准确率。

### 任务采样策略

任务采样策略控制每个任务中不同类别样本的比例：

```python
train_task_sampler = BalancedTaskSampler(
    train_dataset,
    n_way=args.n_way,
    n_shot=args.n_shot,
    n_query=args.n_query,
    kc_shot_multiplier=args.kc_shot_multiplier,
    early_kc_shot_multiplier=args.early_kc_shot_multiplier,
    num_workers=args.num_workers
)
```

增加KC样本在支持集中的比例可以使模型在训练过程中更加关注KC类别，从而提高KC类别的准确率。

### 对比学习

对比学习通过拉近相同类别样本的特征表示，推远不同类别样本的特征表示，来学习更好的特征表示：

```python
contrastive_loss = contrastive_loss_fn(
    query_features, query_labels, support_features, support_labels
)
loss += args.contrastive_weight * contrastive_loss
```

增加对比学习的权重和硬负样本挖掘比例可以提高模型对相似类别的区分能力，特别是对于KC和E-KC这样的相似类别。

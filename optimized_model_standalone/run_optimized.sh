#!/bin/bash

#############################################################
# 优化模型性能脚本 - 独立版本
# 基于消融实验结果，组合最佳配置并进一步优化
# 此版本包含所有必要的代码和数据文件
#############################################################

# ==================== 默认参数设置 ====================

# ----- 运行模式参数 -----
RUN_MODE="nohup"          # 运行模式: nohup(后台运行), direct(直接运行)
SETUP_FIRST=false         # 是否先运行setup.sh脚本准备环境

# ----- GPU和硬件参数 -----
GPU_IDS="0,1"         # 使用的GPU ID，例如"0,1,2,3"表示使用GPU 0,1,2,3
NUM_WORKERS=4             # 数据加载的工作线程数，影响数据加载速度，建议设为CPU核心数的一半

# ----- 训练基本参数 -----
EPOCHS=30                 # 训练轮数，影响总训练时间
LR=0.0002                 # 基础学习率，元学习的外循环学习率
WEIGHT_DECAY=5e-4         # 权重衰减系数，用于L2正则化防止过拟合
FEATURE_DIM=512           # 特征维度，影响模型容量和表达能力
DROPOUT=0.5               # Dropout比率，防止过拟合的另一种方法
SEED=42                   # 随机种子，确保实验可重复性
OUTPUT_DIR="results_optimized"  # 输出目录，保存模型和结果的位置

# ----- MAML算法参数 -----
INNER_STEPS=2             # 内循环步数，MAML算法中每个任务的梯度更新次数
INNER_LR=0.3              # 内循环学习率，MAML算法中任务适应阶段的学习率
TASKS_PER_EPOCH=30       # 每个epoch的任务数，影响训练速度和稳定性
META_BATCH_SIZE=8       # 元批次大小，每次更新处理的任务数量
N_WAY=3                   # 任务中的类别数，对应normal、e-kc、kc三个类别
N_SHOT=5                  # 支持集中每个类别的样本数
N_QUERY=15                # 查询集中每个类别的样本数

# ----- 模型架构参数 -----
PROTO_COUNTS="2,4,2"      # 每个类别的原型数量，分别对应normal(2个)、e-kc(4个)、kc(3个)
PRETRAINED=true           # 是否使用预训练的特征提取器

# ----- 类别权重和损失函数参数 -----
EARLY_KC_WEIGHT=8.0       # 早期圆锥角膜样本在损失函数中的权重，提高对少数类的关注
KC_WEIGHT=4.0             # 圆锥角膜类别在分类损失函数中的权重，平衡类别不均衡
FOCAL_GAMMA=2.0           # Focal Loss的gamma参数，调整难易样本的权重比例

# ----- 数据增强参数 - 全部禁用 -----
EARLY_KC_AUGMENT=false    # 禁用早期圆锥角膜样本增强
KC_AUGMENT=false          # 禁用圆锥角膜样本增强
ADVANCED_AUGMENT=false    # 禁用高级数据增强(Mixup和CutMix)
EARLY_KC_SPECIFIC_AUGMENT=false # 禁用早期圆锥角膜样本特定增强
USE_ENHANCED_GPU_AUGMENT=false  # 禁用增强版GPU数据增强
MIXUP_ALPHA=0.0           # 设置为0以禁用Mixup
CUTMIX_PROB=0.0           # 设置为0以禁用CutMix
AUGMENT_FACTOR=1          # 设置为1以禁用样本增强
KC_AUGMENT_FACTOR=1       # 设置为1以禁用KC样本增强

# ----- 对比学习参数 -----
USE_CONTRASTIVE=true      # 是否使用对比学习
CONTRASTIVE_WEIGHT=0.5    # 对比学习损失在总损失中的权重
TEMPERATURE=0.07          # 对比学习的温度参数，控制特征分布的集中程度
HARD_MINING_RATIO=0.7    # 硬负样本挖掘比例，关注更有挑战性的负样本
EARLY_NORMAL_WEIGHT=2.0   # 早期圆锥角膜与正常样本对比时的权重
KC_NORMAL_WEIGHT=1.0

# ----- 特征归一化参数 -----
USE_NORMALIZED_FEATURES=false  # 是否使用归一化特征
NORMALIZE_SCALE=15.0      # 归一化后的缩放因子，影响特征分布

# ----- 任务采样参数 -----
USE_SEPARATE_TEST_SAMPLER=true # 是否使用单独的测试集任务采样器
USE_BALANCED_TASK_SAMPLER=true # 是否使用平衡任务采样器，确保每个任务包含所有类别
KC_SHOT_MULTIPLIER=3.0    # 圆锥角膜样本在支持集中的倍数，增加少数类在任务中的表示
EARLY_KC_SHOT_MULTIPLIER=1.5 # 早期圆锥角膜样本在支持集中的倍数

# ----- 验证和早停参数 -----
VAL_FREQUENCY=1           # 验证频率，每隔多少个epoch进行一次验证
EARLY_STOPPING=5          # 早停耐心值，连续多少个epoch没有改进后停止训练

# ----- 学习率调度器参数 -----
USE_LR_SCHEDULER=true     # 是否使用学习率调度器
SCHEDULER_TYPE="plateau"  # 学习率调度器类型: onecycle或plateau
MAX_LR=0.0002             # OneCycleLR的最大学习率，训练中会达到的最高学习率
DIV_FACTOR=10.0           # OneCycleLR的初始学习率除数，初始lr = MAX_LR/DIV_FACTOR
FINAL_DIV_FACTOR=100      # OneCycleLR的最终学习率除数，最终lr = MAX_LR/(DIV_FACTOR*FINAL_DIV_FACTOR)
PCT_START=0.3             # OneCycleLR的预热阶段比例，控制学习率上升阶段的长度
PLATEAU_FACTOR=0.5        # ReduceLROnPlateau的学习率衰减因子，每次降低学习率的比例
PLATEAU_PATIENCE=2        # ReduceLROnPlateau的耐心值，连续多少个epoch没有改进后降低学习率
MIN_LR=1e-6               # ReduceLROnPlateau的最小学习率，学习率不会低于此值

# ----- 数据路径参数 -----
# 获取脚本所在目录的绝对路径
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
TRAIN_CSV="${SCRIPT_DIR}/split_result/train_set.csv" # 训练集CSV文件路径
VAL_CSV="${SCRIPT_DIR}/split_result/val_set.csv"     # 验证集CSV文件路径
TEST_CSV="${SCRIPT_DIR}/split_result/test_set.csv"   # 测试集CSV文件路径

# ==================== 命令行参数解析 ====================
while [[ $# -gt 0 ]]; do
  case $1 in
    --run_mode)
      RUN_MODE="$2"
      shift 2
      ;;
    --setup_first)
      SETUP_FIRST="$2"
      shift 2
      ;;
    --gpu_ids)
      GPU_IDS="$2"
      shift 2
      ;;
    # 其他参数省略
    *)
      echo "未知参数: $1"
      exit 1
      ;;
  esac
done

# ==================== 环境准备 ====================
# 如果需要先运行setup.sh
if [ "$SETUP_FIRST" = "true" ]; then
  echo "步骤1: 运行setup.sh准备环境..."
  bash setup.sh
fi

# 创建日志目录
mkdir -p logs

# 获取当前时间戳
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
LOG_FILE="logs/optimized_${TIMESTAMP}_gpu${GPU_IDS}.log"
FINAL_OUTPUT_DIR="${OUTPUT_DIR}_${TIMESTAMP}_gpu${GPU_IDS}"

echo "日志文件: $LOG_FILE"
echo "输出目录: $FINAL_OUTPUT_DIR"

# 设置环境变量
export CUDA_VISIBLE_DEVICES=$GPU_IDS
export OMP_NUM_THREADS=1
export MKL_NUM_THREADS=1
export MPLBACKEND=Agg

# 创建输出目录
mkdir -p $FINAL_OUTPUT_DIR

# ==================== 构建命令参数 ====================
# 构建预训练参数
PRETRAINED_ARGS=""
if [ "$PRETRAINED" = "true" ]; then
  PRETRAINED_ARGS="--pretrained"
fi

# 构建数据增强参数 - 全部禁用
AUGMENT_ARGS="--no_augmentation"  # 添加一个新参数，明确指示不使用任何数据增强

# 构建对比学习参数
CONTRASTIVE_ARGS=""
if [ "$USE_CONTRASTIVE" = "true" ]; then
  CONTRASTIVE_ARGS="--use_contrastive --contrastive_weight $CONTRASTIVE_WEIGHT --temperature $TEMPERATURE --hard_mining_ratio $HARD_MINING_RATIO --early_normal_weight $EARLY_NORMAL_WEIGHT"
fi

# 构建特征归一化参数
NORMALIZE_ARGS=""
if [ "$USE_NORMALIZED_FEATURES" = "true" ]; then
  NORMALIZE_ARGS="--use_normalized_features --normalize_scale $NORMALIZE_SCALE"
fi

# 构建任务采样器参数
SAMPLER_ARGS=""
if [ "$USE_SEPARATE_TEST_SAMPLER" = "true" ]; then
  SAMPLER_ARGS="$SAMPLER_ARGS --use_separate_test_sampler"
fi
if [ "$USE_BALANCED_TASK_SAMPLER" = "true" ]; then
  SAMPLER_ARGS="$SAMPLER_ARGS --use_balanced_task_sampler --kc_shot_multiplier $KC_SHOT_MULTIPLIER --early_kc_shot_multiplier $EARLY_KC_SHOT_MULTIPLIER"
fi

# 构建学习率调度器参数
LR_SCHEDULER_ARGS=""
if [ "$USE_LR_SCHEDULER" = "true" ]; then
  LR_SCHEDULER_ARGS="--use_lr_scheduler --scheduler_type $SCHEDULER_TYPE"
  if [ "$SCHEDULER_TYPE" = "onecycle" ]; then
    LR_SCHEDULER_ARGS="$LR_SCHEDULER_ARGS --max_lr $MAX_LR --div_factor $DIV_FACTOR --final_div_factor $FINAL_DIV_FACTOR --pct_start $PCT_START"
  elif [ "$SCHEDULER_TYPE" = "plateau" ]; then
    LR_SCHEDULER_ARGS="$LR_SCHEDULER_ARGS --plateau_factor $PLATEAU_FACTOR --plateau_patience $PLATEAU_PATIENCE --min_lr $MIN_LR"
  fi
fi

# ==================== 构建完整命令 ====================
# 基础命令
BASE_CMD="python train_balanced_task_sampling.py \
  --train_csv $TRAIN_CSV \
  --val_csv $VAL_CSV \
  --test_csv $TEST_CSV \
  --epochs $EPOCHS \
  --lr $LR \
  --weight_decay $WEIGHT_DECAY \
  --feature_dim $FEATURE_DIM \
  --save_dir $FINAL_OUTPUT_DIR \
  --proto_counts $PROTO_COUNTS \
  $PRETRAINED_ARGS \
  --inner_lr $INNER_LR \
  --inner_steps $INNER_STEPS \
  --n_way $N_WAY \
  --n_shot $N_SHOT \
  --n_query $N_QUERY \
  --tasks_per_epoch $TASKS_PER_EPOCH \
  --meta_batch_size $META_BATCH_SIZE \
  $AUGMENT_ARGS \
  --mixup_alpha $MIXUP_ALPHA \
  --cutmix_prob $CUTMIX_PROB \
  --num_workers $NUM_WORKERS \
  --augment_factor $AUGMENT_FACTOR \
  --kc_augment_factor $KC_AUGMENT_FACTOR \
  --seed $SEED \
  --early_kc_weight $EARLY_KC_WEIGHT \
  --kc_weight $KC_WEIGHT \
  --focal_gamma $FOCAL_GAMMA \
  --val_frequency $VAL_FREQUENCY \
  --early_stopping $EARLY_STOPPING \
  --dropout $DROPOUT \
  $SAMPLER_ARGS \
  $CONTRASTIVE_ARGS \
  $NORMALIZE_ARGS \
  $LR_SCHEDULER_ARGS"

# ==================== 执行命令 ====================
echo "开始优化模型训练..."
echo "使用GPU: $GPU_IDS"

# 将配置保存到输出目录
echo "优化配置:" > "${FINAL_OUTPUT_DIR}/optimization_config.txt"
echo "  PROTO_COUNTS=$PROTO_COUNTS" >> "${FINAL_OUTPUT_DIR}/optimization_config.txt"
echo "  EARLY_KC_WEIGHT=$EARLY_KC_WEIGHT" >> "${FINAL_OUTPUT_DIR}/optimization_config.txt"
echo "  KC_WEIGHT=$KC_WEIGHT" >> "${FINAL_OUTPUT_DIR}/optimization_config.txt"
echo "  KC_SHOT_MULTIPLIER=$KC_SHOT_MULTIPLIER" >> "${FINAL_OUTPUT_DIR}/optimization_config.txt"
echo "  TASKS_PER_EPOCH=$TASKS_PER_EPOCH" >> "${FINAL_OUTPUT_DIR}/optimization_config.txt"
echo "  META_BATCH_SIZE=$META_BATCH_SIZE" >> "${FINAL_OUTPUT_DIR}/optimization_config.txt"
echo "  SCHEDULER_TYPE=$SCHEDULER_TYPE" >> "${FINAL_OUTPUT_DIR}/optimization_config.txt"

# 根据运行模式选择执行方式
if [ "$RUN_MODE" = "nohup" ]; then
  # 使用nohup在后台运行
  echo "使用nohup在后台运行..."
  echo "$BASE_CMD" > $LOG_FILE
  nohup bash -c "$BASE_CMD" >> $LOG_FILE 2>&1 &

  # 获取进程ID
  PID=$!
  echo "进程已启动，PID: $PID"
  echo "您可以使用以下命令监控训练进度:"
  echo "  tail -f $LOG_FILE"
  echo "要停止训练，请使用:"
  echo "  kill $PID"

  # 将PID保存到文件中，方便后续管理
  echo $PID > "logs/pid_optimized_${TIMESTAMP}.txt"
else
  # 直接运行
  echo "直接运行训练脚本..."
  eval $BASE_CMD
fi

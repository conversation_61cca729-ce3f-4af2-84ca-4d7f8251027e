# 预训练特征提取器模型 - 使用说明

## 1. 概述

本文档介绍了使用正常角膜和圆锥角膜预训练的ResNet34作为特征提取器的模型训练方法。这种方法旨在提高圆锥角膜的识别准确率，同时不影响早期圆锥角膜的识别准确率。

**重要说明**：这是一个全新的实现，不会修改现有代码。如果效果不理想，可以轻松回溯到当前版本。

## 2. 预训练特征提取器的优势

1. **领域特定特征**：使用正常角膜和圆锥角膜预训练的ResNet34已经学习了角膜地形图的领域特定特征，能够更好地区分正常角膜和圆锥角膜。

2. **特征迁移**：通过迁移学习，预训练模型学到的特征可以帮助识别早期圆锥角膜，即使预训练数据中没有早期圆锥角膜样本。

3. **更好的初始化**：相比随机初始化，预训练模型提供了更好的参数初始化，可以加速收敛并提高性能。

4. **增强版特征提取器**：我们还提供了增强版特征提取器，添加了注意力机制和残差连接，进一步提高模型性能。

## 3. 实现细节

### 3.1 新增文件

1. **`models/pretrained_feature_extractor.py`**：预训练特征提取器模块，包含两个类：
   - `PretrainedKeratoconusFeatureExtractor`：基本预训练特征提取器
   - `EnhancedPretrainedKeratoconusFeatureExtractor`：增强版预训练特征提取器，添加了注意力机制和残差连接

2. **`train_pretrained_balanced_task_sampling.py`**：使用预训练特征提取器的训练脚本，基于`train_balanced_task_sampling.py`修改

3. **`run_pretrained_model.sh`**：运行预训练特征提取器训练的脚本

4. **`README_PRETRAINED_MODEL.md`**：本文档，详细说明预训练特征提取器的使用方法和预期效果

### 3.2 预训练特征提取器架构

#### 基本预训练特征提取器

```python
class PretrainedKeratoconusFeatureExtractor(nn.Module):
    def __init__(self, feature_dim=512, pretrained=True, pretrained_path=None, dropout=0.5):
        super(PretrainedKeratoconusFeatureExtractor, self).__init__()
        
        # 创建ResNet34模型
        self.resnet = models.resnet34(pretrained=False)
        
        # 修改最后的全连接层
        self.resnet.fc = nn.Identity()  # 移除原始的全连接层
        
        # 添加自定义的特征提取层
        self.feature_extractor = nn.Sequential(
            nn.Linear(512, feature_dim),  # ResNet34的输出是512维
            nn.BatchNorm1d(feature_dim),
            nn.ReLU(),
            nn.Dropout(dropout)
        )
        
        # 加载预训练权重
        if pretrained:
            self._load_pretrained_weights(pretrained_path)
```

#### 增强版预训练特征提取器

```python
class EnhancedPretrainedKeratoconusFeatureExtractor(PretrainedKeratoconusFeatureExtractor):
    def __init__(self, feature_dim=512, pretrained=True, pretrained_path=None, dropout=0.5):
        super(EnhancedPretrainedKeratoconusFeatureExtractor, self).__init__(
            feature_dim=feature_dim, 
            pretrained=pretrained, 
            pretrained_path=pretrained_path, 
            dropout=dropout
        )
        
        # 添加注意力机制
        self.attention = nn.Sequential(
            nn.Linear(512, 512),
            nn.Tanh(),
            nn.Linear(512, 1)
        )
        
        # 替换特征提取层
        self.feature_extractor = nn.Sequential(
            nn.Linear(512, feature_dim),
            nn.BatchNorm1d(feature_dim),
            nn.ReLU(),
            nn.Dropout(dropout)
        )
        
        # 添加残差连接
        self.residual = nn.Sequential(
            nn.Linear(512, feature_dim),
            nn.BatchNorm1d(feature_dim)
        ) if 512 != feature_dim else nn.Identity()
```

## 4. 使用方法

### 4.1 准备预训练模型

首先，您需要准备一个使用正常角膜和圆锥角膜预训练的ResNet34模型。预训练模型应该保存在`../pretrained_models/normal_kc_pretrained_resnet34.pth`路径下。如果您的预训练模型在其他位置，可以通过`--pretrained_path`参数指定。

### 4.2 基本用法

```bash
# 使用默认参数运行
./run_pretrained_model.sh --gpu_ids 0,1,2,3

# 使用特定GPU运行
./run_pretrained_model.sh --gpu_ids 0,1

# 指定预训练模型路径
./run_pretrained_model.sh --gpu_ids 0,1 --pretrained_path /path/to/your/pretrained_model.pth

# 禁用增强特征提取器
./run_pretrained_model.sh --gpu_ids 0,1 --use_enhanced_extractor false

# 直接运行（不在后台）
./run_pretrained_model.sh --gpu_ids 0,1 --run_mode direct
```

### 4.3 监控训练进度

当使用nohup模式运行时，可以通过以下命令监控训练进度：

```bash
# 查看日志文件
tail -f logs/pretrained_<时间戳>_gpu<GPU_IDS>.log
```

### 4.4 停止训练

如果需要停止训练，可以使用以下命令：

```bash
# 停止特定训练进程
kill $(cat logs/pid_pretrained_<时间戳>.txt)

# 停止所有预训练模型训练进程
kill $(cat logs/pid_pretrained_*.txt)
```

## 5. 参数配置

### 5.1 主要参数

```bash
# ----- 预训练特征提取器参数 -----
PRETRAINED=true           # 是否使用预训练权重
PRETRAINED_PATH="../pretrained_models/normal_kc_pretrained_resnet34.pth"  # 预训练模型路径
USE_ENHANCED_EXTRACTOR=true  # 是否使用增强版特征提取器

# ----- 模型架构参数 -----
PROTO_COUNTS="2,4,3"      # 每个类别的原型数量，分别对应normal(2个)、e-kc(4个)、kc(3个)

# ----- 类别权重参数 -----
EARLY_KC_WEIGHT=8.0       # 早期圆锥角膜样本权重
KC_WEIGHT=4.0             # 圆锥角膜类别权重

# ----- 任务采样参数 -----
KC_SHOT_MULTIPLIER=4.0    # 圆锥角膜样本倍数
```

### 5.2 其他重要参数

```bash
# ----- 训练基本参数 -----
EPOCHS=80                 # 训练轮数
TASKS_PER_EPOCH=40        # 每个epoch的任务数
META_BATCH_SIZE=10        # 元批次大小

# ----- 学习率调度器参数 -----
SCHEDULER_TYPE="onecycle" # 使用OneCycle学习率调度器
```

## 6. 预期结果

基于当前最佳模型的性能（总体准确率91.39%，正常样本95.83%，早期圆锥角膜99.17%，圆锥角膜79.17%），我们预期使用预训练特征提取器的模型能够：

1. **提高圆锥角膜准确率**：从79.17%提高到85%以上
2. **保持早期圆锥角膜准确率**：保持在99%左右
3. **保持或略微提高正常样本准确率**：保持在95%以上
4. **提高总体准确率**：从91.39%提高到93%以上

## 7. 结果分析

训练完成后，可以通过以下指标评估模型性能：

1. **总体准确率**：是否有所提升
2. **各类别准确率**：特别是圆锥角膜类别的准确率是否提高
3. **混淆矩阵**：类别之间的错误分类情况是否改善
4. **决策边界**：特别是圆锥角膜和早期圆锥角膜之间的决策边界是否更加清晰

## 8. 进一步优化建议

如果预训练特征提取器的效果不理想，可以考虑以下优化方向：

1. **调整预训练策略**：使用更多的正常角膜和圆锥角膜样本进行预训练
2. **微调预训练模型**：在使用预训练模型之前，先在当前数据集上微调
3. **调整特征提取器架构**：尝试不同的特征提取器架构，如ResNet50或DenseNet
4. **集成学习**：训练多个使用不同预训练模型的模型，然后使用集成方法

## 9. 注意事项

1. 请确保预训练模型的路径正确，否则模型将使用随机初始化权重
2. 增强版特征提取器可能需要更多的计算资源，如果遇到内存不足问题，可以禁用它
3. 预训练模型应该是使用正常角膜和圆锥角膜样本训练的，不应包含早期圆锥角膜样本
4. 这个实现是基于现有代码创建的新文件，不会修改现有代码，可以放心使用

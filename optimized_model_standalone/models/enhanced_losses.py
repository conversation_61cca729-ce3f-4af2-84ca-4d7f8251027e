import torch
import torch.nn as nn
import torch.nn.functional as F

class EnhancedMAMLContrastiveLoss(nn.Module):
    """
    增强版MAML对比学习损失：支持硬负样本挖掘和早期圆锥角膜与正常样本对比权重
    
    参数:
        temperature: 温度参数
        hard_mining_ratio: 硬负样本挖掘比例
        early_normal_weight: 早期圆锥角膜与正常样本对比的权重
        kc_normal_weight: 圆锥角膜与正常样本对比的权重
    """
    def __init__(self, temperature=0.1, hard_mining_ratio=0.7, early_normal_weight=2.0, kc_normal_weight=1.0):
        super(EnhancedMAMLContrastiveLoss, self).__init__()
        self.temperature = temperature
        self.hard_mining_ratio = hard_mining_ratio
        self.early_normal_weight = early_normal_weight
        self.kc_normal_weight = kc_normal_weight
    
    def forward(self, support_features, support_labels, query_features, query_labels):
        """
        计算增强版MAML对比学习损失
        
        参数:
            support_features: 支持集特征，形状为 [n_support, feature_dim]
            support_labels: 支持集标签，形状为 [n_support]
            query_features: 查询集特征，形状为 [n_query, feature_dim]
            query_labels: 查询集标签，形状为 [n_query]
        
        返回:
            loss: 增强版对比学习损失
        """
        device = support_features.device
        
        # 归一化特征
        support_features = F.normalize(support_features, dim=1)
        query_features = F.normalize(query_features, dim=1)
        
        # 计算查询集与支持集之间的相似度矩阵
        similarity = torch.matmul(query_features, support_features.t()) / self.temperature
        
        # 创建标签匹配矩阵 (1表示同类，0表示不同类)
        label_match = (query_labels.unsqueeze(1) == support_labels.unsqueeze(0)).float()
        
        # 创建早期圆锥角膜与正常样本对比的权重矩阵
        # 假设标签1是早期圆锥角膜，标签0是正常样本，标签2是圆锥角膜
        early_kc_query = (query_labels == 1).float().unsqueeze(1)  # 查询集中的早期圆锥角膜
        kc_query = (query_labels == 2).float().unsqueeze(1)  # 查询集中的圆锥角膜
        normal_support = (support_labels == 0).float().unsqueeze(0)  # 支持集中的正常样本
        early_normal_pairs = early_kc_query * normal_support  # 早期圆锥角膜与正常样本对
        kc_normal_pairs = kc_query * normal_support  # 圆锥角膜与正常样本对
        
        # 应用权重
        weighted_label_match = label_match.clone()
        weighted_label_match = weighted_label_match + early_normal_pairs * (self.early_normal_weight - 1.0)
        weighted_label_match = weighted_label_match + kc_normal_pairs * (self.kc_normal_weight - 1.0)
        
        # 硬负样本挖掘
        if self.hard_mining_ratio > 0:
            with torch.no_grad():
                # 创建负样本掩码
                neg_mask = 1.0 - label_match
                # 计算负样本相似度
                neg_similarity = similarity * neg_mask
                # 对每个查询样本，选择最难的负样本
                n_support = support_features.size(0)
                k = max(int(n_support * self.hard_mining_ratio), 1)
                # 将非负样本的相似度设为一个非常小的值
                neg_similarity = neg_similarity.masked_fill(neg_mask == 0, -1e10)
                # 获取每个查询样本的前k个最难负样本
                _, hard_indices = torch.topk(neg_similarity, k=k, dim=1)
                # 创建硬负样本掩码
                hard_neg_mask = torch.zeros_like(neg_mask, device=device)
                for i in range(query_features.size(0)):
                    hard_neg_mask[i, hard_indices[i]] = 1.0
                
                # 更新负样本掩码，只保留硬负样本
                neg_mask = hard_neg_mask
        
        # 计算每个查询样本的对比损失
        # 对数分母：所有支持样本的exp(similarity)之和
        log_denominator = torch.logsumexp(similarity, dim=1, keepdim=True)
        
        # 计算正样本的对数概率
        # 使用掩码选择同类样本的相似度，并应用logsumexp
        # 添加一个小的epsilon值，避免log(0)
        epsilon = 1e-8
        masked_similarity = similarity * weighted_label_match
        # 将非正样本的相似度设为一个非常小的值，这样logsumexp基本上只考虑正样本
        masked_similarity = masked_similarity.masked_fill(weighted_label_match == 0, -1e10)
        log_numerator = torch.logsumexp(masked_similarity, dim=1, keepdim=True)
        
        # 计算每个样本的损失
        per_sample_loss = log_denominator - log_numerator
        
        # 处理没有正样本的情况
        valid_samples = (weighted_label_match.sum(1) > 0).float()
        if valid_samples.sum() > 0:
            loss = (per_sample_loss.squeeze() * valid_samples).sum() / (valid_samples.sum() + epsilon)
        else:
            # 如果没有有效样本，返回一个小的常数损失
            loss = torch.tensor(0.1, device=device, requires_grad=True)
        
        return loss

class EnhancedFocalLoss(nn.Module):
    """
    增强版焦点损失：支持类别权重和样本权重
    
    参数:
        alpha: 类别权重，可以是标量或者张量
        gamma: 聚焦参数，用于降低易分类样本的权重
        reduction: 损失归约方式，可选值为'none'、'mean'、'sum'
        class_weights: 类别权重字典，键为类别标签，值为权重
    """
    def __init__(self, alpha=None, gamma=2.0, reduction='mean', class_weights=None):
        super(EnhancedFocalLoss, self).__init__()
        self.alpha = alpha
        self.gamma = gamma
        self.reduction = reduction
        self.class_weights = class_weights
    
    def forward(self, inputs, targets):
        """
        计算增强版焦点损失
        
        参数:
            inputs: 预测logits，形状为 [batch_size, num_classes]
            targets: 目标类别，形状为 [batch_size]
        
        返回:
            loss: 增强版焦点损失
        """
        # 计算交叉熵损失
        ce_loss = F.cross_entropy(inputs, targets, reduction='none')
        
        # 计算预测概率
        pt = torch.exp(-ce_loss)
        
        # 应用焦点权重
        focal_weight = (1 - pt) ** self.gamma
        
        # 应用类别权重
        if self.alpha is not None:
            if isinstance(self.alpha, torch.Tensor):
                # 如果alpha是张量，根据目标类别选择对应的权重
                alpha_t = self.alpha.gather(0, targets)
                focal_weight = alpha_t * focal_weight
            else:
                # 如果alpha是标量，直接乘以焦点权重
                focal_weight = self.alpha * focal_weight
        
        # 应用类别权重字典
        if self.class_weights is not None:
            # 创建权重张量
            weights = torch.ones_like(targets, dtype=torch.float)
            for label, weight in self.class_weights.items():
                weights[targets == label] = weight
            focal_weight = weights * focal_weight
        
        # 计算最终损失
        loss = focal_weight * ce_loss
        
        # 应用归约
        if self.reduction == 'mean':
            return loss.mean()
        elif self.reduction == 'sum':
            return loss.sum()
        else:  # 'none'
            return loss

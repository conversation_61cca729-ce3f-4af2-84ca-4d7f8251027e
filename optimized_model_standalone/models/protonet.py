import torch
import torch.nn as nn
import torch.nn.functional as F
from sklearn.cluster import DBSCAN
import numpy as np

class MultiPrototypeLayer(nn.Module):
    """
    多原型表示层：为不同类别分配不同数量的原型 - 增强版

    参数:
        n_classes: 类别数量
        proto_counts: 每个类别的原型数量列表
        feature_dim: 特征维度
    """
    def __init__(self, n_classes=3, proto_counts=[1, 5, 2], feature_dim=512):
        super(MultiPrototypeLayer, self).__init__()
        self.n_classes = n_classes
        self.proto_counts = proto_counts
        self.feature_dim = feature_dim

        # 总原型数量
        self.total_protos = sum(proto_counts)

        # 初始化原型参数（可学习）
        self.prototypes = nn.Parameter(torch.randn(self.total_protos, feature_dim))

        # 使用Kaiming初始化改善收敛性
        nn.init.kaiming_normal_(self.prototypes)

        # 记录每个类别的原型索引范围
        self.class_indices = []
        start_idx = 0
        for i in range(n_classes):
            end_idx = start_idx + proto_counts[i]
            self.class_indices.append((start_idx, end_idx))
            start_idx = end_idx

    def forward(self, features):
        """
        计算特征与各原型的距离

        参数:
            features: 形状为 [batch_size, feature_dim] 的特征张量

        返回:
            distances: 形状为 [batch_size, total_protos] 的距离张量
        """
        # 对特征进行L2归一化
        features = F.normalize(features, p=2, dim=1)

        # 对原型进行L2归一化
        normalized_protos = F.normalize(self.prototypes, p=2, dim=1)

        # 计算欧氏距离的平方
        batch_size = features.size(0)

        # 展开特征和原型以便计算距离
        expanded_features = features.unsqueeze(1).expand(batch_size, self.total_protos, self.feature_dim)
        expanded_protos = normalized_protos.unsqueeze(0).expand(batch_size, self.total_protos, self.feature_dim)

        # 计算欧氏距离的平方
        distances = torch.sum((expanded_features - expanded_protos) ** 2, dim=2)

        return distances

    def get_class_probabilities(self, features):
        """
        计算样本属于每个类别的概率

        参数:
            features: 形状为 [batch_size, feature_dim] 的特征张量

        返回:
            probs: 形状为 [batch_size, n_classes] 的概率张量
        """
        # 获取到所有原型的距离
        distances = self.forward(features)

        # 对距离取负并应用softmax得到每个原型的概率
        proto_probs = F.softmax(-distances, dim=1)

        # 聚合每个类别的原型概率
        class_probs = torch.zeros(features.size(0), self.n_classes, device=features.device)

        for i in range(self.n_classes):
            start_idx, end_idx = self.class_indices[i]
            # 对该类别的所有原型概率求和
            class_probs[:, i] = torch.sum(proto_probs[:, start_idx:end_idx], dim=1)

        # 重新归一化
        class_probs = class_probs / torch.sum(class_probs, dim=1, keepdim=True)

        return class_probs

    def compute_prototypes_from_support(self, support_features, support_labels):
        """
        从支持集计算原型 - 使用PyTorch操作以保留梯度信息

        参数:
            support_features: 支持集特征，形状为 [n_support, feature_dim]
            support_labels: 支持集标签，形状为 [n_support]

        返回:
            更新后的原型
        """
        # 使用PyTorch操作而不是NumPy操作，以保留梯度信息
        device = support_features.device

        # 为每个类别计算原型
        new_prototypes = self.prototypes.clone().to(device)

        # 对于每个类别，计算平均特征作为原型
        for class_idx in range(self.n_classes):
            # 获取该类别的样本索引
            class_mask = (support_labels == class_idx)
            if not torch.any(class_mask):
                continue  # 如果没有该类别的样本，跳过

            # 获取该类别的特征
            class_features = support_features[class_mask]

            # 获取该类别的原型数量
            n_protos = self.proto_counts[class_idx]

            # 获取该类别的原型索引范围
            start_idx, end_idx = self.class_indices[class_idx]

            if n_protos == 1:
                # 如果只有一个原型，直接计算平均值
                prototype = torch.mean(class_features, dim=0)
                new_prototypes[start_idx] = prototype
            else:
                # 如果有多个原型，使用简单的聚类方法
                # 这里使用一种简单的方法：将样本平均分配给每个原型
                n_samples = class_features.size(0)
                samples_per_proto = max(1, n_samples // n_protos)

                for i in range(n_protos):
                    start = i * samples_per_proto
                    end = min((i + 1) * samples_per_proto, n_samples)

                    if start < end:
                        # 计算这组样本的平均特征作为原型
                        proto_features = class_features[start:end]
                        prototype = torch.mean(proto_features, dim=0)
                        new_prototypes[start_idx + i] = prototype
                    else:
                        # 如果没有足够的样本，使用最后一个样本作为原型
                        new_prototypes[start_idx + i] = class_features[-1]

        # 更新原型并确保它们可以计算梯度
        self.prototypes = nn.Parameter(new_prototypes)

        return self.prototypes

class ProtoNet(nn.Module):
    """
    原型网络（ProtoNet）模型 - 增强版

    参数:
        feature_extractor: 特征提取器
        n_classes: 类别数量
        proto_counts: 每个类别的原型数量列表 (默认为[1,5,2]，增加早期圆锥角膜的原型数量)
        feature_dim: 特征维度
    """
    def __init__(self, feature_extractor, n_classes=3, proto_counts=[1, 5, 2], feature_dim=512):
        super(ProtoNet, self).__init__()
        self.feature_extractor = feature_extractor
        self.proto_layer = MultiPrototypeLayer(n_classes, proto_counts, feature_dim)

    def forward(self, x):
        """
        前向传播

        参数:
            x: 输入图像，形状为 [batch_size, channels, height, width]

        返回:
            class_probs: 类别概率，形状为 [batch_size, n_classes]
        """
        # 提取特征
        features = self.feature_extractor(x)

        # 计算类别概率
        class_probs = self.proto_layer.get_class_probabilities(features)

        return class_probs

    def get_features(self, x):
        """
        获取特征表示

        参数:
            x: 输入图像，形状为 [batch_size, channels, height, width]

        返回:
            features: 特征表示，形状为 [batch_size, feature_dim]
        """
        return self.feature_extractor(x)

    def compute_prototypes(self, support_images, support_labels):
        """
        从支持集计算原型

        参数:
            support_images: 支持集图像，形状为 [n_support, channels, height, width]
            support_labels: 支持集标签，形状为 [n_support]

        返回:
            更新后的原型
        """
        # 提取支持集特征
        support_features = self.feature_extractor(support_images)

        # 计算原型
        return self.proto_layer.compute_prototypes_from_support(support_features, support_labels)

    def set_prototypes(self, prototypes):
        """
        设置原型

        参数:
            prototypes: 新的原型张量
        """
        # 使用nn.Parameter而不是直接赋值给data，以保留梯度信息
        self.proto_layer.prototypes = nn.Parameter(prototypes)

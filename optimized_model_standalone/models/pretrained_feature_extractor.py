#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
预训练特征提取器模块
使用正常角膜和圆锥角膜预训练的ResNet34作为特征提取器
"""

import torch
import torch.nn as nn
import torchvision.models as models
import os
import logging

logger = logging.getLogger(__name__)

class PretrainedKeratoconusFeatureExtractor(nn.Module):
    """
    使用正常角膜和圆锥角膜预训练的ResNet34作为特征提取器
    """
    def __init__(self, feature_dim=512, pretrained=True, pretrained_path=None, dropout=0.5):
        """
        初始化预训练特征提取器
        
        参数:
            feature_dim (int): 特征维度，默认为512
            pretrained (bool): 是否使用预训练权重，默认为True
            pretrained_path (str): 预训练模型路径，如果为None则使用默认路径
            dropout (float): Dropout比率，防止过拟合，默认为0.5
        """
        super(PretrainedKeratoconusFeatureExtractor, self).__init__()
        
        # 默认预训练模型路径
        if pretrained_path is None:
            pretrained_path = "../pretrained_models/normal_kc_pretrained_resnet34.pth"
        
        # 创建ResNet34模型
        self.resnet = models.resnet34(pretrained=False)
        
        # 修改最后的全连接层
        self.resnet.fc = nn.Identity()  # 移除原始的全连接层
        
        # 添加自定义的特征提取层
        self.feature_extractor = nn.Sequential(
            nn.Linear(512, feature_dim),  # ResNet34的输出是512维
            nn.BatchNorm1d(feature_dim),
            nn.ReLU(),
            nn.Dropout(dropout)
        )
        
        # 加载预训练权重
        if pretrained:
            self._load_pretrained_weights(pretrained_path)
        
        logger.info(f"初始化预训练特征提取器，特征维度: {feature_dim}, Dropout: {dropout}")
        logger.info(f"预训练模型路径: {pretrained_path if pretrained else 'None'}")
    
    def _load_pretrained_weights(self, pretrained_path):
        """
        加载预训练权重
        
        参数:
            pretrained_path (str): 预训练模型路径
        """
        if not os.path.exists(pretrained_path):
            logger.warning(f"预训练模型路径不存在: {pretrained_path}，使用随机初始化权重")
            return
        
        try:
            # 加载预训练权重
            pretrained_dict = torch.load(pretrained_path, map_location='cpu')
            
            # 如果是完整模型，提取特征提取器部分
            if 'state_dict' in pretrained_dict:
                pretrained_dict = pretrained_dict['state_dict']
            
            # 过滤掉不匹配的键
            model_dict = self.resnet.state_dict()
            pretrained_dict = {k: v for k, v in pretrained_dict.items() if k in model_dict and model_dict[k].shape == v.shape}
            
            # 更新模型权重
            model_dict.update(pretrained_dict)
            self.resnet.load_state_dict(model_dict)
            
            logger.info(f"成功加载预训练权重，共加载 {len(pretrained_dict)} 个参数")
        except Exception as e:
            logger.error(f"加载预训练权重失败: {str(e)}")
    
    def forward(self, x):
        """
        前向传播
        
        参数:
            x (torch.Tensor): 输入张量，形状为 [batch_size, channels, height, width]
            
        返回:
            torch.Tensor: 特征向量，形状为 [batch_size, feature_dim]
        """
        # ResNet34特征提取
        x = self.resnet(x)  # [batch_size, 512]
        
        # 自定义特征提取层
        x = self.feature_extractor(x)  # [batch_size, feature_dim]
        
        return x


class EnhancedPretrainedKeratoconusFeatureExtractor(PretrainedKeratoconusFeatureExtractor):
    """
    增强版预训练特征提取器，添加注意力机制和残差连接
    """
    def __init__(self, feature_dim=512, pretrained=True, pretrained_path=None, dropout=0.5):
        """
        初始化增强版预训练特征提取器
        
        参数:
            feature_dim (int): 特征维度，默认为512
            pretrained (bool): 是否使用预训练权重，默认为True
            pretrained_path (str): 预训练模型路径，如果为None则使用默认路径
            dropout (float): Dropout比率，防止过拟合，默认为0.5
        """
        super(EnhancedPretrainedKeratoconusFeatureExtractor, self).__init__(
            feature_dim=feature_dim, 
            pretrained=pretrained, 
            pretrained_path=pretrained_path, 
            dropout=dropout
        )
        
        # 替换特征提取层为带有注意力机制和残差连接的版本
        self.attention = nn.Sequential(
            nn.Linear(512, 512),
            nn.Tanh(),
            nn.Linear(512, 1)
        )
        
        self.feature_extractor = nn.Sequential(
            nn.Linear(512, feature_dim),
            nn.BatchNorm1d(feature_dim),
            nn.ReLU(),
            nn.Dropout(dropout)
        )
        
        # 残差连接
        self.residual = nn.Sequential(
            nn.Linear(512, feature_dim),
            nn.BatchNorm1d(feature_dim)
        ) if 512 != feature_dim else nn.Identity()
        
        logger.info("初始化增强版预训练特征提取器，添加注意力机制和残差连接")
    
    def forward(self, x):
        """
        前向传播
        
        参数:
            x (torch.Tensor): 输入张量，形状为 [batch_size, channels, height, width]
            
        返回:
            torch.Tensor: 特征向量，形状为 [batch_size, feature_dim]
        """
        # ResNet34特征提取
        features = self.resnet(x)  # [batch_size, 512]
        
        # 注意力机制
        attention_weights = torch.softmax(self.attention(features), dim=0)
        attended_features = features * attention_weights
        
        # 特征提取
        x = self.feature_extractor(attended_features)
        
        # 残差连接
        residual = self.residual(features)
        x = x + residual
        
        return x

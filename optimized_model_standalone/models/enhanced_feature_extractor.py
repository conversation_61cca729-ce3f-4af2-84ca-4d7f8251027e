import torch
import torch.nn as nn
import torch.nn.functional as F
import torchvision.models as models

class EnhancedKeratoconusFeatureExtractor(nn.Module):
    """
    增强版圆锥角膜特征提取器：基于ResNet-34，添加了CBAM注意力机制和Dropout
    
    参数:
        pretrained: 是否使用预训练的ResNet-34
        feature_dim: 输出特征维度
        dropout_rate: Dropout比率
    """
    def __init__(self, pretrained=True, feature_dim=512, dropout_rate=0.4):
        super(EnhancedKeratoconusFeatureExtractor, self).__init__()
        
        # 加载预训练的ResNet-34
        resnet = models.resnet34(pretrained=pretrained)
        
        # 提取ResNet的各个阶段
        self.conv1 = resnet.conv1
        self.bn1 = resnet.bn1
        self.relu = resnet.relu
        self.maxpool = resnet.maxpool
        
        self.layer1 = resnet.layer1  # 输出通道: 64
        self.layer2 = resnet.layer2  # 输出通道: 128
        self.layer3 = resnet.layer3  # 输出通道: 256
        self.layer4 = resnet.layer4  # 输出通道: 512
        
        # 添加CBAM注意力机制
        self.cbam3 = CBAM(256)
        self.cbam4 = CBAM(512)
        
        # 添加病理区域定位模块
        self.pathology_localization = PathologyLocalizationModule(512)
        
        # 添加特征增强模块
        self.feature_enhancement = nn.Sequential(
            nn.Conv2d(512, 512, kernel_size=3, padding=1),
            nn.BatchNorm2d(512),
            nn.ReLU(inplace=True)
        )
        
        # 全局平均池化
        self.avgpool = nn.AdaptiveAvgPool2d((1, 1))
        
        # 添加Dropout和全连接层
        self.dropout = nn.Dropout(dropout_rate)
        self.fc = nn.Linear(512, feature_dim)
        self.bn_fc = nn.BatchNorm1d(feature_dim)
        
        # 禁用混合精度训练，避免类型不匹配错误
        self.use_amp = False
    
    def forward(self, x):
        # 使用混合精度计算 (使用新的API格式)
        with torch.amp.autocast('cuda', enabled=self.use_amp and x.is_cuda):
            # 初始卷积层
            x = self.conv1(x)
            x = self.bn1(x)
            x = self.relu(x)
            x = self.maxpool(x)
            
            # ResNet各阶段 + CBAM
            x = self.layer1(x)
            x = self.layer2(x)
            x = self.cbam3(self.layer3(x))
            x = self.cbam4(self.layer4(x))
            
            # 病理区域定位
            x = self.pathology_localization(x)
            
            # 特征增强
            x = self.feature_enhancement(x)
            
            # 全局平均池化得到特征向量
            x = self.avgpool(x)
            x = torch.flatten(x, 1)
            
            # 应用Dropout和全连接层
            x = self.dropout(x)
            x = self.fc(x)
            x = self.bn_fc(x)
        
        return x

# 以下是从feature_extractor.py复制的辅助类

class ChannelAttention(nn.Module):
    """
    通道注意力机制
    
    参数:
        in_planes: 输入特征图的通道数
        ratio: 降维比例
    """
    def __init__(self, in_planes, ratio=16):
        super(ChannelAttention, self).__init__()
        self.avg_pool = nn.AdaptiveAvgPool2d(1)
        self.max_pool = nn.AdaptiveMaxPool2d(1)
        
        # 共享MLP
        self.fc1 = nn.Conv2d(in_planes, in_planes // ratio, 1, bias=False)
        self.relu = nn.ReLU()
        self.fc2 = nn.Conv2d(in_planes // ratio, in_planes, 1, bias=False)
        
        self.sigmoid = nn.Sigmoid()
    
    def forward(self, x):
        avg_out = self.fc2(self.relu(self.fc1(self.avg_pool(x))))
        max_out = self.fc2(self.relu(self.fc1(self.max_pool(x))))
        out = avg_out + max_out
        return self.sigmoid(out)

class SpatialAttention(nn.Module):
    """
    空间注意力机制
    
    参数:
        kernel_size: 卷积核大小
    """
    def __init__(self, kernel_size=7):
        super(SpatialAttention, self).__init__()
        assert kernel_size in (3, 7), '空间注意力的卷积核大小必须为3或7'
        padding = 3 if kernel_size == 7 else 1
        
        self.conv = nn.Conv2d(2, 1, kernel_size, padding=padding, bias=False)
        self.sigmoid = nn.Sigmoid()
    
    def forward(self, x):
        # 沿着通道维度计算平均值和最大值
        avg_out = torch.mean(x, dim=1, keepdim=True)
        max_out, _ = torch.max(x, dim=1, keepdim=True)
        x = torch.cat([avg_out, max_out], dim=1)
        x = self.conv(x)
        return self.sigmoid(x)

class CBAM(nn.Module):
    """
    CBAM注意力机制：结合通道注意力和空间注意力
    
    参数:
        in_planes: 输入特征图的通道数
        ratio: 通道注意力的降维比例
        kernel_size: 空间注意力的卷积核大小
    """
    def __init__(self, in_planes, ratio=16, kernel_size=7):
        super(CBAM, self).__init__()
        self.ca = ChannelAttention(in_planes, ratio)
        self.sa = SpatialAttention(kernel_size)
    
    def forward(self, x):
        x = x * self.ca(x)  # 应用通道注意力
        x = x * self.sa(x)  # 应用空间注意力
        return x

class PathologyLocalizationModule(nn.Module):
    """
    病理区域定位模块：帮助模型关注角膜最薄点、后表面高度等区域
    
    参数:
        in_channels: 输入特征图的通道数
    """
    def __init__(self, in_channels):
        super(PathologyLocalizationModule, self).__init__()
        self.conv1 = nn.Conv2d(in_channels, in_channels // 2, kernel_size=3, padding=1)
        self.bn1 = nn.BatchNorm2d(in_channels // 2)
        self.conv2 = nn.Conv2d(in_channels // 2, 1, kernel_size=1)
        self.sigmoid = nn.Sigmoid()
    
    def forward(self, x):
        # 生成注意力图
        attn = F.relu(self.bn1(self.conv1(x)))
        attn = self.sigmoid(self.conv2(attn))
        
        # 应用注意力
        return x * attn.expand_as(x)

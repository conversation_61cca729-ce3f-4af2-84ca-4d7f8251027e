import torch
import torch.nn as nn
import torch.nn.functional as F

class FocalLoss(nn.Module):
    """
    焦点损失（Focal Loss）：用于处理类别不平衡问题

    参数:
        alpha: 类别权重，可以是标量或者张量
        gamma: 聚焦参数，用于降低易分类样本的权重
        reduction: 损失归约方式，可选值为'none'、'mean'、'sum'
    """
    def __init__(self, alpha=None, gamma=2.0, reduction='mean'):
        super(FocalLoss, self).__init__()
        self.alpha = alpha
        self.gamma = gamma
        self.reduction = reduction

    def forward(self, inputs, targets):
        """
        计算焦点损失

        参数:
            inputs: 预测logits，形状为 [batch_size, num_classes]
            targets: 目标类别，形状为 [batch_size]

        返回:
            loss: 焦点损失
        """
        # 计算交叉熵损失
        ce_loss = F.cross_entropy(inputs, targets, reduction='none')

        # 计算预测概率
        pt = torch.exp(-ce_loss)

        # 应用焦点权重
        focal_weight = (1 - pt) ** self.gamma

        # 应用类别权重
        if self.alpha is not None:
            if isinstance(self.alpha, torch.Tensor):
                # 如果alpha是张量，根据目标类别选择对应的权重
                alpha_t = self.alpha.gather(0, targets)
                focal_weight = alpha_t * focal_weight
            else:
                # 如果alpha是标量，直接乘以焦点权重
                focal_weight = self.alpha * focal_weight

        # 计算最终损失
        loss = focal_weight * ce_loss

        # 应用归约
        if self.reduction == 'mean':
            return loss.mean()
        elif self.reduction == 'sum':
            return loss.sum()
        else:  # 'none'
            return loss

class ContrastivePrototypeLoss(nn.Module):
    """
    对比原型损失：促使同类样本在特征空间中更加聚集，不同类样本更加分离

    参数:
        temperature: 温度参数，控制分布的平滑程度
        margin: 不同类别之间的间隔
    """
    def __init__(self, temperature=0.5, margin=1.0):
        super(ContrastivePrototypeLoss, self).__init__()
        self.temperature = temperature
        self.margin = margin

    def forward(self, features, prototypes, labels):
        """
        计算对比原型损失

        参数:
            features: 样本特征，形状为 [batch_size, feature_dim]
            prototypes: 类别原型，形状为 [num_prototypes, feature_dim]
            labels: 样本标签，形状为 [batch_size]

        返回:
            loss: 对比原型损失
        """
        batch_size = features.size(0)
        num_prototypes = prototypes.size(0)

        # 计算样本与所有原型的相似度
        # 使用余弦相似度
        features_norm = F.normalize(features, p=2, dim=1)
        prototypes_norm = F.normalize(prototypes, p=2, dim=1)

        # 计算余弦相似度矩阵 [batch_size, num_prototypes]
        similarity = torch.matmul(features_norm, prototypes_norm.t()) / self.temperature

        # 创建标签掩码，指示哪些原型属于样本的类别
        # 假设每个类别的原型索引是连续的，并且类别标签从0开始
        # 需要根据实际的原型分配方式进行调整
        prototype_labels = torch.zeros(num_prototypes, device=features.device)
        start_idx = 0
        for i, count in enumerate([1, 3, 2]):  # 假设类别0有1个原型，类别1有3个原型，类别2有2个原型
            prototype_labels[start_idx:start_idx+count] = i
            start_idx += count

        # 创建掩码，指示哪些原型属于样本的类别
        mask = (prototype_labels.unsqueeze(0) == labels.unsqueeze(1)).float()

        # 计算正样本对的损失（样本与同类原型的相似度应该高）
        pos_similarity = similarity * mask
        pos_loss = -torch.sum(pos_similarity) / (torch.sum(mask) + 1e-6)

        # 计算负样本对的损失（样本与不同类原型的相似度应该低）
        neg_mask = 1.0 - mask
        neg_similarity = similarity * neg_mask

        # 使用hinge loss确保负样本对的相似度低于一定阈值
        neg_loss = torch.sum(F.relu(neg_similarity - self.margin)) / (torch.sum(neg_mask) + 1e-6)

        # 总损失
        loss = pos_loss + neg_loss

        return loss

class EnhancedContrastiveLoss(nn.Module):
    """
    增强对比损失：结合三元组损失和对比损失

    参数:
        margin: 三元组损失的间隔
        temperature: 温度参数
    """
    def __init__(self, margin=1.0, temperature=0.5):
        super(EnhancedContrastiveLoss, self).__init__()
        self.margin = margin
        self.temperature = temperature

    def forward(self, features, labels):
        """
        计算增强对比损失

        参数:
            features: 样本特征，形状为 [batch_size, feature_dim]
            labels: 样本标签，形状为 [batch_size]

        返回:
            loss: 增强对比损失
        """
        batch_size = features.size(0)

        # 归一化特征
        features = F.normalize(features, p=2, dim=1)

        # 计算特征之间的余弦相似度矩阵
        similarity_matrix = torch.matmul(features, features.t()) / self.temperature

        # 创建标签掩码，指示哪些样本对属于同一类别
        labels_matrix = labels.unsqueeze(0) == labels.unsqueeze(1)

        # 对角线上的元素（自身与自身的相似度）设为False
        mask = torch.ones_like(labels_matrix, dtype=torch.bool)
        mask.fill_diagonal_(False)

        # 正样本对掩码（同类别）
        pos_mask = labels_matrix & mask

        # 负样本对掩码（不同类别）
        neg_mask = ~labels_matrix & mask

        # 对比损失部分
        # 正样本对的相似度应该高
        pos_similarity = similarity_matrix[pos_mask]

        # 负样本对的相似度应该低
        neg_similarity = similarity_matrix[neg_mask]

        # 计算对比损失
        if pos_similarity.numel() > 0:
            pos_loss = -torch.mean(pos_similarity)
        else:
            pos_loss = 0.0

        if neg_similarity.numel() > 0:
            neg_loss = torch.mean(F.relu(neg_similarity + self.margin))
        else:
            neg_loss = 0.0

        contrastive_loss = pos_loss + neg_loss

        # 三元组损失部分
        triplet_loss = 0.0
        n_triplets = 0

        # 对每个样本
        for i in range(batch_size):
            # 获取正样本（同类别）
            pos_indices = torch.where(labels == labels[i])[0]
            pos_indices = pos_indices[pos_indices != i]  # 排除自身

            # 获取负样本（不同类别）
            neg_indices = torch.where(labels != labels[i])[0]

            if len(pos_indices) > 0 and len(neg_indices) > 0:
                # 对每个正样本
                for pos_idx in pos_indices:
                    # 计算与所有负样本的三元组损失
                    anchor_pos_sim = similarity_matrix[i, pos_idx]
                    anchor_neg_sim = similarity_matrix[i, neg_indices]

                    # 三元组损失：确保锚点与正样本的相似度比与负样本的相似度高出一定间隔
                    triplet_losses = F.relu(anchor_neg_sim - anchor_pos_sim + self.margin)
                    triplet_loss += torch.sum(triplet_losses)
                    n_triplets += len(neg_indices)

        if n_triplets > 0:
            triplet_loss /= n_triplets

        # 总损失
        total_loss = contrastive_loss + triplet_loss

        return total_loss

class SupConLoss(nn.Module):
    """
    监督对比学习损失

    参数:
        temperature: 温度参数，控制分布的平滑程度
        base_temperature: 基础温度参数
        contrast_mode: 对比模式，'all'或'one'
        hard_mining: 是否使用硬负例挖掘
        hard_mining_ratio: 硬负例挖掘比例
    """
    def __init__(self, temperature=0.07, base_temperature=0.07,
                 contrast_mode='all', hard_mining=True, hard_mining_ratio=0.5):
        super(SupConLoss, self).__init__()
        self.temperature = temperature
        self.base_temperature = base_temperature
        self.contrast_mode = contrast_mode
        self.hard_mining = hard_mining
        self.hard_mining_ratio = hard_mining_ratio

    def forward(self, features, labels=None, mask=None):
        """
        计算监督对比学习损失

        参数:
            features: 样本特征，形状为 [batch_size, feature_dim] 或 [batch_size, n_views, feature_dim]
            labels: 样本标签，形状为 [batch_size]
            mask: 自定义掩码，形状为 [batch_size, batch_size]

        返回:
            loss: 监督对比学习损失
        """
        device = features.device

        # 处理特征维度
        if features.dim() < 3:
            # 如果特征是[batch_size, feature_dim]，扩展为[batch_size, 1, feature_dim]
            features = features.unsqueeze(1)

        # 特征维度应为[batch_size, n_views, feature_dim]
        batch_size = features.shape[0]

        # 如果没有提供标签，则假设每个样本都是一个独立的类
        if labels is None:
            labels = torch.arange(batch_size, device=device)

        # 如果没有提供掩码，则根据标签创建掩码
        if mask is None:
            mask = torch.eq(labels.unsqueeze(1), labels.unsqueeze(0)).float().to(device)

        # 对角线上的元素（自身与自身的相似度）设为0
        mask = mask.clone()
        mask.fill_diagonal_(0)

        # 将特征展平为[batch_size * n_views, feature_dim]
        n_views = features.shape[1]
        features = features.reshape(batch_size * n_views, -1)

        # 归一化特征
        features = F.normalize(features, p=2, dim=1)

        # 计算特征之间的余弦相似度矩阵
        similarity_matrix = torch.matmul(features, features.t())

        # 调整相似度矩阵的形状以匹配掩码
        if n_views > 1:
            # 创建新的掩码以匹配展平后的特征
            new_mask = mask.repeat(n_views, n_views)
            # 对于同一样本的不同视图，将掩码设为0（不计算它们之间的损失）
            for i in range(batch_size):
                for v1 in range(n_views):
                    for v2 in range(n_views):
                        idx1 = i * n_views + v1
                        idx2 = i * n_views + v2
                        new_mask[idx1, idx2] = 0
            mask = new_mask

        # 应用温度参数
        similarity_matrix = similarity_matrix / self.temperature

        # 对数掩码，用于计算log_prob
        logits_mask = torch.ones_like(mask).to(device)
        logits_mask.fill_diagonal_(0)

        # 计算exp_logits
        exp_logits = torch.exp(similarity_matrix) * logits_mask

        # 硬负例挖掘
        if self.hard_mining and labels is not None:
            with torch.no_grad():
                # 创建负样本掩码
                neg_mask = torch.ones_like(mask).to(device) - mask
                # 计算负样本相似度
                neg_similarity = similarity_matrix * neg_mask
                # 对每个样本，选择最难的负样本
                k = max(int(batch_size * n_views * self.hard_mining_ratio), 1)
                _, hard_indices = torch.topk(neg_similarity, k=k, dim=1)
                hard_neg_mask = torch.zeros_like(neg_mask).to(device)
                for i in range(batch_size * n_views):
                    hard_neg_mask[i, hard_indices[i]] = 1
                # 更新exp_logits，只保留硬负例
                exp_logits = exp_logits * (mask + hard_neg_mask)

        # 计算log_prob
        log_prob = similarity_matrix - torch.log(exp_logits.sum(1, keepdim=True) + 1e-8)

        # 计算正样本对的平均log-likelihood
        mean_log_prob_pos = (mask * log_prob).sum(1) / (mask.sum(1) + 1e-8)

        # 计算最终损失
        loss = - (self.temperature / self.base_temperature) * mean_log_prob_pos
        loss = loss.mean()

        return loss

class MAMLContrastiveLoss(nn.Module):
    """
    MAML对比学习损失：专为元学习设计的对比损失 - 简化版

    参数:
        temperature: 温度参数
    """
    def __init__(self, temperature=0.1):
        super(MAMLContrastiveLoss, self).__init__()
        self.temperature = temperature

    def forward(self, support_features, support_labels, query_features, query_labels):
        """
        计算MAML对比学习损失 - 简化版，直接使用InfoNCE损失

        参数:
            support_features: 支持集特征，形状为 [n_support, feature_dim]
            support_labels: 支持集标签，形状为 [n_support]
            query_features: 查询集特征，形状为 [n_query, feature_dim]
            query_labels: 查询集标签，形状为 [n_query]

        返回:
            loss: 简化的对比学习损失
        """
        device = support_features.device

        # 归一化特征
        support_features = F.normalize(support_features, dim=1)
        query_features = F.normalize(query_features, dim=1)

        # 计算查询集与支持集之间的相似度矩阵
        similarity = torch.matmul(query_features, support_features.t()) / self.temperature

        # 创建标签匹配矩阵 (1表示同类，0表示不同类)
        label_match = (query_labels.unsqueeze(1) == support_labels.unsqueeze(0)).float()

        # 计算每个查询样本的对比损失
        # 对于每个查询样本，正样本是同类的支持样本，负样本是不同类的支持样本

        # 对数分母：所有支持样本的exp(similarity)之和
        log_denominator = torch.logsumexp(similarity, dim=1, keepdim=True)

        # 计算正样本的对数概率
        # 使用掩码选择同类样本的相似度，并应用logsumexp
        # 添加一个小的epsilon值，避免log(0)
        epsilon = 1e-8
        masked_similarity = similarity * label_match
        # 将非正样本的相似度设为一个非常小的值，这样logsumexp基本上只考虑正样本
        masked_similarity = masked_similarity.masked_fill(label_match == 0, -1e10)
        log_numerator = torch.logsumexp(masked_similarity, dim=1, keepdim=True)

        # 计算每个样本的损失
        per_sample_loss = log_denominator - log_numerator

        # 处理没有正样本的情况
        valid_samples = (label_match.sum(1) > 0).float()
        if valid_samples.sum() > 0:
            loss = (per_sample_loss.squeeze() * valid_samples).sum() / (valid_samples.sum() + epsilon)
        else:
            # 如果没有有效样本，返回一个小的常数损失
            loss = torch.tensor(0.1, device=device, requires_grad=True)

        return loss

class CombinedLoss(nn.Module):
    """
    组合损失：结合焦点损失、对比原型损失和增强对比损失

    参数:
        focal_weight: 焦点损失的权重
        contrastive_proto_weight: 对比原型损失的权重
        enhanced_contrastive_weight: 增强对比损失的权重
        class_weights: 类别权重，用于焦点损失
    """
    def __init__(self, focal_weight=1.0, contrastive_proto_weight=0.5, enhanced_contrastive_weight=0.5, class_weights=None):
        super(CombinedLoss, self).__init__()
        self.focal_loss = FocalLoss(alpha=class_weights, gamma=2.0)
        self.contrastive_proto_loss = ContrastivePrototypeLoss()
        self.enhanced_contrastive_loss = EnhancedContrastiveLoss()

        self.focal_weight = focal_weight
        self.contrastive_proto_weight = contrastive_proto_weight
        self.enhanced_contrastive_weight = enhanced_contrastive_weight

    def forward(self, logits, features, prototypes, labels):
        """
        计算组合损失

        参数:
            logits: 预测logits，形状为 [batch_size, num_classes]
            features: 样本特征，形状为 [batch_size, feature_dim]
            prototypes: 类别原型，形状为 [num_prototypes, feature_dim]
            labels: 样本标签，形状为 [batch_size]

        返回:
            loss: 组合损失
        """
        # 计算各个损失
        focal = self.focal_loss(logits, labels)
        contrastive_proto = self.contrastive_proto_loss(features, prototypes, labels)
        enhanced_contrastive = self.enhanced_contrastive_loss(features, labels)

        # 组合损失
        loss = (self.focal_weight * focal +
                self.contrastive_proto_weight * contrastive_proto +
                self.enhanced_contrastive_weight * enhanced_contrastive)

        return loss, {
            'focal': focal.item(),
            'contrastive_proto': contrastive_proto.item(),
            'enhanced_contrastive': enhanced_contrastive.item()
        }

    def update_weights(self, class_counts):
        """
        根据类别样本数量更新权重

        参数:
            class_counts: 每个类别的样本数量列表
        """
        # 计算类别权重：样本数量越少，权重越高
        total_samples = sum(class_counts)
        class_weights = torch.tensor([total_samples / (len(class_counts) * count) for count in class_counts])

        # 归一化权重
        class_weights = class_weights / class_weights.sum()

        # 更新焦点损失的类别权重
        self.focal_loss.alpha = class_weights

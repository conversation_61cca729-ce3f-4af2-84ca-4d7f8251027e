import torch
import torch.nn as nn
import torch.nn.functional as F
import torchvision.models as models

try:
    from models.feature_extractor import KeratoconusFeatureExtractor, CBAM, PathologyLocalizationModule
except ImportError:
    # 尝试从上级目录导入
    import sys
    import os
    sys.path.insert(0, os.path.join(os.path.dirname(os.path.abspath(__file__)), '..'))
    from models.feature_extractor import KeratoconusFeatureExtractor, CBAM, PathologyLocalizationModule

class NormalizedFeatureExtractor(nn.Module):
    """
    归一化特征提取器：在特征提取后添加L2归一化
    
    参数:
        pretrained: 是否使用预训练的ResNet-34
        feature_dim: 输出特征维度
        normalize_scale: 归一化后的缩放因子
        normalize_output: 是否对输出特征进行归一化
        use_projection: 是否使用特征投影层
        use_batch_norm: 是否使用批归一化
    """
    def __init__(self, pretrained=True, feature_dim=512, normalize_scale=10.0, 
                 normalize_output=True, use_projection=True, use_batch_norm=True):
        super(NormalizedFeatureExtractor, self).__init__()
        self.feature_extractor = KeratoconusFeatureExtractor(pretrained=pretrained, feature_dim=feature_dim)
        self.normalize_scale = normalize_scale
        self.normalize_output = normalize_output
        self.use_projection = use_projection
        self.use_batch_norm = use_batch_norm
        
        # 添加批归一化层，提高训练稳定性
        if self.use_batch_norm:
            self.bn = nn.BatchNorm1d(feature_dim)
        
        # 添加特征投影层，增强特征表示能力
        if self.use_projection:
            self.projection = nn.Sequential(
                nn.Linear(feature_dim, feature_dim),
                nn.ReLU(inplace=True),
                nn.Linear(feature_dim, feature_dim)
            )
        
    def forward(self, x):
        """
        前向传播
        
        参数:
            x: 输入图像，形状为 [batch_size, channels, height, width]
            
        返回:
            features: 归一化的特征向量，形状为 [batch_size, feature_dim]
        """
        # 提取特征
        features = self.feature_extractor(x)
        
        # 应用批归一化
        if self.use_batch_norm:
            features = self.bn(features)
        
        # 特征投影
        if self.use_projection:
            features = self.projection(features)
        
        # 如果启用归一化，对特征进行L2归一化
        if self.normalize_output:
            features = F.normalize(features, p=2, dim=1) * self.normalize_scale
            
        return features
    
    def extract_without_normalization(self, x):
        """
        提取特征但不进行归一化
        
        参数:
            x: 输入图像，形状为 [batch_size, channels, height, width]
            
        返回:
            features: 未归一化的特征向量，形状为 [batch_size, feature_dim]
        """
        features = self.feature_extractor(x)
        if self.use_batch_norm:
            features = self.bn(features)
        if self.use_projection:
            features = self.projection(features)
        return features

class EnhancedNormalizedFeatureExtractor(nn.Module):
    """
    增强版归一化特征提取器：结合了增强特征提取器和特征归一化
    
    参数:
        pretrained: 是否使用预训练的ResNet-34
        feature_dim: 输出特征维度
        normalize_scale: 归一化后的缩放因子
        normalize_output: 是否对输出特征进行归一化
        dropout_rate: Dropout比率
        use_projection: 是否使用特征投影层
        use_batch_norm: 是否使用批归一化
    """
    def __init__(self, pretrained=True, feature_dim=512, normalize_scale=10.0, 
                 normalize_output=True, dropout_rate=0.4, use_projection=True, 
                 use_batch_norm=True):
        super(EnhancedNormalizedFeatureExtractor, self).__init__()
        
        # 加载预训练的ResNet-34
        resnet = models.resnet34(pretrained=pretrained)
        
        # 提取ResNet的各个阶段
        self.conv1 = resnet.conv1
        self.bn1 = resnet.bn1
        self.relu = resnet.relu
        self.maxpool = resnet.maxpool
        
        self.layer1 = resnet.layer1  # 输出通道: 64
        self.layer2 = resnet.layer2  # 输出通道: 128
        self.layer3 = resnet.layer3  # 输出通道: 256
        self.layer4 = resnet.layer4  # 输出通道: 512
        
        # 添加CBAM注意力机制
        self.cbam3 = CBAM(256)
        self.cbam4 = CBAM(512)
        
        # 添加病理区域定位模块
        self.pathology_localization = PathologyLocalizationModule(512)
        
        # 添加特征增强模块
        self.feature_enhancement = nn.Sequential(
            nn.Conv2d(512, 512, kernel_size=3, padding=1),
            nn.BatchNorm2d(512),
            nn.ReLU(inplace=True)
        )
        
        # 全局平均池化
        self.avgpool = nn.AdaptiveAvgPool2d((1, 1))
        
        # 添加Dropout
        self.dropout = nn.Dropout(dropout_rate)
        
        # 添加全连接层
        self.fc = nn.Linear(512, feature_dim)
        
        # 批归一化
        if use_batch_norm:
            self.bn_fc = nn.BatchNorm1d(feature_dim)
        
        # 特征投影层
        if use_projection:
            self.projection = nn.Sequential(
                nn.Linear(feature_dim, feature_dim),
                nn.ReLU(inplace=True),
                nn.Linear(feature_dim, feature_dim)
            )
        
        # 归一化参数
        self.normalize_scale = normalize_scale
        self.normalize_output = normalize_output
        self.use_projection = use_projection
        self.use_batch_norm = use_batch_norm
        
        # 禁用混合精度训练，避免类型不匹配错误
        self.use_amp = False
    
    def forward(self, x):
        """
        前向传播
        
        参数:
            x: 输入图像，形状为 [batch_size, channels, height, width]
            
        返回:
            features: 归一化的特征向量，形状为 [batch_size, feature_dim]
        """
        # 使用混合精度计算 (使用新的API格式)
        with torch.amp.autocast('cuda', enabled=self.use_amp and x.is_cuda):
            # 初始卷积层
            x = self.conv1(x)
            x = self.bn1(x)
            x = self.relu(x)
            x = self.maxpool(x)
            
            # ResNet各阶段 + CBAM
            x = self.layer1(x)
            x = self.layer2(x)
            x = self.cbam3(self.layer3(x))
            x = self.cbam4(self.layer4(x))
            
            # 病理区域定位
            x = self.pathology_localization(x)
            
            # 特征增强
            x = self.feature_enhancement(x)
            
            # 全局平均池化得到特征向量
            x = self.avgpool(x)
            x = torch.flatten(x, 1)
            
            # 应用Dropout和全连接层
            x = self.dropout(x)
            x = self.fc(x)
            
            # 应用批归一化
            if self.use_batch_norm:
                x = self.bn_fc(x)
            
            # 特征投影
            if self.use_projection:
                x = self.projection(x)
            
            # 如果启用归一化，对特征进行L2归一化
            if self.normalize_output:
                x = F.normalize(x, p=2, dim=1) * self.normalize_scale
        
        return x
    
    def extract_without_normalization(self, x):
        """
        提取特征但不进行归一化
        
        参数:
            x: 输入图像，形状为 [batch_size, channels, height, width]
            
        返回:
            features: 未归一化的特征向量，形状为 [batch_size, feature_dim]
        """
        with torch.amp.autocast('cuda', enabled=self.use_amp and x.is_cuda):
            # 初始卷积层
            x = self.conv1(x)
            x = self.bn1(x)
            x = self.relu(x)
            x = self.maxpool(x)
            
            # ResNet各阶段 + CBAM
            x = self.layer1(x)
            x = self.layer2(x)
            x = self.cbam3(self.layer3(x))
            x = self.cbam4(self.layer4(x))
            
            # 病理区域定位
            x = self.pathology_localization(x)
            
            # 特征增强
            x = self.feature_enhancement(x)
            
            # 全局平均池化得到特征向量
            x = self.avgpool(x)
            x = torch.flatten(x, 1)
            
            # 应用Dropout和全连接层
            x = self.dropout(x)
            x = self.fc(x)
            
            # 应用批归一化
            if self.use_batch_norm:
                x = self.bn_fc(x)
            
            # 特征投影
            if self.use_projection:
                x = self.projection(x)
        
        return x

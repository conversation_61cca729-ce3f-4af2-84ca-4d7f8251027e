import torch
import torch.nn as nn
import torch.nn.functional as F
import torchvision.models as models

class ChannelAttention(nn.Module):
    """
    通道注意力机制

    参数:
        in_planes: 输入特征图的通道数
        ratio: 降维比例
    """
    def __init__(self, in_planes, ratio=16):
        super(ChannelAttention, self).__init__()
        self.avg_pool = nn.AdaptiveAvgPool2d(1)
        self.max_pool = nn.AdaptiveMaxPool2d(1)

        # 共享MLP
        self.fc1 = nn.Conv2d(in_planes, in_planes // ratio, 1, bias=False)
        self.relu = nn.ReLU()
        self.fc2 = nn.Conv2d(in_planes // ratio, in_planes, 1, bias=False)

        self.sigmoid = nn.Sigmoid()

    def forward(self, x):
        avg_out = self.fc2(self.relu(self.fc1(self.avg_pool(x))))
        max_out = self.fc2(self.relu(self.fc1(self.max_pool(x))))
        out = avg_out + max_out
        return self.sigmoid(out)

class SpatialAttention(nn.Module):
    """
    空间注意力机制

    参数:
        kernel_size: 卷积核大小
    """
    def __init__(self, kernel_size=7):
        super(SpatialAttention, self).__init__()
        assert kernel_size in (3, 7), '空间注意力的卷积核大小必须为3或7'
        padding = 3 if kernel_size == 7 else 1

        self.conv = nn.Conv2d(2, 1, kernel_size, padding=padding, bias=False)
        self.sigmoid = nn.Sigmoid()

    def forward(self, x):
        # 沿着通道维度计算平均值和最大值
        avg_out = torch.mean(x, dim=1, keepdim=True)
        max_out, _ = torch.max(x, dim=1, keepdim=True)
        x = torch.cat([avg_out, max_out], dim=1)
        x = self.conv(x)
        return self.sigmoid(x)

class CBAM(nn.Module):
    """
    CBAM注意力机制：结合通道注意力和空间注意力

    参数:
        in_planes: 输入特征图的通道数
        ratio: 通道注意力的降维比例
        kernel_size: 空间注意力的卷积核大小
    """
    def __init__(self, in_planes, ratio=16, kernel_size=7):
        super(CBAM, self).__init__()
        self.ca = ChannelAttention(in_planes, ratio)
        self.sa = SpatialAttention(kernel_size)

    def forward(self, x):
        x = x * self.ca(x)  # 应用通道注意力
        x = x * self.sa(x)  # 应用空间注意力
        return x

class PathologyLocalizationModule(nn.Module):
    """
    病理区域定位模块：帮助模型关注角膜最薄点、后表面高度等区域

    参数:
        in_channels: 输入特征图的通道数
    """
    def __init__(self, in_channels):
        super(PathologyLocalizationModule, self).__init__()
        self.conv1 = nn.Conv2d(in_channels, in_channels // 2, kernel_size=3, padding=1)
        self.bn1 = nn.BatchNorm2d(in_channels // 2)
        self.conv2 = nn.Conv2d(in_channels // 2, 1, kernel_size=1)
        self.sigmoid = nn.Sigmoid()

    def forward(self, x):
        # 生成注意力图
        attn = F.relu(self.bn1(self.conv1(x)))
        attn = self.sigmoid(self.conv2(attn))

        # 应用注意力
        return x * attn.expand_as(x)

class FeatureEnhancementModule(nn.Module):
    """
    特征增强模块：增强特征表示能力

    参数:
        in_channels: 输入特征图的通道数
        out_channels: 输出特征图的通道数
    """
    def __init__(self, in_channels, out_channels):
        super(FeatureEnhancementModule, self).__init__()
        self.conv1 = nn.Conv2d(in_channels, out_channels, kernel_size=3, padding=1)
        self.bn1 = nn.BatchNorm2d(out_channels)
        self.conv2 = nn.Conv2d(out_channels, out_channels, kernel_size=3, padding=1)
        self.bn2 = nn.BatchNorm2d(out_channels)
        self.relu = nn.ReLU(inplace=True)

        # 添加残差连接
        self.shortcut = nn.Sequential()
        if in_channels != out_channels:
            self.shortcut = nn.Sequential(
                nn.Conv2d(in_channels, out_channels, kernel_size=1),
                nn.BatchNorm2d(out_channels)
            )

    def forward(self, x):
        residual = x

        out = self.relu(self.bn1(self.conv1(x)))
        out = self.bn2(self.conv2(out))

        out += self.shortcut(residual)
        out = self.relu(out)

        return out

class MultiScaleFeatureFusion(nn.Module):
    """
    多尺度特征融合模块：融合不同尺度的特征

    参数:
        scales: 要融合的特征尺度列表
        in_channels: 每个尺度的输入通道数列表
        out_channels: 融合后的输出通道数
    """
    def __init__(self, scales, in_channels, out_channels):
        super(MultiScaleFeatureFusion, self).__init__()

        self.scales = scales
        self.n_scales = len(scales)

        # 为每个尺度创建卷积层
        self.convs = nn.ModuleList([
            nn.Conv2d(in_channels[i], out_channels, kernel_size=1)
            for i in range(self.n_scales)
        ])

        # 最终融合卷积
        self.fusion_conv = nn.Conv2d(out_channels, out_channels, kernel_size=3, padding=1)
        self.bn = nn.BatchNorm2d(out_channels)
        self.relu = nn.ReLU(inplace=True)

    def forward(self, features):
        # 确保输入特征列表长度与尺度数量一致
        assert len(features) == self.n_scales, f"Expected {self.n_scales} features, got {len(features)}"

        # 处理每个尺度的特征
        processed_features = []
        for i, feature in enumerate(features):
            # 应用1x1卷积调整通道数
            x = self.convs[i](feature)

            # 调整特征图大小到最大尺度
            if i > 0:  # 如果不是最大尺度
                target_size = features[0].size()[2:]  # 获取最大尺度的高和宽
                x = F.interpolate(x, size=target_size, mode='bilinear', align_corners=False)

            processed_features.append(x)

        # 融合所有尺度的特征（元素级相加）
        fused = sum(processed_features)

        # 应用最终融合卷积
        out = self.relu(self.bn(self.fusion_conv(fused)))

        return out

class KeratoconusFeatureExtractor(nn.Module):
    """
    圆锥角膜特征提取器：基于ResNet-34，添加了CBAM注意力机制
    优化版本：减少计算量，提高训练速度

    参数:
        pretrained: 是否使用预训练的ResNet-34
        feature_dim: 输出特征维度
    """
    def __init__(self, pretrained=True, feature_dim=512):
        super(KeratoconusFeatureExtractor, self).__init__()

        # 加载预训练的ResNet-34
        resnet = models.resnet34(pretrained=pretrained)

        # 提取ResNet的各个阶段
        self.conv1 = resnet.conv1
        self.bn1 = resnet.bn1
        self.relu = resnet.relu
        self.maxpool = resnet.maxpool

        self.layer1 = resnet.layer1  # 输出通道: 64
        self.layer2 = resnet.layer2  # 输出通道: 128
        self.layer3 = resnet.layer3  # 输出通道: 256
        self.layer4 = resnet.layer4  # 输出通道: 512

        # 只在最后两个阶段添加CBAM注意力机制，减少计算量
        self.cbam3 = CBAM(256)
        self.cbam4 = CBAM(512)

        # 添加病理区域定位模块
        self.pathology_localization = PathologyLocalizationModule(512)

        # 添加简化版特征增强模块
        self.feature_enhancement = nn.Sequential(
            nn.Conv2d(512, 512, kernel_size=3, padding=1),
            nn.BatchNorm2d(512),
            nn.ReLU(inplace=True)
        )

        # 最终特征映射层
        self.final_conv = nn.Conv2d(512, feature_dim, kernel_size=1)
        self.avgpool = nn.AdaptiveAvgPool2d((1, 1))

        # 禁用混合精度训练，避免类型不匹配错误
        self.use_amp = False

    def forward(self, x):
        # 使用混合精度计算 (使用新的API格式)
        with torch.amp.autocast('cuda', enabled=self.use_amp and x.is_cuda):
            # 初始卷积层
            x = self.conv1(x)
            x = self.bn1(x)
            x = self.relu(x)
            x = self.maxpool(x)

            # ResNet各阶段 + CBAM (只在后两个阶段使用)
            x = self.layer1(x)
            x = self.layer2(x)
            x = self.cbam3(self.layer3(x))
            x = self.cbam4(self.layer4(x))

            # 病理区域定位
            x = self.pathology_localization(x)

            # 特征增强 (简化版)
            x = self.feature_enhancement(x)

            # 最终特征
            x = self.final_conv(x)

            # 全局平均池化得到特征向量
            x = self.avgpool(x)
            x = torch.flatten(x, 1)

        return x

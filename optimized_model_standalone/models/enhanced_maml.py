import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from copy import deepcopy

class EnhancedMAMLProtoNet(nn.Module):
    """
    增强版MAML-ProtoNet模型：支持return_features参数
    
    参数:
        protonet: 原型网络模型
        inner_lr: 内循环学习率
        inner_steps: 内循环更新步数
        task_weight_method: 任务权重计算方法
    """
    def __init__(self, protonet, inner_lr=0.1, inner_steps=3, task_weight_method='difficulty'):
        super(EnhancedMAMLProtoNet, self).__init__()
        self.protonet = protonet
        self.inner_lr = inner_lr
        self.inner_steps = inner_steps
        self.task_weight_method = task_weight_method
    
    def forward(self, support_images=None, support_labels=None, query_images=None, return_features=False):
        """
        前向传播 - 增强版，支持return_features参数
        
        参数:
            support_images: 支持集图像，如果为None则直接使用query_images
            support_labels: 支持集标签
            query_images: 查询集图像
            return_features: 是否返回特征表示
        
        返回:
            如果return_features为False，返回查询集的logits
            如果return_features为True，返回查询集的logits、查询集的特征和支持集的特征
        """
        if support_images is None or support_labels is None:
            # 如果没有提供支持集，直接使用ProtoNet的前向传播
            if return_features:
                # 获取特征表示
                features = self.protonet.get_features(query_images)
                # 获取logits
                logits = self.protonet(query_images)
                return logits, features, None
            else:
                # 只返回logits
                return self.protonet(query_images)
        else:
            # 如果提供了支持集，先适应模型
            adapted_model = self.adapt(support_images, support_labels)
            
            if return_features:
                # 获取查询集的特征表示
                query_features = adapted_model.get_features(query_images)
                # 获取支持集的特征表示
                support_features = adapted_model.get_features(support_images)
                # 获取查询集的logits
                query_logits = adapted_model(query_images)
                return query_logits, query_features, support_features
            else:
                # 只返回查询集的logits
                return adapted_model(query_images)
    
    def adapt(self, support_images, support_labels):
        """
        适应新任务（使用ProtoNet的原型计算）
        
        参数:
            support_images: 支持集图像
            support_labels: 支持集标签
        
        返回:
            适应后的模型副本
        """
        # 使用浅拷贝而不是深拷贝，减少内存使用和计算时间
        adapted_model = self.protonet
        
        # 使用混合精度计算特征
        with torch.amp.autocast('cuda', enabled=support_images.is_cuda):
            # 提取支持集特征
            support_features = adapted_model.get_features(support_images)
            
            # 计算新的原型
            n_classes = 3  # 假设有3个类别
            n_dim = support_features.size(1)  # 特征维度
            
            # 初始化原型
            prototypes = torch.zeros(sum(adapted_model.proto_layer.proto_counts), n_dim, device=support_features.device)
            
            # 计算每个类别的原型 - 使用更高效的向量化操作
            start_idx = 0
            for c in range(n_classes):
                # 获取该类别的样本
                class_mask = (support_labels == c)
                if torch.any(class_mask):
                    class_features = support_features[class_mask]
                    proto_count = adapted_model.proto_layer.proto_counts[c]
                    
                    # 对早期圆锥角膜(类别1)使用特殊处理
                    if c == 1 and proto_count > 1 and class_features.size(0) >= 2:
                        # 使用K-means聚类生成多个原型
                        from sklearn.cluster import KMeans
                        import numpy as np
                        
                        # 将特征转移到CPU进行聚类
                        cpu_features = class_features.detach().cpu().numpy()
                        
                        # 确定聚类数量，不超过样本数量
                        n_clusters = min(proto_count, class_features.size(0))
                        
                        # 应用K-means聚类
                        kmeans = KMeans(n_clusters=n_clusters, random_state=0, n_init=10)
                        cluster_labels = kmeans.fit_predict(cpu_features)
                        
                        # 为每个聚类计算原型
                        for i in range(n_clusters):
                            cluster_mask = (cluster_labels == i)
                            if np.any(cluster_mask):
                                # 计算该聚类的原型
                                cluster_proto = torch.mean(
                                    class_features[torch.tensor(cluster_mask, device=class_features.device)],
                                    dim=0
                                )
                                prototypes[start_idx + i] = cluster_proto
                        
                        # 如果聚类数量少于原型数量，复制最后一个原型
                        if n_clusters < proto_count:
                            for i in range(n_clusters, proto_count):
                                prototypes[start_idx + i] = prototypes[start_idx + n_clusters - 1]
                    
                    elif proto_count == 1 or class_features.size(0) <= proto_count:
                        # 如果只有一个原型或样本数不足，使用均值
                        prototypes[start_idx:start_idx+proto_count] = torch.mean(class_features, dim=0).unsqueeze(0).repeat(proto_count, 1)
                    else:
                        # 如果有多个原型，使用均匀分组
                        # 将样本随机打乱
                        perm = torch.randperm(class_features.size(0), device=class_features.device)
                        shuffled_features = class_features[perm]
                        
                        # 计算每组的大小
                        group_size = class_features.size(0) // proto_count
                        
                        # 为每个原型计算均值
                        for i in range(proto_count):
                            start_sample = i * group_size
                            end_sample = (i + 1) * group_size if i < proto_count - 1 else class_features.size(0)
                            prototypes[start_idx + i] = torch.mean(shuffled_features[start_sample:end_sample], dim=0)
                
                start_idx += proto_count
        
        # 创建一个新的模型，只复制必要的部分
        result_model = type(adapted_model)(adapted_model.feature_extractor, n_classes=3,
                                          proto_counts=adapted_model.proto_layer.proto_counts,
                                          feature_dim=n_dim)
        
        # 设置新的原型
        result_model.proto_layer.prototypes = nn.Parameter(prototypes)
        
        # 将模型移动到与输入相同的设备
        result_model = result_model.to(support_images.device)
        
        return result_model
    
    def get_features(self, x):
        """
        获取特征表示
        
        参数:
            x: 输入数据
        
        返回:
            特征表示
        """
        return self.protonet.get_features(x)

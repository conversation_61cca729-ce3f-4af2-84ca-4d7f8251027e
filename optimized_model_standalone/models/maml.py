import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from copy import deepcopy

class MAML(nn.Module):
    """
    模型无关元学习（MAML）模型

    参数:
        model: 基础模型（在本例中是ProtoNet）
        inner_lr: 内循环学习率
        inner_steps: 内循环更新步数
        task_weight_method: 任务权重计算方法，可选值为'uniform'（均匀）或'difficulty'（基于难度）
    """
    def __init__(self, model, inner_lr=0.01, inner_steps=5, task_weight_method='difficulty'):
        super(MAML, self).__init__()
        self.model = model
        self.inner_lr = inner_lr
        self.inner_steps = inner_steps
        self.task_weight_method = task_weight_method

    def forward(self, x):
        """
        前向传播（直接使用基础模型）

        参数:
            x: 输入数据

        返回:
            基础模型的输出
        """
        return self.model(x)

    def adapt(self, support_images, support_labels, first_order=False):
        """
        适应新任务（内循环优化）

        参数:
            support_images: 支持集图像
            support_labels: 支持集标签
            first_order: 是否使用一阶近似（忽略二阶导数）

        返回:
            适应后的模型副本
        """
        # 创建模型副本
        adapted_model = deepcopy(self.model)

        # 将模型参数设置为需要梯度
        for param in adapted_model.parameters():
            param.requires_grad = True

        # 内循环优化
        for _ in range(self.inner_steps):
            # 前向传播
            logits = adapted_model(support_images)

            # 计算损失
            loss = F.cross_entropy(logits, support_labels)

            # 计算梯度
            grads = torch.autograd.grad(loss, adapted_model.parameters(),
                                        create_graph=not first_order,
                                        allow_unused=True)

            # 更新参数
            for param, grad in zip(adapted_model.parameters(), grads):
                if grad is not None:
                    param.data = param.data - self.inner_lr * grad

        # 确保适应后的模型参数可以计算梯度
        for param in adapted_model.parameters():
            param.requires_grad = True

        # 确保适应后的模型的原型在正确的设备上
        device = support_images.device
        adapted_model.proto_layer.prototypes = adapted_model.proto_layer.prototypes.to(device)

        # 确保适应后的模型参数可以计算梯度
        for param in adapted_model.parameters():
            param.requires_grad = True

        return adapted_model

    def calculate_task_weights(self, task_losses):
        """
        计算任务权重

        参数:
            task_losses: 每个任务的损失列表

        返回:
            任务权重列表
        """
        if self.task_weight_method == 'uniform':
            # 均匀权重
            n_tasks = len(task_losses)
            return torch.ones(n_tasks, device=task_losses[0].device) / n_tasks

        elif self.task_weight_method == 'difficulty':
            # 基于难度的权重（难任务权重更高）
            losses = torch.stack(task_losses)
            # 使用softmax将损失转换为权重
            weights = F.softmax(losses, dim=0)
            return weights

        else:
            raise ValueError(f"未知的任务权重方法: {self.task_weight_method}")

    def meta_learn(self, task_batch, first_order=False):
        """
        元学习（外循环优化）

        参数:
            task_batch: 任务批次，每个任务包含支持集和查询集
            first_order: 是否使用一阶近似

        返回:
            总损失和每个任务的准确率
        """
        task_losses = []
        task_accuracies = []

        for task in task_batch:
            support_images, support_labels = task['support']
            query_images, query_labels = task['query']

            # 内循环适应
            adapted_model = self.adapt(support_images, support_labels, first_order)

            # 在查询集上评估
            query_logits = adapted_model(query_images)

            # 计算损失
            task_loss = F.cross_entropy(query_logits, query_labels)
            task_losses.append(task_loss)

            # 计算准确率（不需要梯度）
            with torch.no_grad():
                pred = torch.argmax(query_logits, dim=1)
                correct = (pred == query_labels).sum().item()
                accuracy = correct / len(query_labels)
                task_accuracies.append(accuracy)

        # 计算任务权重
        task_weights = self.calculate_task_weights(task_losses)

        # 计算加权总损失
        total_loss = sum(w * l for w, l in zip(task_weights, task_losses))

        return total_loss, task_accuracies

    def get_features(self, x):
        """
        获取特征表示

        参数:
            x: 输入数据

        返回:
            特征表示
        """
        return self.model.get_features(x)

class MAMLProtoNet(nn.Module):
    """
    结合MAML和ProtoNet的模型 - 增强版

    参数:
        protonet: 原型网络模型
        inner_lr: 内循环学习率 (默认提高到0.1)
        inner_steps: 内循环更新步数 (默认提高到3)
        task_weight_method: 任务权重计算方法
    """
    def __init__(self, protonet, inner_lr=0.1, inner_steps=3, task_weight_method='difficulty'):
        super(MAMLProtoNet, self).__init__()
        self.protonet = protonet
        self.inner_lr = inner_lr
        self.inner_steps = inner_steps
        self.task_weight_method = task_weight_method

    def forward(self, x):
        """
        前向传播

        参数:
            x: 输入数据

        返回:
            ProtoNet的输出
        """
        return self.protonet(x)

    def adapt(self, support_images, support_labels):
        """
        适应新任务（使用ProtoNet的原型计算）- 增强版

        参数:
            support_images: 支持集图像
            support_labels: 支持集标签

        返回:
            适应后的模型副本
        """
        # 使用浅拷贝而不是深拷贝，减少内存使用和计算时间
        adapted_model = self.protonet

        # 使用混合精度计算特征
        with torch.amp.autocast('cuda', enabled=support_images.is_cuda):
            # 提取支持集特征
            support_features = adapted_model.get_features(support_images)

            # 计算新的原型
            n_classes = 3  # 假设有3个类别
            n_dim = support_features.size(1)  # 特征维度

            # 初始化原型
            prototypes = torch.zeros(sum(adapted_model.proto_layer.proto_counts), n_dim, device=support_features.device)

            # 计算每个类别的原型 - 使用更高效的向量化操作
            start_idx = 0
            for c in range(n_classes):
                # 获取该类别的样本
                class_mask = (support_labels == c)
                if torch.any(class_mask):
                    class_features = support_features[class_mask]
                    proto_count = adapted_model.proto_layer.proto_counts[c]

                    # 对早期圆锥角膜(类别1)使用特殊处理
                    if c == 1 and proto_count > 1 and class_features.size(0) >= 2:
                        # 使用K-means聚类生成多个原型
                        from sklearn.cluster import KMeans
                        import numpy as np

                        # 将特征转移到CPU进行聚类
                        cpu_features = class_features.detach().cpu().numpy()

                        # 确定聚类数量，不超过样本数量
                        n_clusters = min(proto_count, class_features.size(0))

                        # 应用K-means聚类
                        kmeans = KMeans(n_clusters=n_clusters, random_state=0, n_init=10)
                        cluster_labels = kmeans.fit_predict(cpu_features)

                        # 为每个聚类计算原型
                        for i in range(n_clusters):
                            cluster_mask = (cluster_labels == i)
                            if np.any(cluster_mask):
                                # 计算该聚类的原型
                                cluster_proto = torch.mean(
                                    class_features[torch.tensor(cluster_mask, device=class_features.device)],
                                    dim=0
                                )
                                prototypes[start_idx + i] = cluster_proto

                        # 如果聚类数量少于原型数量，复制最后一个原型
                        if n_clusters < proto_count:
                            for i in range(n_clusters, proto_count):
                                prototypes[start_idx + i] = prototypes[start_idx + n_clusters - 1]

                    elif proto_count == 1 or class_features.size(0) <= proto_count:
                        # 如果只有一个原型或样本数不足，使用均值
                        prototypes[start_idx:start_idx+proto_count] = torch.mean(class_features, dim=0).unsqueeze(0).repeat(proto_count, 1)
                    else:
                        # 如果有多个原型，使用均匀分组
                        # 将样本随机打乱
                        perm = torch.randperm(class_features.size(0), device=class_features.device)
                        shuffled_features = class_features[perm]

                        # 计算每组的大小
                        group_size = class_features.size(0) // proto_count

                        # 为每个原型计算均值
                        for i in range(proto_count):
                            start_sample = i * group_size
                            end_sample = (i + 1) * group_size if i < proto_count - 1 else class_features.size(0)
                            prototypes[start_idx + i] = torch.mean(shuffled_features[start_sample:end_sample], dim=0)

                start_idx += proto_count

        # 创建一个新的模型，只复制必要的部分
        result_model = type(adapted_model)(adapted_model.feature_extractor, n_classes=3,
                                          proto_counts=adapted_model.proto_layer.proto_counts,
                                          feature_dim=n_dim)

        # 设置新的原型
        result_model.proto_layer.prototypes = nn.Parameter(prototypes)

        # 将模型移动到与输入相同的设备
        result_model = result_model.to(support_images.device)

        return result_model

    def meta_learn(self, task_batch, train=True, use_contrastive=True, contrastive_weight=0.5):
        """
        元学习（外循环优化）

        参数:
            task_batch: 任务批次，每个任务包含支持集和查询集
            train: 是否处于训练模式
            use_contrastive: 是否使用对比学习损失
            contrastive_weight: 对比学习损失的权重

        返回:
            总损失和每个任务的准确率
        """
        task_losses = []
        task_contrastive_losses = []
        task_accuracies = []

        # 创建对比学习损失函数（如果需要）
        if use_contrastive and train:
            from models.losses import MAMLContrastiveLoss
            contrastive_loss_fn = MAMLContrastiveLoss(temperature=0.07)

        for task in task_batch:
            support_images, support_labels = task['support']
            query_images, query_labels = task['query']

            # 内循环适应（计算原型）
            adapted_model = self.adapt(support_images, support_labels)

            # 在查询集上评估
            if not train:
                # 验证模式下使用torch.no_grad()
                with torch.no_grad():
                    query_logits = adapted_model(query_images)
                    # 获取特征表示（用于对比学习）
                    query_features = adapted_model.get_features(query_images)
                    support_features = adapted_model.get_features(support_images)
            else:
                # 训练模式下允许梯度计算
                query_logits = adapted_model(query_images)
                # 获取特征表示（用于对比学习）
                query_features = adapted_model.get_features(query_images)
                support_features = adapted_model.get_features(support_images)

            # 计算分类损失 - 使用标签平滑以提高泛化能力
            smooth_factor = 0.1 if train else 0.0  # 只在训练时使用标签平滑
            n_classes = 3

            # 创建平滑标签
            target_one_hot = F.one_hot(query_labels, n_classes).float()
            target_smooth = target_one_hot * (1 - smooth_factor) + smooth_factor / n_classes

            # 计算交叉熵损失
            log_probs = F.log_softmax(query_logits, dim=1)
            cls_loss = -(target_smooth * log_probs).sum(dim=1).mean()

            # 计算对比学习损失（如果需要）
            if use_contrastive and train:
                # 计算对比学习损失
                cont_loss = contrastive_loss_fn(support_features, support_labels, query_features, query_labels)
                # 组合损失
                task_loss = cls_loss + contrastive_weight * cont_loss
                # 保存对比损失（用于监控）
                task_contrastive_losses.append(cont_loss)
            else:
                # 只使用分类损失
                task_loss = cls_loss

            task_losses.append(task_loss)

            # 计算准确率 - 这部分不需要梯度
            with torch.no_grad():
                pred = torch.argmax(query_logits, dim=1)
                correct = (pred == query_labels).sum().item()
                accuracy = correct / len(query_labels)
                task_accuracies.append(accuracy)

        # 计算任务权重 - 使用均匀权重以提高稳定性
        n_tasks = len(task_losses)
        task_weights = torch.ones(n_tasks, device=task_losses[0].device) / n_tasks

        # 计算加权总损失
        total_loss = sum(w * l for w, l in zip(task_weights, task_losses))

        # 如果使用对比学习，返回对比损失的平均值（用于监控）
        if use_contrastive and train and task_contrastive_losses:
            avg_contrastive_loss = sum(task_contrastive_losses) / len(task_contrastive_losses)
            return total_loss, task_accuracies, avg_contrastive_loss
        else:
            return total_loss, task_accuracies

    def get_features(self, x):
        """
        获取特征表示

        参数:
            x: 输入数据

        返回:
            特征表示
        """
        return self.protonet.get_features(x)

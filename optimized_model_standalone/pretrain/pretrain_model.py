#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
预训练模型定义模块
定义用于预训练的ResNet34模型
"""

import torch
import torch.nn as nn
import torchvision.models as models

class ResNet34Classifier(nn.Module):
    """
    基于ResNet34的分类器
    用于预训练正常角膜和圆锥角膜的二分类模型
    """
    
    def __init__(self, num_classes=2, pretrained=True, feature_dim=512, dropout=0.5):
        """
        初始化模型
        
        参数:
            num_classes (int): 类别数，默认为2（正常和圆锥角膜）
            pretrained (bool): 是否使用ImageNet预训练权重
            feature_dim (int): 特征维度，默认为512
            dropout (float): Dropout比率，防止过拟合
        """
        super(ResNet34Classifier, self).__init__()
        
        # 加载ResNet34模型
        self.resnet = models.resnet34(pretrained=pretrained)
        
        # 获取ResNet34的输出维度
        resnet_output_dim = self.resnet.fc.in_features  # 512 for ResNet34
        
        # 移除原始的全连接层
        self.resnet.fc = nn.Identity()
        
        # 添加自定义的分类头
        self.classifier = nn.Sequential(
            nn.Linear(resnet_output_dim, feature_dim),
            nn.BatchNorm1d(feature_dim),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(feature_dim, num_classes)
        )
    
    def forward(self, x):
        """
        前向传播
        
        参数:
            x (torch.Tensor): 输入张量，形状为 [batch_size, channels, height, width]
            
        返回:
            torch.Tensor: 分类输出，形状为 [batch_size, num_classes]
        """
        # ResNet34特征提取
        features = self.resnet(x)
        
        # 分类
        outputs = self.classifier(features)
        
        return outputs
    
    def get_features(self, x):
        """
        获取特征向量
        
        参数:
            x (torch.Tensor): 输入张量，形状为 [batch_size, channels, height, width]
            
        返回:
            torch.Tensor: 特征向量，形状为 [batch_size, feature_dim]
        """
        # ResNet34特征提取
        features = self.resnet(x)
        
        # 获取特征向量（分类头的倒数第二层输出）
        feature_vector = self.classifier[:-1](features)
        
        return feature_vector


class EnhancedResNet34Classifier(ResNet34Classifier):
    """
    增强版ResNet34分类器
    添加注意力机制和残差连接
    """
    
    def __init__(self, num_classes=2, pretrained=True, feature_dim=512, dropout=0.5):
        """
        初始化模型
        
        参数:
            num_classes (int): 类别数，默认为2（正常和圆锥角膜）
            pretrained (bool): 是否使用ImageNet预训练权重
            feature_dim (int): 特征维度，默认为512
            dropout (float): Dropout比率，防止过拟合
        """
        super(EnhancedResNet34Classifier, self).__init__(
            num_classes=num_classes,
            pretrained=pretrained,
            feature_dim=feature_dim,
            dropout=dropout
        )
        
        # 获取ResNet34的输出维度
        resnet_output_dim = self.resnet.fc.in_features  # 512 for ResNet34
        
        # 添加注意力机制
        self.attention = nn.Sequential(
            nn.Linear(resnet_output_dim, resnet_output_dim),
            nn.Tanh(),
            nn.Linear(resnet_output_dim, 1)
        )
        
        # 替换分类头
        self.classifier = nn.Sequential(
            nn.Linear(resnet_output_dim, feature_dim),
            nn.BatchNorm1d(feature_dim),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(feature_dim, num_classes)
        )
        
        # 添加残差连接
        self.residual = nn.Sequential(
            nn.Linear(resnet_output_dim, feature_dim),
            nn.BatchNorm1d(feature_dim)
        ) if resnet_output_dim != feature_dim else nn.Identity()
    
    def forward(self, x):
        """
        前向传播
        
        参数:
            x (torch.Tensor): 输入张量，形状为 [batch_size, channels, height, width]
            
        返回:
            torch.Tensor: 分类输出，形状为 [batch_size, num_classes]
        """
        # ResNet34特征提取
        features = self.resnet(x)
        
        # 注意力机制
        attention_weights = torch.softmax(self.attention(features), dim=0)
        attended_features = features * attention_weights
        
        # 分类头的前几层
        x = self.classifier[:-1](attended_features)
        
        # 残差连接
        residual = self.residual(features)
        x = x + residual
        
        # 最后的分类层
        outputs = self.classifier[-1](x)
        
        return outputs
    
    def get_features(self, x):
        """
        获取特征向量
        
        参数:
            x (torch.Tensor): 输入张量，形状为 [batch_size, channels, height, width]
            
        返回:
            torch.Tensor: 特征向量，形状为 [batch_size, feature_dim]
        """
        # ResNet34特征提取
        features = self.resnet(x)
        
        # 注意力机制
        attention_weights = torch.softmax(self.attention(features), dim=0)
        attended_features = features * attention_weights
        
        # 分类头的前几层
        x = self.classifier[:-1](attended_features)
        
        # 残差连接
        residual = self.residual(features)
        x = x + residual
        
        return x

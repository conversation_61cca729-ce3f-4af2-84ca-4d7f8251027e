#!/bin/bash

#############################################################
# 预训练ResNet34模型脚本
# 使用正常角膜和圆锥角膜样本进行二分类训练
#############################################################

# ==================== 默认参数设置 ====================

# ----- 运行模式参数 -----
RUN_MODE="nohup"          # 运行模式: nohup(后台运行), direct(直接运行)

# ----- GPU和硬件参数 -----
GPU_IDS="0,1"         # 使用的GPU ID，例如"0,1,2,3"表示使用GPU 0,1,2,3
NUM_WORKERS=4             # 数据加载的工作线程数，影响数据加载速度，建议设为CPU核心数的一半

# ----- 训练基本参数 -----
EPOCHS=100                # 训练轮数，影响总训练时间
BATCH_SIZE=32             # 批次大小
LR=0.002                  # 学习率
WEIGHT_DECAY=1e-4         # 权重衰减系数，用于L2正则化防止过拟合
DROPOUT=0.5               # Dropout比率，防止过拟合的另一种方法
SEED=42                   # 随机种子，确保实验可重复性
SAVE_DIR="pretrained_models"  # 保存模型和结果的目录
FEATURE_DIM=512           # 特征维度，影响模型容量和表达能力

# ----- 数据增强参数 -----
USE_AUGMENTATION=false     # 是否使用数据增强

# ----- 类别权重参数 -----
USE_CLASS_WEIGHTS=false   # 是否使用类别权重
KC_WEIGHT=1.0             # 圆锥角膜类别权重

# ----- 学习率调度器参数 -----
USE_LR_SCHEDULER=false     # 是否使用学习率调度器
SCHEDULER_TYPE="onecycle" # 学习率调度器类型: onecycle或plateau
MAX_LR=0.001              # OneCycleLR的最大学习率
DIV_FACTOR=10.0           # OneCycleLR的初始学习率除数
FINAL_DIV_FACTOR=100      # OneCycleLR的最终学习率除数
PCT_START=0.3             # OneCycleLR的预热阶段比例
PLATEAU_FACTOR=0.5        # ReduceLROnPlateau的学习率衰减因子
PLATEAU_PATIENCE=5        # ReduceLROnPlateau的耐心值
MIN_LR=1e-6               # ReduceLROnPlateau的最小学习率

# ----- 早停参数 -----
EARLY_STOPPING=10         # 早停耐心值

# ----- 模型参数 -----
PRETRAINED=true           # 是否使用ImageNet预训练权重

# ----- 数据路径参数 -----
TRAIN_CSV="../split_result/train_set.csv" # 训练集CSV文件路径
VAL_CSV="../split_result/val_set.csv"     # 验证集CSV文件路径
TEST_CSV="../split_result/test_set.csv"   # 测试集CSV文件路径
BASE_DIR="../"                           # 图像路径的基础目录

# ==================== 命令行参数解析 ====================
while [[ $# -gt 0 ]]; do
  case $1 in
    --run_mode)
      RUN_MODE="$2"
      shift 2
      ;;
    --gpu_ids)
      GPU_IDS="$2"
      shift 2
      ;;
    --epochs)
      EPOCHS="$2"
      shift 2
      ;;
    --batch_size)
      BATCH_SIZE="$2"
      shift 2
      ;;
    --lr)
      LR="$2"
      shift 2
      ;;
    --save_dir)
      SAVE_DIR="$2"
      shift 2
      ;;
    --train_csv)
      TRAIN_CSV="$2"
      shift 2
      ;;
    --val_csv)
      VAL_CSV="$2"
      shift 2
      ;;
    --test_csv)
      TEST_CSV="$2"
      shift 2
      ;;
    # 其他参数省略
    *)
      echo "未知参数: $1"
      exit 1
      ;;
  esac
done

# ==================== 环境准备 ====================
# 创建日志目录
mkdir -p logs

# 获取当前时间戳
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
LOG_FILE="logs/pretrain_${TIMESTAMP}_gpu${GPU_IDS}.log"
FINAL_SAVE_DIR="${SAVE_DIR}_${TIMESTAMP}_gpu${GPU_IDS}"

echo "日志文件: $LOG_FILE"
echo "保存目录: $FINAL_SAVE_DIR"

# 设置环境变量
export CUDA_VISIBLE_DEVICES=$GPU_IDS
export OMP_NUM_THREADS=1
export MKL_NUM_THREADS=1
export MPLBACKEND=Agg

# 创建保存目录
mkdir -p $FINAL_SAVE_DIR

# ==================== 构建命令参数 ====================
# 构建数据增强参数
AUGMENT_ARGS=""
if [ "$USE_AUGMENTATION" = "true" ]; then
  AUGMENT_ARGS="--use_augmentation"
fi

# 构建类别权重参数
CLASS_WEIGHT_ARGS=""
if [ "$USE_CLASS_WEIGHTS" = "true" ]; then
  CLASS_WEIGHT_ARGS="--use_class_weights --kc_weight $KC_WEIGHT"
fi

# 构建学习率调度器参数
LR_SCHEDULER_ARGS=""
if [ "$USE_LR_SCHEDULER" = "true" ]; then
  LR_SCHEDULER_ARGS="--use_lr_scheduler --scheduler_type $SCHEDULER_TYPE"
  if [ "$SCHEDULER_TYPE" = "onecycle" ]; then
    LR_SCHEDULER_ARGS="$LR_SCHEDULER_ARGS --max_lr $MAX_LR --div_factor $DIV_FACTOR --final_div_factor $FINAL_DIV_FACTOR --pct_start $PCT_START"
  elif [ "$SCHEDULER_TYPE" = "plateau" ]; then
    LR_SCHEDULER_ARGS="$LR_SCHEDULER_ARGS --plateau_factor $PLATEAU_FACTOR --plateau_patience $PLATEAU_PATIENCE --min_lr $MIN_LR"
  fi
fi

# 构建预训练参数
PRETRAINED_ARGS=""
if [ "$PRETRAINED" = "true" ]; then
  PRETRAINED_ARGS="--pretrained"
fi

# ==================== 构建完整命令 ====================
# 基础命令
BASE_CMD="python pretrain_resnet34.py \
  --train_csv $TRAIN_CSV \
  --val_csv $VAL_CSV \
  --test_csv $TEST_CSV \
  --base_dir $BASE_DIR \
  --epochs $EPOCHS \
  --batch_size $BATCH_SIZE \
  --lr $LR \
  --weight_decay $WEIGHT_DECAY \
  --dropout $DROPOUT \
  --seed $SEED \
  --save_dir $FINAL_SAVE_DIR \
  --feature_dim $FEATURE_DIM \
  --num_workers $NUM_WORKERS \
  --early_stopping $EARLY_STOPPING \
  $AUGMENT_ARGS \
  $CLASS_WEIGHT_ARGS \
  $LR_SCHEDULER_ARGS \
  $PRETRAINED_ARGS"

# ==================== 执行命令 ====================
echo "开始预训练ResNet34模型..."
echo "使用GPU: $GPU_IDS"

# 将配置保存到输出目录
echo "预训练配置:" > "${FINAL_SAVE_DIR}/pretrain_config.txt"
echo "  EPOCHS=$EPOCHS" >> "${FINAL_SAVE_DIR}/pretrain_config.txt"
echo "  BATCH_SIZE=$BATCH_SIZE" >> "${FINAL_SAVE_DIR}/pretrain_config.txt"
echo "  LR=$LR" >> "${FINAL_SAVE_DIR}/pretrain_config.txt"
echo "  USE_AUGMENTATION=$USE_AUGMENTATION" >> "${FINAL_SAVE_DIR}/pretrain_config.txt"
echo "  USE_CLASS_WEIGHTS=$USE_CLASS_WEIGHTS" >> "${FINAL_SAVE_DIR}/pretrain_config.txt"
echo "  KC_WEIGHT=$KC_WEIGHT" >> "${FINAL_SAVE_DIR}/pretrain_config.txt"
echo "  SCHEDULER_TYPE=$SCHEDULER_TYPE" >> "${FINAL_SAVE_DIR}/pretrain_config.txt"
echo "  PRETRAINED=$PRETRAINED" >> "${FINAL_SAVE_DIR}/pretrain_config.txt"

# 根据运行模式选择执行方式
if [ "$RUN_MODE" = "nohup" ]; then
  # 使用nohup在后台运行
  echo "使用nohup在后台运行..."
  echo "$BASE_CMD" > $LOG_FILE
  nohup bash -c "$BASE_CMD" >> $LOG_FILE 2>&1 &

  # 获取进程ID
  PID=$!
  echo "进程已启动，PID: $PID"
  echo "您可以使用以下命令监控训练进度:"
  echo "  tail -f $LOG_FILE"
  echo "要停止训练，请使用:"
  echo "  kill $PID"

  # 将PID保存到文件中，方便后续管理
  echo $PID > "logs/pid_pretrain_${TIMESTAMP}.txt"
else
  # 直接运行
  echo "直接运行训练脚本..."
  eval $BASE_CMD
fi

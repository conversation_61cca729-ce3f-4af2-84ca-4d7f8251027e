python pretrain_resnet34.py   --train_csv ../split_result/train_set.csv   --val_csv ../split_result/val_set.csv   --test_csv ../split_result/test_set.csv   --base_dir ../   --epochs 100   --batch_size 32   --lr 0.002   --weight_decay 1e-4   --dropout 0.5   --seed 42   --save_dir pretrained_models_20250517_202721_gpu0,1   --feature_dim 512   --num_workers 4   --early_stopping 10         --use_lr_scheduler --scheduler_type onecycle --max_lr 0.001 --div_factor 10.0 --final_div_factor 100 --pct_start 0.3   --pretrained
2025-05-17 20:27:26,013 - __main__ - INFO - 使用设备: cuda
2025-05-17 20:27:26,040 - __main__ - INFO - 训练集大小: 297
2025-05-17 20:27:26,040 - __main__ - INFO - 验证集大小: 62
2025-05-17 20:27:26,040 - __main__ - INFO - 测试集大小: 64
/home/<USER>/anaconda3/envs/kc1/lib/python3.10/site-packages/torchvision/models/_utils.py:208: UserWarning: The parameter 'pretrained' is deprecated since 0.13 and may be removed in the future, please use 'weights' instead.
  warnings.warn(
/home/<USER>/anaconda3/envs/kc1/lib/python3.10/site-packages/torchvision/models/_utils.py:223: UserWarning: Arguments other than a weight enum or `None` for 'weights' are deprecated since 0.13 and may be removed in the future. The current behavior is equivalent to passing `weights=ResNet34_Weights.IMAGENET1K_V1`. You can also use `weights=ResNet34_Weights.DEFAULT` to get the most up-to-date weights.
  warnings.warn(msg)

Training:   0%|          | 0/10 [00:00<?, ?it/s]
Training:  10%|█         | 1/10 [00:04<00:37,  4.18s/it]
Training:  20%|██        | 2/10 [00:04<00:14,  1.86s/it]
Training:  30%|███       | 3/10 [00:04<00:07,  1.11s/it]
Training:  40%|████      | 4/10 [00:04<00:04,  1.31it/s]
Training:  50%|█████     | 5/10 [00:05<00:02,  1.75it/s]
Training:  60%|██████    | 6/10 [00:05<00:01,  2.20it/s]
Training:  70%|███████   | 7/10 [00:05<00:01,  2.62it/s]
Training:  80%|████████  | 8/10 [00:05<00:00,  3.00it/s]
Training:  90%|█████████ | 9/10 [00:06<00:00,  3.33it/s]
Training: 100%|██████████| 10/10 [00:06<00:00,  1.62it/s]

Validating:   0%|          | 0/2 [00:00<?, ?it/s]
Validating:  50%|█████     | 1/2 [00:00<00:00,  1.44it/s]
Validating: 100%|██████████| 2/2 [00:00<00:00,  2.27it/s]
2025-05-17 20:27:34,980 - __main__ - INFO - Epoch: 1/100, Train Loss: 0.1739, Train Acc: 0.9293, Val Loss: 0.1484, Val Acc: 0.9839
2025-05-17 20:27:35,479 - __main__ - INFO - 保存最佳模型，验证准确率: 0.9839

Training:   0%|          | 0/10 [00:00<?, ?it/s]
Training:  10%|█         | 1/10 [00:01<00:10,  1.14s/it]
Training:  20%|██        | 2/10 [00:01<00:04,  1.66it/s]
Training:  30%|███       | 3/10 [00:01<00:03,  2.31it/s]
Training:  40%|████      | 4/10 [00:01<00:02,  2.84it/s]
Training:  50%|█████     | 5/10 [00:02<00:01,  3.25it/s]
Training:  60%|██████    | 6/10 [00:02<00:01,  3.55it/s]
Training:  70%|███████   | 7/10 [00:02<00:00,  3.79it/s]
Training:  80%|████████  | 8/10 [00:02<00:00,  3.95it/s]
Training:  90%|█████████ | 9/10 [00:02<00:00,  4.07it/s]
Training: 100%|██████████| 10/10 [00:03<00:00,  3.15it/s]

Validating:   0%|          | 0/2 [00:00<?, ?it/s]
Validating:  50%|█████     | 1/2 [00:00<00:00,  1.42it/s]
Validating: 100%|██████████| 2/2 [00:00<00:00,  2.22it/s]
2025-05-17 20:27:39,557 - __main__ - INFO - Epoch: 2/100, Train Loss: 0.0300, Train Acc: 0.9899, Val Loss: 0.0309, Val Acc: 1.0000
2025-05-17 20:27:43,083 - __main__ - INFO - 保存最佳模型，验证准确率: 1.0000

Training:   0%|          | 0/10 [00:00<?, ?it/s]
Training:  10%|█         | 1/10 [00:01<00:10,  1.13s/it]
Training:  20%|██        | 2/10 [00:01<00:04,  1.66it/s]
Training:  30%|███       | 3/10 [00:01<00:03,  2.31it/s]
Training:  40%|████      | 4/10 [00:01<00:02,  2.83it/s]
Training:  50%|█████     | 5/10 [00:02<00:01,  3.24it/s]
Training:  60%|██████    | 6/10 [00:02<00:01,  3.56it/s]
Training:  70%|███████   | 7/10 [00:02<00:00,  3.78it/s]
Training:  80%|████████  | 8/10 [00:02<00:00,  3.95it/s]
Training:  90%|█████████ | 9/10 [00:02<00:00,  4.07it/s]
Training: 100%|██████████| 10/10 [00:03<00:00,  3.14it/s]

Validating:   0%|          | 0/2 [00:00<?, ?it/s]
Validating:  50%|█████     | 1/2 [00:00<00:00,  1.37it/s]
Validating: 100%|██████████| 2/2 [00:00<00:00,  2.13it/s]
2025-05-17 20:27:47,216 - __main__ - INFO - Epoch: 3/100, Train Loss: 0.0125, Train Acc: 1.0000, Val Loss: 0.0074, Val Acc: 1.0000
2025-05-17 20:27:47,216 - __main__ - INFO - 验证准确率未提高，耐心计数: 1/10

Training:   0%|          | 0/10 [00:00<?, ?it/s]
Training:  10%|█         | 1/10 [00:00<00:08,  1.01it/s]
Training:  20%|██        | 2/10 [00:01<00:04,  1.84it/s]
Training:  30%|███       | 3/10 [00:01<00:02,  2.50it/s]
Training:  40%|████      | 4/10 [00:01<00:01,  3.00it/s]
Training:  50%|█████     | 5/10 [00:01<00:01,  3.38it/s]
Training:  60%|██████    | 6/10 [00:02<00:01,  3.65it/s]
Training:  70%|███████   | 7/10 [00:02<00:00,  3.86it/s]
Training:  80%|████████  | 8/10 [00:02<00:00,  3.99it/s]
Training:  90%|█████████ | 9/10 [00:02<00:00,  4.10it/s]
Training: 100%|██████████| 10/10 [00:03<00:00,  3.28it/s]

Validating:   0%|          | 0/2 [00:00<?, ?it/s]
Validating:  50%|█████     | 1/2 [00:00<00:00,  1.35it/s]
Validating: 100%|██████████| 2/2 [00:00<00:00,  2.15it/s]
2025-05-17 20:27:51,202 - __main__ - INFO - Epoch: 4/100, Train Loss: 0.0135, Train Acc: 1.0000, Val Loss: 0.0029, Val Acc: 1.0000
2025-05-17 20:27:51,202 - __main__ - INFO - 验证准确率未提高，耐心计数: 2/10

Training:   0%|          | 0/10 [00:00<?, ?it/s]
Training:  10%|█         | 1/10 [00:01<00:09,  1.10s/it]
Training:  20%|██        | 2/10 [00:01<00:04,  1.70it/s]
Training:  30%|███       | 3/10 [00:01<00:02,  2.35it/s]
Training:  40%|████      | 4/10 [00:01<00:02,  2.87it/s]
Training:  50%|█████     | 5/10 [00:02<00:01,  3.27it/s]
Training:  60%|██████    | 6/10 [00:02<00:01,  3.57it/s]
Training:  70%|███████   | 7/10 [00:02<00:00,  3.78it/s]
Training:  80%|████████  | 8/10 [00:02<00:00,  3.94it/s]
Training:  90%|█████████ | 9/10 [00:02<00:00,  4.05it/s]
Training: 100%|██████████| 10/10 [00:03<00:00,  3.17it/s]

Validating:   0%|          | 0/2 [00:00<?, ?it/s]
Validating:  50%|█████     | 1/2 [00:00<00:00,  1.36it/s]
Validating: 100%|██████████| 2/2 [00:00<00:00,  2.17it/s]
2025-05-17 20:27:55,293 - __main__ - INFO - Epoch: 5/100, Train Loss: 0.0055, Train Acc: 1.0000, Val Loss: 0.0124, Val Acc: 1.0000
2025-05-17 20:27:55,293 - __main__ - INFO - 验证准确率未提高，耐心计数: 3/10

Training:   0%|          | 0/10 [00:00<?, ?it/s]
Training:  10%|█         | 1/10 [00:01<00:09,  1.09s/it]
Training:  20%|██        | 2/10 [00:01<00:04,  1.71it/s]
Training:  30%|███       | 3/10 [00:01<00:02,  2.36it/s]
Training:  40%|████      | 4/10 [00:01<00:02,  2.88it/s]
Training:  50%|█████     | 5/10 [00:02<00:01,  3.28it/s]
Training:  60%|██████    | 6/10 [00:02<00:01,  3.57it/s]
Training:  70%|███████   | 7/10 [00:02<00:00,  3.79it/s]
Training:  80%|████████  | 8/10 [00:02<00:00,  3.96it/s]
Training:  90%|█████████ | 9/10 [00:02<00:00,  4.07it/s]
Training: 100%|██████████| 10/10 [00:03<00:00,  3.18it/s]

Validating:   0%|          | 0/2 [00:00<?, ?it/s]
Validating:  50%|█████     | 1/2 [00:00<00:00,  1.34it/s]
Validating: 100%|██████████| 2/2 [00:00<00:00,  2.10it/s]
2025-05-17 20:27:59,396 - __main__ - INFO - Epoch: 6/100, Train Loss: 0.0206, Train Acc: 0.9966, Val Loss: 0.0157, Val Acc: 1.0000
2025-05-17 20:27:59,396 - __main__ - INFO - 验证准确率未提高，耐心计数: 4/10

Training:   0%|          | 0/10 [00:00<?, ?it/s]
Training:  10%|█         | 1/10 [00:01<00:09,  1.10s/it]
Training:  20%|██        | 2/10 [00:01<00:04,  1.70it/s]
Training:  30%|███       | 3/10 [00:01<00:02,  2.35it/s]
Training:  40%|████      | 4/10 [00:01<00:02,  2.87it/s]
Training:  50%|█████     | 5/10 [00:02<00:01,  3.27it/s]
Training:  60%|██████    | 6/10 [00:02<00:01,  3.56it/s]
Training:  70%|███████   | 7/10 [00:02<00:00,  3.78it/s]
Training:  80%|████████  | 8/10 [00:02<00:00,  3.93it/s]
Training:  90%|█████████ | 9/10 [00:02<00:00,  4.05it/s]
Training: 100%|██████████| 10/10 [00:03<00:00,  3.18it/s]

Validating:   0%|          | 0/2 [00:00<?, ?it/s]
Validating:  50%|█████     | 1/2 [00:00<00:00,  1.35it/s]
Validating: 100%|██████████| 2/2 [00:00<00:00,  2.15it/s]
2025-05-17 20:28:03,479 - __main__ - INFO - Epoch: 7/100, Train Loss: 0.0064, Train Acc: 1.0000, Val Loss: 0.3700, Val Acc: 0.8065
2025-05-17 20:28:03,479 - __main__ - INFO - 验证准确率未提高，耐心计数: 5/10

Training:   0%|          | 0/10 [00:00<?, ?it/s]
Training:  10%|█         | 1/10 [00:01<00:09,  1.06s/it]
Training:  20%|██        | 2/10 [00:01<00:04,  1.75it/s]
Training:  30%|███       | 3/10 [00:01<00:02,  2.40it/s]
Training:  40%|████      | 4/10 [00:01<00:02,  2.92it/s]
Training:  50%|█████     | 5/10 [00:01<00:01,  3.30it/s]
Training:  60%|██████    | 6/10 [00:02<00:01,  3.59it/s]
Training:  70%|███████   | 7/10 [00:02<00:00,  3.81it/s]
Training:  80%|████████  | 8/10 [00:02<00:00,  3.96it/s]
Training:  90%|█████████ | 9/10 [00:02<00:00,  4.07it/s]
Training: 100%|██████████| 10/10 [00:03<00:00,  3.21it/s]

Validating:   0%|          | 0/2 [00:00<?, ?it/s]
Validating:  50%|█████     | 1/2 [00:00<00:00,  1.29it/s]
Validating: 100%|██████████| 2/2 [00:00<00:00,  2.07it/s]
2025-05-17 20:28:07,566 - __main__ - INFO - Epoch: 8/100, Train Loss: 0.0047, Train Acc: 1.0000, Val Loss: 0.0524, Val Acc: 0.9839
2025-05-17 20:28:07,566 - __main__ - INFO - 验证准确率未提高，耐心计数: 6/10

Training:   0%|          | 0/10 [00:00<?, ?it/s]
Training:  10%|█         | 1/10 [00:01<00:09,  1.10s/it]
Training:  20%|██        | 2/10 [00:01<00:04,  1.70it/s]
Training:  30%|███       | 3/10 [00:01<00:02,  2.35it/s]
Training:  40%|████      | 4/10 [00:01<00:02,  2.87it/s]
Training:  50%|█████     | 5/10 [00:02<00:01,  3.26it/s]
Training:  60%|██████    | 6/10 [00:02<00:01,  3.56it/s]
Training:  70%|███████   | 7/10 [00:02<00:00,  3.78it/s]
Training:  80%|████████  | 8/10 [00:02<00:00,  3.94it/s]
Training:  90%|█████████ | 9/10 [00:02<00:00,  4.06it/s]
Training: 100%|██████████| 10/10 [00:03<00:00,  3.16it/s]

Validating:   0%|          | 0/2 [00:00<?, ?it/s]
Validating:  50%|█████     | 1/2 [00:00<00:00,  1.35it/s]
Validating: 100%|██████████| 2/2 [00:00<00:00,  2.16it/s]
2025-05-17 20:28:11,660 - __main__ - INFO - Epoch: 9/100, Train Loss: 0.0040, Train Acc: 1.0000, Val Loss: 0.0038, Val Acc: 1.0000
2025-05-17 20:28:11,660 - __main__ - INFO - 验证准确率未提高，耐心计数: 7/10

Training:   0%|          | 0/10 [00:00<?, ?it/s]
Training:  10%|█         | 1/10 [00:01<00:09,  1.09s/it]
Training:  20%|██        | 2/10 [00:01<00:04,  1.70it/s]
Training:  30%|███       | 3/10 [00:01<00:02,  2.36it/s]
Training:  40%|████      | 4/10 [00:01<00:02,  2.87it/s]
Training:  50%|█████     | 5/10 [00:02<00:01,  3.27it/s]
Training:  60%|██████    | 6/10 [00:02<00:01,  3.57it/s]
Training:  70%|███████   | 7/10 [00:02<00:00,  3.78it/s]
Training:  80%|████████  | 8/10 [00:02<00:00,  3.94it/s]
Training:  90%|█████████ | 9/10 [00:02<00:00,  4.05it/s]
Training: 100%|██████████| 10/10 [00:03<00:00,  3.16it/s]

Validating:   0%|          | 0/2 [00:00<?, ?it/s]
Validating:  50%|█████     | 1/2 [00:00<00:00,  1.35it/s]
Validating: 100%|██████████| 2/2 [00:00<00:00,  2.16it/s]
2025-05-17 20:28:15,762 - __main__ - INFO - Epoch: 10/100, Train Loss: 0.0020, Train Acc: 1.0000, Val Loss: 0.1090, Val Acc: 0.9516
2025-05-17 20:28:15,763 - __main__ - INFO - 验证准确率未提高，耐心计数: 8/10

Training:   0%|          | 0/10 [00:00<?, ?it/s]
Training:  10%|█         | 1/10 [00:01<00:09,  1.02s/it]
Training:  20%|██        | 2/10 [00:01<00:04,  1.79it/s]
Training:  30%|███       | 3/10 [00:01<00:02,  2.45it/s]
Training:  40%|████      | 4/10 [00:01<00:02,  2.96it/s]
Training:  50%|█████     | 5/10 [00:01<00:01,  3.34it/s]
Training:  60%|██████    | 6/10 [00:02<00:01,  3.62it/s]
Training:  70%|███████   | 7/10 [00:02<00:00,  3.81it/s]
Training:  80%|████████  | 8/10 [00:02<00:00,  3.97it/s]
Training:  90%|█████████ | 9/10 [00:02<00:00,  4.06it/s]
Training: 100%|██████████| 10/10 [00:03<00:00,  3.24it/s]

Validating:   0%|          | 0/2 [00:00<?, ?it/s]
Validating:  50%|█████     | 1/2 [00:00<00:00,  1.44it/s]
Validating: 100%|██████████| 2/2 [00:00<00:00,  2.24it/s]
2025-05-17 20:28:19,756 - __main__ - INFO - Epoch: 11/100, Train Loss: 0.0069, Train Acc: 0.9966, Val Loss: 0.0054, Val Acc: 1.0000
2025-05-17 20:28:19,756 - __main__ - INFO - 验证准确率未提高，耐心计数: 9/10

Training:   0%|          | 0/10 [00:00<?, ?it/s]
Training:  10%|█         | 1/10 [00:01<00:10,  1.12s/it]
Training:  20%|██        | 2/10 [00:01<00:04,  1.67it/s]
Training:  30%|███       | 3/10 [00:01<00:03,  2.32it/s]
Training:  40%|████      | 4/10 [00:01<00:02,  2.84it/s]
Training:  50%|█████     | 5/10 [00:02<00:01,  3.25it/s]
Training:  60%|██████    | 6/10 [00:02<00:01,  3.54it/s]
Training:  70%|███████   | 7/10 [00:02<00:00,  3.76it/s]
Training:  80%|████████  | 8/10 [00:02<00:00,  3.92it/s]
Training:  90%|█████████ | 9/10 [00:02<00:00,  4.04it/s]
Training: 100%|██████████| 10/10 [00:03<00:00,  3.13it/s]

Validating:   0%|          | 0/2 [00:00<?, ?it/s]
Validating:  50%|█████     | 1/2 [00:00<00:00,  1.32it/s]
Validating: 100%|██████████| 2/2 [00:00<00:00,  2.10it/s]
2025-05-17 20:28:23,912 - __main__ - INFO - Epoch: 12/100, Train Loss: 0.0287, Train Acc: 0.9966, Val Loss: 1.4826, Val Acc: 0.5645
2025-05-17 20:28:23,912 - __main__ - INFO - 验证准确率未提高，耐心计数: 10/10
2025-05-17 20:28:23,912 - __main__ - INFO - 早停触发，最佳验证准确率: 1.0000，最佳轮次: 2

Validating:   0%|          | 0/2 [00:00<?, ?it/s]
Validating:  50%|█████     | 1/2 [00:00<00:00,  1.23it/s]
Validating: 100%|██████████| 2/2 [00:01<00:00,  1.97it/s]
2025-05-17 20:28:25,620 - __main__ - INFO - 测试损失: 0.0503, 测试准确率: 0.9844, 精确率: 0.9848, 召回率: 0.9844, F1分数: 0.9843
2025-05-17 20:28:25,814 - __main__ - INFO - 训练完成，结果已保存到 pretrained_models_20250517_202721_gpu0,1

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
预训练ResNet34模型脚本
使用正常角膜和圆锥角膜样本进行二分类训练
"""

import os
import sys
import argparse
import logging
import random
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, WeightedRandomSampler
from torch.optim.lr_scheduler import ReduceLROnPlateau, OneCycleLR
import torchvision.transforms as transforms
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, confusion_matrix
import matplotlib.pyplot as plt
import seaborn as sns
from tqdm import tqdm

# 导入自定义模块
from pretrain_dataset import KeratoconusPretrainDataset
from pretrain_model import ResNet34Classifier

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def set_seed(seed):
    """设置随机种子以确保实验可重复性"""
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='预训练ResNet34模型')

    # 数据路径参数
    parser.add_argument('--train_csv', type=str, required=True, help='训练集CSV文件路径')
    parser.add_argument('--val_csv', type=str, required=True, help='验证集CSV文件路径')
    parser.add_argument('--test_csv', type=str, required=True, help='测试集CSV文件路径')
    parser.add_argument('--base_dir', type=str, default='../', help='图像路径的基础目录')

    # 训练基本参数
    parser.add_argument('--epochs', type=int, default=100, help='训练轮数')
    parser.add_argument('--batch_size', type=int, default=32, help='批次大小')
    parser.add_argument('--lr', type=float, default=0.001, help='学习率')
    parser.add_argument('--weight_decay', type=float, default=1e-4, help='权重衰减系数')
    parser.add_argument('--dropout', type=float, default=0.5, help='Dropout比率')
    parser.add_argument('--seed', type=int, default=42, help='随机种子')
    parser.add_argument('--save_dir', type=str, default='pretrained_models', help='保存模型和结果的目录')

    # 数据增强参数
    parser.add_argument('--use_augmentation', action='store_true', help='是否使用数据增强')
    parser.add_argument('--num_workers', type=int, default=4, help='数据加载的工作线程数')

    # 类别权重参数
    parser.add_argument('--use_class_weights', action='store_true', help='是否使用类别权重')
    parser.add_argument('--kc_weight', type=float, default=2.0, help='圆锥角膜类别权重')

    # 学习率调度器参数
    parser.add_argument('--use_lr_scheduler', action='store_true', help='是否使用学习率调度器')
    parser.add_argument('--scheduler_type', type=str, default='onecycle', choices=['plateau', 'onecycle'], help='学习率调度器类型')
    parser.add_argument('--max_lr', type=float, default=0.001, help='OneCycleLR的最大学习率')
    parser.add_argument('--div_factor', type=float, default=10.0, help='OneCycleLR的初始学习率除数')
    parser.add_argument('--final_div_factor', type=float, default=100, help='OneCycleLR的最终学习率除数')
    parser.add_argument('--pct_start', type=float, default=0.3, help='OneCycleLR的预热阶段比例')
    parser.add_argument('--plateau_factor', type=float, default=0.5, help='ReduceLROnPlateau的学习率衰减因子')
    parser.add_argument('--plateau_patience', type=int, default=5, help='ReduceLROnPlateau的耐心值')
    parser.add_argument('--min_lr', type=float, default=1e-6, help='ReduceLROnPlateau的最小学习率')

    # 早停参数
    parser.add_argument('--early_stopping', type=int, default=10, help='早停耐心值')

    # 模型参数
    parser.add_argument('--pretrained', action='store_true', help='是否使用ImageNet预训练权重')
    parser.add_argument('--feature_dim', type=int, default=512, help='特征维度')

    return parser.parse_args()

def train_epoch(model, dataloader, criterion, optimizer, device, scheduler=None, use_onecycle=False):
    """训练一个epoch"""
    model.train()
    running_loss = 0.0
    all_preds = []
    all_labels = []

    for inputs, labels in tqdm(dataloader, desc="Training"):
        inputs = inputs.to(device)
        labels = labels.to(device)

        # 前向传播
        outputs = model(inputs)
        loss = criterion(outputs, labels)

        # 反向传播
        optimizer.zero_grad()
        loss.backward()
        optimizer.step()

        # 如果使用OneCycleLR，每个batch更新一次学习率
        if scheduler is not None and use_onecycle:
            scheduler.step()

        # 更新统计信息
        running_loss += loss.item() * inputs.size(0)
        _, preds = torch.max(outputs, 1)
        all_preds.extend(preds.cpu().numpy())
        all_labels.extend(labels.cpu().numpy())

    # 计算平均损失和准确率
    epoch_loss = running_loss / len(dataloader.dataset)
    epoch_acc = accuracy_score(all_labels, all_preds)

    return epoch_loss, epoch_acc, all_preds, all_labels

def validate(model, dataloader, criterion, device):
    """验证模型"""
    model.eval()
    running_loss = 0.0
    all_preds = []
    all_labels = []

    with torch.no_grad():
        for inputs, labels in tqdm(dataloader, desc="Validating"):
            inputs = inputs.to(device)
            labels = labels.to(device)

            # 前向传播
            outputs = model(inputs)
            loss = criterion(outputs, labels)

            # 更新统计信息
            running_loss += loss.item() * inputs.size(0)
            _, preds = torch.max(outputs, 1)
            all_preds.extend(preds.cpu().numpy())
            all_labels.extend(labels.cpu().numpy())

    # 计算平均损失和准确率
    epoch_loss = running_loss / len(dataloader.dataset)
    epoch_acc = accuracy_score(all_labels, all_preds)

    return epoch_loss, epoch_acc, all_preds, all_labels

def plot_metrics(train_losses, val_losses, train_accs, val_accs, save_path):
    """绘制训练和验证的损失和准确率曲线"""
    plt.figure(figsize=(12, 5))

    # 绘制损失曲线
    plt.subplot(1, 2, 1)
    plt.plot(train_losses, label='Train Loss')
    plt.plot(val_losses, label='Val Loss')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.title('Training and Validation Loss')
    plt.legend()

    # 绘制准确率曲线
    plt.subplot(1, 2, 2)
    plt.plot(train_accs, label='Train Acc')
    plt.plot(val_accs, label='Val Acc')
    plt.xlabel('Epoch')
    plt.ylabel('Accuracy')
    plt.title('Training and Validation Accuracy')
    plt.legend()

    plt.tight_layout()
    plt.savefig(save_path)
    plt.close()

def plot_confusion_matrix(y_true, y_pred, save_path):
    """绘制混淆矩阵"""
    cm = confusion_matrix(y_true, y_pred)
    plt.figure(figsize=(8, 6))
    sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', xticklabels=['Normal', 'KC'], yticklabels=['Normal', 'KC'])
    plt.xlabel('Predicted')
    plt.ylabel('True')
    plt.title('Confusion Matrix')
    plt.tight_layout()
    plt.savefig(save_path)
    plt.close()

def main():
    """主函数"""
    args = parse_args()

    # 设置随机种子
    set_seed(args.seed)

    # 创建保存目录
    os.makedirs(args.save_dir, exist_ok=True)

    # 保存参数
    with open(os.path.join(args.save_dir, 'args.txt'), 'w') as f:
        for arg in vars(args):
            f.write(f'{arg}: {getattr(args, arg)}\n')

    # 设置设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logger.info(f'使用设备: {device}')

    # 定义数据转换
    if args.use_augmentation:
        train_transform = transforms.Compose([
            transforms.RandomHorizontalFlip(),
            transforms.RandomVerticalFlip(),
            transforms.RandomRotation(10),
            transforms.ColorJitter(brightness=0.1, contrast=0.1),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])
    else:
        train_transform = transforms.Compose([
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])

    val_transform = transforms.Compose([
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
    ])

    # 创建数据集
    train_dataset = KeratoconusPretrainDataset(
        csv_file=args.train_csv,
        transform=train_transform,
        binary_classification=True,  # 只使用正常和圆锥角膜样本
        base_dir=args.base_dir
    )

    val_dataset = KeratoconusPretrainDataset(
        csv_file=args.val_csv,
        transform=val_transform,
        binary_classification=True,
        base_dir=args.base_dir
    )

    test_dataset = KeratoconusPretrainDataset(
        csv_file=args.test_csv,
        transform=val_transform,
        binary_classification=True,
        base_dir=args.base_dir
    )

    logger.info(f'训练集大小: {len(train_dataset)}')
    logger.info(f'验证集大小: {len(val_dataset)}')
    logger.info(f'测试集大小: {len(test_dataset)}')

    # 使用类别权重
    if args.use_class_weights:
        # 计算类别权重
        class_counts = train_dataset.get_class_counts()
        logger.info(f'类别分布: {class_counts}')

        # 创建样本权重
        class_weights = {0: 1.0, 1: args.kc_weight}  # 0: normal, 1: kc
        sample_weights = [class_weights[label] for _, label in train_dataset.samples]
        sampler = WeightedRandomSampler(sample_weights, len(sample_weights))

        train_loader = DataLoader(
            dataset=train_dataset,
            batch_size=args.batch_size,
            sampler=sampler,
            num_workers=args.num_workers,
            pin_memory=True
        )
    else:
        train_loader = DataLoader(
            dataset=train_dataset,
            batch_size=args.batch_size,
            shuffle=True,
            num_workers=args.num_workers,
            pin_memory=True
        )

    val_loader = DataLoader(
        dataset=val_dataset,
        batch_size=args.batch_size,
        shuffle=False,
        num_workers=args.num_workers,
        pin_memory=True
    )

    test_loader = DataLoader(
        dataset=test_dataset,
        batch_size=args.batch_size,
        shuffle=False,
        num_workers=args.num_workers,
        pin_memory=True
    )

    # 创建模型
    model = ResNet34Classifier(
        num_classes=2,  # 二分类：正常和圆锥角膜
        pretrained=args.pretrained,
        feature_dim=args.feature_dim,
        dropout=args.dropout
    )

    model = model.to(device)

    # 定义损失函数
    if args.use_class_weights:
        # 使用类别权重的交叉熵损失
        weight = torch.tensor([1.0, args.kc_weight], device=device)
        criterion = nn.CrossEntropyLoss(weight=weight)
    else:
        criterion = nn.CrossEntropyLoss()

    # 创建优化器
    optimizer = optim.Adam(
        model.parameters(),
        lr=args.lr,
        weight_decay=args.weight_decay
    )

    # 创建学习率调度器
    if args.use_lr_scheduler:
        if args.scheduler_type == 'plateau':
            scheduler = ReduceLROnPlateau(
                optimizer,
                mode='max',
                factor=args.plateau_factor,
                patience=args.plateau_patience,
                min_lr=args.min_lr,
                verbose=True
            )
        elif args.scheduler_type == 'onecycle':
            scheduler = OneCycleLR(
                optimizer,
                max_lr=args.max_lr,
                steps_per_epoch=len(train_loader),
                epochs=args.epochs,
                div_factor=args.div_factor,
                final_div_factor=args.final_div_factor,
                pct_start=args.pct_start
            )
    else:
        scheduler = None

    # 训练模型
    best_val_acc = 0.0
    best_epoch = 0
    patience_counter = 0

    train_losses = []
    val_losses = []
    train_accs = []
    val_accs = []

    for epoch in range(args.epochs):
        # 训练
        train_loss, train_acc, _, _ = train_epoch(
            model=model,
            dataloader=train_loader,
            criterion=criterion,
            optimizer=optimizer,
            device=device,
            scheduler=scheduler,
            use_onecycle=(args.use_lr_scheduler and args.scheduler_type == 'onecycle')
        )

        # 验证
        val_loss, val_acc, _, _ = validate(
            model=model,
            dataloader=val_loader,
            criterion=criterion,
            device=device
        )

        # 更新学习率调度器
        if args.use_lr_scheduler and args.scheduler_type == 'plateau':
            scheduler.step(val_acc)

        # 记录损失和准确率
        train_losses.append(train_loss)
        val_losses.append(val_loss)
        train_accs.append(train_acc)
        val_accs.append(val_acc)

        logger.info(f'Epoch: {epoch+1}/{args.epochs}, Train Loss: {train_loss:.4f}, Train Acc: {train_acc:.4f}, Val Loss: {val_loss:.4f}, Val Acc: {val_acc:.4f}')

        # 保存最佳模型
        if val_acc > best_val_acc:
            best_val_acc = val_acc
            best_epoch = epoch + 1
            patience_counter = 0

            # 保存模型
            torch.save({
                'epoch': epoch + 1,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'val_acc': val_acc,
                'val_loss': val_loss
            }, os.path.join(args.save_dir, 'best_model.pth'))

            # 单独保存特征提取器
            torch.save(model.resnet.state_dict(), os.path.join(args.save_dir, 'normal_kc_pretrained_resnet34.pth'))

            logger.info(f'保存最佳模型，验证准确率: {best_val_acc:.4f}')
        else:
            patience_counter += 1
            logger.info(f'验证准确率未提高，耐心计数: {patience_counter}/{args.early_stopping}')

            # 早停
            if patience_counter >= args.early_stopping:
                logger.info(f'早停触发，最佳验证准确率: {best_val_acc:.4f}，最佳轮次: {best_epoch}')
                break

    # 绘制训练曲线
    plot_metrics(
        train_losses=train_losses,
        val_losses=val_losses,
        train_accs=train_accs,
        val_accs=val_accs,
        save_path=os.path.join(args.save_dir, 'training_curves.png')
    )

    # 加载最佳模型
    checkpoint = torch.load(os.path.join(args.save_dir, 'best_model.pth'))
    model.load_state_dict(checkpoint['model_state_dict'])

    # 测试
    test_loss, test_acc, test_preds, test_labels = validate(
        model=model,
        dataloader=test_loader,
        criterion=criterion,
        device=device
    )

    # 计算其他指标
    precision = precision_score(test_labels, test_preds, average='weighted')
    recall = recall_score(test_labels, test_preds, average='weighted')
    f1 = f1_score(test_labels, test_preds, average='weighted')

    logger.info(f'测试损失: {test_loss:.4f}, 测试准确率: {test_acc:.4f}, 精确率: {precision:.4f}, 召回率: {recall:.4f}, F1分数: {f1:.4f}')

    # 绘制混淆矩阵
    plot_confusion_matrix(
        y_true=test_labels,
        y_pred=test_preds,
        save_path=os.path.join(args.save_dir, 'confusion_matrix.png')
    )

    # 保存测试结果
    with open(os.path.join(args.save_dir, 'test_results.txt'), 'w') as f:
        f.write(f'测试损失: {test_loss:.4f}\n')
        f.write(f'测试准确率: {test_acc:.4f}\n')
        f.write(f'精确率: {precision:.4f}\n')
        f.write(f'召回率: {recall:.4f}\n')
        f.write(f'F1分数: {f1:.4f}\n')

    logger.info(f'训练完成，结果已保存到 {args.save_dir}')

if __name__ == '__main__':
    main()

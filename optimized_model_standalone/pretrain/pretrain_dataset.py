#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
预训练数据集模块
用于加载和预处理角膜地形图数据
"""

import os
import pandas as pd
import numpy as np
from PIL import Image
import torch
from torch.utils.data import Dataset
from collections import Counter

class KeratoconusPretrainDataset(Dataset):
    """
    角膜地形图预训练数据集
    用于加载和预处理角膜地形图数据，支持二分类（正常vs圆锥角膜）
    """

    def __init__(self, csv_file, transform=None, binary_classification=True, base_dir='../'):
        """
        初始化数据集

        参数:
            csv_file (str): CSV文件路径，包含图像路径和标签
            transform (callable, optional): 应用于图像的转换
            binary_classification (bool): 是否只使用正常和圆锥角膜样本进行二分类
            base_dir (str): 图像路径的基础目录
        """
        self.data = pd.read_csv(csv_file)
        self.transform = transform
        self.binary_classification = binary_classification
        self.base_dir = base_dir

        # 处理标签
        # CSV文件中的标签列为'category'，值为'normal', 'early_kc', 'kc'
        # 对于二分类，我们只使用'normal'和'kc'样本
        if binary_classification:
            # 过滤掉early_kc样本
            self.data = self.data[self.data['category'].isin(['normal', 'kc'])]

            # 重新映射标签：normal -> 0, kc -> 1
            self.label_map = {'normal': 0, 'kc': 1}
        else:
            # 三分类：normal -> 0, early_kc -> 1, kc -> 2
            self.label_map = {'normal': 0, 'early_kc': 1, 'kc': 2}

        # 创建样本列表
        self.samples = []
        for _, row in self.data.iterrows():
            img_path = row['path']  # 使用'path'列作为图像路径
            label = self.label_map[row['category']]  # 使用'category'列作为标签
            self.samples.append((img_path, label))

    def __len__(self):
        """返回数据集大小"""
        return len(self.samples)

    def __getitem__(self, idx):
        """获取数据集中的一个样本"""
        img_path, label = self.samples[idx]

        # 构建完整的图像路径
        full_img_path = os.path.join(self.base_dir, img_path)

        # 加载图像
        try:
            img = Image.open(full_img_path).convert('RGB')
        except Exception as e:
            print(f"无法加载图像 {full_img_path}: {str(e)}")
            # 如果无法加载图像，返回一个黑色图像
            img = Image.new('RGB', (224, 224), color=0)

        # 应用转换
        if self.transform:
            img = self.transform(img)

        return img, label

    def get_class_counts(self):
        """获取各类别的样本数量"""
        labels = [label for _, label in self.samples]
        return Counter(labels)

    def get_class_weights(self):
        """计算类别权重，用于处理类别不平衡"""
        class_counts = self.get_class_counts()
        total_samples = len(self.samples)

        # 计算类别权重：样本总数 / (类别数 * 类别样本数)
        num_classes = len(class_counts)
        weights = {cls: total_samples / (num_classes * count) for cls, count in class_counts.items()}

        return weights


class KeratoconusPretrainDatasetWithAugmentation(KeratoconusPretrainDataset):
    """
    带有高级数据增强的角膜地形图预训练数据集
    """

    def __init__(self, csv_file, transform=None, binary_classification=True,
                 augment_factor=2, kc_augment_factor=2, base_dir='../'):
        """
        初始化数据集

        参数:
            csv_file (str): CSV文件路径，包含图像路径和标签
            transform (callable, optional): 应用于图像的转换
            binary_classification (bool): 是否只使用正常和圆锥角膜样本进行二分类
            augment_factor (int): 数据增强倍数
            kc_augment_factor (int): 圆锥角膜样本增强倍数
            base_dir (str): 图像路径的基础目录
        """
        super(KeratoconusPretrainDatasetWithAugmentation, self).__init__(
            csv_file=csv_file,
            transform=transform,
            binary_classification=binary_classification,
            base_dir=base_dir
        )

        self.augment_factor = augment_factor
        self.kc_augment_factor = kc_augment_factor

        # 增强样本
        self._augment_samples()

    def _augment_samples(self):
        """增强样本"""
        original_samples = self.samples.copy()
        augmented_samples = []

        for img_path, label in original_samples:
            # 对于圆锥角膜样本，使用更高的增强倍数
            if label == 1:  # kc
                factor = self.kc_augment_factor
            else:
                factor = self.augment_factor

            # 添加原始样本
            augmented_samples.append((img_path, label))

            # 添加增强样本
            for i in range(factor - 1):
                augmented_samples.append((img_path, label))

        self.samples = augmented_samples

    def __getitem__(self, idx):
        """获取数据集中的一个样本"""
        img_path, label = self.samples[idx]

        # 加载图像
        img = Image.open(img_path).convert('RGB')

        # 应用转换
        if self.transform:
            img = self.transform(img)

        return img, label

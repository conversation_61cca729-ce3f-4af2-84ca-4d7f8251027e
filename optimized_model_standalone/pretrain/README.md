# ResNet34预训练模型 - 使用说明

## 1. 概述

本项目提供了一套用于预训练ResNet34模型的代码，该模型将使用正常角膜和圆锥角膜样本进行二分类训练，然后可以用作特征提取器。预训练的目的是让模型学习角膜地形图的领域特定特征，从而在后续的三分类任务（正常、早期圆锥角膜、圆锥角膜）中提高性能，特别是提高圆锥角膜的识别准确率。

## 2. 文件结构

```
pretrain/
├── pretrain_resnet34.py         # 预训练ResNet34模型的主脚本
├── pretrain_dataset.py          # 数据加载和预处理模块
├── pretrain_model.py            # 模型定义模块
├── run_pretrain.sh              # 运行预训练的脚本
└── README.md                    # 本文档
```

## 3. 预训练流程

预训练流程包括以下步骤：

1. **数据准备**：从CSV文件中加载角膜地形图数据，只使用正常和圆锥角膜样本（不包含早期圆锥角膜样本）
2. **模型初始化**：创建ResNet34模型，可以选择是否使用ImageNet预训练权重
3. **模型训练**：使用交叉熵损失函数和Adam优化器训练模型，支持类别权重、数据增强和学习率调度器
4. **模型评估**：在验证集和测试集上评估模型性能，计算准确率、精确率、召回率和F1分数
5. **模型保存**：保存最佳模型和特征提取器部分，以便后续使用

## 4. 使用方法

### 4.1 环境准备

确保已安装以下依赖项：

```bash
pip install torch torchvision pandas numpy pillow scikit-learn matplotlib seaborn tqdm
```

### 4.2 数据准备

准备包含角膜地形图路径和标签的CSV文件，格式如下：

```
image_path,label
/path/to/image1.jpg,normal
/path/to/image2.jpg,kc
/path/to/image3.jpg,normal
...
```

其中，`label`列的值应为`normal`、`early_kc`或`kc`。预训练过程中，只会使用`normal`和`kc`样本。

### 4.3 运行预训练

使用`run_pretrain.sh`脚本运行预训练：

```bash
# 创建预训练目录
mkdir -p pretrain
cd pretrain

# 复制预训练代码
cp ../pretrain_*.py .
cp ../run_pretrain.sh .

# 设置执行权限
chmod +x run_pretrain.sh

# 使用默认参数运行
./run_pretrain.sh

# 使用自定义参数运行
./run_pretrain.sh --gpu_ids 0,1 --epochs 150 --batch_size 64 --lr 0.0005
```

### 4.4 监控训练进度

当使用nohup模式运行时，可以通过以下命令监控训练进度：

```bash
# 查看日志文件
tail -f logs/pretrain_<时间戳>_gpu<GPU_IDS>.log
```

### 4.5 使用预训练模型

预训练完成后，可以在`pretrained_models_<时间戳>_gpu<GPU_IDS>`目录中找到以下文件：

- `best_model.pth`：完整的预训练模型
- `normal_kc_pretrained_resnet34.pth`：只包含特征提取器部分的模型，可以用于后续任务

将`normal_kc_pretrained_resnet34.pth`复制到`../pretrained_models/`目录中，然后可以在后续的三分类任务中使用：

```bash
# 创建预训练模型目录
mkdir -p ../pretrained_models

# 复制预训练模型
cp pretrained_models_<时间戳>_gpu<GPU_IDS>/normal_kc_pretrained_resnet34.pth ../pretrained_models/
```

## 5. 参数配置

### 5.1 主要参数

```bash
# ----- 训练基本参数 -----
EPOCHS=100                # 训练轮数
BATCH_SIZE=32             # 批次大小
LR=0.001                  # 学习率
WEIGHT_DECAY=1e-4         # 权重衰减系数
DROPOUT=0.5               # Dropout比率
FEATURE_DIM=512           # 特征维度

# ----- 数据增强参数 -----
USE_AUGMENTATION=true     # 是否使用数据增强

# ----- 类别权重参数 -----
USE_CLASS_WEIGHTS=true    # 是否使用类别权重
KC_WEIGHT=2.0             # 圆锥角膜类别权重

# ----- 学习率调度器参数 -----
USE_LR_SCHEDULER=true     # 是否使用学习率调度器
SCHEDULER_TYPE="onecycle" # 学习率调度器类型: onecycle或plateau
```

### 5.2 命令行参数

```bash
python pretrain_resnet34.py --help
```

## 6. 预期结果

预训练模型应该能够在正常角膜和圆锥角膜的二分类任务上达到较高的准确率（>95%）。预训练过程会生成以下结果：

1. **训练曲线**：显示训练和验证的损失和准确率随时间的变化
2. **混淆矩阵**：显示模型在测试集上的预测结果
3. **测试结果**：包括测试准确率、精确率、召回率和F1分数

## 7. 后续使用

预训练完成后，可以使用`normal_kc_pretrained_resnet34.pth`作为特征提取器，在三分类任务（正常、早期圆锥角膜、圆锥角膜）中提高性能。具体步骤如下：

1. 将`normal_kc_pretrained_resnet34.pth`复制到`../pretrained_models/`目录中
2. 使用`balanced_task_sampling/run_pretrained_model.sh`脚本运行三分类任务
3. 观察模型性能，特别是圆锥角膜的识别准确率是否有所提高

## 8. 注意事项

1. 预训练过程可能需要较长时间，建议使用GPU加速训练
2. 如果GPU内存不足，可以减小批次大小（`BATCH_SIZE`）
3. 如果训练不稳定，可以尝试调整学习率（`LR`）或使用不同的学习率调度器
4. 预训练模型的质量对后续任务的性能有重要影响，建议尝试不同的预训练参数
5. 预训练数据应该只包含正常角膜和圆锥角膜样本，不应包含早期圆锥角膜样本

## 9. 故障排除

1. **内存不足**：减小批次大小或使用更少的GPU
2. **训练不稳定**：减小学习率或使用不同的学习率调度器
3. **过拟合**：增加权重衰减系数或Dropout比率
4. **欠拟合**：增加训练轮数或使用更大的模型
5. **类别不平衡**：调整类别权重或使用不同的采样策略

## 10. 参考资料

1. [ResNet论文](https://arxiv.org/abs/1512.03385)
2. [PyTorch文档](https://pytorch.org/docs/stable/index.html)
3. [迁移学习教程](https://pytorch.org/tutorials/beginner/transfer_learning_tutorial.html)
4. [类别不平衡问题解决方案](https://www.tensorflow.org/tutorials/structured_data/imbalanced_data)

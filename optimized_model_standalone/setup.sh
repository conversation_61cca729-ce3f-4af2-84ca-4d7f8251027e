#!/bin/bash

# 创建模型文件夹
mkdir -p models

# 复制基本模型文件
echo "Copying basic model files..."
cp -v ../models/feature_extractor.py models/ || cp -v /home/<USER>/protonet-maml/models/feature_extractor.py models/
cp -v ../models/protonet.py models/ || cp -v /home/<USER>/protonet-maml/models/protonet.py models/
cp -v ../models/maml.py models/ || cp -v /home/<USER>/protonet-maml/models/maml.py models/
cp -v ../models/losses.py models/ || cp -v /home/<USER>/protonet-maml/models/losses.py models/

# 尝试复制增强版模型文件（如果存在）
echo "Trying to copy enhanced model files..."
cp -v ../models/enhanced_feature_extractor.py models/ 2>/dev/null || cp -v /home/<USER>/protonet-maml/models/enhanced_feature_extractor.py models/ 2>/dev/null || echo "Enhanced feature extractor not found, skipping..."
cp -v ../models/enhanced_maml.py models/ 2>/dev/null || cp -v /home/<USER>/protonet-maml/models/enhanced_maml.py models/ 2>/dev/null || echo "Enhanced MAML not found, skipping..."
cp -v ../models/enhanced_losses.py models/ 2>/dev/null || cp -v /home/<USER>/protonet-maml/models/enhanced_losses.py models/ 2>/dev/null || echo "Enhanced losses not found, skipping..."

# 复制数据增强模块
echo "Copying data augmentation modules..."
cp -v ../datasets/augmentation.py datasets/ 2>/dev/null || cp -v /home/<USER>/protonet-maml/datasets/augmentation.py datasets/ 2>/dev/null || echo "Augmentation module not found, skipping..."
cp -v ../datasets/gpu_augmentation.py datasets/ 2>/dev/null || cp -v /home/<USER>/protonet-maml/datasets/gpu_augmentation.py datasets/ 2>/dev/null || echo "GPU augmentation module not found, skipping..."
cp -v ../datasets/enhanced_gpu_augmentation.py datasets/ 2>/dev/null || cp -v /home/<USER>/protonet-maml/datasets/enhanced_gpu_augmentation.py datasets/ 2>/dev/null || echo "Enhanced GPU augmentation module not found, skipping..."

# 创建__init__.py文件，使models成为一个包
echo "Creating __init__.py files..."
touch models/__init__.py
touch datasets/__init__.py

# 添加执行权限
echo "Adding execute permissions to scripts..."
chmod +x run.sh
chmod +x run_kc_improved.sh 2>/dev/null || echo "run_kc_improved.sh not found, skipping..."
chmod +x run_normalized.sh 2>/dev/null || echo "run_normalized.sh not found, skipping..."

echo "Setup completed successfully!"
echo "Please make sure your data is ready in ../split_result/ folder."
echo "You can now run the training using:"
echo "  ./run.sh --gpu_ids 0,1 --epochs 50"
echo "or"
echo "  ./run_kc_improved.sh --gpu_ids 0,1 --epochs 50"

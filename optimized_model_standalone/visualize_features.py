#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
可视化特征分布的脚本
使用t-SNE和UMAP降维方法可视化特征空间
"""

import os
import sys
import json
import argparse
import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import DataLoader
from sklearn.manifold import TSNE
from sklearn.decomposition import PCA
import matplotlib.pyplot as plt
import seaborn as sns
from tqdm import tqdm
import time
from collections import defaultdict

# 尝试导入UMAP
try:
    import umap
    has_umap = True
except ImportError:
    has_umap = False
    print("警告: 未安装UMAP，将只使用t-SNE进行可视化")

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入自定义模块
try:
    from datasets.keratoconus_dataset import KeratoconusDataset, get_transforms
    from datasets.balanced_task_sampler import BalancedTaskSampler
except ImportError:
    print("无法导入数据集模块，请确保已运行setup.sh")
    sys.exit(1)

try:
    from models.feature_extractor import KeratoconusFeatureExtractor
    from models.protonet import ProtoNet
    from models.maml import MAMLProtoNet
except ImportError:
    print("无法导入模型模块，请确保已运行setup.sh")
    sys.exit(1)

# 设置通用字体
plt.rcParams['font.family'] = 'DejaVu Sans'
plt.rcParams['axes.unicode_minus'] = True

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="可视化圆锥角膜分类模型的特征分布")

    # 基本参数
    parser.add_argument('--model_path', type=str, required=True, help='模型权重路径')
    parser.add_argument('--test_csv', type=str, default='../split_result/test_set.csv', help='测试集CSV文件路径')
    parser.add_argument('--output_dir', type=str, default='feature_visualization', help='输出目录')
    parser.add_argument('--device', type=str, default='cuda', help='设备: cuda或cpu')
    parser.add_argument('--seed', type=int, default=42, help='随机种子')
    parser.add_argument('--english', action='store_true', help='使用英文标签')

    # 模型参数
    parser.add_argument('--feature_dim', type=int, default=512, help='特征维度')
    parser.add_argument('--proto_counts', type=str, default='2,4,2', help='每个类别的原型数量')
    parser.add_argument('--inner_lr', type=float, default=0.1, help='内循环学习率')
    parser.add_argument('--inner_steps', type=int, default=3, help='内循环步数')

    # 任务采样参数
    parser.add_argument('--n_way', type=int, default=3, help='任务中的类别数')
    parser.add_argument('--n_shot', type=int, default=5, help='支持集中每个类别的样本数')
    parser.add_argument('--n_query', type=int, default=15, help='查询集中每个类别的样本数')
    parser.add_argument('--n_tasks', type=int, default=10, help='评估任务数')
    parser.add_argument('--use_balanced_task_sampler', action='store_true', help='使用平衡任务采样器')
    parser.add_argument('--kc_shot_multiplier', type=float, default=3.0, help='圆锥角膜样本在支持集中的倍数')
    parser.add_argument('--early_kc_shot_multiplier', type=float, default=1.5, help='早期圆锥角膜样本在支持集中的倍数')

    # 可视化参数
    parser.add_argument('--batch_size', type=int, default=32, help='批次大小')
    parser.add_argument('--num_workers', type=int, default=4, help='数据加载的工作线程数')
    parser.add_argument('--perplexity', type=int, default=30, help='t-SNE的困惑度参数')
    parser.add_argument('--n_iter', type=int, default=1000, help='t-SNE的迭代次数')
    parser.add_argument('--n_neighbors', type=int, default=15, help='UMAP的邻居数量')
    parser.add_argument('--min_dist', type=float, default=0.1, help='UMAP的最小距离')
    parser.add_argument('--show_prototypes', action='store_true', help='是否在可视化中显示原型')
    parser.add_argument('--show_centroids', action='store_true', help='是否在可视化中显示类别中心')
    parser.add_argument('--show_decision_boundary', action='store_true', help='是否显示决策边界')
    parser.add_argument('--interactive', action='store_true', help='是否生成交互式可视化')

    return parser.parse_args()

def set_seed(seed):
    """设置随机种子以确保结果可重复"""
    torch.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    np.random.seed(seed)
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False

def load_model(args, device):
    """加载模型"""
    # 解析原型配置
    proto_counts = [int(x) for x in args.proto_counts.split(',')]
    print(f"使用原型配置: {proto_counts}")

    # 创建特征提取器
    try:
        # 尝试导入增强版特征提取器
        from models.enhanced_feature_extractor import EnhancedKeratoconusFeatureExtractor
        print("使用增强版特征提取器")
        feature_extractor = EnhancedKeratoconusFeatureExtractor(
            pretrained=False,  # 加载权重时不需要预训练
            feature_dim=args.feature_dim
        ).to(device)
    except ImportError:
        # 如果导入失败，使用基础版特征提取器
        print("使用基础版特征提取器")
        feature_extractor = KeratoconusFeatureExtractor(
            pretrained=False,  # 加载权重时不需要预训练
            feature_dim=args.feature_dim
        ).to(device)

    # 创建ProtoNet模型
    protonet = ProtoNet(
        feature_extractor,
        n_classes=3,
        proto_counts=proto_counts,
        feature_dim=args.feature_dim
    ).to(device)

    # 创建MAML-ProtoNet模型
    maml_model = MAMLProtoNet(
        protonet,
        inner_lr=args.inner_lr,
        inner_steps=args.inner_steps
    ).to(device)

    # 加载模型权重
    print(f"加载模型权重: {args.model_path}")
    try:
        checkpoint = torch.load(args.model_path, map_location=device)
        # 检查是否是完整的检查点文件
        if 'model_state_dict' in checkpoint:
            print("检测到完整的检查点文件，加载model_state_dict")
            maml_model.load_state_dict(checkpoint['model_state_dict'])
        else:
            print("直接加载模型权重")
            maml_model.load_state_dict(checkpoint)
        print("模型加载成功")
    except Exception as e:
        print(f"加载模型时出错: {e}")
        sys.exit(1)

    return maml_model

def extract_features(model, test_dataset, args, device):
    """提取特征"""
    model.eval()

    # 创建任务采样器
    if args.use_balanced_task_sampler:
        print("使用平衡任务采样器进行特征提取")
        task_sampler = BalancedTaskSampler(
            test_dataset,
            n_way=args.n_way,
            n_shot=args.n_shot,
            n_query=args.n_query,
            kc_shot_multiplier=args.kc_shot_multiplier,
            early_kc_shot_multiplier=args.early_kc_shot_multiplier,
            num_workers=args.num_workers
        )
    else:
        # 使用默认任务采样器
        from datasets.keratoconus_dataset import get_task_sampler
        print("使用默认任务采样器进行特征提取")
        task_sampler = get_task_sampler(
            test_dataset,
            args.n_way,
            args.n_shot,
            args.n_query
        )

    # 收集特征、标签和预测
    all_features = []
    all_labels = []
    all_preds = []
    all_probs = []

    # 提取多个任务的特征
    for _ in tqdm(range(args.n_tasks), desc="提取特征"):
        # 采样一个任务
        task = task_sampler.sample_task()
        support_images, support_labels = task['support']
        query_images, query_labels = task['query']

        # 将数据移动到设备
        support_images = support_images.to(device)
        support_labels = support_labels.to(device)
        query_images = query_images.to(device)
        query_labels = query_labels.to(device)

        # 前向传播
        with torch.no_grad():
            # 适应当前任务
            adapted_model = model.adapt(support_images, support_labels)

            # 使用适应后的模型进行预测
            query_logits = adapted_model(query_images)

            # 提取特征
            query_features = adapted_model.get_features(query_images)
            support_features = adapted_model.get_features(support_images)

            # 计算预测和概率
            query_probs = F.softmax(query_logits, dim=1)
            _, preds = torch.max(query_logits, 1)

            # 收集特征、标签和预测
            all_features.append(query_features.cpu().numpy())
            all_labels.append(query_labels.cpu().numpy())
            all_preds.append(preds.cpu().numpy())
            all_probs.append(query_probs.cpu().numpy())

    # 合并所有任务的数据
    all_features = np.vstack(all_features)
    all_labels = np.concatenate(all_labels)
    all_preds = np.concatenate(all_preds)
    all_probs = np.vstack(all_probs)

    return all_features, all_labels, all_preds, all_probs

def visualize_tsne(features, labels, preds, class_names, output_path, perplexity=30, n_iter=1000):
    """使用t-SNE可视化特征"""
    print("使用t-SNE降维...")

    # 使用t-SNE降维
    tsne = TSNE(n_components=2, perplexity=perplexity, n_iter=n_iter, random_state=42)
    features_2d = tsne.fit_transform(features)

    # 创建DataFrame以便使用seaborn
    df = pd.DataFrame({
        'x': features_2d[:, 0],
        'y': features_2d[:, 1],
        'label': [class_names[l] for l in labels],
        'pred': [class_names[p] for p in preds],
        'correct': labels == preds
    })

    # 绘制t-SNE可视化图
    plt.figure(figsize=(12, 10))

    # 按类别绘制散点图
    sns.scatterplot(
        x='x', y='y',
        hue='label',
        style='correct',
        palette='deep',
        data=df,
        s=100,
        alpha=0.7
    )

    # 设置图表属性
    plt.title('t-SNE Visualization of Features')
    plt.xlabel('t-SNE Dimension 1')
    plt.ylabel('t-SNE Dimension 2')
    plt.legend(title='Class')

    # 保存图像
    plt.tight_layout()
    plt.savefig(output_path)
    plt.close()

    # 绘制按预测类别着色的图
    plt.figure(figsize=(12, 10))

    # 按预测类别绘制散点图
    sns.scatterplot(
        x='x', y='y',
        hue='pred',
        style='correct',
        palette='deep',
        data=df,
        s=100,
        alpha=0.7
    )

    # 设置图表属性
    plt.title('t-SNE Visualization of Features (Colored by Prediction)')
    plt.xlabel('t-SNE Dimension 1')
    plt.ylabel('t-SNE Dimension 2')
    plt.legend(title='Predicted Class')

    # 保存图像
    plt.tight_layout()
    plt.savefig(output_path.replace('.png', '_pred.png'))
    plt.close()

    # 保存t-SNE结果
    np.savez(
        output_path.replace('.png', '.npz'),
        features_2d=features_2d,
        labels=labels,
        preds=preds
    )

def visualize_umap(features, labels, preds, class_names, output_path, n_neighbors=15, min_dist=0.1):
    """使用UMAP可视化特征"""
    if not has_umap:
        print("未安装UMAP，跳过UMAP可视化")
        return

    print("使用UMAP降维...")

    # 使用UMAP降维
    reducer = umap.UMAP(n_neighbors=n_neighbors, min_dist=min_dist, random_state=42)
    features_2d = reducer.fit_transform(features)

    # 创建DataFrame以便使用seaborn
    df = pd.DataFrame({
        'x': features_2d[:, 0],
        'y': features_2d[:, 1],
        'label': [class_names[l] for l in labels],
        'pred': [class_names[p] for p in preds],
        'correct': labels == preds
    })

    # 绘制UMAP可视化图
    plt.figure(figsize=(12, 10))

    # 按类别绘制散点图
    sns.scatterplot(
        x='x', y='y',
        hue='label',
        style='correct',
        palette='deep',
        data=df,
        s=100,
        alpha=0.7
    )

    # 设置图表属性
    plt.title('UMAP Visualization of Features')
    plt.xlabel('UMAP Dimension 1')
    plt.ylabel('UMAP Dimension 2')
    plt.legend(title='Class')

    # 保存图像
    plt.tight_layout()
    plt.savefig(output_path)
    plt.close()

    # 绘制按预测类别着色的图
    plt.figure(figsize=(12, 10))

    # 按预测类别绘制散点图
    sns.scatterplot(
        x='x', y='y',
        hue='pred',
        style='correct',
        palette='deep',
        data=df,
        s=100,
        alpha=0.7
    )

    # 设置图表属性
    plt.title('UMAP Visualization of Features (Colored by Prediction)')
    plt.xlabel('UMAP Dimension 1')
    plt.ylabel('UMAP Dimension 2')
    plt.legend(title='Predicted Class')

    # 保存图像
    plt.tight_layout()
    plt.savefig(output_path.replace('.png', '_pred.png'))
    plt.close()

    # 保存UMAP结果
    np.savez(
        output_path.replace('.png', '.npz'),
        features_2d=features_2d,
        labels=labels,
        preds=preds
    )

def visualize_pca(features, labels, preds, class_names, output_path):
    """使用PCA可视化特征"""
    print("使用PCA降维...")

    # 使用PCA降维
    pca = PCA(n_components=2)
    features_2d = pca.fit_transform(features)

    # 创建DataFrame以便使用seaborn
    df = pd.DataFrame({
        'x': features_2d[:, 0],
        'y': features_2d[:, 1],
        'label': [class_names[l] for l in labels],
        'pred': [class_names[p] for p in preds],
        'correct': labels == preds
    })

    # 绘制PCA可视化图
    plt.figure(figsize=(12, 10))

    # 按类别绘制散点图
    sns.scatterplot(
        x='x', y='y',
        hue='label',
        style='correct',
        palette='deep',
        data=df,
        s=100,
        alpha=0.7
    )

    # 设置图表属性
    plt.title('PCA Visualization of Features')
    plt.xlabel('PC1 ({:.1f}%)'.format(pca.explained_variance_ratio_[0] * 100))
    plt.ylabel('PC2 ({:.1f}%)'.format(pca.explained_variance_ratio_[1] * 100))
    plt.legend(title='Class')

    # 保存图像
    plt.tight_layout()
    plt.savefig(output_path)
    plt.close()

    # 绘制按预测类别着色的图
    plt.figure(figsize=(12, 10))

    # 按预测类别绘制散点图
    sns.scatterplot(
        x='x', y='y',
        hue='pred',
        style='correct',
        palette='deep',
        data=df,
        s=100,
        alpha=0.7
    )

    # 设置图表属性
    plt.title('PCA Visualization of Features (Colored by Prediction)')
    plt.xlabel('PC1 ({:.1f}%)'.format(pca.explained_variance_ratio_[0] * 100))
    plt.ylabel('PC2 ({:.1f}%)'.format(pca.explained_variance_ratio_[1] * 100))
    plt.legend(title='Predicted Class')

    # 保存图像
    plt.tight_layout()
    plt.savefig(output_path.replace('.png', '_pred.png'))
    plt.close()

    # 保存PCA结果
    np.savez(
        output_path.replace('.png', '.npz'),
        features_2d=features_2d,
        labels=labels,
        preds=preds,
        explained_variance_ratio=pca.explained_variance_ratio_
    )

def main():
    """主函数"""
    # 解析命令行参数
    args = parse_args()

    # 设置随机种子
    set_seed(args.seed)

    # 设置设备
    device = torch.device(args.device if torch.cuda.is_available() and args.device == 'cuda' else 'cpu')
    print(f"使用设备: {device}")

    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)

    # 加载测试数据集
    test_transform = get_transforms('test')
    test_dataset = KeratoconusDataset(args.test_csv, transform=test_transform)
    print(f"测试集大小: {len(test_dataset)}")

    # 加载模型
    model = load_model(args, device)

    # 提取特征
    print("开始提取特征...")
    features, labels, preds, probs = extract_features(model, test_dataset, args, device)

    # 设置类别名称 - 始终使用英文
    class_names = ['normal', 'e-kc', 'kc']

    # 使用t-SNE可视化特征
    tsne_path = os.path.join(args.output_dir, 'tsne_visualization.png')
    visualize_tsne(features, labels, preds, class_names, tsne_path,
                  perplexity=args.perplexity, n_iter=args.n_iter)

    # 使用UMAP可视化特征
    if has_umap:
        umap_path = os.path.join(args.output_dir, 'umap_visualization.png')
        visualize_umap(features, labels, preds, class_names, umap_path,
                      n_neighbors=args.n_neighbors, min_dist=args.min_dist)

    # 使用PCA可视化特征
    pca_path = os.path.join(args.output_dir, 'pca_visualization.png')
    visualize_pca(features, labels, preds, class_names, pca_path)

    print(f"可视化完成，结果已保存到 {args.output_dir}")

if __name__ == "__main__":
    main()

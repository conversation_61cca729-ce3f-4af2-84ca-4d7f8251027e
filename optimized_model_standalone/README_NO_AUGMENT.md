# 圆锥角膜分类系统 - 无数据增强版本

## 1. 项目概述

本文档详细介绍了圆锥角膜分类系统的无数据增强版本。该版本基于MAML-ProtoNet混合架构，完全禁用了所有数据增强策略，用于研究数据增强对模型性能的影响，以及评估模型在原始数据上的基础性能。

### 1.1 无数据增强版本的特点

- **禁用所有数据增强**：完全禁用了早期圆锥角膜样本增强、圆锥角膜样本增强、高级数据增强(Mixup和CutMix)等所有数据增强功能
- **保留核心功能**：保留了MAML-ProtoNet混合架构、多原型表示、平衡任务采样和对比学习等核心功能
- **类别权重优化**：使用经过调优的类别权重（早期圆锥角膜:7.883194，圆锥角膜:6.611334）
- **支持多种运行模式**：支持直接运行和后台运行(nohup)两种模式

## 2. 技术架构

### 2.1 MAML-ProtoNet混合架构

无数据增强版本基于MAML-ProtoNet混合架构，结合了两种元学习方法的优势：

- **MAML (Model-Agnostic Meta-Learning)**：
  - 提供快速适应能力，通过梯度下降在支持集上更新模型参数
  - 内循环学习率：0.347389（经过调优）
  - 内循环步数：5

- **ProtoNet (Prototype Network)**：
  - 通过原型表示类别，提高样本利用率和计算效率
  - 多原型表示：正常(2个)、早期圆锥角膜(4个)、圆锥角膜(2个)

### 2.2 平衡任务采样策略

为解决类别不平衡问题，无数据增强版本实现了平衡任务采样器：

- **确保类别平衡**：每个元学习任务都包含所有三个类别（正常、早期圆锥角膜、圆锥角膜）
- **少数类过采样**：
  - 圆锥角膜样本倍数：3.0
  - 早期圆锥角膜样本倍数：1.5
- **独立测试采样**：使用单独的测试集任务采样器，确保评估的公平性

### 2.3 损失函数设计

- **Focal Loss**：减轻类别不平衡影响，关注难分类样本（gamma=2.0）
- **类别加权**：为少数类分配更高权重
  - 早期圆锥角膜权重：7.883194
  - 圆锥角膜权重：6.611334
- **对比学习**：增强类间差异和类内相似性
  - 温度参数：0.032777
  - 硬样本挖掘比例：0.744846
  - 对比学习权重：0.553402
  - 早期圆锥角膜与正常样本对比权重：2.0
  - 圆锥角膜与正常样本对比权重：1.0

### 2.4 优化策略

- **双层优化**：
  - 内循环（任务适应）：学习率 0.347389，5步更新
  - 外循环（元学习）：学习率 0.0002
- **学习率调度**：使用ReduceLROnPlateau策略
  - 衰减因子：0.5
  - 耐心值：2
  - 最小学习率：1e-6
- **早停机制**：连续3个epoch无改进后停止训练

## 3. 禁用的数据增强策略

在无数据增强版本中，以下所有数据增强策略均被禁用：

```bash
# ----- 数据增强参数 - 全部禁用 -----
EARLY_KC_AUGMENT=false    # 禁用早期圆锥角膜样本增强
KC_AUGMENT=false          # 禁用圆锥角膜样本增强
ADVANCED_AUGMENT=false    # 禁用高级数据增强(Mixup和CutMix)
EARLY_KC_SPECIFIC_AUGMENT=false # 禁用早期圆锥角膜样本特定增强
USE_ENHANCED_GPU_AUGMENT=false  # 禁用增强版GPU数据增强
MIXUP_ALPHA=0.0           # 设置为0以禁用Mixup
CUTMIX_PROB=0.0           # 设置为0以禁用CutMix
AUGMENT_FACTOR=1          # 设置为1以禁用样本增强
KC_AUGMENT_FACTOR=1       # 设置为1以禁用KC样本增强
```

## 4. 使用方法

### 4.1 基本用法

```bash
# 使用默认参数运行无数据增强版本
bash run_no_augment.sh
```

### 4.2 自定义参数

可以通过环境变量自定义关键参数：

```bash
# 自定义GPU和训练轮数
GPU_IDS="0,1" EPOCHS=100 bash run_no_augment.sh

# 自定义输出目录
OUTPUT_DIR="my_results_no_augment" bash run_no_augment.sh

# 自定义内循环学习率和任务数
INNER_LR=0.4 TASKS_PER_EPOCH=50 bash run_no_augment.sh

# 自定义类别权重
EARLY_KC_WEIGHT=10.0 KC_WEIGHT=5.0 bash run_no_augment.sh
```

### 4.3 运行模式

```bash
# 后台运行（默认）
RUN_MODE="nohup" bash run_no_augment.sh

# 直接运行
RUN_MODE="direct" bash run_no_augment.sh
```

### 4.4 监控训练进度

当使用nohup模式运行时，可以通过以下命令监控训练进度：

```bash
# 查看日志文件
tail -f results_no_augment/training_no_augment_<时间戳>_gpu<GPU_IDS>.log
```

## 5. 主要参数说明

| 参数类别 | 参数名称 | 默认值 | 说明 |
|---------|---------|-------|------|
| **运行模式** | RUN_MODE | nohup | 运行模式：nohup(后台)或direct(直接) |
| **硬件** | GPU_IDS | 3 | 使用的GPU ID |
| | NUM_WORKERS | 4 | 数据加载的工作线程数 |
| **训练基本参数** | EPOCHS | 50 | 训练轮数 |
| | LR | 0.0002 | 外循环学习率 |
| | FEATURE_DIM | 512 | 特征维度 |
| | DROPOUT | 0.5 | Dropout比率 |
| **MAML参数** | INNER_STEPS | 5 | 内循环步数 |
| | INNER_LR | 0.347389 | 内循环学习率 |
| | TASKS_PER_EPOCH | 30 | 每个epoch的任务数 |
| | META_BATCH_SIZE | 8 | 元批次大小 |
| **模型架构** | PROTO_COUNTS | 2,4,2 | 每个类别的原型数量 |
| | PRETRAINED | true | 是否使用预训练特征提取器 |
| **类别权重** | EARLY_KC_WEIGHT | 7.883194 | 早期圆锥角膜样本权重 |
| | KC_WEIGHT | 6.611334 | 圆锥角膜样本权重 |
| | FOCAL_GAMMA | 2.0 | Focal Loss的gamma参数 |
| **对比学习** | USE_CONTRASTIVE | true | 是否使用对比学习 |
| | TEMPERATURE | 0.032777 | 温度参数 |
| | HARD_MINING_RATIO | 0.744846 | 硬样本挖掘比例 |
| | CONTRASTIVE_WEIGHT | 0.553402 | 对比学习权重 |
| | EARLY_NORMAL_WEIGHT | 2.0 | 早期圆锥角膜与正常样本对比权重 |
| | KC_NORMAL_WEIGHT | 1.0 | 圆锥角膜与正常样本对比权重 |
| **任务采样** | USE_BALANCED_TASK_SAMPLER | true | 是否使用平衡任务采样器 |
| | KC_SHOT_MULTIPLIER | 3.0 | 圆锥角膜样本在支持集中的倍数 |
| | EARLY_KC_SHOT_MULTIPLIER | 1.5 | 早期圆锥角膜样本在支持集中的倍数 |
| **验证和早停** | VAL_FREQUENCY | 1 | 验证频率（每隔多少个epoch） |
| | EARLY_STOPPING | 3 | 早停耐心值 |

## 6. 与数据增强版本的对比

无数据增强版本与标准数据增强版本的主要区别：

1. **样本多样性**：无数据增强版本仅使用原始样本，而标准版本通过数据增强扩充了样本多样性
2. **过拟合风险**：无数据增强版本可能面临更高的过拟合风险，特别是在小数据集上
3. **训练稳定性**：无数据增强版本的训练过程可能不如标准版本稳定
4. **泛化能力**：无数据增强版本的泛化能力可能不如标准版本

## 7. 适用场景

无数据增强版本适用于以下场景：

1. **基线性能评估**：评估模型在无任何数据增强情况下的基础性能
2. **数据增强效果研究**：与标准版本对比，研究数据增强对模型性能的影响
3. **数据质量评估**：评估原始数据的质量和信息量
4. **快速原型验证**：在开发初期快速验证模型架构

## 8. 日志和结果

训练日志和结果将保存在`results_no_augment`目录下（除非通过`OUTPUT_DIR`参数自定义）：

- 训练日志：`training_no_augment_[时间戳]_gpu[GPU_ID].log`
- 模型检查点：保存最佳模型和最后一个epoch的模型
- 性能指标：准确率、混淆矩阵、F1分数等

## 9. 注意事项

1. 无数据增强版本可能需要更多训练轮数才能达到与标准版本相当的性能
2. 在小数据集上，建议适当增加正则化强度（如增大`WEIGHT_DECAY`值）
3. 如果遇到过拟合问题，可以尝试增加`DROPOUT`值或减少`FEATURE_DIM`
4. 对于特别小的数据集，可能需要调整`EARLY_STOPPING`值以避免过早停止训练
5. 本版本使用的参数已经过调优，如果需要修改，建议小范围调整

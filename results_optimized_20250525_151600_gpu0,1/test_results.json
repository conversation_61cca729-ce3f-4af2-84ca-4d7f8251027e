{"test_acc": 50.27777777777778, "class_accuracies": {"kc": 64.16666666666667, "e-kc": 86.66666666666667, "normal": 0.0}, "confusion_matrix": [[0, 120, 0], [0, 104, 16], [0, 43, 77]], "best_epoch": 2, "best_val_acc": 0.5111111111111111, "history": {"train_loss": [0.5781311174233754, -0.08758646647135417, -0.15443094968795776, -0.15683043201764424, -0.06887751817703247, 0.1682634989420573, -0.1689664403597514, -0.17627297242482504], "train_acc": [0.5044444444444444, 0.5540740740740738, 0.5599999999999999, 0.5666666666666665, 0.5651851851851852, 0.5111111111111111, 0.5748148148148148, 0.5637037037037035], "val_loss": [1.9403603196144104, 2.212617373466492, 1.563446056842804, 1.941365694999695, 2.0588461995124816, 2.0343077421188354, 1.9772636413574218, 1.7096229314804077], "val_acc": [0.4222222222222222, 0.4222222222222222, 0.5111111111111111, 0.4111111111111111, 0.4333333333333333, 0.4222222222222222, 0.4444444444444445, 0.45555555555555555], "contrastive_loss": [-1.8279320955897371, -2.8145373264948526, -2.8635350704193114, -2.851682448387146, -2.71513090133667, -2.661354653040568, -2.799676863352458, -2.9141036748886107], "learning_rates": [0.0002, 0.0002, 0.0002, 0.0002, 0.0002, 0.0002, 0.0001, 0.0001], "batch_learning_rates": []}, "args": {"train_csv": "/home/<USER>/balanced_task_sampling/split_result/train_set.csv", "val_csv": "/home/<USER>/balanced_task_sampling/split_result/val_set.csv", "test_csv": "/home/<USER>/balanced_task_sampling/split_result/test_set.csv", "save_dir": "results_optimized_20250525_151600_gpu0,1", "device": "cuda", "proto_counts": "2,6,3", "feature_dim": 512, "inner_lr": 0.3, "inner_steps": 2, "pretrained": true, "dropout": 0.5, "use_normalized_features": false, "normalize_scale": 10.0, "epochs": 30, "lr": 0.0002, "weight_decay": 0.0005, "n_way": 3, "n_shot": 5, "n_query": 15, "tasks_per_epoch": 30, "meta_batch_size": 8, "val_frequency": 1, "early_stopping": 5, "use_lr_scheduler": true, "scheduler_type": "plateau", "max_lr": 0.001, "div_factor": 25.0, "final_div_factor": 10000.0, "pct_start": 0.1, "plateau_factor": 0.5, "plateau_patience": 2, "min_lr": 1e-06, "record_batch_lr": false, "no_augmentation": true, "early_kc_augment": false, "kc_augment": false, "advanced_augment": false, "mixup_alpha": 0.0, "cutmix_prob": 0.0, "early_kc_specific_augment": false, "augment_factor": 1, "kc_augment_factor": 1, "use_enhanced_gpu_augment": false, "early_kc_weight": 7.0, "kc_weight": 5.0, "focal_gamma": 2.0, "use_contrastive": true, "contrastive_weight": 0.5, "temperature": 0.07, "hard_mining_ratio": 0.7, "early_normal_weight": 3.0, "kc_normal_weight": 1.0, "use_balanced_task_sampler": true, "kc_shot_multiplier": 3.0, "early_kc_shot_multiplier": 1.5, "seed": 42, "num_workers": 4, "use_separate_test_sampler": true}}
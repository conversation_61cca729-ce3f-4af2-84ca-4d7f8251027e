#!/bin/bash

#############################################################
# 特征分离模块批量实验脚本
# 自动运行多种配置的对比实验，评估特征分离模块的效果
#############################################################

echo "🚀 开始特征分离模块批量实验..."
echo "=================================================="

# 设置基础参数
GPU_ID="0"
EPOCHS=30
BASE_OUTPUT_DIR="experiments_feature_separation_$(date +%Y%m%d_%H%M%S)"

# 创建实验目录
mkdir -p $BASE_OUTPUT_DIR
mkdir -p logs

echo "实验目录: $BASE_OUTPUT_DIR"
echo "使用GPU: $GPU_ID"
echo "训练轮数: $EPOCHS"
echo ""

# 实验配置列表
declare -a experiments=(
    "baseline:false:false:false:0.0"
    "basic_separation:true:false:false:0.1"
    "enhanced_separation:true:true:false:0.1"
    "adaptive_separation:true:false:true:0.1"
    "separation_weight_005:true:false:false:0.05"
    "separation_weight_02:true:false:false:0.2"
)

# 函数：运行单个实验
run_experiment() {
    local exp_name=$1
    local use_separation=$2
    local use_enhanced=$3
    local use_adaptive=$4
    local separation_weight=$5
    
    echo "🔬 开始实验: $exp_name"
    echo "  - 特征分离: $use_separation"
    echo "  - 增强版: $use_enhanced"
    echo "  - 自适应: $use_adaptive"
    echo "  - 分离权重: $separation_weight"
    
    # 构建实验目录
    exp_dir="${BASE_OUTPUT_DIR}/${exp_name}"
    mkdir -p $exp_dir
    
    # 构建日志文件
    log_file="logs/${exp_name}_$(date +%Y%m%d_%H%M%S).log"
    
    # 构建命令
    cmd="./run_optimized_with_feature_separation.sh \
        --run_mode direct \
        --gpu_ids $GPU_ID \
        --epochs $EPOCHS \
        --use_feature_separation $use_separation \
        --use_enhanced_separation $use_enhanced \
        --use_adaptive_separation $use_adaptive \
        --separation_weight $separation_weight"
    
    echo "  命令: $cmd"
    echo "  日志: $log_file"
    
    # 执行实验
    echo "  开始时间: $(date)"
    eval $cmd > $log_file 2>&1
    exit_code=$?
    echo "  结束时间: $(date)"
    echo "  退出码: $exit_code"
    
    if [ $exit_code -eq 0 ]; then
        echo "  ✅ 实验 $exp_name 完成"
    else
        echo "  ❌ 实验 $exp_name 失败"
    fi
    
    echo ""
}

# 函数：显示实验进度
show_progress() {
    local current=$1
    local total=$2
    local exp_name=$3
    
    echo "=================================================="
    echo "📊 实验进度: $current/$total"
    echo "当前实验: $exp_name"
    echo "=================================================="
}

# 运行所有实验
total_experiments=${#experiments[@]}
current_experiment=0

for experiment in "${experiments[@]}"; do
    current_experiment=$((current_experiment + 1))
    
    # 解析实验配置
    IFS=':' read -r exp_name use_separation use_enhanced use_adaptive separation_weight <<< "$experiment"
    
    # 显示进度
    show_progress $current_experiment $total_experiments $exp_name
    
    # 运行实验
    run_experiment $exp_name $use_separation $use_enhanced $use_adaptive $separation_weight
    
    # 等待一段时间，避免GPU过热
    if [ $current_experiment -lt $total_experiments ]; then
        echo "⏳ 等待30秒后开始下一个实验..."
        sleep 30
    fi
done

echo "=================================================="
echo "🎉 所有实验完成！"
echo "=================================================="

# 生成实验总结
summary_file="${BASE_OUTPUT_DIR}/experiment_summary.txt"
echo "实验总结报告" > $summary_file
echo "生成时间: $(date)" >> $summary_file
echo "实验目录: $BASE_OUTPUT_DIR" >> $summary_file
echo "使用GPU: $GPU_ID" >> $summary_file
echo "训练轮数: $EPOCHS" >> $summary_file
echo "" >> $summary_file

echo "实验配置列表:" >> $summary_file
for experiment in "${experiments[@]}"; do
    IFS=':' read -r exp_name use_separation use_enhanced use_adaptive separation_weight <<< "$experiment"
    echo "  $exp_name:" >> $summary_file
    echo "    - 特征分离: $use_separation" >> $summary_file
    echo "    - 增强版: $use_enhanced" >> $summary_file
    echo "    - 自适应: $use_adaptive" >> $summary_file
    echo "    - 分离权重: $separation_weight" >> $summary_file
    echo "" >> $summary_file
done

echo "📋 实验总结已保存到: $summary_file"

# 显示结果目录结构
echo ""
echo "📁 实验结果目录结构:"
ls -la $BASE_OUTPUT_DIR/

echo ""
echo "📊 实验对比分析建议:"
echo "1. 查看各实验的准确率对比"
echo "2. 重点关注KC和E-KC类别的分类性能"
echo "3. 分析特征分离损失的收敛情况"
echo "4. 比较不同分离权重的效果"

echo ""
echo "🔍 查看实验结果的命令:"
echo "  # 查看所有实验日志"
echo "  ls logs/*feature_separation*"
echo ""
echo "  # 查看特定实验的详细日志"
echo "  tail -f logs/basic_separation_*.log"
echo ""
echo "  # 比较不同实验的配置"
echo "  cat ${BASE_OUTPUT_DIR}/*/experiment_config.txt"

echo ""
echo "✨ 特征分离模块批量实验完成！"
echo "   基线实验: baseline (无特征分离)"
echo "   基础分离: basic_separation"
echo "   增强分离: enhanced_separation (多尺度)"
echo "   自适应分离: adaptive_separation"
echo "   权重对比: separation_weight_005, separation_weight_02"
echo ""
echo "🎯 预期效果: 特征分离模块应该提高E-KC和KC之间的区分能力"

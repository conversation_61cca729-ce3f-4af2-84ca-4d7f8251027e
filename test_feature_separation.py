#!/usr/bin/env python3
"""
测试特征分离模块的脚本

该脚本用于验证特征分离模块是否正常工作，
包括模块的前向传播、损失计算等功能。
"""

import torch
import torch.nn as nn
import numpy as np
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_feature_separation_module():
    """测试基础特征分离模块"""
    print("=" * 50)
    print("测试基础特征分离模块")
    print("=" * 50)
    
    try:
        from models.feature_separation import FeatureSeparationModule
        
        # 创建模块
        module = FeatureSeparationModule(
            feature_dim=512,
            separation_dim=128,
            dropout_rate=0.3,
            use_attention=True
        )
        
        # 创建测试数据
        batch_size = 8
        feature_dim = 512
        x = torch.randn(batch_size, feature_dim)
        labels = torch.tensor([0, 1, 2, 1, 2, 0, 1, 2])  # Normal, E-KC, <PERSON>
        
        print(f"输入特征形状: {x.shape}")
        print(f"标签: {labels}")
        
        # 前向传播
        enhanced_features, separation_info = module(x, labels)
        
        print(f"增强特征形状: {enhanced_features.shape}")
        print(f"分离信息键: {list(separation_info.keys())}")
        
        # 计算分离损失
        separation_loss = module.compute_separation_loss(
            separation_info['ekc_features'],
            separation_info['kc_features'],
            labels
        )
        
        print(f"分离损失: {separation_loss.item():.4f}")
        
        # 检查梯度
        loss = enhanced_features.sum() + separation_loss
        loss.backward()
        
        print("✓ 基础特征分离模块测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 基础特征分离模块测试失败: {e}")
        return False


def test_adaptive_feature_separation_module():
    """测试自适应特征分离模块"""
    print("\n" + "=" * 50)
    print("测试自适应特征分离模块")
    print("=" * 50)
    
    try:
        from models.feature_separation import AdaptiveFeatureSeparationModule
        
        # 创建模块
        module = AdaptiveFeatureSeparationModule(
            feature_dim=512,
            separation_dim=128,
            dropout_rate=0.3,
            use_attention=True,
            adaptive_strength=1.0
        )
        
        # 创建测试数据
        batch_size = 8
        feature_dim = 512
        x = torch.randn(batch_size, feature_dim)
        labels = torch.tensor([0, 1, 2, 1, 2, 0, 1, 2])
        
        print(f"输入特征形状: {x.shape}")
        
        # 训练模式前向传播
        module.train()
        enhanced_features, separation_info = module(x, labels)
        
        print(f"增强特征形状: {enhanced_features.shape}")
        print(f"自适应因子: {separation_info['adaptive_factor']:.4f}")
        print(f"训练步数: {separation_info['training_step']}")
        
        # 评估模式前向传播
        module.eval()
        enhanced_features_eval, separation_info_eval = module(x, labels)
        
        print(f"评估模式自适应因子: {separation_info_eval['adaptive_factor']:.4f}")
        
        print("✓ 自适应特征分离模块测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 自适应特征分离模块测试失败: {e}")
        return False


def test_feature_separation_extractor():
    """测试特征分离提取器"""
    print("\n" + "=" * 50)
    print("测试特征分离提取器")
    print("=" * 50)
    
    try:
        from models.feature_separation_extractor import FeatureSeparationExtractor
        
        # 创建提取器
        extractor = FeatureSeparationExtractor(
            pretrained=False,  # 测试时不使用预训练
            feature_dim=512,
            separation_dim=128,
            dropout_rate=0.3,
            use_separation=True,
            use_adaptive=False,
            use_attention=True,
            separation_weight=0.1
        )
        
        # 创建测试数据
        batch_size = 4
        x = torch.randn(batch_size, 3, 224, 224)  # 图像数据
        labels = torch.tensor([0, 1, 2, 1])
        
        print(f"输入图像形状: {x.shape}")
        print(f"标签: {labels}")
        
        # 前向传播
        features = extractor(x, labels)
        print(f"输出特征形状: {features.shape}")
        
        # 获取分离信息
        features_with_info, separation_info = extractor(x, labels, return_separation_info=True)
        print(f"分离信息键: {list(separation_info.keys()) if separation_info else 'None'}")
        
        # 计算分离损失
        separation_loss = extractor.compute_separation_loss(x, labels)
        print(f"分离损失: {separation_loss.item():.4f}")
        
        print("✓ 特征分离提取器测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 特征分离提取器测试失败: {e}")
        return False


def test_enhanced_feature_separation_extractor():
    """测试增强版特征分离提取器"""
    print("\n" + "=" * 50)
    print("测试增强版特征分离提取器")
    print("=" * 50)
    
    try:
        from models.feature_separation_extractor import EnhancedFeatureSeparationExtractor
        
        # 创建提取器
        extractor = EnhancedFeatureSeparationExtractor(
            pretrained=False,
            feature_dim=512,
            separation_dim=128,
            dropout_rate=0.3,
            use_separation=True,
            use_adaptive=True,
            use_attention=True,
            separation_weight=0.1,
            use_multiscale=True
        )
        
        # 创建测试数据
        batch_size = 4
        x = torch.randn(batch_size, 3, 224, 224)
        labels = torch.tensor([0, 1, 2, 1])
        
        print(f"输入图像形状: {x.shape}")
        
        # 前向传播
        features = extractor(x, labels)
        print(f"输出特征形状: {features.shape}")
        
        # 计算总分离损失
        total_loss = extractor.compute_total_separation_loss(x, labels)
        print(f"总分离损失: {total_loss.item():.4f}")
        
        print("✓ 增强版特征分离提取器测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 增强版特征分离提取器测试失败: {e}")
        return False


def test_enhanced_feature_extractor_with_separation():
    """测试集成了特征分离的增强版特征提取器"""
    print("\n" + "=" * 50)
    print("测试集成特征分离的增强版特征提取器")
    print("=" * 50)
    
    try:
        from models.enhanced_feature_extractor import EnhancedKeratoconusFeatureExtractor
        
        # 创建提取器（启用特征分离）
        extractor = EnhancedKeratoconusFeatureExtractor(
            pretrained=False,
            feature_dim=512,
            dropout_rate=0.3,
            use_feature_separation=True,
            separation_dim=128
        )
        
        # 创建测试数据
        batch_size = 4
        x = torch.randn(batch_size, 3, 224, 224)
        labels = torch.tensor([0, 1, 2, 1])
        
        print(f"输入图像形状: {x.shape}")
        
        # 前向传播
        features = extractor(x, labels)
        print(f"输出特征形状: {features.shape}")
        
        # 获取分离信息
        features_with_info, separation_info = extractor(x, labels, return_separation_info=True)
        print(f"分离信息可用: {separation_info is not None}")
        
        # 计算分离损失
        separation_loss = extractor.compute_separation_loss(x, labels)
        print(f"分离损失: {separation_loss.item():.4f}")
        
        print("✓ 集成特征分离的增强版特征提取器测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 集成特征分离的增强版特征提取器测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("开始测试特征分离模块...")
    
    # 设置随机种子
    torch.manual_seed(42)
    np.random.seed(42)
    
    # 运行所有测试
    tests = [
        test_feature_separation_module,
        test_adaptive_feature_separation_module,
        test_feature_separation_extractor,
        test_enhanced_feature_separation_extractor,
        test_enhanced_feature_extractor_with_separation
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        if test_func():
            passed += 1
    
    print("\n" + "=" * 50)
    print("测试总结")
    print("=" * 50)
    print(f"通过测试: {passed}/{total}")
    
    if passed == total:
        print("🎉 所有测试通过！特征分离模块工作正常。")
        return True
    else:
        print("❌ 部分测试失败，请检查错误信息。")
        return False


if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)

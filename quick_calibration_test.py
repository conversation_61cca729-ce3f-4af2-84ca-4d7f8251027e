#!/usr/bin/env python3
"""
快速校准测试

使用现有模型快速测试校准功能。
"""

import os
import sys
import torch
import torch.nn.functional as F
import numpy as np
import json

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_calibration_on_existing_model():
    """在现有模型上测试校准功能"""
    print("🔬 快速校准测试")
    print("=" * 50)
    
    # 查找现有的测试结果
    test_dirs = [
        "test_feature_separation_final",
        "test_feature_separation",
        "checkpoints"
    ]
    
    test_results_file = None
    for test_dir in test_dirs:
        potential_file = os.path.join(test_dir, "test_results.json")
        if os.path.exists(potential_file):
            test_results_file = potential_file
            break
    
    if not test_results_file:
        print("❌ 未找到现有的测试结果文件")
        print("请先运行一个训练实验生成测试结果")
        return False
    
    print(f"使用测试结果文件: {test_results_file}")
    
    # 加载测试结果
    with open(test_results_file, 'r') as f:
        results = json.load(f)
    
    print(f"原始测试结果:")
    print(f"  总体准确率: {results.get('test_acc', 0)*100:.2f}%")
    if 'class_accuracies' in results:
        for class_name, acc in results['class_accuracies'].items():
            print(f"  {class_name}: {acc:.2f}%")
    
    # 模拟一些预测概率和标签（基于混淆矩阵）
    if 'confusion_matrix' in results:
        confusion_matrix = np.array(results['confusion_matrix'])
        print(f"\n混淆矩阵:")
        print(confusion_matrix)
        
        # 从混淆矩阵生成模拟的预测概率
        n_samples = 100
        probs, labels = generate_probs_from_confusion_matrix(confusion_matrix, n_samples)
        
        print(f"\n生成了 {n_samples} 个模拟样本进行校准测试")
        
        # 测试不同的校准方法
        test_calibration_methods(probs, labels)
        
        return True
    else:
        print("❌ 测试结果中没有混淆矩阵信息")
        return False


def generate_probs_from_confusion_matrix(confusion_matrix, n_samples=100):
    """从混淆矩阵生成模拟的预测概率和标签"""
    n_classes = confusion_matrix.shape[0]
    
    # 计算每个类别的样本数量（按比例）
    total_predictions = confusion_matrix.sum()
    class_counts = confusion_matrix.sum(axis=1)
    class_proportions = class_counts / total_predictions
    
    # 生成标签
    labels = []
    for class_idx in range(n_classes):
        n_class_samples = int(class_proportions[class_idx] * n_samples)
        labels.extend([class_idx] * n_class_samples)
    
    # 补齐到n_samples
    while len(labels) < n_samples:
        labels.append(np.random.randint(0, n_classes))
    labels = labels[:n_samples]
    
    # 生成概率
    probs = []
    for true_label in labels:
        # 基于混淆矩阵的该行生成概率
        row = confusion_matrix[true_label]
        if row.sum() > 0:
            # 归一化为概率
            base_probs = row / row.sum()
            # 添加一些噪声
            noise = np.random.normal(0, 0.1, n_classes)
            prob = base_probs + noise
            prob = np.maximum(prob, 0.01)  # 确保概率为正
            prob = prob / prob.sum()  # 重新归一化
        else:
            # 如果该行全为0，生成随机概率
            prob = np.random.dirichlet([1] * n_classes)
        
        probs.append(prob)
    
    return torch.tensor(probs, dtype=torch.float32), torch.tensor(labels, dtype=torch.long)


def test_calibration_methods(probs, labels):
    """测试不同的校准方法"""
    from models.calibration import create_calibrator
    
    print("\n📊 测试校准方法:")
    print("-" * 30)
    
    # 计算原始性能
    original_preds = torch.argmax(probs, dim=1)
    original_acc = (original_preds == labels).float().mean().item() * 100
    
    # 计算原始类别准确率
    class_names = ['Normal', 'E-KC', 'KC']
    original_class_accs = {}
    for i, class_name in enumerate(class_names):
        class_mask = (labels == i)
        if class_mask.any():
            class_acc = (original_preds[class_mask] == labels[class_mask]).float().mean().item() * 100
            original_class_accs[class_name] = class_acc
        else:
            original_class_accs[class_name] = 0.0
    
    print(f"原始性能:")
    print(f"  总体准确率: {original_acc:.2f}%")
    for class_name, acc in original_class_accs.items():
        print(f"  {class_name}: {acc:.2f}%")
    
    # 分割数据用于校准拟合和测试
    n_total = len(probs)
    n_cal = n_total // 2
    
    cal_probs, cal_labels = probs[:n_cal], labels[:n_cal]
    test_probs, test_labels = probs[n_cal:], labels[n_cal:]
    
    # 测试不同校准方法
    methods = ['kc_ekc_boundary', 'temperature', 'adaptive_threshold']
    
    for method in methods:
        print(f"\n{method.upper()} 校准:")
        try:
            # 创建校准器
            calibrator = create_calibrator(method)
            
            # 拟合校准器
            if method == 'temperature':
                logits = torch.log(cal_probs + 1e-8)
                calibrator.fit(logits, cal_labels)
                print(f"  温度参数: {calibrator.temperature.item():.3f}")
            elif method == 'kc_ekc_boundary':
                calibrator.fit(cal_probs, cal_labels, target_kc_recall=0.8)
                print(f"  KC增强因子: {calibrator.kc_boost_factor:.2f}")
                print(f"  E-KC抑制因子: {calibrator.ekc_suppress_factor:.2f}")
            elif method == 'adaptive_threshold':
                calibrator.fit(cal_probs, cal_labels)
                print(f"  置信度阈值: {calibrator.confidence_thresholds}")
            
            # 应用校准
            if method == 'temperature':
                test_logits = torch.log(test_probs + 1e-8)
                calibrated_probs = calibrator(test_logits)
            else:
                calibrated_probs = calibrator.calibrate(test_probs)
            
            # 计算校准后性能
            calibrated_preds = torch.argmax(calibrated_probs, dim=1)
            calibrated_acc = (calibrated_preds == test_labels).float().mean().item() * 100
            
            # 计算校准后类别准确率
            calibrated_class_accs = {}
            for i, class_name in enumerate(class_names):
                class_mask = (test_labels == i)
                if class_mask.any():
                    class_acc = (calibrated_preds[class_mask] == test_labels[class_mask]).float().mean().item() * 100
                    calibrated_class_accs[class_name] = class_acc
                else:
                    calibrated_class_accs[class_name] = 0.0
            
            print(f"  校准后总体准确率: {calibrated_acc:.2f}% (改进: {calibrated_acc - original_acc:+.2f}%)")
            for class_name in class_names:
                original = original_class_accs[class_name]
                calibrated = calibrated_class_accs[class_name]
                improvement = calibrated - original
                print(f"  {class_name}: {calibrated:.2f}% (改进: {improvement:+.2f}%)")
            
            # 特别关注KC类别的改进
            kc_improvement = calibrated_class_accs['KC'] - original_class_accs['KC']
            if kc_improvement > 0:
                print(f"  ✅ KC类别准确率提升 {kc_improvement:.2f}%")
            elif kc_improvement < 0:
                print(f"  ❌ KC类别准确率下降 {kc_improvement:.2f}%")
            else:
                print(f"  ➖ KC类别准确率无变化")
                
        except Exception as e:
            print(f"  ❌ {method} 校准失败: {e}")


def main():
    """主函数"""
    print("开始快速校准测试...")
    
    success = test_calibration_on_existing_model()
    
    if success:
        print("\n🎉 校准测试完成！")
        print("\n💡 下一步建议:")
        print("1. 如果KC类别准确率有提升，可以运行完整的校准评估")
        print("2. 运行: python evaluate_with_calibration.py --model_path <model_path>")
        print("3. 或运行完整实验: ./run_calibration_and_hybrid.sh")
    else:
        print("\n❌ 校准测试失败")
        print("请确保有可用的测试结果文件")


if __name__ == '__main__':
    main()

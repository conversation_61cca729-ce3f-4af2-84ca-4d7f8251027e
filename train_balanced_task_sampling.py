#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
使用平衡任务采样器的训练脚本AA
- 确保每个任务都包含圆锥角膜和早期圆锥角膜样本
- 增加圆锥角膜和早期圆锥角膜样本在支持集中的数量
"""

import os
import sys
import argparse
import random
import numpy as np
import json
import time
from tqdm import tqdm
from collections import defaultdict
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime

# 确保PyTorch导入正确
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torch.utils.data import DataLoader

# 添加当前目录到Python搜索路径
import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 导入自定义模块
try:
    from models.feature_extractor import KeratoconusFeatureExtractor
except ImportError:
    # 尝试从上级目录导入
    sys.path.insert(0, os.path.join(os.path.dirname(os.path.abspath(__file__)), '..'))
    from models.feature_extractor import KeratoconusFeatureExtractor

try:
    # 尝试导入增强版特征提取器
    from models.enhanced_feature_extractor import EnhancedKeratoconusFeatureExtractor
    has_enhanced_extractor = True
    print("成功导入增强版特征提取器")
except ImportError:
    has_enhanced_extractor = False
    print("未找到增强版特征提取器，将使用标准版")

try:
    # 尝试导入归一化特征提取器
    from models.normalized_feature_extractor import NormalizedFeatureExtractor, EnhancedNormalizedFeatureExtractor
    has_normalized_extractor = True
    print("成功导入归一化特征提取器")
except ImportError:
    has_normalized_extractor = False
    print("未找到归一化特征提取器，将使用标准版")

try:
    # 尝试导入特征分离提取器
    from models.feature_separation_extractor import FeatureSeparationExtractor, EnhancedFeatureSeparationExtractor
    has_feature_separation = True
    print("成功导入特征分离提取器")
except ImportError:
    has_feature_separation = False
    print("未找到特征分离提取器，将禁用特征分离功能")

# 导入损失函数
try:
    from models.losses import FocalLoss
except ImportError:
    # 尝试从上级目录导入
    from models.losses import FocalLoss

try:
    # 尝试导入增强版损失函数
    from models.enhanced_losses import EnhancedMAMLContrastiveLoss, EnhancedFocalLoss
    has_enhanced_losses = True
    print("成功导入增强版损失函数")
except ImportError:
    has_enhanced_losses = False
    print("未找到增强版损失函数，将使用标准版")
    try:
        from models.losses import MAMLContrastiveLoss
    except ImportError:
        # 尝试从上级目录导入
        from models.losses import MAMLContrastiveLoss

# 导入学习率调度器
from torch.optim.lr_scheduler import OneCycleLR, ReduceLROnPlateau

# 导入MAML模型
try:
    from models.protonet import ProtoNet
    from models.maml import MAMLProtoNet
except ImportError:
    # 尝试从上级目录导入
    from models.protonet import ProtoNet
    from models.maml import MAMLProtoNet

try:
    # 尝试导入增强版MAML模型
    from models.enhanced_maml import EnhancedMAMLProtoNet
    has_enhanced_maml = True
    print("成功导入增强版MAML模型")
except ImportError:
    has_enhanced_maml = False
    print("未找到增强版MAML模型，将使用标准版")

from datasets.keratoconus_dataset import KeratoconusDataset, get_transforms, get_dataloaders
from datasets.balanced_task_sampler import BalancedTaskSampler

def parse_args():
    parser = argparse.ArgumentParser(description='Train with Balanced Task Sampling')

    # 基本参数
    parser.add_argument('--train_csv', type=str, required=True, help='Path to training CSV file')
    parser.add_argument('--val_csv', type=str, required=True, help='Path to validation CSV file')
    parser.add_argument('--test_csv', type=str, required=True, help='Path to test CSV file')
    parser.add_argument('--save_dir', type=str, default='results', help='Directory to save results')
    parser.add_argument('--device', type=str, default='cuda', help='Device to use')

    # 模型参数
    parser.add_argument('--proto_counts', type=str, default='2,4,2', help='Prototype counts for each class')
    parser.add_argument('--feature_dim', type=int, default=512, help='Feature dimension')
    parser.add_argument('--inner_lr', type=float, default=0.1, help='Inner learning rate')
    parser.add_argument('--inner_steps', type=int, default=3, help='Inner update steps')
    parser.add_argument('--pretrained', action='store_true', help='Use pretrained feature extractor')
    parser.add_argument('--dropout', type=float, default=0.4, help='Dropout rate')

    # 特征归一化参数
    parser.add_argument('--use_normalized_features', action='store_true', help='Use normalized features')
    parser.add_argument('--normalize_scale', type=float, default=10.0, help='Scale factor for normalized features')

    # 特征分离参数
    parser.add_argument('--use_feature_separation', action='store_true', help='Use feature separation module')
    parser.add_argument('--use_enhanced_separation', action='store_true', help='Use enhanced feature separation')
    parser.add_argument('--use_adaptive_separation', action='store_true', help='Use adaptive feature separation')
    parser.add_argument('--separation_dim', type=int, default=128, help='Separation feature dimension')
    parser.add_argument('--separation_weight', type=float, default=0.1, help='Weight for separation loss')
    parser.add_argument('--use_attention_in_separation', action='store_true', help='Use attention in separation module')

    # 训练参数
    parser.add_argument('--epochs', type=int, default=50, help='Number of epochs')
    parser.add_argument('--lr', type=float, default=0.0002, help='Learning rate')
    parser.add_argument('--weight_decay', type=float, default=1e-4, help='Weight decay')
    parser.add_argument('--n_way', type=int, default=3, help='Number of classes per task')
    parser.add_argument('--n_shot', type=int, default=5, help='Number of support samples per class')
    parser.add_argument('--n_query', type=int, default=15, help='Number of query samples per class')
    parser.add_argument('--tasks_per_epoch', type=int, default=30, help='Number of tasks per epoch')
    parser.add_argument('--meta_batch_size', type=int, default=8, help='Meta batch size')
    parser.add_argument('--val_frequency', type=int, default=1, help='Validation frequency in epochs')
    parser.add_argument('--early_stopping', type=int, default=5, help='Early stopping patience')

    # 学习率调度器参数
    parser.add_argument('--use_lr_scheduler', action='store_true', help='Use learning rate scheduler')
    parser.add_argument('--scheduler_type', type=str, default='onecycle', choices=['onecycle', 'plateau'], help='Type of learning rate scheduler')
    parser.add_argument('--max_lr', type=float, default=0.001, help='Maximum learning rate for OneCycleLR')
    parser.add_argument('--div_factor', type=float, default=25.0, help='Initial learning rate division factor for OneCycleLR')
    parser.add_argument('--final_div_factor', type=float, default=1e4, help='Final learning rate division factor for OneCycleLR')
    parser.add_argument('--pct_start', type=float, default=0.1, help='Percentage of cycle spent increasing learning rate for OneCycleLR')
    parser.add_argument('--plateau_factor', type=float, default=0.5, help='Factor by which the learning rate will be reduced for ReduceLROnPlateau')
    parser.add_argument('--plateau_patience', type=int, default=2, help='Number of epochs with no improvement after which learning rate will be reduced for ReduceLROnPlateau')
    parser.add_argument('--min_lr', type=float, default=1e-6, help='Minimum learning rate for ReduceLROnPlateau')
    parser.add_argument('--record_batch_lr', action='store_true', help='Record learning rate at batch level for visualization')

    # 数据增强参数
    parser.add_argument('--no_augmentation', action='store_true', help='Disable all data augmentation')
    parser.add_argument('--early_kc_augment', action='store_true', help='Apply augmentation to early KC samples')
    parser.add_argument('--kc_augment', action='store_true', help='Apply augmentation to KC samples')
    parser.add_argument('--advanced_augment', action='store_true', help='Use advanced augmentation')
    parser.add_argument('--mixup_alpha', type=float, default=0.2, help='Mixup alpha parameter')
    parser.add_argument('--cutmix_prob', type=float, default=0.3, help='CutMix probability')
    parser.add_argument('--early_kc_specific_augment', action='store_true', help='Apply specific augmentation to early KC')
    parser.add_argument('--augment_factor', type=int, default=6, help='Augmentation factor for early KC samples')
    parser.add_argument('--kc_augment_factor', type=int, default=3, help='Augmentation factor for KC samples')
    parser.add_argument('--use_enhanced_gpu_augment', action='store_true', help='Use enhanced GPU augmentation')

    # 损失函数参数
    parser.add_argument('--early_kc_weight', type=float, default=8.0, help='Weight for early KC samples')
    parser.add_argument('--kc_weight', type=float, default=3.0, help='Weight for KC samples')
    parser.add_argument('--focal_gamma', type=float, default=2.0, help='Gamma for focal loss')
    parser.add_argument('--use_contrastive', action='store_true', help='Use contrastive learning')
    parser.add_argument('--contrastive_weight', type=float, default=0.5, help='Weight for contrastive loss')
    parser.add_argument('--temperature', type=float, default=0.07, help='Temperature for contrastive loss')
    parser.add_argument('--hard_mining_ratio', type=float, default=0.7, help='Hard negative mining ratio')
    parser.add_argument('--early_normal_weight', type=float, default=2.0, help='Weight for early KC vs normal contrast')
    parser.add_argument('--kc_normal_weight', type=float, default=1.0, help='Weight for KC vs normal contrast')

    # 平衡任务采样器参数
    parser.add_argument('--use_balanced_task_sampler', action='store_true', help='Use balanced task sampler')
    parser.add_argument('--kc_shot_multiplier', type=float, default=3.0, help='Multiplier for KC shot count')
    parser.add_argument('--early_kc_shot_multiplier', type=float, default=1.5, help='Multiplier for early KC shot count')

    # 其他参数
    parser.add_argument('--seed', type=int, default=42, help='Random seed')
    parser.add_argument('--num_workers', type=int, default=2, help='Number of workers for data loading')
    parser.add_argument('--use_separate_test_sampler', action='store_true', help='Use separate test sampler')

    return parser.parse_args()

def set_seed(seed):
    """设置随机种子以确保结果可重复"""
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed(seed)
        torch.cuda.manual_seed_all(seed)
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False

def create_model(args, device):
    """创建模型"""
    # 解析原型配置
    proto_counts = [int(x) for x in args.proto_counts.split(',')]
    print(f"使用原型配置: {proto_counts}")

    # 创建特征提取器
    if hasattr(args, 'use_feature_separation') and args.use_feature_separation and has_feature_separation:
        # 使用特征分离提取器
        if hasattr(args, 'use_enhanced_separation') and args.use_enhanced_separation:
            # 使用增强版特征分离提取器
            feature_extractor = EnhancedFeatureSeparationExtractor(
                pretrained=args.pretrained,
                feature_dim=args.feature_dim,
                separation_dim=args.separation_dim,
                dropout_rate=args.dropout,
                use_separation=True,
                use_adaptive=getattr(args, 'use_adaptive_separation', False),
                use_attention=getattr(args, 'use_attention_in_separation', True),
                separation_weight=args.separation_weight,
                use_multiscale=True
            ).to(device)
            print(f"使用增强版特征分离提取器，分离维度: {args.separation_dim}, 分离权重: {args.separation_weight}")
        else:
            # 使用基础特征分离提取器
            feature_extractor = FeatureSeparationExtractor(
                pretrained=args.pretrained,
                feature_dim=args.feature_dim,
                separation_dim=args.separation_dim,
                dropout_rate=args.dropout,
                use_separation=True,
                use_adaptive=getattr(args, 'use_adaptive_separation', False),
                use_attention=getattr(args, 'use_attention_in_separation', True),
                separation_weight=args.separation_weight
            ).to(device)
            print(f"使用基础特征分离提取器，分离维度: {args.separation_dim}, 分离权重: {args.separation_weight}")
    elif hasattr(args, 'use_normalized_features') and args.use_normalized_features and has_normalized_extractor:
        # 使用归一化特征提取器
        if has_enhanced_extractor:
            # 使用增强版归一化特征提取器
            feature_extractor = EnhancedNormalizedFeatureExtractor(
                pretrained=args.pretrained,
                feature_dim=args.feature_dim,
                normalize_scale=args.normalize_scale,
                normalize_output=True,
                dropout_rate=args.dropout,
                use_projection=True,
                use_batch_norm=True
            ).to(device)
            print(f"使用增强版归一化特征提取器，normalize_scale: {args.normalize_scale}, dropout率: {args.dropout}")
        else:
            # 使用标准版归一化特征提取器
            feature_extractor = NormalizedFeatureExtractor(
                pretrained=args.pretrained,
                feature_dim=args.feature_dim,
                normalize_scale=args.normalize_scale,
                normalize_output=True
            ).to(device)
            print(f"使用标准版归一化特征提取器，normalize_scale: {args.normalize_scale}")
    elif has_enhanced_extractor:
        # 使用增强版特征提取器（可选启用特征分离）
        use_separation = getattr(args, 'use_feature_separation', False) and has_feature_separation
        feature_extractor = EnhancedKeratoconusFeatureExtractor(
            pretrained=args.pretrained,
            feature_dim=args.feature_dim,
            dropout_rate=args.dropout,
            use_feature_separation=use_separation,
            separation_dim=getattr(args, 'separation_dim', 128)
        ).to(device)
        if use_separation:
            print(f"使用增强版特征提取器（集成特征分离），dropout率: {args.dropout}, 分离维度: {getattr(args, 'separation_dim', 128)}")
        else:
            print(f"使用增强版特征提取器，dropout率: {args.dropout}")
    else:
        # 使用标准版特征提取器
        feature_extractor = KeratoconusFeatureExtractor(
            pretrained=args.pretrained,
            feature_dim=args.feature_dim
        ).to(device)
        print("使用标准版特征提取器")

    # 创建ProtoNet模型
    protonet = ProtoNet(
        feature_extractor,
        n_classes=3,
        proto_counts=proto_counts,
        feature_dim=args.feature_dim
    ).to(device)

    # 创建MAML-ProtoNet模型
    if has_enhanced_maml:
        # 使用增强版MAML-ProtoNet模型
        maml_model = EnhancedMAMLProtoNet(
            protonet,
            inner_lr=args.inner_lr,
            inner_steps=args.inner_steps
        ).to(device)
        print(f"使用增强版MAML-ProtoNet模型，内循环学习率: {args.inner_lr}, 内循环步数: {args.inner_steps}")
    else:
        # 使用标准版MAML-ProtoNet模型
        maml_model = MAMLProtoNet(
            protonet,
            inner_lr=args.inner_lr,
            inner_steps=args.inner_steps
        ).to(device)
        print("使用标准版MAML-ProtoNet模型")

    return maml_model

def train_epoch(model, task_sampler, optimizer, device, args, scheduler=None):
    """训练一个epoch"""
    model.train()
    epoch_loss = 0.0
    epoch_acc = 0.0
    epoch_contrastive_loss = 0.0

    # 创建损失函数
    class_weights = torch.tensor([1.0, args.early_kc_weight, args.kc_weight], device=device)
    print(f"使用类别权重: 正常={1.0}, 早期圆锥角膜={args.early_kc_weight}, 圆锥角膜={args.kc_weight}")
    criterion = FocalLoss(gamma=args.focal_gamma, alpha=class_weights)

    # 创建对比损失函数
    contrastive_loss_fn = None
    if args.use_contrastive:
        if has_enhanced_losses:
            # 使用增强版对比损失函数
            contrastive_loss_fn = EnhancedMAMLContrastiveLoss(
                temperature=args.temperature,
                hard_mining_ratio=args.hard_mining_ratio,
                early_normal_weight=args.early_normal_weight,
                kc_normal_weight=args.kc_normal_weight
            )
            print(f"使用增强版对比损失函数，温度: {args.temperature}, 硬负样本挖掘比例: {args.hard_mining_ratio}, 早期圆锥角膜与正常样本对比权重: {args.early_normal_weight}, 圆锥角膜与正常样本对比权重: {args.kc_normal_weight}")
        else:
            # 使用标准版对比损失函数
            contrastive_loss_fn = MAMLContrastiveLoss(
                temperature=args.temperature
            )
            print(f"使用标准版对比损失函数，温度: {args.temperature}")

    # 训练任务
    for _ in tqdm(range(args.tasks_per_epoch), desc="Training tasks"):
        # 采样一个任务
        task = task_sampler.sample_task()
        support_images, support_labels = task['support']
        query_images, query_labels = task['query']

        # 将数据移动到设备
        support_images = support_images.to(device)
        support_labels = support_labels.to(device)
        query_images = query_images.to(device)
        query_labels = query_labels.to(device)

        # 前向传播
        optimizer.zero_grad()

        # 不使用混合精度训练
        query_logits, query_features, support_features = model(
            support_images, support_labels, query_images, return_features=True
        )

        # 计算分类损失
        loss = criterion(query_logits, query_labels)

        # 计算对比损失
        contrastive_loss = 0.0
        if args.use_contrastive and contrastive_loss_fn is not None:
            contrastive_loss = contrastive_loss_fn(
                query_features, query_labels, support_features, support_labels
            )
            loss += args.contrastive_weight * contrastive_loss
            epoch_contrastive_loss += contrastive_loss.item()

        # 计算特征分离损失
        separation_loss = 0.0
        if hasattr(args, 'use_feature_separation') and args.use_feature_separation:
            feature_extractor = model.protonet.feature_extractor
            if hasattr(feature_extractor, 'compute_separation_loss'):
                separation_loss = feature_extractor.compute_separation_loss(query_images, query_labels)
                loss += separation_loss
            elif hasattr(feature_extractor, 'compute_total_separation_loss'):
                separation_loss = feature_extractor.compute_total_separation_loss(query_images, query_labels)
                loss += separation_loss

        # 反向传播
        loss.backward()
        optimizer.step()

        # 更新学习率调度器（如果使用OneCycleLR）
        if scheduler is not None and args.scheduler_type == 'onecycle':
            # 检查是否已经达到最大步数
            try:
                scheduler.step()
                # 记录当前batch的学习率
                if hasattr(args, 'record_batch_lr') and args.record_batch_lr:
                    args.batch_learning_rates.append(optimizer.param_groups[0]['lr'])
            except ValueError as e:
                print(f"警告: 学习率调度器步数已达上限，跳过更新: {e}")
                # 继续训练，但不再更新学习率

        # 计算准确率
        pred = torch.argmax(query_logits, dim=1)
        correct = (pred == query_labels).sum().item()
        accuracy = correct / len(query_labels)

        # 更新统计信息
        epoch_loss += loss.item()
        epoch_acc += accuracy

    # 计算平均值
    epoch_loss /= args.tasks_per_epoch
    epoch_acc /= args.tasks_per_epoch
    if args.use_contrastive:
        epoch_contrastive_loss /= args.tasks_per_epoch

    return epoch_loss, epoch_acc, epoch_contrastive_loss

def validate(model, val_dataset, device, args):
    """验证模型"""
    model.eval()
    val_loss = 0.0
    val_acc = 0.0

    # 创建验证任务采样器
    val_task_sampler = BalancedTaskSampler(
        val_dataset,
        n_way=args.n_way,
        n_shot=args.n_shot,
        n_query=args.n_query,
        kc_shot_multiplier=args.kc_shot_multiplier,
        early_kc_shot_multiplier=args.early_kc_shot_multiplier
    )

    # 创建损失函数
    class_weights = torch.tensor([1.0, args.early_kc_weight, args.kc_weight], device=device)
    criterion = FocalLoss(gamma=args.focal_gamma, alpha=class_weights)

    # 验证任务
    n_tasks = max(10, args.tasks_per_epoch // 3)  # 验证任务数量

    with torch.no_grad():
        for _ in tqdm(range(n_tasks), desc="Validating tasks"):
            # 采样一个任务
            task = val_task_sampler.sample_task()
            support_images, support_labels = task['support']
            query_images, query_labels = task['query']

            # 将数据移动到设备
            support_images = support_images.to(device)
            support_labels = support_labels.to(device)
            query_images = query_images.to(device)
            query_labels = query_labels.to(device)

            # 前向传播
            query_logits = model(support_images, support_labels, query_images)

            # 计算损失
            loss = criterion(query_logits, query_labels)

            # 计算准确率
            pred = torch.argmax(query_logits, dim=1)
            correct = (pred == query_labels).sum().item()
            accuracy = correct / len(query_labels)

            # 更新统计信息
            val_loss += loss.item()
            val_acc += accuracy

    # 计算平均值
    val_loss /= n_tasks
    val_acc /= n_tasks

    return val_loss, val_acc

def test(model, test_dataset, device, args):
    """测试模型"""
    model.eval()
    test_acc = 0.0
    class_accuracies = defaultdict(list)
    confusion_matrix = np.zeros((3, 3), dtype=int)

    # 创建测试任务采样器
    test_task_sampler = BalancedTaskSampler(
        test_dataset,
        n_way=args.n_way,
        n_shot=args.n_shot,
        n_query=args.n_query,
        kc_shot_multiplier=args.kc_shot_multiplier,
        early_kc_shot_multiplier=args.early_kc_shot_multiplier
    )

    # 测试任务
    n_tasks = 30  # 测试任务数量

    with torch.no_grad():
        for _ in tqdm(range(n_tasks), desc="Testing tasks"):
            # 采样一个任务
            task = test_task_sampler.sample_task()
            support_images, support_labels = task['support']
            query_images, query_labels = task['query']

            # 将数据移动到设备
            support_images = support_images.to(device)
            support_labels = support_labels.to(device)
            query_images = query_images.to(device)
            query_labels = query_labels.to(device)

            # 前向传播
            query_logits = model(support_images, support_labels, query_images)

            # 计算准确率
            pred = torch.argmax(query_logits, dim=1)
            correct = (pred == query_labels).sum().item()
            accuracy = correct / len(query_labels)

            # 更新统计信息
            test_acc += accuracy

            # 按类别统计准确率
            for i, label in enumerate(query_labels.cpu().numpy()):
                class_name = ['normal', 'e-kc', 'kc'][label]
                is_correct = int(pred[i].cpu().numpy() == label)
                class_accuracies[class_name].append(is_correct)

                # 更新混淆矩阵
                confusion_matrix[label][pred[i].cpu().numpy()] += 1

    # 计算平均值
    test_acc /= n_tasks

    # 计算每个类别的准确率
    class_avg_accuracies = {}
    for class_name, accs in class_accuracies.items():
        class_avg_accuracies[class_name] = np.mean(accs) * 100

    return test_acc, class_avg_accuracies, confusion_matrix

def main():
    args = parse_args()

    # 设置随机种子
    set_seed(args.seed)

    # 创建输出目录
    os.makedirs(args.save_dir, exist_ok=True)

    # 设置设备
    device = torch.device(args.device if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")

    # 加载数据集
    if args.no_augmentation:
        # 如果禁用所有数据增强，直接加载原始数据集
        print("禁用所有数据增强，直接加载原始数据集...")
        train_transform, val_transform = get_transforms('train'), get_transforms('val')
        train_dataset = KeratoconusDataset(args.train_csv, transform=train_transform)
        val_dataset = KeratoconusDataset(args.val_csv, transform=val_transform)
        test_dataset = KeratoconusDataset(args.test_csv, transform=val_transform)
    elif args.early_kc_augment or args.kc_augment:
        # 如果启用了数据增强，使用get_dataloaders函数处理
        print("使用数据增强加载数据集...")

        # 首先加载原始数据集，以便创建任务采样器
        train_transform, val_transform = get_transforms('train'), get_transforms('val')
        train_dataset = KeratoconusDataset(args.train_csv, transform=train_transform)
        val_dataset = KeratoconusDataset(args.val_csv, transform=val_transform)
        test_dataset = KeratoconusDataset(args.test_csv, transform=val_transform)

        # 如果使用GPU增强，我们仍然需要创建数据加载器，但不会直接使用它
        # 而是在任务采样器中使用原始数据集
        if args.use_enhanced_gpu_augment:
            print("使用GPU数据增强，但任务采样器将使用原始数据集")
            # 创建数据加载器，但不使用它的返回值
            get_dataloaders(
                args.train_csv,
                args.test_csv,
                batch_size=32,
                early_kc_augment=args.early_kc_augment,
                kc_augment=args.kc_augment,
                use_gpu_augment=args.use_enhanced_gpu_augment,
                num_workers=args.num_workers,
                augment_factor=args.augment_factor,
                kc_augment_factor=args.kc_augment_factor
            )
        else:
            # 如果不使用GPU增强，使用get_dataloaders函数处理
            train_loader, _ = get_dataloaders(
                args.train_csv,
                args.test_csv,
                batch_size=32,
                early_kc_augment=args.early_kc_augment,
                kc_augment=args.kc_augment,
                use_gpu_augment=False,  # 确保不使用GPU增强
                num_workers=args.num_workers,
                augment_factor=args.augment_factor,
                kc_augment_factor=args.kc_augment_factor
            )
            # 从train_loader中获取增强后的数据集
            train_dataset = train_loader.dataset
            print(f"数据集加载完成，训练集大小: {len(train_dataset)}")
    else:
        # 常规加载数据集
        train_transform, val_transform = get_transforms('train'), get_transforms('val')
        train_dataset = KeratoconusDataset(args.train_csv, transform=train_transform)
        val_dataset = KeratoconusDataset(args.val_csv, transform=val_transform)
        test_dataset = KeratoconusDataset(args.test_csv, transform=val_transform)

    # 创建平衡任务采样器
    if args.use_balanced_task_sampler:
        print("使用平衡任务采样器")
        train_task_sampler = BalancedTaskSampler(
            train_dataset,
            n_way=args.n_way,
            n_shot=args.n_shot,
            n_query=args.n_query,
            kc_shot_multiplier=args.kc_shot_multiplier,
            early_kc_shot_multiplier=args.early_kc_shot_multiplier,
            num_workers=args.num_workers
        )
    else:
        # 使用默认任务采样器
        from datasets.keratoconus_dataset import get_task_sampler
        print("使用默认任务采样器")
        train_task_sampler = get_task_sampler(
            train_dataset,
            args.n_way,
            args.n_shot,
            args.n_query
        )

    # 创建模型
    model = create_model(args, device)

    # 创建优化器
    optimizer = optim.Adam(model.parameters(), lr=args.lr, weight_decay=args.weight_decay)

    # 创建学习率调度器
    scheduler = None
    if args.use_lr_scheduler:
        if args.scheduler_type == 'onecycle':
            # 计算总步数 - 添加一些余量以避免步数不足的错误
            total_steps = args.epochs * args.tasks_per_epoch + 10

            # 创建OneCycleLR调度器
            scheduler = OneCycleLR(
                optimizer,
                max_lr=args.max_lr,
                total_steps=total_steps,
                div_factor=args.div_factor,
                final_div_factor=args.final_div_factor,
                pct_start=args.pct_start,
                anneal_strategy='cos',
                three_phase=False
            )
            print(f"使用OneCycleLR学习率调度器，最大学习率: {args.max_lr}, 总步数: {total_steps}")

            # 初始化batch级别的学习率记录
            if args.record_batch_lr:
                args.batch_learning_rates = []
                print("启用batch级别学习率记录")
        elif args.scheduler_type == 'plateau':
            # 创建ReduceLROnPlateau调度器
            scheduler = ReduceLROnPlateau(
                optimizer,
                mode='max',
                factor=args.plateau_factor,
                patience=args.plateau_patience,
                min_lr=args.min_lr
            )
            print(f"使用ReduceLROnPlateau学习率调度器，因子: {args.plateau_factor}, 耐心值: {args.plateau_patience}")

    # 训练循环
    best_val_acc = 0.0
    best_epoch = 0
    patience_counter = 0
    history = {
        'train_loss': [],
        'train_acc': [],
        'val_loss': [],
        'val_acc': [],
        'contrastive_loss': [],
        'learning_rates': [],
        'batch_learning_rates': []  # 添加batch级别的学习率记录
    }

    print(f"开始训练，共{args.epochs}个epoch")
    for epoch in range(args.epochs):
        print(f"Epoch {epoch+1}/{args.epochs}")

        # 获取当前学习率
        current_lr = optimizer.param_groups[0]['lr']
        print(f"当前学习率: {current_lr:.6f}")
        history['learning_rates'].append(current_lr)

        # 训练
        train_loss, train_acc, contrastive_loss = train_epoch(
            model, train_task_sampler, optimizer, device, args, scheduler
        )

        # 记录训练历史
        history['train_loss'].append(train_loss)
        history['train_acc'].append(train_acc)
        history['contrastive_loss'].append(contrastive_loss)

        # 验证
        if (epoch + 1) % args.val_frequency == 0:
            val_loss, val_acc = validate(model, val_dataset, device, args)
            history['val_loss'].append(val_loss)
            history['val_acc'].append(val_acc)

            print(f"Epoch {epoch+1}: Train Loss: {train_loss:.4f}, Train Acc: {train_acc:.4f}, Val Loss: {val_loss:.4f}, Val Acc: {val_acc:.4f}")

            # 更新学习率调度器（如果使用ReduceLROnPlateau）
            if scheduler is not None and args.scheduler_type == 'plateau':
                scheduler.step(val_acc)

            # 保存最佳模型
            if val_acc > best_val_acc:
                best_val_acc = val_acc
                best_epoch = epoch
                patience_counter = 0

                # 保存模型
                torch.save({
                    'model_state_dict': model.state_dict(),
                    'optimizer_state_dict': optimizer.state_dict(),
                    'epoch': epoch,
                    'val_acc': val_acc,
                    'history': history,
                    'args': vars(args)
                }, os.path.join(args.save_dir, 'best_maml_model.pth'))

                print(f"保存最佳模型，验证准确率: {val_acc:.4f}")
            else:
                patience_counter += 1
                print(f"验证准确率未提高，当前耐心值: {patience_counter}/{args.early_stopping}")

                # 早停
                if patience_counter >= args.early_stopping:
                    print(f"早停触发，停止训练")
                    break
        else:
            print(f"Epoch {epoch+1}: Train Loss: {train_loss:.4f}, Train Acc: {train_acc:.4f}")

    # 加载最佳模型
    checkpoint = torch.load(os.path.join(args.save_dir, 'best_maml_model.pth'))
    model.load_state_dict(checkpoint['model_state_dict'])

    # 测试最佳模型
    test_acc, class_accuracies, confusion_matrix = test(model, test_dataset, device, args)

    # 打印测试结果
    print(f"\n测试结果:")
    print(f"总体准确率: {test_acc*100:.2f}%")
    print("类别准确率:")
    for class_name, acc in class_accuracies.items():
        print(f"  {class_name}: {acc:.2f}%")

    # 保存测试结果
    results = {
        'test_acc': test_acc * 100,
        'class_accuracies': class_accuracies,
        'confusion_matrix': confusion_matrix.tolist(),
        'best_epoch': best_epoch,
        'best_val_acc': best_val_acc,
        'history': history,
        'args': vars(args)
    }

    with open(os.path.join(args.save_dir, 'test_results.json'), 'w') as f:
        json.dump(results, f, indent=4)

    # 可视化训练历史
    plt.figure(figsize=(15, 8))

    # 损失曲线
    plt.subplot(2, 2, 1)
    plt.plot(history['train_loss'], label='Train Loss')
    plt.plot(history['val_loss'], label='Val Loss')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.title('Training and Validation Loss')
    plt.legend()

    # 准确率曲线
    plt.subplot(2, 2, 2)
    plt.plot(history['train_acc'], label='Train Acc')
    plt.plot(history['val_acc'], label='Val Acc')
    plt.xlabel('Epoch')
    plt.ylabel('Accuracy')
    plt.title('Training and Validation Accuracy')
    plt.legend()

    # 学习率曲线
    plt.subplot(2, 2, 3)
    plt.plot(history['learning_rates'], label='Epoch LR')

    # 如果有batch级别的学习率记录，也绘制出来
    if hasattr(args, 'batch_learning_rates') and args.batch_learning_rates:
        # 计算每个epoch的batch数
        batches_per_epoch = args.tasks_per_epoch
        # 创建x轴坐标（小数表示epoch内的batch位置）
        x_coords = [i/batches_per_epoch for i in range(len(args.batch_learning_rates))]
        plt.plot(x_coords, args.batch_learning_rates, 'r-', alpha=0.5, label='Batch LR')

    plt.xlabel('Epoch')
    plt.ylabel('Learning Rate')
    plt.title('Learning Rate Schedule')
    plt.legend()

    # 对比损失曲线
    plt.subplot(2, 2, 4)
    plt.plot(history['contrastive_loss'])
    plt.xlabel('Epoch')
    plt.ylabel('Contrastive Loss')
    plt.title('Contrastive Loss')

    plt.tight_layout()
    plt.savefig(os.path.join(args.save_dir, 'training_history.png'))

    # 可视化混淆矩阵
    plt.figure(figsize=(8, 6))
    sns.heatmap(confusion_matrix, annot=True, fmt='d', cmap='Blues',
                xticklabels=['normal', 'e-kc', 'kc'],
                yticklabels=['normal', 'e-kc', 'kc'])
    plt.xlabel('Predicted')
    plt.ylabel('True')
    plt.title('Confusion Matrix')
    plt.tight_layout()
    plt.savefig(os.path.join(args.save_dir, 'confusion_matrix.png'))

    print(f"训练完成，结果已保存到 {args.save_dir}")

if __name__ == "__main__":
    main()

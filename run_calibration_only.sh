#!/bin/bash

#############################################################
# 仅校准评估脚本
# 
# 该脚本专门用于对现有最佳模型进行校准评估，
# 避免混合决策训练的复杂性，专注于快速验证校准效果。
#############################################################

echo "🎯 开始校准评估实验..."
echo "=================================================="

# 设置基础参数
GPU_ID="0"
BASE_OUTPUT_DIR="calibration_only_$(date +%Y%m%d_%H%M%S)"

# 数据路径
VAL_CSV="/home/<USER>/balanced_task_sampling/split_result/val_set.csv"
TEST_CSV="/home/<USER>/balanced_task_sampling/split_result/test_set.csv"

# 现有最佳模型路径
BEST_MODEL_PATH="/home/<USER>/balanced_task_sampling/results_optimized_20250525_202558_gpu0,1/best_maml_model.pth"

# 创建实验目录
mkdir -p $BASE_OUTPUT_DIR
mkdir -p logs

echo "实验目录: $BASE_OUTPUT_DIR"
echo "使用GPU: $GPU_ID"
echo "使用模型: $BEST_MODEL_PATH"
echo ""

# 设置环境变量
export CUDA_VISIBLE_DEVICES=$GPU_ID

# 检查模型文件是否存在
if [ ! -f "$BEST_MODEL_PATH" ]; then
    echo "❌ 模型文件不存在: $BEST_MODEL_PATH"
    echo "请检查模型路径或先训练一个模型"
    exit 1
fi

echo "✅ 模型文件存在，开始校准评估..."

# ==================== 校准评估 ====================
echo "📊 校准评估实验"
echo "=================================================="

# 校准结果目录
CALIBRATION_DIR="${BASE_OUTPUT_DIR}/calibration_results"

echo "开始校准评估..."
echo "保存目录: $CALIBRATION_DIR"

# 运行校准评估
python evaluate_with_calibration.py \
    --model_path "$BEST_MODEL_PATH" \
    --val_csv "$VAL_CSV" \
    --test_csv "$TEST_CSV" \
    --save_dir "$CALIBRATION_DIR" \
    --device "cuda:0" \
    --calibration_methods kc_ekc_boundary temperature adaptive_threshold class_specific \
    --target_kc_recall 0.85 \
    --n_tasks 50 \
    --n_way 3 \
    --n_shot 5 \
    --n_query 15 \
    > "logs/calibration_only_$(date +%Y%m%d_%H%M%S).log" 2>&1

# 检查执行结果
if [ $? -eq 0 ]; then
    echo "✅ 校准评估完成"
    echo "结果保存在: $CALIBRATION_DIR"
    
    # 显示结果摘要
    if [ -f "$CALIBRATION_DIR/calibration_results.json" ]; then
        echo ""
        echo "📊 校准结果摘要:"
        echo "=================================================="
        
        # 使用Python快速解析结果
        python -c "
import json
import os

results_file = '$CALIBRATION_DIR/calibration_results.json'
if os.path.exists(results_file):
    with open(results_file, 'r') as f:
        results = json.load(f)
    
    print('校准方法效果对比:')
    print('-' * 50)
    
    for result in results:
        method = result['method']
        original_kc = result['original']['class_accuracies']['kc']
        calibrated_kc = result['calibrated']['class_accuracies']['kc']
        improvement = result['improvements']['class_accuracies']['kc']
        
        print(f'{method:20s}: KC准确率 {original_kc:.1f}% → {calibrated_kc:.1f}% ({improvement:+.1f}%)')
    
    print('')
    print('详细结果请查看: $CALIBRATION_DIR/calibration_results.json')
else:
    print('结果文件未找到，请检查日志')
" 2>/dev/null || echo "结果解析失败，请手动查看结果文件"
        
    fi
    
else
    echo "❌ 校准评估失败，请检查日志"
    echo "日志文件: logs/calibration_only_*.log"
fi

echo ""

# ==================== 生成对比图表 ====================
echo "📈 生成对比图表"
echo "=================================================="

if [ -f "$CALIBRATION_DIR/calibration_results.json" ]; then
    # 创建简单的结果可视化脚本
    cat > "${CALIBRATION_DIR}/visualize_results.py" << 'EOF'
#!/usr/bin/env python3
import json
import matplotlib.pyplot as plt
import numpy as np
import sys
import os

def create_calibration_comparison(results_file, save_dir):
    """创建校准结果对比图"""
    try:
        with open(results_file, 'r') as f:
            results = json.load(f)
        
        methods = []
        original_kc = []
        calibrated_kc = []
        improvements = []
        
        for result in results:
            methods.append(result['method'])
            original_kc.append(result['original']['class_accuracies']['kc'])
            calibrated_kc.append(result['calibrated']['class_accuracies']['kc'])
            improvements.append(result['improvements']['class_accuracies']['kc'])
        
        # 创建对比图
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        
        # KC准确率对比
        x = np.arange(len(methods))
        width = 0.35
        
        ax1.bar(x - width/2, original_kc, width, label='原始', alpha=0.8, color='lightcoral')
        ax1.bar(x + width/2, calibrated_kc, width, label='校准后', alpha=0.8, color='skyblue')
        
        ax1.set_xlabel('校准方法')
        ax1.set_ylabel('KC准确率 (%)')
        ax1.set_title('KC准确率对比')
        ax1.set_xticks(x)
        ax1.set_xticklabels(methods, rotation=45)
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 改进幅度
        colors = ['green' if imp > 0 else 'red' if imp < 0 else 'gray' for imp in improvements]
        ax2.bar(x, improvements, alpha=0.8, color=colors)
        ax2.set_xlabel('校准方法')
        ax2.set_ylabel('KC准确率改进 (%)')
        ax2.set_title('KC准确率改进幅度')
        ax2.set_xticks(x)
        ax2.set_xticklabels(methods, rotation=45)
        ax2.axhline(y=0, color='black', linestyle='-', alpha=0.5)
        ax2.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(os.path.join(save_dir, 'calibration_comparison.png'), dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"✅ 对比图表已保存到: {os.path.join(save_dir, 'calibration_comparison.png')}")
        
        # 生成文本报告
        report_file = os.path.join(save_dir, 'calibration_report.txt')
        with open(report_file, 'w') as f:
            f.write("校准评估报告\n")
            f.write("=" * 50 + "\n\n")
            
            f.write("校准方法效果对比:\n")
            f.write("-" * 30 + "\n")
            
            for i, result in enumerate(results):
                method = result['method']
                original_overall = result['original']['overall_accuracy']
                calibrated_overall = result['calibrated']['overall_accuracy']
                original_kc = result['original']['class_accuracies']['kc']
                calibrated_kc = result['calibrated']['class_accuracies']['kc']
                kc_improvement = result['improvements']['class_accuracies']['kc']
                
                f.write(f"\n{i+1}. {method.upper()}:\n")
                f.write(f"   总体准确率: {original_overall:.1f}% → {calibrated_overall:.1f}%\n")
                f.write(f"   KC准确率: {original_kc:.1f}% → {calibrated_kc:.1f}% ({kc_improvement:+.1f}%)\n")
                
                if kc_improvement > 2:
                    f.write(f"   ✅ 显著提升KC分类性能\n")
                elif kc_improvement > 0:
                    f.write(f"   🟡 轻微提升KC分类性能\n")
                else:
                    f.write(f"   ❌ 未提升KC分类性能\n")
            
            # 推荐
            best_method = max(results, key=lambda x: x['improvements']['class_accuracies']['kc'])
            f.write(f"\n推荐使用: {best_method['method'].upper()}\n")
            f.write(f"KC准确率提升: {best_method['improvements']['class_accuracies']['kc']:+.1f}%\n")
        
        print(f"✅ 文本报告已保存到: {report_file}")
        
        return True
        
    except Exception as e:
        print(f"❌ 可视化失败: {e}")
        return False

if __name__ == '__main__':
    results_file = sys.argv[1] if len(sys.argv) > 1 else 'calibration_results.json'
    save_dir = sys.argv[2] if len(sys.argv) > 2 else '.'
    
    if os.path.exists(results_file):
        create_calibration_comparison(results_file, save_dir)
    else:
        print(f"结果文件不存在: {results_file}")
EOF

    # 运行可视化
    python "${CALIBRATION_DIR}/visualize_results.py" "$CALIBRATION_DIR/calibration_results.json" "$CALIBRATION_DIR"
    
else
    echo "❌ 未找到校准结果文件，跳过可视化"
fi

echo ""
echo "=================================================="
echo "🎉 校准评估实验完成！"
echo "=================================================="

echo "实验结果目录: $BASE_OUTPUT_DIR"
echo ""
echo "📊 查看结果："
echo "  # 详细结果"
echo "  cat ${CALIBRATION_DIR}/calibration_results.json"
echo ""
echo "  # 文本报告"
echo "  cat ${CALIBRATION_DIR}/calibration_report.txt"
echo ""
echo "  # 对比图表"
echo "  open ${CALIBRATION_DIR}/calibration_comparison.png"
echo ""
echo "💡 使用建议："
echo "- 如果KC-EKC边界校准效果好，可以直接使用校准后的模型"
echo "- 校准器已保存，可以在推理时直接应用"
echo "- 如果效果显著，可以考虑进一步的混合决策策略训练"

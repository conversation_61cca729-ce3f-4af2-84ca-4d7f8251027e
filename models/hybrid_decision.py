#!/usr/bin/env python3
"""
混合决策策略模块

该模块实现了结合原型网络和分类器的混合决策策略，
特别针对KC类别提供更灵活的决策边界。
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np


class HybridClassifier(nn.Module):
    """
    混合分类器

    结合原型网络的距离度量和传统分类器的决策边界，
    为KC类别提供更强的分类能力。
    """
    def __init__(self, feature_dim=512, num_classes=3, hidden_dim=256, dropout_rate=0.3):
        super(HybridClassifier, self).__init__()
        self.feature_dim = feature_dim
        self.num_classes = num_classes

        # 传统分类器分支
        self.classifier = nn.Sequential(
            nn.Linear(feature_dim, hidden_dim),
            nn.BatchNorm1d(hidden_dim),
            nn.ReLU(inplace=True),
            nn.Dropout(dropout_rate),
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.BatchNorm1d(hidden_dim // 2),
            nn.ReLU(inplace=True),
            nn.Dropout(dropout_rate),
            nn.Linear(hidden_dim // 2, num_classes)
        )

        # KC特定分类器（专门用于KC vs 其他的二分类）
        self.kc_specific_classifier = nn.Sequential(
            nn.Linear(feature_dim, hidden_dim // 2),
            nn.BatchNorm1d(hidden_dim // 2),
            nn.ReLU(inplace=True),
            nn.Dropout(dropout_rate),
            nn.Linear(hidden_dim // 2, 2)  # KC vs 非KC
        )

        # 融合权重学习
        self.fusion_weights = nn.Parameter(torch.tensor([0.6, 0.4]))  # [prototype_weight, classifier_weight]

        # KC增强权重
        self.kc_enhancement_weight = nn.Parameter(torch.tensor(1.2))

    def forward(self, query_features, prototype_logits):
        """
        混合决策前向传播

        参数:
            query_features: 查询样本特征 [batch_size, feature_dim]
            prototype_logits: 原型网络的logits [batch_size, num_classes]

        返回:
            hybrid_logits: 混合决策的logits
            decision_info: 决策信息字典
        """
        batch_size = query_features.size(0)

        # 传统分类器预测
        classifier_logits = self.classifier(query_features)

        # KC特定分类器预测
        kc_specific_logits = self.kc_specific_classifier(query_features)
        kc_confidence = F.softmax(kc_specific_logits, dim=1)[:, 1]  # KC的置信度

        # 归一化融合权重
        normalized_weights = F.softmax(self.fusion_weights, dim=0)

        # 基础混合
        hybrid_logits = (normalized_weights[0] * prototype_logits +
                        normalized_weights[1] * classifier_logits)

        # KC增强：根据KC特定分类器的置信度增强KC类别的logits
        kc_enhancement = self.kc_enhancement_weight * kc_confidence.unsqueeze(1)
        hybrid_logits[:, 2] += kc_enhancement.squeeze()  # 增强KC类别（索引2）

        # 决策信息
        decision_info = {
            'prototype_logits': prototype_logits.detach(),
            'classifier_logits': classifier_logits.detach(),
            'kc_specific_logits': kc_specific_logits.detach(),
            'kc_confidence': kc_confidence.detach(),
            'fusion_weights': normalized_weights.detach(),
            'kc_enhancement_weight': self.kc_enhancement_weight.detach(),
            'kc_enhancement': kc_enhancement.detach()
        }

        return hybrid_logits, decision_info


class AdaptiveHybridClassifier(HybridClassifier):
    """
    自适应混合分类器

    根据样本特征动态调整原型网络和分类器的融合权重。
    """
    def __init__(self, feature_dim=512, num_classes=3, hidden_dim=256, dropout_rate=0.3):
        super(AdaptiveHybridClassifier, self).__init__(feature_dim, num_classes, hidden_dim, dropout_rate)

        # 自适应权重网络
        self.adaptive_weight_net = nn.Sequential(
            nn.Linear(feature_dim, hidden_dim // 4),
            nn.ReLU(inplace=True),
            nn.Dropout(dropout_rate),
            nn.Linear(hidden_dim // 4, 2),  # 输出两个权重
            nn.Softmax(dim=1)
        )

        # 难度评估网络
        self.difficulty_net = nn.Sequential(
            nn.Linear(feature_dim, hidden_dim // 4),
            nn.ReLU(inplace=True),
            nn.Linear(hidden_dim // 4, 1),
            nn.Sigmoid()
        )

    def forward(self, query_features, prototype_logits):
        """
        自适应混合决策前向传播
        """
        batch_size = query_features.size(0)

        # 传统分类器预测
        classifier_logits = self.classifier(query_features)

        # KC特定分类器预测
        kc_specific_logits = self.kc_specific_classifier(query_features)
        kc_confidence = F.softmax(kc_specific_logits, dim=1)[:, 1]

        # 自适应权重计算
        adaptive_weights = self.adaptive_weight_net(query_features)  # [batch_size, 2]

        # 难度评估
        difficulty_scores = self.difficulty_net(query_features).squeeze()  # [batch_size]

        # 对于困难样本，更多依赖分类器；对于简单样本，更多依赖原型网络
        difficulty_adjusted_weights = adaptive_weights.clone()
        difficulty_adjusted_weights[:, 0] *= (1 - difficulty_scores)  # 原型网络权重
        difficulty_adjusted_weights[:, 1] *= (1 + difficulty_scores)  # 分类器权重

        # 重新归一化
        difficulty_adjusted_weights = F.normalize(difficulty_adjusted_weights, p=1, dim=1)

        # 逐样本混合
        hybrid_logits = torch.zeros_like(prototype_logits)
        for i in range(batch_size):
            hybrid_logits[i] = (difficulty_adjusted_weights[i, 0] * prototype_logits[i] +
                               difficulty_adjusted_weights[i, 1] * classifier_logits[i])

        # KC增强
        kc_enhancement = self.kc_enhancement_weight * kc_confidence.unsqueeze(1)
        hybrid_logits[:, 2] += kc_enhancement.squeeze()

        # 决策信息
        decision_info = {
            'prototype_logits': prototype_logits.detach(),
            'classifier_logits': classifier_logits.detach(),
            'kc_specific_logits': kc_specific_logits.detach(),
            'kc_confidence': kc_confidence.detach(),
            'adaptive_weights': adaptive_weights.detach(),
            'difficulty_scores': difficulty_scores.detach(),
            'difficulty_adjusted_weights': difficulty_adjusted_weights.detach(),
            'kc_enhancement': kc_enhancement.detach()
        }

        return hybrid_logits, decision_info


class HybridProtoNet(nn.Module):
    """
    混合原型网络

    在原有ProtoNet基础上集成混合决策策略。
    """
    def __init__(self, base_protonet, hybrid_classifier_type='basic', **kwargs):
        super(HybridProtoNet, self).__init__()
        self.base_protonet = base_protonet
        # 从base_protonet获取特征维度，兼容不同的ProtoNet实现
        if hasattr(base_protonet, 'feature_dim'):
            self.feature_dim = base_protonet.feature_dim
        elif hasattr(base_protonet, 'feature_extractor'):
            # 从特征提取器获取特征维度
            if hasattr(base_protonet.feature_extractor, 'fc'):
                self.feature_dim = base_protonet.feature_extractor.fc.out_features
            else:
                self.feature_dim = 512  # 默认值
        else:
            self.feature_dim = 512  # 默认值

        if hasattr(base_protonet, 'n_classes'):
            self.n_classes = base_protonet.n_classes
        else:
            self.n_classes = 3  # 默认值

        # 创建混合分类器
        if hybrid_classifier_type == 'adaptive':
            self.hybrid_classifier = AdaptiveHybridClassifier(
                feature_dim=self.feature_dim,
                num_classes=self.n_classes,
                **kwargs
            )
        else:
            self.hybrid_classifier = HybridClassifier(
                feature_dim=self.feature_dim,
                num_classes=self.n_classes,
                **kwargs
            )

        self.use_hybrid = True

    def forward(self, support_images, support_labels, query_images, return_features=False, return_decision_info=False):
        """
        混合原型网络前向传播
        """
        # 首先计算原型
        self.base_protonet.compute_prototypes(support_images, support_labels)

        # 获取查询特征
        query_features = self.base_protonet.get_features(query_images)

        # 获取原型网络的预测（logits形式）
        prototype_probs = self.base_protonet(query_images)
        prototype_logits = torch.log(prototype_probs + 1e-8)  # 转换为logits

        # 如果需要支持特征
        if return_features:
            support_features = self.base_protonet.get_features(support_images)

        if self.use_hybrid:
            # 应用混合决策
            hybrid_logits, decision_info = self.hybrid_classifier(query_features, prototype_logits)

            if return_decision_info:
                if return_features:
                    return hybrid_logits, query_features, support_features, decision_info
                else:
                    return hybrid_logits, decision_info
            else:
                if return_features:
                    return hybrid_logits, query_features, support_features
                else:
                    return hybrid_logits
        else:
            # 使用原始原型网络
            if return_features:
                return prototype_logits, query_features, support_features
            else:
                return prototype_logits

    def get_query_features(self, query_images):
        """获取查询图像的特征"""
        return self.base_protonet.get_features(query_images)

    def set_hybrid_mode(self, use_hybrid=True):
        """设置是否使用混合决策"""
        self.use_hybrid = use_hybrid

    def compute_hybrid_loss(self, hybrid_logits, prototype_logits, classifier_logits, labels,
                           prototype_weight=0.7, classifier_weight=0.3, consistency_weight=0.1):
        """
        计算混合损失

        参数:
            hybrid_logits: 混合决策的logits
            prototype_logits: 原型网络的logits
            classifier_logits: 分类器的logits
            labels: 真实标签
            prototype_weight: 原型损失权重
            classifier_weight: 分类器损失权重
            consistency_weight: 一致性损失权重

        返回:
            total_loss: 总损失
            loss_components: 损失组件字典
        """
        # 主要损失（混合决策）
        main_loss = F.cross_entropy(hybrid_logits, labels)

        # 原型网络损失
        prototype_loss = F.cross_entropy(prototype_logits, labels)

        # 分类器损失
        classifier_loss = F.cross_entropy(classifier_logits, labels)

        # 一致性损失（鼓励原型网络和分类器的预测一致）
        prototype_probs = F.softmax(prototype_logits, dim=1)
        classifier_probs = F.softmax(classifier_logits, dim=1)
        consistency_loss = F.kl_div(
            F.log_softmax(prototype_logits, dim=1),
            classifier_probs,
            reduction='batchmean'
        )

        # 总损失
        total_loss = (main_loss +
                     prototype_weight * prototype_loss +
                     classifier_weight * classifier_loss +
                     consistency_weight * consistency_loss)

        loss_components = {
            'main_loss': main_loss.item(),
            'prototype_loss': prototype_loss.item(),
            'classifier_loss': classifier_loss.item(),
            'consistency_loss': consistency_loss.item(),
            'total_loss': total_loss.item()
        }

        return total_loss, loss_components


def create_hybrid_protonet(base_protonet, hybrid_type='basic', **kwargs):
    """
    创建混合原型网络的工厂函数

    参数:
        base_protonet: 基础原型网络
        hybrid_type: 混合类型 ('basic', 'adaptive')
        **kwargs: 混合分类器参数

    返回:
        hybrid_protonet: 混合原型网络实例
    """
    return HybridProtoNet(base_protonet, hybrid_classifier_type=hybrid_type, **kwargs)

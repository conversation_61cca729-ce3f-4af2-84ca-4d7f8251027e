import torch
import torch.nn as nn
import torch.nn.functional as F


class FeatureSeparationModule(nn.Module):
    """特征分离模块：增强E-KC和KC之间的特征区分

    该模块通过共享特征提取器和类别特定特征提取器，
    学习E-KC和KC之间的细微差异，提高分类性能。

    参数:
        feature_dim: 输入特征维度
        separation_dim: 分离特征维度
        dropout_rate: Dropout比率
        use_attention: 是否使用注意力机制
    """
    def __init__(self, feature_dim=512, separation_dim=128, dropout_rate=0.3, use_attention=True):
        super(FeatureSeparationModule, self).__init__()
        self.feature_dim = feature_dim
        self.separation_dim = separation_dim
        self.use_attention = use_attention

        # 共享特征提取器
        self.shared_extractor = nn.Sequential(
            nn.Linear(feature_dim, feature_dim),
            nn.BatchNorm1d(feature_dim),
            nn.ReLU(inplace=True),
            nn.Dropout(dropout_rate)
        )

        # E-KC特定特征提取器
        self.ekc_extractor = nn.Sequential(
            nn.Linear(feature_dim, separation_dim),
            nn.BatchNorm1d(separation_dim),
            nn.ReLU(inplace=True),
            nn.Dropout(dropout_rate * 0.5)  # 较低的dropout率
        )

        # KC特定特征提取器
        self.kc_extractor = nn.Sequential(
            nn.Linear(feature_dim, separation_dim),
            nn.BatchNorm1d(separation_dim),
            nn.ReLU(inplace=True),
            nn.Dropout(dropout_rate * 0.5)  # 较低的dropout率
        )

        # 注意力机制（可选）
        if self.use_attention:
            self.attention = nn.MultiheadAttention(
                embed_dim=separation_dim,
                num_heads=4,
                dropout=dropout_rate,
                batch_first=True
            )

        # 特征融合层
        fusion_input_dim = feature_dim + separation_dim * 2
        self.fusion = nn.Sequential(
            nn.Linear(fusion_input_dim, feature_dim),
            nn.BatchNorm1d(feature_dim),
            nn.ReLU(inplace=True),
            nn.Dropout(dropout_rate * 0.5)
        )

        # 残差连接的权重
        self.residual_weight = nn.Parameter(torch.tensor(0.5))

    def forward(self, x, labels=None):
        """
        前向传播

        参数:
            x: 输入特征，形状为 [batch_size, feature_dim]
            labels: 样本标签，形状为 [batch_size]，可选

        返回:
            enhanced_features: 增强后的特征，形状为 [batch_size, feature_dim]
            separation_info: 分离信息字典（用于分析和可视化）
        """
        batch_size = x.size(0)

        # 共享特征提取
        shared_features = self.shared_extractor(x)

        # 提取E-KC和KC特定特征
        ekc_features = self.ekc_extractor(shared_features)
        kc_features = self.kc_extractor(shared_features)

        # 如果提供了标签，使用标签信息增强特征分离
        if labels is not None:
            ekc_mask = (labels == 1).float().unsqueeze(1)  # E-KC类别
            kc_mask = (labels == 2).float().unsqueeze(1)   # KC类别
            normal_mask = (labels == 0).float().unsqueeze(1)  # Normal类别

            # 对于E-KC样本，增强E-KC特征，抑制KC特征
            ekc_enhanced = ekc_features * (1 + ekc_mask * 0.5)
            ekc_suppressed = kc_features * (1 - ekc_mask * 0.3)

            # 对于KC样本，增强KC特征，抑制E-KC特征
            kc_enhanced = kc_features * (1 + kc_mask * 0.5)
            kc_suppressed = ekc_features * (1 - kc_mask * 0.3)

            # 对于Normal样本，保持原始特征
            ekc_features = ekc_enhanced * (1 - normal_mask) + ekc_features * normal_mask
            kc_features = kc_enhanced * (1 - normal_mask) + kc_features * normal_mask

        # 应用注意力机制（如果启用）
        if self.use_attention:
            # 将E-KC和KC特征作为序列输入注意力机制
            combined_features = torch.stack([ekc_features, kc_features], dim=1)  # [batch_size, 2, separation_dim]
            attended_features, attention_weights = self.attention(
                combined_features, combined_features, combined_features
            )
            ekc_features = attended_features[:, 0, :]  # [batch_size, separation_dim]
            kc_features = attended_features[:, 1, :]   # [batch_size, separation_dim]

        # 融合特征
        combined = torch.cat([shared_features, ekc_features, kc_features], dim=1)
        fused_features = self.fusion(combined)

        # 残差连接
        enhanced_features = self.residual_weight * fused_features + (1 - self.residual_weight) * x

        # 准备分离信息（用于分析）
        separation_info = {
            'shared_features': shared_features.detach(),
            'ekc_features': ekc_features.detach(),
            'kc_features': kc_features.detach(),
            'fused_features': fused_features.detach(),
            'residual_weight': self.residual_weight.item()
        }

        if self.use_attention and 'attention_weights' in locals():
            separation_info['attention_weights'] = attention_weights.detach()

        return enhanced_features, separation_info

    def compute_separation_loss(self, ekc_features, kc_features, labels):
        """
        计算特征分离损失，鼓励E-KC和KC特征在特征空间中分离

        参数:
            ekc_features: E-KC特定特征
            kc_features: KC特定特征
            labels: 样本标签

        返回:
            separation_loss: 分离损失
        """
        # 只考虑E-KC和KC样本
        ekc_mask = (labels == 1)
        kc_mask = (labels == 2)

        if not ekc_mask.any() or not kc_mask.any():
            return torch.tensor(0.0, device=ekc_features.device)

        # 获取E-KC和KC样本的特征
        ekc_samples = ekc_features[ekc_mask]
        kc_samples = kc_features[kc_mask]

        # 计算类内聚合度（同类特征应该相似）
        ekc_center = ekc_samples.mean(dim=0, keepdim=True)
        kc_center = kc_samples.mean(dim=0, keepdim=True)

        intra_ekc_loss = F.mse_loss(ekc_samples, ekc_center.expand_as(ekc_samples))
        intra_kc_loss = F.mse_loss(kc_samples, kc_center.expand_as(kc_samples))

        # 计算类间分离度（不同类特征应该不同）
        inter_class_distance = F.cosine_similarity(ekc_center, kc_center, dim=1)
        inter_class_loss = torch.relu(inter_class_distance - 0.1)  # 鼓励余弦相似度小于0.1

        # 总分离损失
        separation_loss = intra_ekc_loss + intra_kc_loss + inter_class_loss.mean()

        return separation_loss


class AdaptiveFeatureSeparationModule(FeatureSeparationModule):
    """自适应特征分离模块：根据训练进度动态调整分离强度

    参数:
        feature_dim: 输入特征维度
        separation_dim: 分离特征维度
        dropout_rate: Dropout比率
        use_attention: 是否使用注意力机制
        adaptive_strength: 自适应强度调整因子
    """
    def __init__(self, feature_dim=512, separation_dim=128, dropout_rate=0.3,
                 use_attention=True, adaptive_strength=1.0):
        super(AdaptiveFeatureSeparationModule, self).__init__(
            feature_dim, separation_dim, dropout_rate, use_attention
        )
        self.adaptive_strength = adaptive_strength
        self.training_step = 0

        # 自适应权重学习
        self.adaptive_weight = nn.Parameter(torch.tensor(1.0))

    def forward(self, x, labels=None):
        """
        自适应前向传播

        参数:
            x: 输入特征
            labels: 样本标签

        返回:
            enhanced_features: 增强后的特征
            separation_info: 分离信息
        """
        # 更新训练步数
        if self.training:
            self.training_step += 1

        # 计算自适应强度（训练初期较强，后期逐渐减弱）
        adaptive_factor = self.adaptive_strength * torch.sigmoid(self.adaptive_weight)
        if self.training:
            # 训练过程中逐渐减弱分离强度
            decay_factor = max(0.1, 1.0 - self.training_step / 10000.0)
            adaptive_factor *= decay_factor

        # 调用父类的前向传播
        enhanced_features, separation_info = super().forward(x, labels)

        # 应用自适应强度
        enhanced_features = adaptive_factor * enhanced_features + (1 - adaptive_factor) * x

        # 更新分离信息
        separation_info['adaptive_factor'] = adaptive_factor.item()
        separation_info['training_step'] = self.training_step

        return enhanced_features, separation_info

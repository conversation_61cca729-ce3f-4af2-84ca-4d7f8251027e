#!/usr/bin/env python3
"""
类别校准后处理模块

该模块实现了多种校准策略，用于在推理阶段调整预测概率，
特别是针对E-KC和KC之间的决策边界进行优化。
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from sklearn.calibration import CalibratedClassifierCV
from sklearn.isotonic import IsotonicRegression
from sklearn.linear_model import LogisticRegression
import pickle
import os


class TemperatureScaling(nn.Module):
    """
    温度缩放校准
    
    通过学习一个温度参数来校准预测概率，使其更好地反映真实的置信度。
    """
    def __init__(self):
        super(TemperatureScaling, self).__init__()
        self.temperature = nn.Parameter(torch.ones(1))
    
    def forward(self, logits):
        """
        应用温度缩放
        
        参数:
            logits: 模型输出的logits，形状为 [batch_size, num_classes]
            
        返回:
            calibrated_probs: 校准后的概率，形状为 [batch_size, num_classes]
        """
        return F.softmax(logits / self.temperature, dim=1)
    
    def fit(self, logits, labels, lr=0.01, max_iter=50):
        """
        在验证集上拟合温度参数
        
        参数:
            logits: 验证集的logits
            labels: 验证集的真实标签
            lr: 学习率
            max_iter: 最大迭代次数
        """
        self.cuda() if logits.is_cuda else self.cpu()
        
        optimizer = torch.optim.LBFGS([self.temperature], lr=lr, max_iter=max_iter)
        
        def eval():
            loss = F.cross_entropy(logits / self.temperature, labels)
            loss.backward()
            return loss
        
        optimizer.step(eval)
        
        return self.temperature.item()


class ClassSpecificCalibration(nn.Module):
    """
    类别特定校准
    
    为每个类别学习不同的校准参数，特别针对E-KC和KC类别进行优化。
    """
    def __init__(self, num_classes=3):
        super(ClassSpecificCalibration, self).__init__()
        self.num_classes = num_classes
        # 为每个类别学习温度和偏置参数
        self.temperatures = nn.Parameter(torch.ones(num_classes))
        self.biases = nn.Parameter(torch.zeros(num_classes))
    
    def forward(self, logits):
        """
        应用类别特定校准
        
        参数:
            logits: 模型输出的logits
            
        返回:
            calibrated_probs: 校准后的概率
        """
        # 应用类别特定的温度和偏置
        calibrated_logits = logits / self.temperatures + self.biases
        return F.softmax(calibrated_logits, dim=1)
    
    def fit(self, logits, labels, lr=0.01, max_iter=50):
        """
        拟合类别特定参数
        """
        self.cuda() if logits.is_cuda else self.cpu()
        
        optimizer = torch.optim.LBFGS([self.temperatures, self.biases], lr=lr, max_iter=max_iter)
        
        def eval():
            calibrated_logits = logits / self.temperatures + self.biases
            loss = F.cross_entropy(calibrated_logits, labels)
            loss.backward()
            return loss
        
        optimizer.step(eval)
        
        return self.temperatures.detach().cpu().numpy(), self.biases.detach().cpu().numpy()


class KCEKCBoundaryCalibration:
    """
    KC-EKC边界校准
    
    专门针对KC和E-KC之间的决策边界进行校准，
    通过调整这两个类别之间的相对概率来提高KC的识别率。
    """
    def __init__(self):
        self.kc_boost_factor = 1.0
        self.ekc_suppress_factor = 1.0
        self.boundary_threshold = 0.5
        self.fitted = False
    
    def fit(self, probs, labels, target_kc_recall=0.8):
        """
        基于验证集拟合校准参数
        
        参数:
            probs: 验证集的预测概率 [batch_size, 3]
            labels: 验证集的真实标签
            target_kc_recall: 目标KC召回率
        """
        probs = probs.detach().cpu().numpy() if torch.is_tensor(probs) else probs
        labels = labels.detach().cpu().numpy() if torch.is_tensor(labels) else labels
        
        # 找到KC样本
        kc_mask = (labels == 2)
        ekc_mask = (labels == 1)
        
        if not kc_mask.any() or not ekc_mask.any():
            print("警告: 验证集中缺少KC或E-KC样本，使用默认参数")
            self.fitted = True
            return
        
        # 分析KC样本被误分类为E-KC的情况
        kc_probs = probs[kc_mask]
        kc_pred_classes = np.argmax(kc_probs, axis=1)
        
        # 计算当前KC召回率
        current_kc_recall = np.mean(kc_pred_classes == 2)
        print(f"当前KC召回率: {current_kc_recall:.3f}")
        
        if current_kc_recall >= target_kc_recall:
            print("KC召回率已达到目标，无需校准")
            self.fitted = True
            return
        
        # 寻找最佳校准参数
        best_kc_boost = 1.0
        best_ekc_suppress = 1.0
        best_recall = current_kc_recall
        
        # 网格搜索最佳参数
        for kc_boost in np.arange(1.0, 2.0, 0.1):
            for ekc_suppress in np.arange(0.5, 1.0, 0.1):
                # 应用校准
                calibrated_probs = self._apply_calibration(probs, kc_boost, ekc_suppress)
                
                # 计算KC召回率
                kc_calibrated = calibrated_probs[kc_mask]
                kc_calibrated_pred = np.argmax(kc_calibrated, axis=1)
                kc_recall = np.mean(kc_calibrated_pred == 2)
                
                # 检查是否更接近目标
                if abs(kc_recall - target_kc_recall) < abs(best_recall - target_kc_recall):
                    best_recall = kc_recall
                    best_kc_boost = kc_boost
                    best_ekc_suppress = ekc_suppress
        
        self.kc_boost_factor = best_kc_boost
        self.ekc_suppress_factor = best_ekc_suppress
        self.fitted = True
        
        print(f"校准参数: KC增强={self.kc_boost_factor:.2f}, E-KC抑制={self.ekc_suppress_factor:.2f}")
        print(f"校准后KC召回率: {best_recall:.3f}")
    
    def _apply_calibration(self, probs, kc_boost, ekc_suppress):
        """应用校准参数"""
        calibrated_probs = probs.copy()
        calibrated_probs[:, 1] *= ekc_suppress  # 抑制E-KC
        calibrated_probs[:, 2] *= kc_boost      # 增强KC
        
        # 重新归一化
        calibrated_probs = calibrated_probs / calibrated_probs.sum(axis=1, keepdims=True)
        return calibrated_probs
    
    def calibrate(self, probs):
        """
        校准预测概率
        
        参数:
            probs: 原始预测概率 [batch_size, 3]
            
        返回:
            calibrated_probs: 校准后的概率
        """
        if not self.fitted:
            print("警告: 校准器未拟合，返回原始概率")
            return probs
        
        is_tensor = torch.is_tensor(probs)
        device = probs.device if is_tensor else None
        
        if is_tensor:
            probs_np = probs.detach().cpu().numpy()
        else:
            probs_np = probs
        
        # 应用校准
        calibrated_probs_np = self._apply_calibration(probs_np, self.kc_boost_factor, self.ekc_suppress_factor)
        
        if is_tensor:
            return torch.tensor(calibrated_probs_np, device=device, dtype=probs.dtype)
        else:
            return calibrated_probs_np
    
    def save(self, filepath):
        """保存校准参数"""
        params = {
            'kc_boost_factor': self.kc_boost_factor,
            'ekc_suppress_factor': self.ekc_suppress_factor,
            'boundary_threshold': self.boundary_threshold,
            'fitted': self.fitted
        }
        with open(filepath, 'wb') as f:
            pickle.dump(params, f)
    
    def load(self, filepath):
        """加载校准参数"""
        with open(filepath, 'rb') as f:
            params = pickle.load(f)
        
        self.kc_boost_factor = params['kc_boost_factor']
        self.ekc_suppress_factor = params['ekc_suppress_factor']
        self.boundary_threshold = params['boundary_threshold']
        self.fitted = params['fitted']


class AdaptiveThresholdCalibration:
    """
    自适应阈值校准
    
    根据样本的置信度动态调整决策阈值，
    对于低置信度的样本给予KC类别更多的机会。
    """
    def __init__(self):
        self.confidence_thresholds = [0.6, 0.8]  # 低、中、高置信度阈值
        self.kc_boost_factors = [1.5, 1.2, 1.0]  # 对应的KC增强因子
        self.fitted = False
    
    def fit(self, probs, labels):
        """
        基于验证集数据拟合自适应阈值
        """
        probs = probs.detach().cpu().numpy() if torch.is_tensor(probs) else probs
        labels = labels.detach().cpu().numpy() if torch.is_tensor(labels) else labels
        
        # 计算预测置信度（最大概率）
        confidences = np.max(probs, axis=1)
        
        # 分析不同置信度区间的KC误分类情况
        kc_mask = (labels == 2)
        if not kc_mask.any():
            print("警告: 验证集中没有KC样本")
            self.fitted = True
            return
        
        kc_confidences = confidences[kc_mask]
        kc_probs = probs[kc_mask]
        kc_pred_classes = np.argmax(kc_probs, axis=1)
        
        # 分析不同置信度区间的表现
        for i, threshold in enumerate(self.confidence_thresholds):
            if i == 0:
                mask = kc_confidences < threshold
                region_name = f"低置信度 (<{threshold})"
            elif i == len(self.confidence_thresholds) - 1:
                mask = kc_confidences >= self.confidence_thresholds[i-1]
                region_name = f"高置信度 (>={self.confidence_thresholds[i-1]})"
            else:
                mask = (kc_confidences >= self.confidence_thresholds[i-1]) & (kc_confidences < threshold)
                region_name = f"中置信度 ({self.confidence_thresholds[i-1]}-{threshold})"
            
            if mask.any():
                region_accuracy = np.mean(kc_pred_classes[mask] == 2)
                print(f"{region_name}: KC准确率 {region_accuracy:.3f}, 样本数 {mask.sum()}")
        
        self.fitted = True
    
    def calibrate(self, probs):
        """
        应用自适应阈值校准
        """
        if not self.fitted:
            return probs
        
        is_tensor = torch.is_tensor(probs)
        device = probs.device if is_tensor else None
        
        if is_tensor:
            probs_np = probs.detach().cpu().numpy()
        else:
            probs_np = probs.copy()
        
        # 计算置信度
        confidences = np.max(probs_np, axis=1)
        
        # 根据置信度应用不同的KC增强因子
        for i, confidence in enumerate(confidences):
            if confidence < self.confidence_thresholds[0]:
                boost_factor = self.kc_boost_factors[0]  # 低置信度，强增强
            elif confidence < self.confidence_thresholds[1]:
                boost_factor = self.kc_boost_factors[1]  # 中置信度，中等增强
            else:
                boost_factor = self.kc_boost_factors[2]  # 高置信度，不增强
            
            probs_np[i, 2] *= boost_factor  # 增强KC概率
        
        # 重新归一化
        probs_np = probs_np / probs_np.sum(axis=1, keepdims=True)
        
        if is_tensor:
            return torch.tensor(probs_np, device=device, dtype=probs.dtype)
        else:
            return probs_np


def create_calibrator(method='kc_ekc_boundary', **kwargs):
    """
    创建校准器的工厂函数
    
    参数:
        method: 校准方法 ('temperature', 'class_specific', 'kc_ekc_boundary', 'adaptive_threshold')
        **kwargs: 校准器特定参数
        
    返回:
        calibrator: 校准器实例
    """
    if method == 'temperature':
        return TemperatureScaling()
    elif method == 'class_specific':
        return ClassSpecificCalibration(**kwargs)
    elif method == 'kc_ekc_boundary':
        return KCEKCBoundaryCalibration()
    elif method == 'adaptive_threshold':
        return AdaptiveThresholdCalibration()
    else:
        raise ValueError(f"未知的校准方法: {method}")

import torch
import torch.nn as nn
import torch.nn.functional as F
from .enhanced_feature_extractor import EnhancedKeratoconusFeatureExtractor
from .feature_separation import FeatureSeparationModule, AdaptiveFeatureSeparationModule


class FeatureSeparationExtractor(nn.Module):
    """带特征分离功能的特征提取器
    
    该类集成了增强版特征提取器和特征分离模块，
    专门用于提高E-KC和KC之间的区分能力。
    
    参数:
        pretrained: 是否使用预训练的ResNet-34
        feature_dim: 输出特征维度
        separation_dim: 分离特征维度
        dropout_rate: Dropout比率
        use_separation: 是否启用特征分离模块
        use_adaptive: 是否使用自适应特征分离
        use_attention: 是否在分离模块中使用注意力机制
        separation_weight: 特征分离损失的权重
    """
    def __init__(self, pretrained=True, feature_dim=512, separation_dim=128, 
                 dropout_rate=0.3, use_separation=True, use_adaptive=False,
                 use_attention=True, separation_weight=0.1):
        super(FeatureSeparationExtractor, self).__init__()
        
        # 基础特征提取器
        self.base_extractor = EnhancedKeratoconusFeatureExtractor(
            pretrained=pretrained,
            feature_dim=feature_dim,
            dropout_rate=dropout_rate
        )
        
        self.use_separation = use_separation
        self.separation_weight = separation_weight
        
        # 特征分离模块
        if self.use_separation:
            if use_adaptive:
                self.separation_module = AdaptiveFeatureSeparationModule(
                    feature_dim=feature_dim,
                    separation_dim=separation_dim,
                    dropout_rate=dropout_rate,
                    use_attention=use_attention
                )
            else:
                self.separation_module = FeatureSeparationModule(
                    feature_dim=feature_dim,
                    separation_dim=separation_dim,
                    dropout_rate=dropout_rate,
                    use_attention=use_attention
                )
    
    def forward(self, x, labels=None, return_separation_info=False):
        """
        前向传播
        
        参数:
            x: 输入图像，形状为 [batch_size, channels, height, width]
            labels: 样本标签，形状为 [batch_size]，可选
            return_separation_info: 是否返回分离信息
            
        返回:
            features: 特征表示，形状为 [batch_size, feature_dim]
            separation_info: 分离信息（如果return_separation_info=True）
        """
        # 基础特征提取
        base_features = self.base_extractor(x)
        
        if not self.use_separation:
            if return_separation_info:
                return base_features, None
            return base_features
        
        # 特征分离
        enhanced_features, separation_info = self.separation_module(base_features, labels)
        
        if return_separation_info:
            return enhanced_features, separation_info
        return enhanced_features
    
    def compute_separation_loss(self, x, labels):
        """
        计算特征分离损失
        
        参数:
            x: 输入图像
            labels: 样本标签
            
        返回:
            separation_loss: 分离损失
        """
        if not self.use_separation:
            return torch.tensor(0.0, device=x.device)
        
        # 获取基础特征
        base_features = self.base_extractor(x)
        
        # 获取分离特征
        _, separation_info = self.separation_module(base_features, labels)
        
        # 计算分离损失
        separation_loss = self.separation_module.compute_separation_loss(
            separation_info['ekc_features'],
            separation_info['kc_features'],
            labels
        )
        
        return separation_loss * self.separation_weight
    
    def get_separation_info(self, x, labels=None):
        """
        获取详细的分离信息（用于分析和可视化）
        
        参数:
            x: 输入图像
            labels: 样本标签
            
        返回:
            separation_info: 详细的分离信息
        """
        if not self.use_separation:
            return None
        
        base_features = self.base_extractor(x)
        _, separation_info = self.separation_module(base_features, labels)
        
        return separation_info


class EnhancedFeatureSeparationExtractor(FeatureSeparationExtractor):
    """增强版特征分离提取器
    
    在基础特征分离提取器的基础上，添加了更多的增强功能：
    - 多尺度特征分离
    - 动态权重调整
    - 特征对比学习
    """
    def __init__(self, pretrained=True, feature_dim=512, separation_dim=128,
                 dropout_rate=0.3, use_separation=True, use_adaptive=True,
                 use_attention=True, separation_weight=0.1, use_multiscale=True):
        super(EnhancedFeatureSeparationExtractor, self).__init__(
            pretrained, feature_dim, separation_dim, dropout_rate,
            use_separation, use_adaptive, use_attention, separation_weight
        )
        
        self.use_multiscale = use_multiscale
        
        # 多尺度特征分离
        if self.use_multiscale and self.use_separation:
            self.multiscale_separations = nn.ModuleList([
                FeatureSeparationModule(
                    feature_dim=feature_dim,
                    separation_dim=separation_dim // 2,
                    dropout_rate=dropout_rate,
                    use_attention=False  # 简化版本
                ),
                FeatureSeparationModule(
                    feature_dim=feature_dim,
                    separation_dim=separation_dim,
                    dropout_rate=dropout_rate,
                    use_attention=use_attention
                )
            ])
            
            # 多尺度融合层
            self.multiscale_fusion = nn.Sequential(
                nn.Linear(feature_dim * 3, feature_dim),  # 原始 + 两个尺度
                nn.BatchNorm1d(feature_dim),
                nn.ReLU(inplace=True),
                nn.Dropout(dropout_rate * 0.5)
            )
    
    def forward(self, x, labels=None, return_separation_info=False):
        """
        增强版前向传播
        
        参数:
            x: 输入图像
            labels: 样本标签
            return_separation_info: 是否返回分离信息
            
        返回:
            features: 增强后的特征
            separation_info: 分离信息（可选）
        """
        # 基础特征提取
        base_features = self.base_extractor(x)
        
        if not self.use_separation:
            if return_separation_info:
                return base_features, None
            return base_features
        
        # 主要特征分离
        main_features, main_separation_info = self.separation_module(base_features, labels)
        
        # 多尺度特征分离
        if self.use_multiscale:
            multiscale_features = []
            multiscale_infos = []
            
            for separation_module in self.multiscale_separations:
                ms_features, ms_info = separation_module(base_features, labels)
                multiscale_features.append(ms_features)
                multiscale_infos.append(ms_info)
            
            # 融合多尺度特征
            all_features = torch.cat([main_features] + multiscale_features, dim=1)
            final_features = self.multiscale_fusion(all_features)
            
            # 更新分离信息
            main_separation_info['multiscale_infos'] = multiscale_infos
        else:
            final_features = main_features
        
        if return_separation_info:
            return final_features, main_separation_info
        return final_features
    
    def compute_total_separation_loss(self, x, labels):
        """
        计算总的特征分离损失（包括多尺度）
        
        参数:
            x: 输入图像
            labels: 样本标签
            
        返回:
            total_loss: 总分离损失
        """
        if not self.use_separation:
            return torch.tensor(0.0, device=x.device)
        
        # 主要分离损失
        main_loss = self.compute_separation_loss(x, labels)
        
        if not self.use_multiscale:
            return main_loss
        
        # 多尺度分离损失
        base_features = self.base_extractor(x)
        multiscale_loss = 0.0
        
        for separation_module in self.multiscale_separations:
            _, separation_info = separation_module(base_features, labels)
            ms_loss = separation_module.compute_separation_loss(
                separation_info['ekc_features'],
                separation_info['kc_features'],
                labels
            )
            multiscale_loss += ms_loss
        
        # 总损失（多尺度损失权重较小）
        total_loss = main_loss + 0.5 * multiscale_loss * self.separation_weight
        
        return total_loss

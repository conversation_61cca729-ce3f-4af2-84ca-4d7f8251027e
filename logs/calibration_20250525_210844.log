/home/<USER>/anaconda3/envs/kc1/lib/python3.10/site-packages/torchvision/models/_utils.py:208: UserWarning: The parameter 'pretrained' is deprecated since 0.13 and may be removed in the future, please use 'weights' instead.
  warnings.warn(
/home/<USER>/anaconda3/envs/kc1/lib/python3.10/site-packages/torchvision/models/_utils.py:223: UserWarning: Arguments other than a weight enum or `None` for 'weights' are deprecated since 0.13 and may be removed in the future. The current behavior is equivalent to passing `weights=ResNet34_Weights.IMAGENET1K_V1`. You can also use `weights=ResNet34_Weights.DEFAULT` to get the most up-to-date weights.
  warnings.warn(msg)
成功导入KC特定增强模块
成功导入增强版GPU数据增强模块
使用设备: cuda:0
加载模型...
加载数据集...
验证集样本数: 67
测试集样本数: 71
收集验证集预测...
警告: 没有足够的有效类别进行3-way任务采样，尝试调整参数...
类别样本数量: {0: 33, 1: 5, 2: 29}
调整后的参数: n_shot=2, n_query=3
类别 normal (标签 0): 33个样本
类别 e-kc (标签 1): 5个样本
类别 kc (标签 2): 29个样本
开始收集 50 个任务的预测结果...

Collecting predictions:   0%|          | 0/50 [00:00<?, ?it/s]
Collecting predictions:   2%|▏         | 1/50 [00:02<02:25,  2.97s/it]
Collecting predictions:   4%|▍         | 2/50 [00:03<01:02,  1.31s/it]
Collecting predictions:   6%|▌         | 3/50 [00:03<00:36,  1.29it/s]
Collecting predictions:   8%|▊         | 4/50 [00:03<00:24,  1.91it/s]
Collecting predictions:  10%|█         | 5/50 [00:03<00:17,  2.59it/s]
Collecting predictions:  12%|█▏        | 6/50 [00:03<00:13,  3.32it/s]
Collecting predictions:  14%|█▍        | 7/50 [00:03<00:10,  4.01it/s]
Collecting predictions:  16%|█▌        | 8/50 [00:03<00:09,  4.67it/s]
Collecting predictions:  18%|█▊        | 9/50 [00:04<00:07,  5.24it/s]
Collecting predictions:  20%|██        | 10/50 [00:04<00:07,  5.66it/s]
Collecting predictions:  22%|██▏       | 11/50 [00:04<00:06,  6.02it/s]
Collecting predictions:  24%|██▍       | 12/50 [00:04<00:05,  6.34it/s]
Collecting predictions:  26%|██▌       | 13/50 [00:04<00:05,  6.57it/s]
Collecting predictions:  28%|██▊       | 14/50 [00:04<00:05,  6.66it/s]
Collecting predictions:  30%|███       | 15/50 [00:04<00:05,  6.79it/s]
Collecting predictions:  32%|███▏      | 16/50 [00:05<00:04,  6.91it/s]
Collecting predictions:  34%|███▍      | 17/50 [00:05<00:04,  7.29it/s]
Collecting predictions:  36%|███▌      | 18/50 [00:05<00:04,  7.59it/s]
Collecting predictions:  38%|███▊      | 19/50 [00:05<00:03,  7.83it/s]
Collecting predictions:  40%|████      | 20/50 [00:05<00:03,  7.98it/s]
Collecting predictions:  42%|████▏     | 21/50 [00:05<00:03,  8.12it/s]
Collecting predictions:  44%|████▍     | 22/50 [00:05<00:03,  8.00it/s]
Collecting predictions:  46%|████▌     | 23/50 [00:05<00:03,  8.06it/s]
Collecting predictions:  48%|████▊     | 24/50 [00:06<00:03,  8.13it/s]
Collecting predictions:  50%|█████     | 25/50 [00:06<00:03,  8.22it/s]
Collecting predictions:  52%|█████▏    | 26/50 [00:06<00:02,  8.24it/s]
Collecting predictions:  54%|█████▍    | 27/50 [00:06<00:02,  8.28it/s]
Collecting predictions:  56%|█████▌    | 28/50 [00:06<00:02,  8.26it/s]
Collecting predictions:  58%|█████▊    | 29/50 [00:06<00:02,  8.31it/s]
Collecting predictions:  60%|██████    | 30/50 [00:06<00:02,  8.37it/s]
Collecting predictions:  62%|██████▏   | 31/50 [00:06<00:02,  8.39it/s]
Collecting predictions:  64%|██████▍   | 32/50 [00:07<00:02,  8.40it/s]
Collecting predictions:  66%|██████▌   | 33/50 [00:07<00:02,  8.40it/s]
Collecting predictions:  68%|██████▊   | 34/50 [00:07<00:01,  8.40it/s]
Collecting predictions:  70%|███████   | 35/50 [00:07<00:01,  8.36it/s]
Collecting predictions:  72%|███████▏  | 36/50 [00:07<00:01,  8.33it/s]
Collecting predictions:  74%|███████▍  | 37/50 [00:07<00:01,  8.30it/s]
Collecting predictions:  76%|███████▌  | 38/50 [00:07<00:01,  8.27it/s]
Collecting predictions:  78%|███████▊  | 39/50 [00:07<00:01,  7.91it/s]
Collecting predictions:  80%|████████  | 40/50 [00:08<00:01,  7.70it/s]
Collecting predictions:  82%|████████▏ | 41/50 [00:08<00:01,  7.45it/s]
Collecting predictions:  84%|████████▍ | 42/50 [00:08<00:01,  7.38it/s]
Collecting predictions:  86%|████████▌ | 43/50 [00:08<00:00,  7.30it/s]
Collecting predictions:  88%|████████▊ | 44/50 [00:08<00:00,  7.27it/s]
Collecting predictions:  90%|█████████ | 45/50 [00:08<00:00,  7.23it/s]
Collecting predictions:  92%|█████████▏| 46/50 [00:08<00:00,  7.24it/s]
Collecting predictions:  94%|█████████▍| 47/50 [00:08<00:00,  7.25it/s]
Collecting predictions:  96%|█████████▌| 48/50 [00:09<00:00,  7.25it/s]
Collecting predictions:  98%|█████████▊| 49/50 [00:09<00:00,  7.22it/s]
Collecting predictions: 100%|██████████| 50/50 [00:09<00:00,  7.21it/s]
Collecting predictions: 100%|██████████| 50/50 [00:09<00:00,  5.32it/s]
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
成功收集到 450 个样本的预测结果
收集测试集预测...
警告: 没有足够的有效类别进行3-way任务采样，尝试调整参数...
类别样本数量: {0: 37, 1: 7, 2: 27}
调整后的参数: n_shot=3, n_query=4
类别 normal (标签 0): 37个样本
类别 e-kc (标签 1): 7个样本
类别 kc (标签 2): 27个样本
开始收集 50 个任务的预测结果...

Collecting predictions:   0%|          | 0/50 [00:00<?, ?it/s]
Collecting predictions:   2%|▏         | 1/50 [00:00<00:10,  4.53it/s]
Collecting predictions:   4%|▍         | 2/50 [00:00<00:09,  4.90it/s]
Collecting predictions:   6%|▌         | 3/50 [00:00<00:09,  5.01it/s]
Collecting predictions:   8%|▊         | 4/50 [00:00<00:09,  4.86it/s]
Collecting predictions:  10%|█         | 5/50 [00:01<00:09,  4.89it/s]
Collecting predictions:  12%|█▏        | 6/50 [00:01<00:08,  4.90it/s]
Collecting predictions:  14%|█▍        | 7/50 [00:01<00:08,  5.00it/s]
Collecting predictions:  16%|█▌        | 8/50 [00:01<00:08,  5.09it/s]
Collecting predictions:  18%|█▊        | 9/50 [00:01<00:07,  5.13it/s]
Collecting predictions:  20%|██        | 10/50 [00:01<00:07,  5.15it/s]
Collecting predictions:  22%|██▏       | 11/50 [00:02<00:07,  5.18it/s]
Collecting predictions:  24%|██▍       | 12/50 [00:02<00:07,  5.18it/s]
Collecting predictions:  26%|██▌       | 13/50 [00:02<00:07,  5.21it/s]
Collecting predictions:  28%|██▊       | 14/50 [00:02<00:06,  5.19it/s]
Collecting predictions:  30%|███       | 15/50 [00:02<00:06,  5.18it/s]
Collecting predictions:  32%|███▏      | 16/50 [00:03<00:06,  5.18it/s]
Collecting predictions:  34%|███▍      | 17/50 [00:03<00:06,  5.18it/s]
Collecting predictions:  36%|███▌      | 18/50 [00:03<00:06,  5.20it/s]
Collecting predictions:  38%|███▊      | 19/50 [00:03<00:05,  5.21it/s]
Collecting predictions:  40%|████      | 20/50 [00:03<00:05,  5.21it/s]
Collecting predictions:  42%|████▏     | 21/50 [00:04<00:05,  5.24it/s]
Collecting predictions:  44%|████▍     | 22/50 [00:04<00:05,  5.31it/s]
Collecting predictions:  46%|████▌     | 23/50 [00:04<00:04,  5.52it/s]
Collecting predictions:  48%|████▊     | 24/50 [00:04<00:04,  5.71it/s]
Collecting predictions:  50%|█████     | 25/50 [00:04<00:04,  5.82it/s]
Collecting predictions:  52%|█████▏    | 26/50 [00:04<00:04,  5.87it/s]
Collecting predictions:  54%|█████▍    | 27/50 [00:05<00:03,  5.95it/s]
Collecting predictions:  56%|█████▌    | 28/50 [00:05<00:03,  6.00it/s]
Collecting predictions:  58%|█████▊    | 29/50 [00:05<00:03,  6.04it/s]
Collecting predictions:  60%|██████    | 30/50 [00:05<00:03,  5.74it/s]
Collecting predictions:  62%|██████▏   | 31/50 [00:05<00:03,  5.67it/s]
Collecting predictions:  64%|██████▍   | 32/50 [00:06<00:03,  5.34it/s]
Collecting predictions:  66%|██████▌   | 33/50 [00:06<00:03,  5.30it/s]
Collecting predictions:  68%|██████▊   | 34/50 [00:06<00:03,  5.22it/s]
Collecting predictions:  70%|███████   | 35/50 [00:06<00:02,  5.19it/s]
Collecting predictions:  72%|███████▏  | 36/50 [00:06<00:02,  5.20it/s]
Collecting predictions:  74%|███████▍  | 37/50 [00:06<00:02,  5.19it/s]
Collecting predictions:  76%|███████▌  | 38/50 [00:07<00:02,  5.21it/s]
Collecting predictions:  78%|███████▊  | 39/50 [00:07<00:02,  5.08it/s]
Collecting predictions:  80%|████████  | 40/50 [00:07<00:02,  4.96it/s]
Collecting predictions:  82%|████████▏ | 41/50 [00:07<00:01,  5.00it/s]
Collecting predictions:  84%|████████▍ | 42/50 [00:07<00:01,  5.04it/s]
Collecting predictions:  86%|████████▌ | 43/50 [00:08<00:01,  5.08it/s]
Collecting predictions:  88%|████████▊ | 44/50 [00:08<00:01,  5.12it/s]
Collecting predictions:  90%|█████████ | 45/50 [00:08<00:00,  5.16it/s]
Collecting predictions:  92%|█████████▏| 46/50 [00:08<00:00,  5.18it/s]
Collecting predictions:  94%|█████████▍| 47/50 [00:08<00:00,  5.18it/s]
Collecting predictions:  96%|█████████▌| 48/50 [00:09<00:00,  5.20it/s]
Collecting predictions:  98%|█████████▊| 49/50 [00:09<00:00,  5.17it/s]
Collecting predictions: 100%|██████████| 50/50 [00:09<00:00,  5.27it/s]
Collecting predictions: 100%|██████████| 50/50 [00:09<00:00,  5.25it/s]
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 9 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 9 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 9 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 9 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 9 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 9 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 9 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 9 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 9 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 9 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 9 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 9 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 9 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 9 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 9 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 9 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 9 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 9 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 9 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 9 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 9 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 9 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 9 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 9 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 9 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 9 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 9 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 9 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 9 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 9 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 9 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 9 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 9 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 9 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 9 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 9 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 9 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 9 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 9 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 9 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 9 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 9 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 9 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 9 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 9 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 9 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 9 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 9 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 9 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 9 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
成功收集到 600 个样本的预测结果

开始校准评估...

评估校准方法: kc_ekc_boundary
当前KC召回率: 0.600
校准参数: KC增强=1.10, E-KC抑制=0.80
校准后KC召回率: 0.800
原始性能:
  总体准确率: 91.00%
  normal: 96.50%
  e-kc: 99.00%
  kc: 77.50%
校准后性能:
  总体准确率: 90.17%
  normal: 98.00%
  e-kc: 94.50%
  kc: 78.00%
改进:
  总体准确率: -0.83%
  normal: +1.50%
  e-kc: -4.50%
  kc: +0.50%
校准器已保存到: calibration_hybrid_experiments_20250525_210844/calibration_results/kc_ekc_boundary_calibrator.pkl

评估校准方法: temperature
温度参数: 0.271
原始性能:
  总体准确率: 91.00%
  normal: 96.50%
  e-kc: 99.00%
  kc: 77.50%
校准后性能:
  总体准确率: 91.00%
  normal: 96.50%
  e-kc: 99.00%
  kc: 77.50%
改进:
  总体准确率: +0.00%
  normal: +0.00%
  e-kc: +0.00%
  kc: +0.00%

评估校准方法: adaptive_threshold
低置信度 (<0.6): KC准确率 0.600, 样本数 150
原始性能:
  总体准确率: 91.00%
  normal: 96.50%
  e-kc: 99.00%
  kc: 77.50%
校准后性能:
  总体准确率: 90.00%
  normal: 96.50%
  e-kc: 94.50%
  kc: 79.00%
改进:
  总体准确率: -1.00%
  normal: +0.00%
  e-kc: -4.50%
  kc: +1.50%

评估校准方法: class_specific
类别温度: [0.12647344 0.68848884 0.41607854]
类别偏置: [-0.06646226 -0.05425847  0.12072072]
原始性能:
  总体准确率: 91.00%
  normal: 96.50%
  e-kc: 99.00%
  kc: 77.50%
校准后性能:
  总体准确率: 59.67%
  normal: 0.00%
  e-kc: 99.00%
  kc: 80.00%
改进:
  总体准确率: -31.33%
  normal: -96.50%
  e-kc: +0.00%
  kc: +2.50%

所有结果已保存到: calibration_hybrid_experiments_20250525_210844/calibration_results/calibration_results.json
对比图表已保存到: calibration_hybrid_experiments_20250525_210844/calibration_results
校准评估完成！

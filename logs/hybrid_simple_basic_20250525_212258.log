/home/<USER>/anaconda3/envs/kc1/lib/python3.10/site-packages/torchvision/models/_utils.py:208: UserWarning: The parameter 'pretrained' is deprecated since 0.13 and may be removed in the future, please use 'weights' instead.
  warnings.warn(
/home/<USER>/anaconda3/envs/kc1/lib/python3.10/site-packages/torchvision/models/_utils.py:223: UserWarning: Arguments other than a weight enum or `None` for 'weights' are deprecated since 0.13 and may be removed in the future. The current behavior is equivalent to passing `weights=ResNet34_Weights.IMAGENET1K_V1`. You can also use `weights=ResNet34_Weights.DEFAULT` to get the most up-to-date weights.
  warnings.warn(msg)
成功导入KC特定增强模块
成功导入增强版GPU数据增强模块
使用设备: cuda:0
训练集样本数: 323
验证集样本数: 67
测试集样本数: 71
类别 normal (标签 0): 164个样本
类别 e-kc (标签 1): 26个样本
类别 kc (标签 2): 133个样本
使用原型配置: [2, 4, 2]
使用增强版特征提取器，dropout率: 0.5
使用basic混合决策策略
MAML参数: 内循环学习率=0.3, 内循环步数=2
开始训练，共15个epoch
Epoch 1/15

Training tasks:   0%|          | 0/20 [00:00<?, ?it/s]
Training tasks:   0%|          | 0/20 [00:04<?, ?it/s]
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
Traceback (most recent call last):
  File "/home/<USER>/balanced_task_sampling/train_hybrid_decision.py", line 475, in <module>
    main()
  File "/home/<USER>/balanced_task_sampling/train_hybrid_decision.py", line 392, in main
    train_loss, train_acc, loss_components = train_epoch(
  File "/home/<USER>/balanced_task_sampling/train_hybrid_decision.py", line 198, in train_epoch
    epoch_loss_components[key] += value
KeyError: 'total_loss'

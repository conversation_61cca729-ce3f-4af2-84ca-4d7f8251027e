/home/<USER>/anaconda3/envs/kc1/lib/python3.10/site-packages/torchvision/models/_utils.py:208: UserWarning: The parameter 'pretrained' is deprecated since 0.13 and may be removed in the future, please use 'weights' instead.
  warnings.warn(
/home/<USER>/anaconda3/envs/kc1/lib/python3.10/site-packages/torchvision/models/_utils.py:223: UserWarning: Arguments other than a weight enum or `None` for 'weights' are deprecated since 0.13 and may be removed in the future. The current behavior is equivalent to passing `weights=ResNet34_Weights.IMAGENET1K_V1`. You can also use `weights=ResNet34_Weights.DEFAULT` to get the most up-to-date weights.
  warnings.warn(msg)
成功导入KC特定增强模块
成功导入增强版GPU数据增强模块
使用设备: cuda:0
训练集样本数: 323
验证集样本数: 67
测试集样本数: 71
类别 normal (标签 0): 164个样本
类别 e-kc (标签 1): 26个样本
类别 kc (标签 2): 133个样本
使用原型配置: [2, 4, 2]
使用增强版特征提取器，dropout率: 0.5
使用basic混合决策策略
MAML参数: 内循环学习率=0.3, 内循环步数=2
开始训练，共15个epoch
Epoch 1/15

Training tasks:   0%|          | 0/20 [00:00<?, ?it/s]
Training tasks:   5%|▌         | 1/20 [00:03<01:09,  3.65s/it]
Training tasks:  10%|█         | 2/20 [00:04<00:37,  2.09s/it]
Training tasks:  15%|█▌        | 3/20 [00:05<00:26,  1.57s/it]
Training tasks:  20%|██        | 4/20 [00:06<00:21,  1.33s/it]
Training tasks:  25%|██▌       | 5/20 [00:07<00:18,  1.21s/it]
Training tasks:  30%|███       | 6/20 [00:08<00:15,  1.12s/it]
Training tasks:  35%|███▌      | 7/20 [00:09<00:13,  1.07s/it]
Training tasks:  40%|████      | 8/20 [00:10<00:12,  1.03s/it]
Training tasks:  45%|████▌     | 9/20 [00:11<00:10,  1.00it/s]
Training tasks:  50%|█████     | 10/20 [00:12<00:09,  1.02it/s]
Training tasks:  55%|█████▌    | 11/20 [00:13<00:08,  1.03it/s]
Training tasks:  60%|██████    | 12/20 [00:14<00:07,  1.04it/s]
Training tasks:  65%|██████▌   | 13/20 [00:15<00:06,  1.04it/s]
Training tasks:  70%|███████   | 14/20 [00:16<00:05,  1.05it/s]
Training tasks:  75%|███████▌  | 15/20 [00:16<00:04,  1.07it/s]
Training tasks:  80%|████████  | 16/20 [00:17<00:03,  1.07it/s]
Training tasks:  85%|████████▌ | 17/20 [00:18<00:02,  1.07it/s]
Training tasks:  90%|█████████ | 18/20 [00:19<00:01,  1.07it/s]
Training tasks:  95%|█████████▌| 19/20 [00:20<00:00,  1.07it/s]
Training tasks: 100%|██████████| 20/20 [00:21<00:00,  1.07it/s]
Training tasks: 100%|██████████| 20/20 [00:21<00:00,  1.08s/it]
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
Epoch 1: Train Loss: 1.7092, Train Acc: 0.5944
Epoch 2/15

Training tasks:   0%|          | 0/20 [00:00<?, ?it/s]
Training tasks:   5%|▌         | 1/20 [00:00<00:18,  1.01it/s]
Training tasks:  10%|█         | 2/20 [00:01<00:17,  1.03it/s]
Training tasks:  15%|█▌        | 3/20 [00:02<00:16,  1.05it/s]
Training tasks:  20%|██        | 4/20 [00:03<00:15,  1.05it/s]
Training tasks:  25%|██▌       | 5/20 [00:04<00:14,  1.06it/s]
Training tasks:  30%|███       | 6/20 [00:05<00:13,  1.06it/s]
Training tasks:  35%|███▌      | 7/20 [00:06<00:12,  1.06it/s]
Training tasks:  40%|████      | 8/20 [00:07<00:11,  1.07it/s]
Training tasks:  45%|████▌     | 9/20 [00:08<00:10,  1.07it/s]
Training tasks:  50%|█████     | 10/20 [00:09<00:09,  1.07it/s]
Training tasks:  55%|█████▌    | 11/20 [00:10<00:08,  1.07it/s]
Training tasks:  60%|██████    | 12/20 [00:11<00:07,  1.07it/s]
Training tasks:  65%|██████▌   | 13/20 [00:12<00:06,  1.07it/s]
Training tasks:  70%|███████   | 14/20 [00:13<00:05,  1.07it/s]
Training tasks:  75%|███████▌  | 15/20 [00:14<00:04,  1.07it/s]
Training tasks:  80%|████████  | 16/20 [00:15<00:03,  1.07it/s]
Training tasks:  85%|████████▌ | 17/20 [00:16<00:03,  1.00s/it]
Training tasks:  90%|█████████ | 18/20 [00:17<00:01,  1.01it/s]
Training tasks:  95%|█████████▌| 19/20 [00:18<00:01,  1.06s/it]
Training tasks: 100%|██████████| 20/20 [00:19<00:00,  1.02s/it]
Training tasks: 100%|██████████| 20/20 [00:19<00:00,  1.04it/s]
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
警告: 没有足够的有效类别进行3-way任务采样，尝试调整参数...
类别样本数量: {0: 33, 1: 5, 2: 29}
调整后的参数: n_shot=2, n_query=3
类别 normal (标签 0): 33个样本
类别 e-kc (标签 1): 5个样本
类别 kc (标签 2): 29个样本

Validating tasks:   0%|          | 0/10 [00:00<?, ?it/s]
Validating tasks:   0%|          | 0/10 [00:00<?, ?it/s]
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
Traceback (most recent call last):
  File "/home/<USER>/balanced_task_sampling/train_hybrid_decision.py", line 477, in <module>
    main()
  File "/home/<USER>/balanced_task_sampling/train_hybrid_decision.py", line 406, in main
    val_loss, val_acc = validate(model, val_dataset, device, args)
  File "/home/<USER>/balanced_task_sampling/train_hybrid_decision.py", line 246, in validate
    hybrid_logits = model(support_images, support_labels, query_images)
  File "/home/<USER>/anaconda3/envs/kc1/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1739, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/home/<USER>/anaconda3/envs/kc1/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1750, in _call_impl
    return forward_call(*args, **kwargs)
TypeError: MAMLProtoNet.forward() takes 2 positional arguments but 4 were given

/home/<USER>/anaconda3/envs/kc1/lib/python3.10/site-packages/torchvision/models/_utils.py:208: UserWarning: The parameter 'pretrained' is deprecated since 0.13 and may be removed in the future, please use 'weights' instead.
  warnings.warn(
/home/<USER>/anaconda3/envs/kc1/lib/python3.10/site-packages/torchvision/models/_utils.py:223: UserWarning: Arguments other than a weight enum or `None` for 'weights' are deprecated since 0.13 and may be removed in the future. The current behavior is equivalent to passing `weights=ResNet34_Weights.IMAGENET1K_V1`. You can also use `weights=ResNet34_Weights.DEFAULT` to get the most up-to-date weights.
  warnings.warn(msg)
成功导入KC特定增强模块
成功导入增强版GPU数据增强模块
使用设备: cuda:0
训练集样本数: 323
验证集样本数: 67
测试集样本数: 71
类别 normal (标签 0): 164个样本
类别 e-kc (标签 1): 26个样本
类别 kc (标签 2): 133个样本
使用原型配置: [2, 4, 2]
使用增强版特征提取器，dropout率: 0.5
使用adaptive混合决策策略
MAML参数: 内循环学习率=0.3, 内循环步数=2
开始训练，共15个epoch
Epoch 1/15

Training tasks:   0%|          | 0/20 [00:00<?, ?it/s]
Training tasks:   5%|▌         | 1/20 [00:03<01:10,  3.69s/it]
Training tasks:  10%|█         | 2/20 [00:04<00:37,  2.10s/it]
Training tasks:  15%|█▌        | 3/20 [00:05<00:26,  1.58s/it]
Training tasks:  20%|██        | 4/20 [00:06<00:21,  1.33s/it]
Training tasks:  20%|██        | 4/20 [00:06<00:27,  1.74s/it]
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
Traceback (most recent call last):
  File "/home/<USER>/balanced_task_sampling/train_hybrid_decision.py", line 477, in <module>
    main()
  File "/home/<USER>/balanced_task_sampling/train_hybrid_decision.py", line 394, in main
    train_loss, train_acc, loss_components = train_epoch(
  File "/home/<USER>/balanced_task_sampling/train_hybrid_decision.py", line 157, in train_epoch
    task = task_sampler.sample_task()
  File "/home/<USER>/balanced_task_sampling/datasets/balanced_task_sampler.py", line 158, in sample_task
    result = self.dataset[idx]
  File "/home/<USER>/balanced_task_sampling/datasets/keratoconus_dataset.py", line 142, in __getitem__
    image = self.transform(image)
  File "/home/<USER>/anaconda3/envs/kc1/lib/python3.10/site-packages/torchvision/transforms/transforms.py", line 95, in __call__
    img = t(img)
  File "/home/<USER>/anaconda3/envs/kc1/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1739, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/home/<USER>/anaconda3/envs/kc1/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1750, in _call_impl
    return forward_call(*args, **kwargs)
  File "/home/<USER>/anaconda3/envs/kc1/lib/python3.10/site-packages/torchvision/transforms/transforms.py", line 1280, in forward
    img = F.adjust_hue(img, hue_factor)
  File "/home/<USER>/anaconda3/envs/kc1/lib/python3.10/site-packages/torchvision/transforms/functional.py", line 968, in adjust_hue
    return F_pil.adjust_hue(img, hue_factor)
  File "/home/<USER>/anaconda3/envs/kc1/lib/python3.10/site-packages/torchvision/transforms/_functional_pil.py", line 117, in adjust_hue
    img = Image.merge("HSV", (h, s, v)).convert(input_mode)
  File "/home/<USER>/anaconda3/envs/kc1/lib/python3.10/site-packages/PIL/Image.py", line 1143, in convert
    im = self.im.convert(mode, dither)
KeyboardInterrupt

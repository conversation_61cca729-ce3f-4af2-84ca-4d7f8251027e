python train_balanced_task_sampling.py   --train_csv /home/<USER>/balanced_task_sampling/split_result/train_set.csv   --val_csv /home/<USER>/balanced_task_sampling/split_result/val_set.csv   --test_csv /home/<USER>/balanced_task_sampling/split_result/test_set.csv   --epochs 30   --lr 0.0002   --weight_decay 5e-4   --feature_dim 512   --save_dir results_optimized_20250525_152539_gpu0,1   --proto_counts 2,4,2   --pretrained   --inner_lr 0.3   --inner_steps 2   --n_way 3   --n_shot 5   --n_query 15   --tasks_per_epoch 30   --meta_batch_size 8   --no_augmentation   --mixup_alpha 0.0   --cutmix_prob 0.0   --num_workers 4   --augment_factor 1   --kc_augment_factor 1   --seed 42   --early_kc_weight 8.0   --kc_weight 4.0   --focal_gamma 2.0   --val_frequency 1   --early_stopping 5   --dropout 0.5    --use_separate_test_sampler --use_balanced_task_sampler --kc_shot_multiplier 3.0 --early_kc_shot_multiplier 1.5   --use_contrastive --contrastive_weight 0.5 --temperature 0.07 --hard_mining_ratio 0.7 --early_normal_weight 3.0      --use_lr_scheduler --scheduler_type plateau --plateau_factor 0.5 --plateau_patience 2 --min_lr 1e-6
/home/<USER>/anaconda3/envs/kc1/lib/python3.10/site-packages/torchvision/models/_utils.py:208: UserWarning: The parameter 'pretrained' is deprecated since 0.13 and may be removed in the future, please use 'weights' instead.
  warnings.warn(
/home/<USER>/anaconda3/envs/kc1/lib/python3.10/site-packages/torchvision/models/_utils.py:223: UserWarning: Arguments other than a weight enum or `None` for 'weights' are deprecated since 0.13 and may be removed in the future. The current behavior is equivalent to passing `weights=ResNet34_Weights.IMAGENET1K_V1`. You can also use `weights=ResNet34_Weights.DEFAULT` to get the most up-to-date weights.
  warnings.warn(msg)
/home/<USER>/anaconda3/envs/kc1/lib/python3.10/site-packages/torch/optim/lr_scheduler.py:62: UserWarning: The verbose parameter is deprecated. Please use get_last_lr() to access the learning rate.
  warnings.warn(
成功导入增强版特征提取器
成功导入归一化特征提取器
成功导入增强版损失函数
成功导入增强版MAML模型
成功导入KC特定增强模块
成功导入增强版GPU数据增强模块
使用设备: cuda
禁用所有数据增强，直接加载原始数据集...
使用平衡任务采样器
类别 normal (标签 0): 164个样本
类别 e-kc (标签 1): 26个样本
类别 kc (标签 2): 133个样本
使用原型配置: [2, 4, 2]
使用增强版特征提取器，dropout率: 0.5
使用增强版MAML-ProtoNet模型，内循环学习率: 0.3, 内循环步数: 2
使用ReduceLROnPlateau学习率调度器，因子: 0.5, 耐心值: 2
开始训练，共30个epoch
Epoch 1/30
当前学习率: 0.000200
使用类别权重: 正常=1.0, 早期圆锥角膜=8.0, 圆锥角膜=4.0
使用增强版对比损失函数，温度: 0.07, 硬负样本挖掘比例: 0.7, 早期圆锥角膜与正常样本对比权重: 3.0, 圆锥角膜与正常样本对比权重: 1.0

Training tasks:   0%|          | 0/30 [00:00<?, ?it/s]
Training tasks:   3%|▎         | 1/30 [00:04<02:21,  4.87s/it]
Training tasks:   7%|▋         | 2/30 [00:06<01:17,  2.75s/it]
Training tasks:  10%|█         | 3/30 [00:07<00:53,  2.00s/it]
Training tasks:  13%|█▎        | 4/30 [00:08<00:43,  1.66s/it]
Training tasks:  17%|█▋        | 5/30 [00:09<00:37,  1.48s/it]
Training tasks:  20%|██        | 6/30 [00:10<00:32,  1.36s/it]
Training tasks:  23%|██▎       | 7/30 [00:11<00:29,  1.27s/it]
Training tasks:  27%|██▋       | 8/30 [00:12<00:26,  1.21s/it]
Training tasks:  30%|███       | 9/30 [00:13<00:24,  1.16s/it]
Training tasks:  33%|███▎      | 10/30 [00:14<00:22,  1.13s/it]
Training tasks:  37%|███▋      | 11/30 [00:15<00:20,  1.09s/it]
Training tasks:  40%|████      | 12/30 [00:17<00:19,  1.10s/it]
Training tasks:  43%|████▎     | 13/30 [00:18<00:18,  1.08s/it]
Training tasks:  47%|████▋     | 14/30 [00:19<00:16,  1.06s/it]
Training tasks:  50%|█████     | 15/30 [00:20<00:15,  1.06s/it]
Training tasks:  53%|█████▎    | 16/30 [00:21<00:14,  1.05s/it]
Training tasks:  57%|█████▋    | 17/30 [00:22<00:14,  1.10s/it]
Training tasks:  60%|██████    | 18/30 [00:23<00:13,  1.13s/it]
Training tasks:  63%|██████▎   | 19/30 [00:24<00:12,  1.12s/it]
Training tasks:  67%|██████▋   | 20/30 [00:25<00:10,  1.10s/it]
Training tasks:  70%|███████   | 21/30 [00:26<00:10,  1.12s/it]
Training tasks:  73%|███████▎  | 22/30 [00:28<00:08,  1.10s/it]
Training tasks:  77%|███████▋  | 23/30 [00:29<00:07,  1.08s/it]
Training tasks:  80%|████████  | 24/30 [00:30<00:06,  1.07s/it]
Training tasks:  83%|████████▎ | 25/30 [00:31<00:05,  1.06s/it]
Training tasks:  87%|████████▋ | 26/30 [00:32<00:04,  1.07s/it]
Training tasks:  90%|█████████ | 27/30 [00:33<00:03,  1.06s/it]
Training tasks:  93%|█████████▎| 28/30 [00:34<00:02,  1.16s/it]
Training tasks:  97%|█████████▋| 29/30 [00:35<00:01,  1.14s/it]
Training tasks: 100%|██████████| 30/30 [00:36<00:00,  1.13s/it]
Training tasks: 100%|██████████| 30/30 [00:36<00:00,  1.23s/it]
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
警告: 没有足够的有效类别进行3-way任务采样，尝试调整参数...
类别样本数量: {0: 33, 1: 5, 2: 29}
调整后的参数: n_shot=2, n_query=3
类别 normal (标签 0): 33个样本
类别 e-kc (标签 1): 5个样本
类别 kc (标签 2): 29个样本

Validating tasks:   0%|          | 0/10 [00:00<?, ?it/s]
Validating tasks:  10%|█         | 1/10 [00:00<00:01,  4.87it/s]
Validating tasks:  20%|██        | 2/10 [00:00<00:01,  5.72it/s]
Validating tasks:  30%|███       | 3/10 [00:00<00:01,  6.23it/s]
Validating tasks:  40%|████      | 4/10 [00:00<00:00,  6.43it/s]
Validating tasks:  50%|█████     | 5/10 [00:00<00:00,  6.61it/s]
Validating tasks:  60%|██████    | 6/10 [00:00<00:00,  6.64it/s]
Validating tasks:  70%|███████   | 7/10 [00:01<00:00,  6.71it/s]
Validating tasks:  80%|████████  | 8/10 [00:01<00:00,  6.76it/s]
Validating tasks:  90%|█████████ | 9/10 [00:01<00:00,  6.42it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  6.45it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  6.40it/s]
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
Epoch 1: Train Loss: 0.5734, Train Acc: 0.5163, Val Loss: 2.2066, Val Acc: 0.4222
保存最佳模型，验证准确率: 0.4222
Epoch 2/30
当前学习率: 0.000200
使用类别权重: 正常=1.0, 早期圆锥角膜=8.0, 圆锥角膜=4.0
使用增强版对比损失函数，温度: 0.07, 硬负样本挖掘比例: 0.7, 早期圆锥角膜与正常样本对比权重: 3.0, 圆锥角膜与正常样本对比权重: 1.0

Training tasks:   0%|          | 0/30 [00:00<?, ?it/s]
Training tasks:   3%|▎         | 1/30 [00:01<00:34,  1.19s/it]
Training tasks:   7%|▋         | 2/30 [00:02<00:32,  1.14s/it]
Training tasks:  10%|█         | 3/30 [00:03<00:30,  1.12s/it]
Training tasks:  13%|█▎        | 4/30 [00:04<00:29,  1.13s/it]
Training tasks:  17%|█▋        | 5/30 [00:05<00:27,  1.11s/it]
Training tasks:  20%|██        | 6/30 [00:06<00:26,  1.09s/it]
Training tasks:  23%|██▎       | 7/30 [00:07<00:25,  1.11s/it]
Training tasks:  27%|██▋       | 8/30 [00:08<00:24,  1.11s/it]
Training tasks:  30%|███       | 9/30 [00:10<00:23,  1.13s/it]
Training tasks:  33%|███▎      | 10/30 [00:11<00:22,  1.11s/it]
Training tasks:  37%|███▋      | 11/30 [00:12<00:20,  1.08s/it]
Training tasks:  40%|████      | 12/30 [00:13<00:19,  1.10s/it]
Training tasks:  43%|████▎     | 13/30 [00:14<00:19,  1.12s/it]
Training tasks:  47%|████▋     | 14/30 [00:15<00:17,  1.11s/it]
Training tasks:  50%|█████     | 15/30 [00:16<00:17,  1.15s/it]
Training tasks:  53%|█████▎    | 16/30 [00:17<00:15,  1.14s/it]
Training tasks:  57%|█████▋    | 17/30 [00:19<00:14,  1.14s/it]
Training tasks:  60%|██████    | 18/30 [00:20<00:14,  1.17s/it]
Training tasks:  63%|██████▎   | 19/30 [00:21<00:12,  1.15s/it]
Training tasks:  67%|██████▋   | 20/30 [00:22<00:11,  1.19s/it]
Training tasks:  70%|███████   | 21/30 [00:23<00:10,  1.17s/it]
Training tasks:  73%|███████▎  | 22/30 [00:24<00:09,  1.15s/it]
Training tasks:  77%|███████▋  | 23/30 [00:26<00:08,  1.19s/it]
Training tasks:  80%|████████  | 24/30 [00:27<00:06,  1.16s/it]
Training tasks:  83%|████████▎ | 25/30 [00:28<00:05,  1.15s/it]
Training tasks:  87%|████████▋ | 26/30 [00:29<00:04,  1.11s/it]
Training tasks:  90%|█████████ | 27/30 [00:30<00:03,  1.14s/it]
Training tasks:  93%|█████████▎| 28/30 [00:31<00:02,  1.15s/it]
Training tasks:  97%|█████████▋| 29/30 [00:33<00:01,  1.20s/it]
Training tasks: 100%|██████████| 30/30 [00:34<00:00,  1.19s/it]
Training tasks: 100%|██████████| 30/30 [00:34<00:00,  1.14s/it]
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
警告: 没有足够的有效类别进行3-way任务采样，尝试调整参数...
类别样本数量: {0: 33, 1: 5, 2: 29}
调整后的参数: n_shot=2, n_query=3
类别 normal (标签 0): 33个样本
类别 e-kc (标签 1): 5个样本
类别 kc (标签 2): 29个样本

Validating tasks:   0%|          | 0/10 [00:00<?, ?it/s]
Validating tasks:  10%|█         | 1/10 [00:00<00:01,  6.92it/s]
Validating tasks:  20%|██        | 2/10 [00:00<00:01,  6.86it/s]
Validating tasks:  30%|███       | 3/10 [00:00<00:01,  6.32it/s]
Validating tasks:  40%|████      | 4/10 [00:00<00:00,  6.12it/s]
Validating tasks:  50%|█████     | 5/10 [00:00<00:00,  6.45it/s]
Validating tasks:  60%|██████    | 6/10 [00:00<00:00,  6.68it/s]
Validating tasks:  70%|███████   | 7/10 [00:01<00:00,  6.52it/s]
Validating tasks:  80%|████████  | 8/10 [00:01<00:00,  6.68it/s]
Validating tasks:  90%|█████████ | 9/10 [00:01<00:00,  6.76it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  6.85it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  6.65it/s]
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
Epoch 2: Train Loss: 0.0118, Train Acc: 0.5467, Val Loss: 1.5942, Val Acc: 0.5444
保存最佳模型，验证准确率: 0.5444
Epoch 3/30
当前学习率: 0.000200
使用类别权重: 正常=1.0, 早期圆锥角膜=8.0, 圆锥角膜=4.0
使用增强版对比损失函数，温度: 0.07, 硬负样本挖掘比例: 0.7, 早期圆锥角膜与正常样本对比权重: 3.0, 圆锥角膜与正常样本对比权重: 1.0

Training tasks:   0%|          | 0/30 [00:00<?, ?it/s]
Training tasks:   3%|▎         | 1/30 [00:01<00:36,  1.27s/it]
Training tasks:   7%|▋         | 2/30 [00:02<00:32,  1.15s/it]
Training tasks:  10%|█         | 3/30 [00:03<00:30,  1.14s/it]
Training tasks:  13%|█▎        | 4/30 [00:04<00:33,  1.28s/it]
Training tasks:  17%|█▋        | 5/30 [00:06<00:30,  1.22s/it]
Training tasks:  20%|██        | 6/30 [00:07<00:29,  1.22s/it]
Training tasks:  23%|██▎       | 7/30 [00:08<00:26,  1.17s/it]
Training tasks:  27%|██▋       | 8/30 [00:09<00:26,  1.19s/it]
Training tasks:  30%|███       | 9/30 [00:10<00:25,  1.22s/it]
Training tasks:  33%|███▎      | 10/30 [00:12<00:24,  1.23s/it]
Training tasks:  37%|███▋      | 11/30 [00:13<00:22,  1.18s/it]
Training tasks:  40%|████      | 12/30 [00:14<00:20,  1.15s/it]
Training tasks:  43%|████▎     | 13/30 [00:15<00:19,  1.15s/it]
Training tasks:  47%|████▋     | 14/30 [00:16<00:18,  1.15s/it]
Training tasks:  50%|█████     | 15/30 [00:17<00:17,  1.19s/it]
Training tasks:  53%|█████▎    | 16/30 [00:19<00:16,  1.20s/it]
Training tasks:  57%|█████▋    | 17/30 [00:20<00:15,  1.17s/it]
Training tasks:  60%|██████    | 18/30 [00:21<00:13,  1.15s/it]
Training tasks:  63%|██████▎   | 19/30 [00:22<00:12,  1.17s/it]
Training tasks:  67%|██████▋   | 20/30 [00:23<00:11,  1.17s/it]
Training tasks:  70%|███████   | 21/30 [00:24<00:10,  1.13s/it]
Training tasks:  73%|███████▎  | 22/30 [00:25<00:09,  1.14s/it]
Training tasks:  77%|███████▋  | 23/30 [00:26<00:07,  1.13s/it]
Training tasks:  80%|████████  | 24/30 [00:28<00:06,  1.12s/it]
Training tasks:  83%|████████▎ | 25/30 [00:29<00:05,  1.11s/it]
Training tasks:  87%|████████▋ | 26/30 [00:30<00:04,  1.16s/it]
Training tasks:  90%|█████████ | 27/30 [00:31<00:03,  1.14s/it]
Training tasks:  93%|█████████▎| 28/30 [00:32<00:02,  1.13s/it]
Training tasks:  97%|█████████▋| 29/30 [00:33<00:01,  1.16s/it]
Training tasks: 100%|██████████| 30/30 [00:34<00:00,  1.13s/it]
Training tasks: 100%|██████████| 30/30 [00:34<00:00,  1.16s/it]
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
警告: 没有足够的有效类别进行3-way任务采样，尝试调整参数...
类别样本数量: {0: 33, 1: 5, 2: 29}
调整后的参数: n_shot=2, n_query=3
类别 normal (标签 0): 33个样本
类别 e-kc (标签 1): 5个样本
类别 kc (标签 2): 29个样本

Validating tasks:   0%|          | 0/10 [00:00<?, ?it/s]
Validating tasks:  10%|█         | 1/10 [00:00<00:01,  6.44it/s]
Validating tasks:  20%|██        | 2/10 [00:00<00:01,  6.99it/s]
Validating tasks:  30%|███       | 3/10 [00:00<00:00,  7.13it/s]
Validating tasks:  40%|████      | 4/10 [00:00<00:00,  7.23it/s]
Validating tasks:  50%|█████     | 5/10 [00:00<00:00,  7.18it/s]
Validating tasks:  60%|██████    | 6/10 [00:00<00:00,  6.93it/s]
Validating tasks:  70%|███████   | 7/10 [00:00<00:00,  7.01it/s]
Validating tasks:  80%|████████  | 8/10 [00:01<00:00,  7.06it/s]
Validating tasks:  90%|█████████ | 9/10 [00:01<00:00,  7.11it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  7.19it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  7.09it/s]
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
Epoch 3: Train Loss: 0.0102, Train Acc: 0.5504, Val Loss: 1.7551, Val Acc: 0.4667
验证准确率未提高，当前耐心值: 1/5
Epoch 4/30
当前学习率: 0.000200
使用类别权重: 正常=1.0, 早期圆锥角膜=8.0, 圆锥角膜=4.0
使用增强版对比损失函数，温度: 0.07, 硬负样本挖掘比例: 0.7, 早期圆锥角膜与正常样本对比权重: 3.0, 圆锥角膜与正常样本对比权重: 1.0

Training tasks:   0%|          | 0/30 [00:00<?, ?it/s]
Training tasks:   3%|▎         | 1/30 [00:01<00:30,  1.05s/it]
Training tasks:   7%|▋         | 2/30 [00:02<00:30,  1.10s/it]
Training tasks:  10%|█         | 3/30 [00:03<00:30,  1.11s/it]
Training tasks:  13%|█▎        | 4/30 [00:04<00:28,  1.08s/it]
Training tasks:  17%|█▋        | 5/30 [00:05<00:26,  1.07s/it]
Training tasks:  20%|██        | 6/30 [00:06<00:26,  1.12s/it]
Training tasks:  23%|██▎       | 7/30 [00:07<00:25,  1.10s/it]
Training tasks:  27%|██▋       | 8/30 [00:08<00:24,  1.10s/it]
Training tasks:  30%|███       | 9/30 [00:09<00:22,  1.09s/it]
Training tasks:  33%|███▎      | 10/30 [00:10<00:21,  1.09s/it]
Training tasks:  37%|███▋      | 11/30 [00:11<00:20,  1.08s/it]
Training tasks:  40%|████      | 12/30 [00:13<00:19,  1.11s/it]
Training tasks:  43%|████▎     | 13/30 [00:14<00:19,  1.15s/it]
Training tasks:  47%|████▋     | 14/30 [00:15<00:18,  1.18s/it]
Training tasks:  50%|█████     | 15/30 [00:16<00:17,  1.17s/it]
Training tasks:  53%|█████▎    | 16/30 [00:17<00:15,  1.14s/it]
Training tasks:  57%|█████▋    | 17/30 [00:18<00:14,  1.12s/it]
Training tasks:  60%|██████    | 18/30 [00:19<00:13,  1.10s/it]
Training tasks:  63%|██████▎   | 19/30 [00:20<00:11,  1.06s/it]
Training tasks:  67%|██████▋   | 20/30 [00:21<00:10,  1.05s/it]
Training tasks:  70%|███████   | 21/30 [00:23<00:09,  1.09s/it]
Training tasks:  73%|███████▎  | 22/30 [00:24<00:08,  1.11s/it]
Training tasks:  77%|███████▋  | 23/30 [00:25<00:07,  1.09s/it]
Training tasks:  80%|████████  | 24/30 [00:26<00:06,  1.09s/it]
Training tasks:  83%|████████▎ | 25/30 [00:27<00:05,  1.11s/it]
Training tasks:  87%|████████▋ | 26/30 [00:28<00:04,  1.13s/it]
Training tasks:  90%|█████████ | 27/30 [00:29<00:03,  1.12s/it]
Training tasks:  93%|█████████▎| 28/30 [00:31<00:02,  1.14s/it]
Training tasks:  97%|█████████▋| 29/30 [00:32<00:01,  1.12s/it]
Training tasks: 100%|██████████| 30/30 [00:33<00:00,  1.19s/it]
Training tasks: 100%|██████████| 30/30 [00:33<00:00,  1.12s/it]
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
警告: 没有足够的有效类别进行3-way任务采样，尝试调整参数...
类别样本数量: {0: 33, 1: 5, 2: 29}
调整后的参数: n_shot=2, n_query=3
类别 normal (标签 0): 33个样本
类别 e-kc (标签 1): 5个样本
类别 kc (标签 2): 29个样本

Validating tasks:   0%|          | 0/10 [00:00<?, ?it/s]
Validating tasks:  10%|█         | 1/10 [00:00<00:01,  6.93it/s]
Validating tasks:  20%|██        | 2/10 [00:00<00:01,  7.19it/s]
Validating tasks:  30%|███       | 3/10 [00:00<00:00,  7.11it/s]
Validating tasks:  40%|████      | 4/10 [00:00<00:00,  7.26it/s]
Validating tasks:  50%|█████     | 5/10 [00:00<00:00,  7.24it/s]
Validating tasks:  60%|██████    | 6/10 [00:00<00:00,  7.21it/s]
Validating tasks:  70%|███████   | 7/10 [00:00<00:00,  7.20it/s]
Validating tasks:  80%|████████  | 8/10 [00:01<00:00,  6.95it/s]
Validating tasks:  90%|█████████ | 9/10 [00:01<00:00,  6.73it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  6.62it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  6.93it/s]
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
Epoch 4: Train Loss: 0.0272, Train Acc: 0.5600, Val Loss: 1.8309, Val Acc: 0.4889
验证准确率未提高，当前耐心值: 2/5
Epoch 5/30
当前学习率: 0.000200
使用类别权重: 正常=1.0, 早期圆锥角膜=8.0, 圆锥角膜=4.0
使用增强版对比损失函数，温度: 0.07, 硬负样本挖掘比例: 0.7, 早期圆锥角膜与正常样本对比权重: 3.0, 圆锥角膜与正常样本对比权重: 1.0

Training tasks:   0%|          | 0/30 [00:00<?, ?it/s]
Training tasks:   3%|▎         | 1/30 [00:01<00:30,  1.06s/it]
Training tasks:   7%|▋         | 2/30 [00:02<00:33,  1.18s/it]
Training tasks:  10%|█         | 3/30 [00:03<00:29,  1.10s/it]
Training tasks:  13%|█▎        | 4/30 [00:04<00:27,  1.07s/it]
Training tasks:  17%|█▋        | 5/30 [00:05<00:28,  1.14s/it]
Training tasks:  20%|██        | 6/30 [00:06<00:26,  1.10s/it]
Training tasks:  23%|██▎       | 7/30 [00:07<00:24,  1.08s/it]
Training tasks:  27%|██▋       | 8/30 [00:08<00:24,  1.12s/it]
Training tasks:  30%|███       | 9/30 [00:10<00:24,  1.16s/it]
Training tasks:  33%|███▎      | 10/30 [00:11<00:22,  1.12s/it]
Training tasks:  37%|███▋      | 11/30 [00:12<00:20,  1.10s/it]
Training tasks:  40%|████      | 12/30 [00:13<00:20,  1.13s/it]
Training tasks:  43%|████▎     | 13/30 [00:14<00:19,  1.13s/it]
Training tasks:  47%|████▋     | 14/30 [00:15<00:17,  1.11s/it]
Training tasks:  50%|█████     | 15/30 [00:16<00:16,  1.10s/it]
Training tasks:  53%|█████▎    | 16/30 [00:17<00:16,  1.15s/it]
Training tasks:  57%|█████▋    | 17/30 [00:18<00:14,  1.12s/it]
Training tasks:  60%|██████    | 18/30 [00:19<00:12,  1.08s/it]
Training tasks:  63%|██████▎   | 19/30 [00:21<00:12,  1.11s/it]
Training tasks:  67%|██████▋   | 20/30 [00:22<00:10,  1.10s/it]
Training tasks:  70%|███████   | 21/30 [00:23<00:10,  1.13s/it]
Training tasks:  73%|███████▎  | 22/30 [00:24<00:08,  1.12s/it]
Training tasks:  77%|███████▋  | 23/30 [00:25<00:07,  1.11s/it]
Training tasks:  80%|████████  | 24/30 [00:26<00:06,  1.13s/it]
Training tasks:  83%|████████▎ | 25/30 [00:27<00:05,  1.11s/it]
Training tasks:  87%|████████▋ | 26/30 [00:29<00:04,  1.15s/it]
Training tasks:  90%|█████████ | 27/30 [00:30<00:03,  1.17s/it]
Training tasks:  93%|█████████▎| 28/30 [00:31<00:02,  1.14s/it]
Training tasks:  97%|█████████▋| 29/30 [00:32<00:01,  1.11s/it]
Training tasks: 100%|██████████| 30/30 [00:33<00:00,  1.13s/it]
Training tasks: 100%|██████████| 30/30 [00:33<00:00,  1.12s/it]
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
警告: 没有足够的有效类别进行3-way任务采样，尝试调整参数...
类别样本数量: {0: 33, 1: 5, 2: 29}
调整后的参数: n_shot=2, n_query=3
类别 normal (标签 0): 33个样本
类别 e-kc (标签 1): 5个样本
类别 kc (标签 2): 29个样本

Validating tasks:   0%|          | 0/10 [00:00<?, ?it/s]
Validating tasks:  10%|█         | 1/10 [00:00<00:01,  6.81it/s]
Validating tasks:  20%|██        | 2/10 [00:00<00:01,  7.01it/s]
Validating tasks:  30%|███       | 3/10 [00:00<00:01,  6.93it/s]
Validating tasks:  40%|████      | 4/10 [00:00<00:00,  6.99it/s]
Validating tasks:  50%|█████     | 5/10 [00:00<00:00,  6.60it/s]
Validating tasks:  60%|██████    | 6/10 [00:00<00:00,  6.32it/s]
Validating tasks:  70%|███████   | 7/10 [00:01<00:00,  6.25it/s]
Validating tasks:  80%|████████  | 8/10 [00:01<00:00,  6.54it/s]
Validating tasks:  90%|█████████ | 9/10 [00:01<00:00,  6.44it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  6.62it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  6.60it/s]
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
Epoch 5: Train Loss: 0.1027, Train Acc: 0.5319, Val Loss: 1.9621, Val Acc: 0.4222
验证准确率未提高，当前耐心值: 3/5
Epoch 6/30
当前学习率: 0.000100
使用类别权重: 正常=1.0, 早期圆锥角膜=8.0, 圆锥角膜=4.0
使用增强版对比损失函数，温度: 0.07, 硬负样本挖掘比例: 0.7, 早期圆锥角膜与正常样本对比权重: 3.0, 圆锥角膜与正常样本对比权重: 1.0

Training tasks:   0%|          | 0/30 [00:00<?, ?it/s]
Training tasks:   3%|▎         | 1/30 [00:01<00:34,  1.19s/it]
Training tasks:   7%|▋         | 2/30 [00:02<00:33,  1.18s/it]
Training tasks:  10%|█         | 3/30 [00:03<00:30,  1.12s/it]
Training tasks:  13%|█▎        | 4/30 [00:04<00:28,  1.10s/it]
Training tasks:  17%|█▋        | 5/30 [00:05<00:28,  1.15s/it]
Training tasks:  20%|██        | 6/30 [00:06<00:27,  1.13s/it]
Training tasks:  23%|██▎       | 7/30 [00:07<00:26,  1.14s/it]
Training tasks:  27%|██▋       | 8/30 [00:09<00:24,  1.13s/it]
Training tasks:  30%|███       | 9/30 [00:10<00:23,  1.11s/it]
Training tasks:  33%|███▎      | 10/30 [00:11<00:22,  1.12s/it]
Training tasks:  37%|███▋      | 11/30 [00:12<00:21,  1.11s/it]
Training tasks:  40%|████      | 12/30 [00:13<00:19,  1.10s/it]
Training tasks:  43%|████▎     | 13/30 [00:14<00:18,  1.09s/it]
Training tasks:  47%|████▋     | 14/30 [00:15<00:17,  1.08s/it]
Training tasks:  50%|█████     | 15/30 [00:16<00:16,  1.07s/it]
Training tasks:  53%|█████▎    | 16/30 [00:17<00:14,  1.06s/it]
Training tasks:  57%|█████▋    | 17/30 [00:18<00:13,  1.04s/it]
Training tasks:  60%|██████    | 18/30 [00:19<00:12,  1.03s/it]
Training tasks:  63%|██████▎   | 19/30 [00:20<00:11,  1.06s/it]
Training tasks:  67%|██████▋   | 20/30 [00:21<00:10,  1.09s/it]
Training tasks:  70%|███████   | 21/30 [00:23<00:09,  1.09s/it]
Training tasks:  73%|███████▎  | 22/30 [00:24<00:08,  1.09s/it]
Training tasks:  77%|███████▋  | 23/30 [00:25<00:07,  1.14s/it]
Training tasks:  80%|████████  | 24/30 [00:26<00:06,  1.12s/it]
Training tasks:  83%|████████▎ | 25/30 [00:27<00:05,  1.14s/it]
Training tasks:  87%|████████▋ | 26/30 [00:28<00:04,  1.16s/it]
Training tasks:  90%|█████████ | 27/30 [00:29<00:03,  1.15s/it]
Training tasks:  93%|█████████▎| 28/30 [00:31<00:02,  1.15s/it]
Training tasks:  97%|█████████▋| 29/30 [00:32<00:01,  1.12s/it]
Training tasks: 100%|██████████| 30/30 [00:33<00:00,  1.11s/it]
Training tasks: 100%|██████████| 30/30 [00:33<00:00,  1.11s/it]
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
警告: 没有足够的有效类别进行3-way任务采样，尝试调整参数...
类别样本数量: {0: 33, 1: 5, 2: 29}
调整后的参数: n_shot=2, n_query=3
类别 normal (标签 0): 33个样本
类别 e-kc (标签 1): 5个样本
类别 kc (标签 2): 29个样本

Validating tasks:   0%|          | 0/10 [00:00<?, ?it/s]
Validating tasks:  10%|█         | 1/10 [00:00<00:01,  7.18it/s]
Validating tasks:  20%|██        | 2/10 [00:00<00:01,  7.09it/s]
Validating tasks:  30%|███       | 3/10 [00:00<00:01,  6.66it/s]
Validating tasks:  40%|████      | 4/10 [00:00<00:00,  6.33it/s]
Validating tasks:  50%|█████     | 5/10 [00:00<00:00,  6.53it/s]
Validating tasks:  60%|██████    | 6/10 [00:00<00:00,  6.66it/s]
Validating tasks:  70%|███████   | 7/10 [00:01<00:00,  6.80it/s]
Validating tasks:  80%|████████  | 8/10 [00:01<00:00,  6.47it/s]
Validating tasks:  90%|█████████ | 9/10 [00:01<00:00,  6.69it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  6.83it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  6.71it/s]
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
Epoch 6: Train Loss: 0.0799, Train Acc: 0.5296, Val Loss: 2.2215, Val Acc: 0.3333
验证准确率未提高，当前耐心值: 4/5
Epoch 7/30
当前学习率: 0.000100
使用类别权重: 正常=1.0, 早期圆锥角膜=8.0, 圆锥角膜=4.0
使用增强版对比损失函数，温度: 0.07, 硬负样本挖掘比例: 0.7, 早期圆锥角膜与正常样本对比权重: 3.0, 圆锥角膜与正常样本对比权重: 1.0

Training tasks:   0%|          | 0/30 [00:00<?, ?it/s]
Training tasks:   3%|▎         | 1/30 [00:00<00:28,  1.01it/s]
Training tasks:   7%|▋         | 2/30 [00:02<00:29,  1.06s/it]
Training tasks:  10%|█         | 3/30 [00:03<00:29,  1.10s/it]
Training tasks:  13%|█▎        | 4/30 [00:04<00:28,  1.09s/it]
Training tasks:  17%|█▋        | 5/30 [00:05<00:28,  1.15s/it]
Training tasks:  20%|██        | 6/30 [00:06<00:26,  1.12s/it]
Training tasks:  23%|██▎       | 7/30 [00:07<00:25,  1.11s/it]
Training tasks:  27%|██▋       | 8/30 [00:08<00:24,  1.10s/it]
Training tasks:  30%|███       | 9/30 [00:10<00:24,  1.15s/it]
Training tasks:  33%|███▎      | 10/30 [00:11<00:22,  1.14s/it]
Training tasks:  37%|███▋      | 11/30 [00:12<00:21,  1.15s/it]
Training tasks:  40%|████      | 12/30 [00:13<00:20,  1.14s/it]
Training tasks:  43%|████▎     | 13/30 [00:14<00:19,  1.14s/it]
Training tasks:  47%|████▋     | 14/30 [00:15<00:17,  1.12s/it]
Training tasks:  50%|█████     | 15/30 [00:16<00:17,  1.16s/it]
Training tasks:  53%|█████▎    | 16/30 [00:17<00:15,  1.11s/it]
Training tasks:  57%|█████▋    | 17/30 [00:18<00:14,  1.08s/it]
Training tasks:  60%|██████    | 18/30 [00:19<00:12,  1.06s/it]
Training tasks:  63%|██████▎   | 19/30 [00:21<00:11,  1.06s/it]
Training tasks:  67%|██████▋   | 20/30 [00:22<00:10,  1.09s/it]
Training tasks:  70%|███████   | 21/30 [00:23<00:10,  1.13s/it]
Training tasks:  73%|███████▎  | 22/30 [00:24<00:08,  1.12s/it]
Training tasks:  77%|███████▋  | 23/30 [00:25<00:07,  1.11s/it]
Training tasks:  80%|████████  | 24/30 [00:26<00:06,  1.15s/it]
Training tasks:  83%|████████▎ | 25/30 [00:27<00:05,  1.13s/it]
Training tasks:  87%|████████▋ | 26/30 [00:28<00:04,  1.11s/it]
Training tasks:  90%|█████████ | 27/30 [00:30<00:03,  1.16s/it]
Training tasks:  93%|█████████▎| 28/30 [00:31<00:02,  1.16s/it]
Training tasks:  97%|█████████▋| 29/30 [00:32<00:01,  1.15s/it]
Training tasks: 100%|██████████| 30/30 [00:33<00:00,  1.12s/it]
Training tasks: 100%|██████████| 30/30 [00:33<00:00,  1.12s/it]
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
警告: 没有足够的有效类别进行3-way任务采样，尝试调整参数...
类别样本数量: {0: 33, 1: 5, 2: 29}
调整后的参数: n_shot=2, n_query=3
类别 normal (标签 0): 33个样本
类别 e-kc (标签 1): 5个样本
类别 kc (标签 2): 29个样本

Validating tasks:   0%|          | 0/10 [00:00<?, ?it/s]
Validating tasks:  10%|█         | 1/10 [00:00<00:01,  7.52it/s]
Validating tasks:  20%|██        | 2/10 [00:00<00:01,  6.50it/s]
Validating tasks:  30%|███       | 3/10 [00:00<00:01,  6.92it/s]
Validating tasks:  40%|████      | 4/10 [00:00<00:00,  7.19it/s]
Validating tasks:  50%|█████     | 5/10 [00:00<00:00,  7.06it/s]
Validating tasks:  60%|██████    | 6/10 [00:00<00:00,  7.06it/s]
Validating tasks:  70%|███████   | 7/10 [00:00<00:00,  7.40it/s]
Validating tasks:  80%|████████  | 8/10 [00:01<00:00,  7.14it/s]
Validating tasks:  90%|█████████ | 9/10 [00:01<00:00,  6.84it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  7.18it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  7.09it/s]
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
Epoch 7: Train Loss: 0.0438, Train Acc: 0.5459, Val Loss: 2.0732, Val Acc: 0.4000
验证准确率未提高，当前耐心值: 5/5
早停触发，停止训练
警告: 没有足够的有效类别进行3-way任务采样，尝试调整参数...
类别样本数量: {0: 37, 1: 7, 2: 27}
调整后的参数: n_shot=3, n_query=4
类别 normal (标签 0): 37个样本
类别 e-kc (标签 1): 7个样本
类别 kc (标签 2): 27个样本

Testing tasks:   0%|          | 0/30 [00:00<?, ?it/s]
Testing tasks:   3%|▎         | 1/30 [00:00<00:06,  4.35it/s]
Testing tasks:   7%|▋         | 2/30 [00:00<00:06,  4.37it/s]
Testing tasks:  10%|█         | 3/30 [00:00<00:06,  4.43it/s]
Testing tasks:  13%|█▎        | 4/30 [00:00<00:05,  4.57it/s]
Testing tasks:  17%|█▋        | 5/30 [00:01<00:05,  4.72it/s]
Testing tasks:  20%|██        | 6/30 [00:01<00:05,  4.78it/s]
Testing tasks:  23%|██▎       | 7/30 [00:01<00:04,  4.82it/s]
Testing tasks:  27%|██▋       | 8/30 [00:01<00:04,  4.89it/s]
Testing tasks:  30%|███       | 9/30 [00:01<00:04,  4.72it/s]
Testing tasks:  33%|███▎      | 10/30 [00:02<00:04,  4.68it/s]
Testing tasks:  37%|███▋      | 11/30 [00:02<00:04,  4.74it/s]
Testing tasks:  40%|████      | 12/30 [00:02<00:03,  4.82it/s]
Testing tasks:  43%|████▎     | 13/30 [00:02<00:03,  4.88it/s]
Testing tasks:  47%|████▋     | 14/30 [00:02<00:03,  4.83it/s]
Testing tasks:  50%|█████     | 15/30 [00:03<00:03,  4.82it/s]
Testing tasks:  53%|█████▎    | 16/30 [00:03<00:02,  4.87it/s]
Testing tasks:  57%|█████▋    | 17/30 [00:03<00:02,  4.87it/s]
Testing tasks:  60%|██████    | 18/30 [00:03<00:02,  4.88it/s]
Testing tasks:  63%|██████▎   | 19/30 [00:03<00:02,  4.78it/s]
Testing tasks:  67%|██████▋   | 20/30 [00:04<00:02,  4.69it/s]
Testing tasks:  70%|███████   | 21/30 [00:04<00:01,  4.87it/s]
Testing tasks:  73%|███████▎  | 22/30 [00:04<00:01,  4.96it/s]
Testing tasks:  77%|███████▋  | 23/30 [00:04<00:01,  5.01it/s]
Testing tasks:  80%|████████  | 24/30 [00:05<00:01,  4.93it/s]
Testing tasks:  83%|████████▎ | 25/30 [00:05<00:00,  5.05it/s]
Testing tasks:  87%|████████▋ | 26/30 [00:05<00:00,  5.06it/s]
Testing tasks:  90%|█████████ | 27/30 [00:05<00:00,  5.08it/s]
Testing tasks:  93%|█████████▎| 28/30 [00:05<00:00,  4.96it/s]
Testing tasks:  97%|█████████▋| 29/30 [00:05<00:00,  5.04it/s]
Testing tasks: 100%|██████████| 30/30 [00:06<00:00,  5.03it/s]
Testing tasks: 100%|██████████| 30/30 [00:06<00:00,  4.85it/s]
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 9 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 9 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 9 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 9 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 9 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 9 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 9 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 9 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 9 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 9 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 9 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 9 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 9 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 9 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 9 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 9 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 9 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 9 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 9 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 9 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 9 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 9 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 9 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 9 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 9 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 9 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 9 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 9 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 9 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 9 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本

测试结果:
总体准确率: 50.83%
类别准确率:
  kc: 67.50%
  e-kc: 85.00%
  normal: 0.00%
训练完成，结果已保存到 results_optimized_20250525_152539_gpu0,1

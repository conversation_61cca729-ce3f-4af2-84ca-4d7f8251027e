python train_balanced_task_sampling.py   --train_csv /home/<USER>/balanced_task_sampling/split_result/train_set.csv   --val_csv /home/<USER>/balanced_task_sampling/split_result/val_set.csv   --test_csv /home/<USER>/balanced_task_sampling/split_result/test_set.csv   --epochs 30   --lr 0.0002   --weight_decay 5e-4   --feature_dim 512   --save_dir results_optimized_20250525_142053_gpu0,1   --proto_counts 2,4,2   --pretrained   --inner_lr 0.3   --inner_steps 2   --n_way 3   --n_shot 5   --n_query 15   --tasks_per_epoch 30   --meta_batch_size 8   --no_augmentation   --mixup_alpha 0.0   --cutmix_prob 0.0   --num_workers 4   --augment_factor 1   --kc_augment_factor 1   --seed 42   --early_kc_weight 8.0   --kc_weight 4.0   --focal_gamma 2.0   --val_frequency 1   --early_stopping 5   --dropout 0.5    --use_separate_test_sampler --use_balanced_task_sampler --kc_shot_multiplier 3.0 --early_kc_shot_multiplier 1.5   --use_contrastive --contrastive_weight 0.5 --temperature 0.07 --hard_mining_ratio 0.7 --early_normal_weight 2.0      --use_lr_scheduler --scheduler_type plateau --plateau_factor 0.5 --plateau_patience 2 --min_lr 1e-6
/home/<USER>/anaconda3/envs/kc1/lib/python3.10/site-packages/torchvision/models/_utils.py:208: UserWarning: The parameter 'pretrained' is deprecated since 0.13 and may be removed in the future, please use 'weights' instead.
  warnings.warn(
/home/<USER>/anaconda3/envs/kc1/lib/python3.10/site-packages/torchvision/models/_utils.py:223: UserWarning: Arguments other than a weight enum or `None` for 'weights' are deprecated since 0.13 and may be removed in the future. The current behavior is equivalent to passing `weights=ResNet34_Weights.IMAGENET1K_V1`. You can also use `weights=ResNet34_Weights.DEFAULT` to get the most up-to-date weights.
  warnings.warn(msg)
/home/<USER>/anaconda3/envs/kc1/lib/python3.10/site-packages/torch/optim/lr_scheduler.py:62: UserWarning: The verbose parameter is deprecated. Please use get_last_lr() to access the learning rate.
  warnings.warn(
成功导入增强版特征提取器
成功导入归一化特征提取器
成功导入增强版损失函数
成功导入增强版MAML模型
成功导入KC特定增强模块
成功导入增强版GPU数据增强模块
使用设备: cuda
禁用所有数据增强，直接加载原始数据集...
使用平衡任务采样器
类别 normal (标签 0): 164个样本
类别 e-kc (标签 1): 26个样本
类别 kc (标签 2): 133个样本
使用原型配置: [2, 4, 2]
使用增强版特征提取器，dropout率: 0.5
使用增强版MAML-ProtoNet模型，内循环学习率: 0.3, 内循环步数: 2
使用ReduceLROnPlateau学习率调度器，因子: 0.5, 耐心值: 2
开始训练，共30个epoch
Epoch 1/30
当前学习率: 0.000200
使用类别权重: 正常=1.0, 早期圆锥角膜=8.0, 圆锥角膜=4.0
使用增强版对比损失函数，温度: 0.07, 硬负样本挖掘比例: 0.7, 早期圆锥角膜与正常样本对比权重: 2.0, 圆锥角膜与正常样本对比权重: 1.0

Training tasks:   0%|          | 0/30 [00:00<?, ?it/s]
Training tasks:   3%|▎         | 1/30 [00:04<02:16,  4.71s/it]
Training tasks:   7%|▋         | 2/30 [00:05<01:15,  2.70s/it]
Training tasks:  10%|█         | 3/30 [00:07<00:54,  2.04s/it]
Training tasks:  13%|█▎        | 4/30 [00:08<00:44,  1.72s/it]
Training tasks:  17%|█▋        | 5/30 [00:09<00:37,  1.49s/it]
Training tasks:  20%|██        | 6/30 [00:10<00:32,  1.36s/it]
Training tasks:  23%|██▎       | 7/30 [00:11<00:29,  1.28s/it]
Training tasks:  27%|██▋       | 8/30 [00:12<00:27,  1.25s/it]
Training tasks:  30%|███       | 9/30 [00:14<00:24,  1.19s/it]
Training tasks:  33%|███▎      | 10/30 [00:15<00:23,  1.20s/it]
Training tasks:  37%|███▋      | 11/30 [00:16<00:22,  1.20s/it]
Training tasks:  40%|████      | 12/30 [00:17<00:21,  1.18s/it]
Training tasks:  43%|████▎     | 13/30 [00:18<00:20,  1.19s/it]
Training tasks:  47%|████▋     | 14/30 [00:19<00:18,  1.16s/it]
Training tasks:  50%|█████     | 15/30 [00:21<00:17,  1.16s/it]
Training tasks:  53%|█████▎    | 16/30 [00:22<00:16,  1.15s/it]
Training tasks:  57%|█████▋    | 17/30 [00:23<00:14,  1.13s/it]
Training tasks:  60%|██████    | 18/30 [00:24<00:13,  1.13s/it]
Training tasks:  63%|██████▎   | 19/30 [00:25<00:11,  1.09s/it]
Training tasks:  67%|██████▋   | 20/30 [00:26<00:10,  1.07s/it]
Training tasks:  70%|███████   | 21/30 [00:27<00:09,  1.08s/it]
Training tasks:  73%|███████▎  | 22/30 [00:28<00:09,  1.13s/it]
Training tasks:  77%|███████▋  | 23/30 [00:29<00:08,  1.15s/it]
Training tasks:  80%|████████  | 24/30 [00:31<00:07,  1.17s/it]
Training tasks:  83%|████████▎ | 25/30 [00:32<00:05,  1.16s/it]
Training tasks:  87%|████████▋ | 26/30 [00:33<00:04,  1.16s/it]
Training tasks:  90%|█████████ | 27/30 [00:34<00:03,  1.21s/it]
Training tasks:  93%|█████████▎| 28/30 [00:35<00:02,  1.18s/it]
Training tasks:  97%|█████████▋| 29/30 [00:37<00:01,  1.18s/it]
Training tasks: 100%|██████████| 30/30 [00:38<00:00,  1.20s/it]
Training tasks: 100%|██████████| 30/30 [00:38<00:00,  1.28s/it]
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
警告: 没有足够的有效类别进行3-way任务采样，尝试调整参数...
类别样本数量: {0: 33, 1: 5, 2: 29}
调整后的参数: n_shot=2, n_query=3
类别 normal (标签 0): 33个样本
类别 e-kc (标签 1): 5个样本
类别 kc (标签 2): 29个样本

Validating tasks:   0%|          | 0/10 [00:00<?, ?it/s]
Validating tasks:  10%|█         | 1/10 [00:00<00:01,  5.85it/s]
Validating tasks:  20%|██        | 2/10 [00:00<00:01,  6.39it/s]
Validating tasks:  30%|███       | 3/10 [00:00<00:01,  6.15it/s]
Validating tasks:  40%|████      | 4/10 [00:00<00:00,  6.48it/s]
Validating tasks:  50%|█████     | 5/10 [00:00<00:00,  6.61it/s]
Validating tasks:  60%|██████    | 6/10 [00:00<00:00,  6.45it/s]
Validating tasks:  70%|███████   | 7/10 [00:01<00:00,  6.55it/s]
Validating tasks:  80%|████████  | 8/10 [00:01<00:00,  6.61it/s]
Validating tasks:  90%|█████████ | 9/10 [00:01<00:00,  6.54it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  6.48it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  6.46it/s]
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
Epoch 1: Train Loss: 1.8869, Train Acc: 0.5252, Val Loss: 1.9017, Val Acc: 0.5333
保存最佳模型，验证准确率: 0.5333
Epoch 2/30
当前学习率: 0.000200
使用类别权重: 正常=1.0, 早期圆锥角膜=8.0, 圆锥角膜=4.0
使用增强版对比损失函数，温度: 0.07, 硬负样本挖掘比例: 0.7, 早期圆锥角膜与正常样本对比权重: 2.0, 圆锥角膜与正常样本对比权重: 1.0

Training tasks:   0%|          | 0/30 [00:00<?, ?it/s]
Training tasks:   3%|▎         | 1/30 [00:01<00:31,  1.08s/it]
Training tasks:   7%|▋         | 2/30 [00:02<00:30,  1.08s/it]
Training tasks:  10%|█         | 3/30 [00:03<00:30,  1.15s/it]
Training tasks:  13%|█▎        | 4/30 [00:04<00:28,  1.11s/it]
Training tasks:  17%|█▋        | 5/30 [00:05<00:27,  1.10s/it]
Training tasks:  20%|██        | 6/30 [00:06<00:26,  1.09s/it]
Training tasks:  23%|██▎       | 7/30 [00:07<00:26,  1.14s/it]
Training tasks:  27%|██▋       | 8/30 [00:08<00:24,  1.09s/it]
Training tasks:  30%|███       | 9/30 [00:09<00:22,  1.05s/it]
Training tasks:  33%|███▎      | 10/30 [00:10<00:21,  1.06s/it]
Training tasks:  37%|███▋      | 11/30 [00:11<00:20,  1.05s/it]
Training tasks:  40%|████      | 12/30 [00:13<00:19,  1.09s/it]
Training tasks:  43%|████▎     | 13/30 [00:14<00:18,  1.09s/it]
Training tasks:  47%|████▋     | 14/30 [00:15<00:17,  1.10s/it]
Training tasks:  50%|█████     | 15/30 [00:16<00:16,  1.09s/it]
Training tasks:  53%|█████▎    | 16/30 [00:17<00:15,  1.13s/it]
Training tasks:  57%|█████▋    | 17/30 [00:18<00:14,  1.11s/it]
Training tasks:  60%|██████    | 18/30 [00:19<00:13,  1.10s/it]
Training tasks:  63%|██████▎   | 19/30 [00:20<00:12,  1.10s/it]
Training tasks:  67%|██████▋   | 20/30 [00:21<00:11,  1.12s/it]
Training tasks:  70%|███████   | 21/30 [00:23<00:09,  1.11s/it]
Training tasks:  73%|███████▎  | 22/30 [00:24<00:08,  1.10s/it]
Training tasks:  77%|███████▋  | 23/30 [00:25<00:07,  1.10s/it]
Training tasks:  80%|████████  | 24/30 [00:26<00:06,  1.14s/it]
Training tasks:  83%|████████▎ | 25/30 [00:27<00:05,  1.12s/it]
Training tasks:  87%|████████▋ | 26/30 [00:28<00:04,  1.15s/it]
Training tasks:  90%|█████████ | 27/30 [00:30<00:03,  1.18s/it]
Training tasks:  93%|█████████▎| 28/30 [00:31<00:02,  1.17s/it]
Training tasks:  97%|█████████▋| 29/30 [00:32<00:01,  1.14s/it]
Training tasks: 100%|██████████| 30/30 [00:33<00:00,  1.14s/it]
Training tasks: 100%|██████████| 30/30 [00:33<00:00,  1.11s/it]
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
警告: 没有足够的有效类别进行3-way任务采样，尝试调整参数...
类别样本数量: {0: 33, 1: 5, 2: 29}
调整后的参数: n_shot=2, n_query=3
类别 normal (标签 0): 33个样本
类别 e-kc (标签 1): 5个样本
类别 kc (标签 2): 29个样本

Validating tasks:   0%|          | 0/10 [00:00<?, ?it/s]
Validating tasks:  10%|█         | 1/10 [00:00<00:01,  6.94it/s]
Validating tasks:  20%|██        | 2/10 [00:00<00:01,  7.45it/s]
Validating tasks:  30%|███       | 3/10 [00:00<00:00,  7.75it/s]
Validating tasks:  40%|████      | 4/10 [00:00<00:00,  7.77it/s]
Validating tasks:  50%|█████     | 5/10 [00:00<00:00,  7.92it/s]
Validating tasks:  60%|██████    | 6/10 [00:00<00:00,  7.99it/s]
Validating tasks:  70%|███████   | 7/10 [00:00<00:00,  8.07it/s]
Validating tasks:  80%|████████  | 8/10 [00:01<00:00,  8.00it/s]
Validating tasks:  90%|█████████ | 9/10 [00:01<00:00,  7.82it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  7.67it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  7.77it/s]
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
Epoch 2: Train Loss: 1.4251, Train Acc: 0.6089, Val Loss: 1.9408, Val Acc: 0.5556
保存最佳模型，验证准确率: 0.5556
Epoch 3/30
当前学习率: 0.000200
使用类别权重: 正常=1.0, 早期圆锥角膜=8.0, 圆锥角膜=4.0
使用增强版对比损失函数，温度: 0.07, 硬负样本挖掘比例: 0.7, 早期圆锥角膜与正常样本对比权重: 2.0, 圆锥角膜与正常样本对比权重: 1.0

Training tasks:   0%|          | 0/30 [00:00<?, ?it/s]
Training tasks:   3%|▎         | 1/30 [00:01<00:32,  1.14s/it]
Training tasks:   7%|▋         | 2/30 [00:02<00:31,  1.11s/it]
Training tasks:  10%|█         | 3/30 [00:03<00:29,  1.09s/it]
Training tasks:  13%|█▎        | 4/30 [00:04<00:29,  1.14s/it]
Training tasks:  17%|█▋        | 5/30 [00:05<00:27,  1.12s/it]
Training tasks:  20%|██        | 6/30 [00:06<00:26,  1.11s/it]
Training tasks:  23%|██▎       | 7/30 [00:07<00:25,  1.09s/it]
Training tasks:  27%|██▋       | 8/30 [00:08<00:23,  1.08s/it]
Training tasks:  30%|███       | 9/30 [00:09<00:22,  1.07s/it]
Training tasks:  33%|███▎      | 10/30 [00:10<00:21,  1.06s/it]
Training tasks:  37%|███▋      | 11/30 [00:11<00:20,  1.06s/it]
Training tasks:  40%|████      | 12/30 [00:13<00:20,  1.12s/it]
Training tasks:  43%|████▎     | 13/30 [00:14<00:18,  1.12s/it]
Training tasks:  47%|████▋     | 14/30 [00:15<00:17,  1.09s/it]
Training tasks:  50%|█████     | 15/30 [00:16<00:16,  1.13s/it]
Training tasks:  53%|█████▎    | 16/30 [00:17<00:16,  1.17s/it]
Training tasks:  57%|█████▋    | 17/30 [00:19<00:15,  1.18s/it]
Training tasks:  60%|██████    | 18/30 [00:20<00:14,  1.19s/it]
Training tasks:  63%|██████▎   | 19/30 [00:21<00:13,  1.20s/it]
Training tasks:  67%|██████▋   | 20/30 [00:22<00:11,  1.14s/it]
Training tasks:  70%|███████   | 21/30 [00:23<00:09,  1.11s/it]
Training tasks:  73%|███████▎  | 22/30 [00:24<00:08,  1.09s/it]
Training tasks:  77%|███████▋  | 23/30 [00:25<00:07,  1.14s/it]
Training tasks:  80%|████████  | 24/30 [00:26<00:06,  1.11s/it]
Training tasks:  83%|████████▎ | 25/30 [00:27<00:05,  1.09s/it]
Training tasks:  87%|████████▋ | 26/30 [00:28<00:04,  1.08s/it]
Training tasks:  90%|█████████ | 27/30 [00:30<00:03,  1.11s/it]
Training tasks:  93%|█████████▎| 28/30 [00:31<00:02,  1.10s/it]
Training tasks:  97%|█████████▋| 29/30 [00:32<00:01,  1.09s/it]
Training tasks: 100%|██████████| 30/30 [00:33<00:00,  1.12s/it]
Training tasks: 100%|██████████| 30/30 [00:33<00:00,  1.12s/it]
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
警告: 没有足够的有效类别进行3-way任务采样，尝试调整参数...
类别样本数量: {0: 33, 1: 5, 2: 29}
调整后的参数: n_shot=2, n_query=3
类别 normal (标签 0): 33个样本
类别 e-kc (标签 1): 5个样本
类别 kc (标签 2): 29个样本

Validating tasks:   0%|          | 0/10 [00:00<?, ?it/s]
Validating tasks:  10%|█         | 1/10 [00:00<00:01,  7.15it/s]
Validating tasks:  20%|██        | 2/10 [00:00<00:01,  6.47it/s]
Validating tasks:  30%|███       | 3/10 [00:00<00:01,  6.32it/s]
Validating tasks:  40%|████      | 4/10 [00:00<00:00,  6.27it/s]
Validating tasks:  50%|█████     | 5/10 [00:00<00:00,  6.55it/s]
Validating tasks:  60%|██████    | 6/10 [00:00<00:00,  6.73it/s]
Validating tasks:  70%|███████   | 7/10 [00:01<00:00,  6.73it/s]
Validating tasks:  80%|████████  | 8/10 [00:01<00:00,  6.81it/s]
Validating tasks:  90%|█████████ | 9/10 [00:01<00:00,  6.56it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  6.56it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  6.58it/s]
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
Epoch 3: Train Loss: 1.0843, Train Acc: 0.8726, Val Loss: 1.3896, Val Acc: 0.8222
保存最佳模型，验证准确率: 0.8222
Epoch 4/30
当前学习率: 0.000200
使用类别权重: 正常=1.0, 早期圆锥角膜=8.0, 圆锥角膜=4.0
使用增强版对比损失函数，温度: 0.07, 硬负样本挖掘比例: 0.7, 早期圆锥角膜与正常样本对比权重: 2.0, 圆锥角膜与正常样本对比权重: 1.0

Training tasks:   0%|          | 0/30 [00:00<?, ?it/s]
Training tasks:   3%|▎         | 1/30 [00:01<00:32,  1.11s/it]
Training tasks:   7%|▋         | 2/30 [00:02<00:30,  1.09s/it]
Training tasks:  10%|█         | 3/30 [00:03<00:31,  1.17s/it]
Training tasks:  13%|█▎        | 4/30 [00:04<00:30,  1.18s/it]
Training tasks:  17%|█▋        | 5/30 [00:05<00:29,  1.16s/it]
Training tasks:  20%|██        | 6/30 [00:06<00:27,  1.13s/it]
Training tasks:  23%|██▎       | 7/30 [00:07<00:25,  1.12s/it]
Training tasks:  27%|██▋       | 8/30 [00:09<00:25,  1.14s/it]
Training tasks:  30%|███       | 9/30 [00:10<00:22,  1.08s/it]
Training tasks:  33%|███▎      | 10/30 [00:11<00:21,  1.09s/it]
Training tasks:  37%|███▋      | 11/30 [00:12<00:21,  1.14s/it]
Training tasks:  40%|████      | 12/30 [00:13<00:19,  1.11s/it]
Training tasks:  43%|████▎     | 13/30 [00:14<00:19,  1.14s/it]
Training tasks:  47%|████▋     | 14/30 [00:15<00:18,  1.15s/it]
Training tasks:  50%|█████     | 15/30 [00:17<00:17,  1.18s/it]
Training tasks:  53%|█████▎    | 16/30 [00:18<00:16,  1.20s/it]
Training tasks:  57%|█████▋    | 17/30 [00:19<00:15,  1.18s/it]
Training tasks:  60%|██████    | 18/30 [00:20<00:13,  1.17s/it]
Training tasks:  63%|██████▎   | 19/30 [00:21<00:12,  1.13s/it]
Training tasks:  67%|██████▋   | 20/30 [00:22<00:11,  1.13s/it]
Training tasks:  70%|███████   | 21/30 [00:24<00:10,  1.17s/it]
Training tasks:  73%|███████▎  | 22/30 [00:25<00:09,  1.19s/it]
Training tasks:  77%|███████▋  | 23/30 [00:26<00:08,  1.15s/it]
Training tasks:  80%|████████  | 24/30 [00:27<00:06,  1.12s/it]
Training tasks:  83%|████████▎ | 25/30 [00:28<00:05,  1.12s/it]
Training tasks:  87%|████████▋ | 26/30 [00:29<00:04,  1.15s/it]
Training tasks:  90%|█████████ | 27/30 [00:30<00:03,  1.15s/it]
Training tasks:  93%|█████████▎| 28/30 [00:32<00:02,  1.14s/it]
Training tasks:  97%|█████████▋| 29/30 [00:33<00:01,  1.16s/it]
Training tasks: 100%|██████████| 30/30 [00:34<00:00,  1.13s/it]
Training tasks: 100%|██████████| 30/30 [00:34<00:00,  1.14s/it]
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
警告: 没有足够的有效类别进行3-way任务采样，尝试调整参数...
类别样本数量: {0: 33, 1: 5, 2: 29}
调整后的参数: n_shot=2, n_query=3
类别 normal (标签 0): 33个样本
类别 e-kc (标签 1): 5个样本
类别 kc (标签 2): 29个样本

Validating tasks:   0%|          | 0/10 [00:00<?, ?it/s]
Validating tasks:  10%|█         | 1/10 [00:00<00:01,  8.16it/s]
Validating tasks:  20%|██        | 2/10 [00:00<00:00,  8.08it/s]
Validating tasks:  30%|███       | 3/10 [00:00<00:00,  8.05it/s]
Validating tasks:  40%|████      | 4/10 [00:00<00:00,  7.85it/s]
Validating tasks:  50%|█████     | 5/10 [00:00<00:00,  7.79it/s]
Validating tasks:  60%|██████    | 6/10 [00:00<00:00,  7.68it/s]
Validating tasks:  70%|███████   | 7/10 [00:00<00:00,  7.66it/s]
Validating tasks:  80%|████████  | 8/10 [00:01<00:00,  7.56it/s]
Validating tasks:  90%|█████████ | 9/10 [00:01<00:00,  7.58it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  7.51it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  7.68it/s]
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
Epoch 4: Train Loss: 1.0487, Train Acc: 0.9044, Val Loss: 1.6425, Val Acc: 0.6111
验证准确率未提高，当前耐心值: 1/5
Epoch 5/30
当前学习率: 0.000200
使用类别权重: 正常=1.0, 早期圆锥角膜=8.0, 圆锥角膜=4.0
使用增强版对比损失函数，温度: 0.07, 硬负样本挖掘比例: 0.7, 早期圆锥角膜与正常样本对比权重: 2.0, 圆锥角膜与正常样本对比权重: 1.0

Training tasks:   0%|          | 0/30 [00:00<?, ?it/s]
Training tasks:   3%|▎         | 1/30 [00:01<00:32,  1.12s/it]
Training tasks:   7%|▋         | 2/30 [00:02<00:31,  1.11s/it]
Training tasks:  10%|█         | 3/30 [00:03<00:30,  1.12s/it]
Training tasks:  13%|█▎        | 4/30 [00:04<00:28,  1.10s/it]
Training tasks:  17%|█▋        | 5/30 [00:05<00:27,  1.11s/it]
Training tasks:  20%|██        | 6/30 [00:06<00:27,  1.15s/it]
Training tasks:  23%|██▎       | 7/30 [00:07<00:25,  1.12s/it]
Training tasks:  27%|██▋       | 8/30 [00:09<00:25,  1.17s/it]
Training tasks:  30%|███       | 9/30 [00:10<00:23,  1.13s/it]
Training tasks:  33%|███▎      | 10/30 [00:11<00:22,  1.12s/it]
Training tasks:  37%|███▋      | 11/30 [00:12<00:20,  1.10s/it]
Training tasks:  40%|████      | 12/30 [00:13<00:20,  1.14s/it]
Training tasks:  43%|████▎     | 13/30 [00:14<00:19,  1.17s/it]
Training tasks:  47%|████▋     | 14/30 [00:15<00:18,  1.15s/it]
Training tasks:  50%|█████     | 15/30 [00:16<00:16,  1.13s/it]
Training tasks:  53%|█████▎    | 16/30 [00:18<00:15,  1.12s/it]
Training tasks:  57%|█████▋    | 17/30 [00:19<00:14,  1.14s/it]
Training tasks:  60%|██████    | 18/30 [00:20<00:14,  1.17s/it]
Training tasks:  63%|██████▎   | 19/30 [00:21<00:13,  1.19s/it]
Training tasks:  67%|██████▋   | 20/30 [00:23<00:12,  1.21s/it]
Training tasks:  70%|███████   | 21/30 [00:24<00:10,  1.17s/it]
Training tasks:  73%|███████▎  | 22/30 [00:25<00:08,  1.12s/it]
Training tasks:  77%|███████▋  | 23/30 [00:26<00:07,  1.09s/it]
Training tasks:  80%|████████  | 24/30 [00:27<00:06,  1.07s/it]
Training tasks:  83%|████████▎ | 25/30 [00:28<00:05,  1.08s/it]
Training tasks:  87%|████████▋ | 26/30 [00:29<00:04,  1.09s/it]
Training tasks:  90%|█████████ | 27/30 [00:30<00:03,  1.10s/it]
Training tasks:  93%|█████████▎| 28/30 [00:31<00:02,  1.09s/it]
Training tasks:  97%|█████████▋| 29/30 [00:32<00:01,  1.12s/it]
Training tasks: 100%|██████████| 30/30 [00:33<00:00,  1.12s/it]
Training tasks: 100%|██████████| 30/30 [00:33<00:00,  1.13s/it]
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
警告: 没有足够的有效类别进行3-way任务采样，尝试调整参数...
类别样本数量: {0: 33, 1: 5, 2: 29}
调整后的参数: n_shot=2, n_query=3
类别 normal (标签 0): 33个样本
类别 e-kc (标签 1): 5个样本
类别 kc (标签 2): 29个样本

Validating tasks:   0%|          | 0/10 [00:00<?, ?it/s]
Validating tasks:  10%|█         | 1/10 [00:00<00:01,  7.35it/s]
Validating tasks:  20%|██        | 2/10 [00:00<00:01,  7.08it/s]
Validating tasks:  30%|███       | 3/10 [00:00<00:00,  7.23it/s]
Validating tasks:  40%|████      | 4/10 [00:00<00:00,  7.14it/s]
Validating tasks:  50%|█████     | 5/10 [00:00<00:00,  7.05it/s]
Validating tasks:  60%|██████    | 6/10 [00:00<00:00,  6.86it/s]
Validating tasks:  70%|███████   | 7/10 [00:01<00:00,  6.62it/s]
Validating tasks:  80%|████████  | 8/10 [00:01<00:00,  6.24it/s]
Validating tasks:  90%|█████████ | 9/10 [00:01<00:00,  6.51it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  6.67it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  6.76it/s]
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
Epoch 5: Train Loss: 1.0833, Train Acc: 0.8874, Val Loss: 1.8828, Val Acc: 0.5444
验证准确率未提高，当前耐心值: 2/5
Epoch 6/30
当前学习率: 0.000200
使用类别权重: 正常=1.0, 早期圆锥角膜=8.0, 圆锥角膜=4.0
使用增强版对比损失函数，温度: 0.07, 硬负样本挖掘比例: 0.7, 早期圆锥角膜与正常样本对比权重: 2.0, 圆锥角膜与正常样本对比权重: 1.0

Training tasks:   0%|          | 0/30 [00:00<?, ?it/s]
Training tasks:   3%|▎         | 1/30 [00:01<00:32,  1.11s/it]
Training tasks:   7%|▋         | 2/30 [00:02<00:33,  1.19s/it]
Training tasks:  10%|█         | 3/30 [00:03<00:32,  1.20s/it]
Training tasks:  13%|█▎        | 4/30 [00:04<00:30,  1.19s/it]
Training tasks:  17%|█▋        | 5/30 [00:05<00:28,  1.15s/it]
Training tasks:  20%|██        | 6/30 [00:06<00:26,  1.12s/it]
Training tasks:  23%|██▎       | 7/30 [00:07<00:25,  1.10s/it]
Training tasks:  27%|██▋       | 8/30 [00:09<00:24,  1.09s/it]
Training tasks:  30%|███       | 9/30 [00:10<00:23,  1.14s/it]
Training tasks:  33%|███▎      | 10/30 [00:11<00:22,  1.13s/it]
Training tasks:  37%|███▋      | 11/30 [00:12<00:21,  1.11s/it]
Training tasks:  40%|████      | 12/30 [00:13<00:19,  1.09s/it]
Training tasks:  43%|████▎     | 13/30 [00:14<00:18,  1.06s/it]
Training tasks:  47%|████▋     | 14/30 [00:15<00:16,  1.03s/it]
Training tasks:  50%|█████     | 15/30 [00:16<00:15,  1.04s/it]
Training tasks:  53%|█████▎    | 16/30 [00:17<00:14,  1.05s/it]
Training tasks:  57%|█████▋    | 17/30 [00:18<00:13,  1.05s/it]
Training tasks:  60%|██████    | 18/30 [00:19<00:12,  1.06s/it]
Training tasks:  63%|██████▎   | 19/30 [00:20<00:12,  1.13s/it]
Training tasks:  67%|██████▋   | 20/30 [00:22<00:11,  1.13s/it]
Training tasks:  70%|███████   | 21/30 [00:23<00:10,  1.12s/it]
Training tasks:  73%|███████▎  | 22/30 [00:24<00:08,  1.12s/it]
Training tasks:  77%|███████▋  | 23/30 [00:25<00:07,  1.13s/it]
Training tasks:  80%|████████  | 24/30 [00:26<00:06,  1.11s/it]
Training tasks:  83%|████████▎ | 25/30 [00:27<00:05,  1.14s/it]
Training tasks:  87%|████████▋ | 26/30 [00:29<00:04,  1.18s/it]
Training tasks:  90%|█████████ | 27/30 [00:30<00:03,  1.19s/it]
Training tasks:  93%|█████████▎| 28/30 [00:31<00:02,  1.21s/it]
Training tasks:  97%|█████████▋| 29/30 [00:32<00:01,  1.21s/it]
Training tasks: 100%|██████████| 30/30 [00:33<00:00,  1.17s/it]
Training tasks: 100%|██████████| 30/30 [00:33<00:00,  1.13s/it]
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
警告: 没有足够的有效类别进行3-way任务采样，尝试调整参数...
类别样本数量: {0: 33, 1: 5, 2: 29}
调整后的参数: n_shot=2, n_query=3
类别 normal (标签 0): 33个样本
类别 e-kc (标签 1): 5个样本
类别 kc (标签 2): 29个样本

Validating tasks:   0%|          | 0/10 [00:00<?, ?it/s]
Validating tasks:  10%|█         | 1/10 [00:00<00:01,  5.95it/s]
Validating tasks:  20%|██        | 2/10 [00:00<00:01,  6.18it/s]
Validating tasks:  30%|███       | 3/10 [00:00<00:01,  6.67it/s]
Validating tasks:  40%|████      | 4/10 [00:00<00:00,  6.85it/s]
Validating tasks:  50%|█████     | 5/10 [00:00<00:00,  6.98it/s]
Validating tasks:  60%|██████    | 6/10 [00:00<00:00,  7.04it/s]
Validating tasks:  70%|███████   | 7/10 [00:01<00:00,  7.01it/s]
Validating tasks:  80%|████████  | 8/10 [00:01<00:00,  6.56it/s]
Validating tasks:  90%|█████████ | 9/10 [00:01<00:00,  6.47it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  6.24it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  6.54it/s]
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
Epoch 6: Train Loss: 0.9768, Train Acc: 0.9252, Val Loss: 1.8255, Val Acc: 0.5444
验证准确率未提高，当前耐心值: 3/5
Epoch 7/30
当前学习率: 0.000100
使用类别权重: 正常=1.0, 早期圆锥角膜=8.0, 圆锥角膜=4.0
使用增强版对比损失函数，温度: 0.07, 硬负样本挖掘比例: 0.7, 早期圆锥角膜与正常样本对比权重: 2.0, 圆锥角膜与正常样本对比权重: 1.0

Training tasks:   0%|          | 0/30 [00:00<?, ?it/s]
Training tasks:   3%|▎         | 1/30 [00:01<00:32,  1.11s/it]
Training tasks:   7%|▋         | 2/30 [00:02<00:31,  1.11s/it]
Training tasks:  10%|█         | 3/30 [00:03<00:28,  1.04s/it]
Training tasks:  13%|█▎        | 4/30 [00:04<00:26,  1.02s/it]
Training tasks:  17%|█▋        | 5/30 [00:05<00:25,  1.02s/it]
Training tasks:  20%|██        | 6/30 [00:06<00:26,  1.09s/it]
Training tasks:  23%|██▎       | 7/30 [00:07<00:25,  1.11s/it]
Training tasks:  27%|██▋       | 8/30 [00:08<00:23,  1.09s/it]
Training tasks:  30%|███       | 9/30 [00:09<00:22,  1.08s/it]
Training tasks:  33%|███▎      | 10/30 [00:10<00:21,  1.09s/it]
Training tasks:  37%|███▋      | 11/30 [00:11<00:20,  1.09s/it]
Training tasks:  40%|████      | 12/30 [00:13<00:20,  1.13s/it]
Training tasks:  43%|████▎     | 13/30 [00:14<00:19,  1.15s/it]
Training tasks:  47%|████▋     | 14/30 [00:15<00:18,  1.13s/it]
Training tasks:  50%|█████     | 15/30 [00:16<00:16,  1.11s/it]
Training tasks:  53%|█████▎    | 16/30 [00:17<00:15,  1.10s/it]
Training tasks:  57%|█████▋    | 17/30 [00:18<00:14,  1.09s/it]
Training tasks:  60%|██████    | 18/30 [00:19<00:13,  1.14s/it]
Training tasks:  63%|██████▎   | 19/30 [00:21<00:13,  1.18s/it]
Training tasks:  67%|██████▋   | 20/30 [00:22<00:11,  1.17s/it]
Training tasks:  70%|███████   | 21/30 [00:23<00:10,  1.16s/it]
Training tasks:  73%|███████▎  | 22/30 [00:24<00:09,  1.16s/it]
Training tasks:  77%|███████▋  | 23/30 [00:25<00:08,  1.18s/it]
Training tasks:  80%|████████  | 24/30 [00:27<00:07,  1.19s/it]
Training tasks:  83%|████████▎ | 25/30 [00:28<00:05,  1.16s/it]
Training tasks:  87%|████████▋ | 26/30 [00:29<00:04,  1.11s/it]
Training tasks:  90%|█████████ | 27/30 [00:30<00:03,  1.07s/it]
Training tasks:  93%|█████████▎| 28/30 [00:31<00:02,  1.07s/it]
Training tasks:  97%|█████████▋| 29/30 [00:32<00:01,  1.08s/it]
Training tasks: 100%|██████████| 30/30 [00:33<00:00,  1.07s/it]
Training tasks: 100%|██████████| 30/30 [00:33<00:00,  1.11s/it]
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
警告: 没有足够的有效类别进行3-way任务采样，尝试调整参数...
类别样本数量: {0: 33, 1: 5, 2: 29}
调整后的参数: n_shot=2, n_query=3
类别 normal (标签 0): 33个样本
类别 e-kc (标签 1): 5个样本
类别 kc (标签 2): 29个样本

Validating tasks:   0%|          | 0/10 [00:00<?, ?it/s]
Validating tasks:  10%|█         | 1/10 [00:00<00:01,  6.09it/s]
Validating tasks:  20%|██        | 2/10 [00:00<00:01,  6.25it/s]
Validating tasks:  30%|███       | 3/10 [00:00<00:01,  6.68it/s]
Validating tasks:  40%|████      | 4/10 [00:00<00:00,  6.89it/s]
Validating tasks:  50%|█████     | 5/10 [00:00<00:00,  6.96it/s]
Validating tasks:  60%|██████    | 6/10 [00:00<00:00,  6.68it/s]
Validating tasks:  70%|███████   | 7/10 [00:01<00:00,  6.38it/s]
Validating tasks:  80%|████████  | 8/10 [00:01<00:00,  6.55it/s]
Validating tasks:  90%|█████████ | 9/10 [00:01<00:00,  6.34it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  6.38it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  6.50it/s]
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
Epoch 7: Train Loss: 0.8310, Train Acc: 0.9430, Val Loss: 1.8708, Val Acc: 0.4556
验证准确率未提高，当前耐心值: 4/5
Epoch 8/30
当前学习率: 0.000100
使用类别权重: 正常=1.0, 早期圆锥角膜=8.0, 圆锥角膜=4.0
使用增强版对比损失函数，温度: 0.07, 硬负样本挖掘比例: 0.7, 早期圆锥角膜与正常样本对比权重: 2.0, 圆锥角膜与正常样本对比权重: 1.0

Training tasks:   0%|          | 0/30 [00:00<?, ?it/s]
Training tasks:   3%|▎         | 1/30 [00:01<00:35,  1.21s/it]
Training tasks:   7%|▋         | 2/30 [00:02<00:35,  1.27s/it]
Training tasks:  10%|█         | 3/30 [00:03<00:33,  1.24s/it]
Training tasks:  13%|█▎        | 4/30 [00:04<00:31,  1.21s/it]
Training tasks:  17%|█▋        | 5/30 [00:05<00:28,  1.14s/it]
Training tasks:  20%|██        | 6/30 [00:06<00:26,  1.12s/it]
Training tasks:  23%|██▎       | 7/30 [00:08<00:25,  1.11s/it]
Training tasks:  27%|██▋       | 8/30 [00:09<00:23,  1.09s/it]
Training tasks:  30%|███       | 9/30 [00:10<00:22,  1.08s/it]
Training tasks:  33%|███▎      | 10/30 [00:11<00:21,  1.08s/it]
Training tasks:  37%|███▋      | 11/30 [00:12<00:20,  1.08s/it]
Training tasks:  40%|████      | 12/30 [00:13<00:19,  1.10s/it]
Training tasks:  43%|████▎     | 13/30 [00:14<00:19,  1.14s/it]
Training tasks:  47%|████▋     | 14/30 [00:15<00:18,  1.14s/it]
Training tasks:  50%|█████     | 15/30 [00:17<00:17,  1.15s/it]
Training tasks:  53%|█████▎    | 16/30 [00:18<00:16,  1.16s/it]
Training tasks:  57%|█████▋    | 17/30 [00:19<00:14,  1.12s/it]
Training tasks:  60%|██████    | 18/30 [00:20<00:12,  1.08s/it]
Training tasks:  63%|██████▎   | 19/30 [00:21<00:11,  1.08s/it]
Training tasks:  67%|██████▋   | 20/30 [00:22<00:10,  1.07s/it]
Training tasks:  70%|███████   | 21/30 [00:23<00:09,  1.08s/it]
Training tasks:  73%|███████▎  | 22/30 [00:24<00:08,  1.06s/it]
Training tasks:  77%|███████▋  | 23/30 [00:25<00:07,  1.07s/it]
Training tasks:  80%|████████  | 24/30 [00:26<00:06,  1.08s/it]
Training tasks:  83%|████████▎ | 25/30 [00:27<00:05,  1.09s/it]
Training tasks:  87%|████████▋ | 26/30 [00:28<00:04,  1.10s/it]
Training tasks:  90%|█████████ | 27/30 [00:30<00:03,  1.11s/it]
Training tasks:  93%|█████████▎| 28/30 [00:31<00:02,  1.10s/it]
Training tasks:  97%|█████████▋| 29/30 [00:32<00:01,  1.08s/it]
Training tasks: 100%|██████████| 30/30 [00:33<00:00,  1.07s/it]
Training tasks: 100%|██████████| 30/30 [00:33<00:00,  1.11s/it]
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 15 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
警告: 没有足够的有效类别进行3-way任务采样，尝试调整参数...
类别样本数量: {0: 33, 1: 5, 2: 29}
调整后的参数: n_shot=2, n_query=3
类别 normal (标签 0): 33个样本
类别 e-kc (标签 1): 5个样本
类别 kc (标签 2): 29个样本

Validating tasks:   0%|          | 0/10 [00:00<?, ?it/s]
Validating tasks:  10%|█         | 1/10 [00:00<00:01,  7.35it/s]
Validating tasks:  20%|██        | 2/10 [00:00<00:01,  6.99it/s]
Validating tasks:  30%|███       | 3/10 [00:00<00:01,  6.59it/s]
Validating tasks:  40%|████      | 4/10 [00:00<00:00,  6.32it/s]
Validating tasks:  50%|█████     | 5/10 [00:00<00:00,  6.19it/s]
Validating tasks:  60%|██████    | 6/10 [00:00<00:00,  6.46it/s]
Validating tasks:  70%|███████   | 7/10 [00:01<00:00,  6.43it/s]
Validating tasks:  80%|████████  | 8/10 [00:01<00:00,  6.40it/s]
Validating tasks:  90%|█████████ | 9/10 [00:01<00:00,  6.35it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  6.60it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  6.51it/s]
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
Epoch 8: Train Loss: 0.7220, Train Acc: 0.9719, Val Loss: 1.8164, Val Acc: 0.5444
验证准确率未提高，当前耐心值: 5/5
早停触发，停止训练
警告: 没有足够的有效类别进行3-way任务采样，尝试调整参数...
类别样本数量: {0: 37, 1: 7, 2: 27}
调整后的参数: n_shot=3, n_query=4
类别 normal (标签 0): 37个样本
类别 e-kc (标签 1): 7个样本
类别 kc (标签 2): 27个样本

Testing tasks:   0%|          | 0/30 [00:00<?, ?it/s]
Testing tasks:   3%|▎         | 1/30 [00:00<00:07,  4.03it/s]
Testing tasks:   7%|▋         | 2/30 [00:00<00:05,  4.70it/s]
Testing tasks:  10%|█         | 3/30 [00:00<00:05,  4.95it/s]
Testing tasks:  13%|█▎        | 4/30 [00:00<00:05,  5.06it/s]
Testing tasks:  17%|█▋        | 5/30 [00:01<00:05,  4.71it/s]
Testing tasks:  20%|██        | 6/30 [00:01<00:05,  4.77it/s]
Testing tasks:  23%|██▎       | 7/30 [00:01<00:04,  4.74it/s]
Testing tasks:  27%|██▋       | 8/30 [00:01<00:04,  4.91it/s]
Testing tasks:  30%|███       | 9/30 [00:01<00:04,  4.99it/s]
Testing tasks:  33%|███▎      | 10/30 [00:02<00:03,  5.07it/s]
Testing tasks:  37%|███▋      | 11/30 [00:02<00:03,  4.87it/s]
Testing tasks:  40%|████      | 12/30 [00:02<00:03,  4.81it/s]
Testing tasks:  43%|████▎     | 13/30 [00:02<00:03,  4.88it/s]
Testing tasks:  47%|████▋     | 14/30 [00:02<00:03,  4.98it/s]
Testing tasks:  50%|█████     | 15/30 [00:03<00:02,  5.01it/s]
Testing tasks:  53%|█████▎    | 16/30 [00:03<00:02,  5.05it/s]
Testing tasks:  57%|█████▋    | 17/30 [00:03<00:02,  4.86it/s]
Testing tasks:  60%|██████    | 18/30 [00:03<00:02,  4.78it/s]
Testing tasks:  63%|██████▎   | 19/30 [00:03<00:02,  4.86it/s]
Testing tasks:  67%|██████▋   | 20/30 [00:04<00:02,  4.98it/s]
Testing tasks:  70%|███████   | 21/30 [00:04<00:01,  5.04it/s]
Testing tasks:  73%|███████▎  | 22/30 [00:04<00:01,  5.06it/s]
Testing tasks:  77%|███████▋  | 23/30 [00:04<00:01,  4.85it/s]
Testing tasks:  80%|████████  | 24/30 [00:04<00:01,  4.99it/s]
Testing tasks:  83%|████████▎ | 25/30 [00:05<00:01,  4.68it/s]
Testing tasks:  87%|████████▋ | 26/30 [00:05<00:00,  4.84it/s]
Testing tasks:  90%|█████████ | 27/30 [00:05<00:00,  4.82it/s]
Testing tasks:  93%|█████████▎| 28/30 [00:05<00:00,  4.91it/s]
Testing tasks:  97%|█████████▋| 29/30 [00:05<00:00,  4.97it/s]
Testing tasks: 100%|██████████| 30/30 [00:06<00:00,  5.07it/s]
Testing tasks: 100%|██████████| 30/30 [00:06<00:00,  4.91it/s]
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 9 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 9 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 9 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 9 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 9 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 9 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 9 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 9 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 9 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 9 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 9 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 9 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 9 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 9 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 9 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 9 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 9 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 9 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 9 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 9 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 9 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 9 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 9 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 9 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 9 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 9 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 9 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 9 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 9 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 9 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本

测试结果:
总体准确率: 91.39%
类别准确率:
  kc: 77.50%
  e-kc: 99.17%
  normal: 97.50%
训练完成，结果已保存到 results_optimized_20250525_142053_gpu0,1

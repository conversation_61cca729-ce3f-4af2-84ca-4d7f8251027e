{"test_acc": 83.33333333333334, "class_accuracies": {"kc": 65.83333333333333, "e-kc": 84.16666666666667, "normal": 100.0}, "confusion_matrix": [[120, 0, 0], [16, 101, 3], [3, 38, 79]], "best_epoch": 1, "best_val_acc": 0.8333333333333334, "history": {"train_loss": [1.7770177364349364, 1.3193077007929483, 1.0172829846541087, 0.9406003137429555, 1.0295282781124115, 0.8846779068311056, 0.7322617808977763], "train_acc": [0.49703703703703694, 0.7644444444444447, 0.9325925925925928, 0.9318518518518519, 0.9000000000000001, 0.951851851851852, 0.9740740740740742], "val_loss": [1.7208450198173524, 1.2854477763175964, 1.6992581725120544, 1.8417427301406861, 1.7067661166191102, 1.7017266988754272, 1.8655609369277955], "val_acc": [0.5888888888888889, 0.8333333333333334, 0.6333333333333334, 0.6555555555555557, 0.6444444444444445, 0.6111111111111112, 0.4888888888888888], "contrastive_loss": [0.5000323981046677, 0.29654263829191524, 0.2292012868759533, 0.17480824794620275, 0.2632257304930439, 0.16652207750982295, 0.08183083385689921], "learning_rates": [0.0002, 0.0002, 0.0002, 0.0002, 0.0002, 0.0001, 0.0001], "batch_learning_rates": []}, "args": {"train_csv": "/home/<USER>/balanced_task_sampling/split_result/train_set.csv", "val_csv": "/home/<USER>/balanced_task_sampling/split_result/val_set.csv", "test_csv": "/home/<USER>/balanced_task_sampling/split_result/test_set.csv", "save_dir": "results_optimized_20250525_150650_gpu0,1", "device": "cuda", "proto_counts": "2,6,3", "feature_dim": 512, "inner_lr": 0.3, "inner_steps": 2, "pretrained": true, "dropout": 0.5, "use_normalized_features": false, "normalize_scale": 10.0, "epochs": 30, "lr": 0.0002, "weight_decay": 0.0005, "n_way": 3, "n_shot": 5, "n_query": 15, "tasks_per_epoch": 30, "meta_batch_size": 8, "val_frequency": 1, "early_stopping": 5, "use_lr_scheduler": true, "scheduler_type": "plateau", "max_lr": 0.001, "div_factor": 25.0, "final_div_factor": 10000.0, "pct_start": 0.1, "plateau_factor": 0.5, "plateau_patience": 2, "min_lr": 1e-06, "record_batch_lr": false, "no_augmentation": true, "early_kc_augment": false, "kc_augment": false, "advanced_augment": false, "mixup_alpha": 0.0, "cutmix_prob": 0.0, "early_kc_specific_augment": false, "augment_factor": 1, "kc_augment_factor": 1, "use_enhanced_gpu_augment": false, "early_kc_weight": 7.0, "kc_weight": 5.0, "focal_gamma": 2.0, "use_contrastive": true, "contrastive_weight": 0.5, "temperature": 0.07, "hard_mining_ratio": 0.7, "early_normal_weight": 2.0, "kc_normal_weight": 1.0, "use_balanced_task_sampler": true, "kc_shot_multiplier": 3.0, "early_kc_shot_multiplier": 1.5, "seed": 42, "num_workers": 4, "use_separate_test_sampler": true}}
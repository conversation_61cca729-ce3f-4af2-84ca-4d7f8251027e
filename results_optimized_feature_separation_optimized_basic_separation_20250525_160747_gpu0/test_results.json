{"test_acc": 77.5, "class_accuracies": {"kc": 61.66666666666667, "e-kc": 85.0, "normal": 85.83333333333333}, "confusion_matrix": [[103, 17, 0], [0, 102, 18], [0, 46, 74]], "best_epoch": 3, "best_val_acc": 0.7777777777777778, "history": {"train_loss": [1.8360904614130655, 1.255826195081075, 1.1079720954100292, 0.9752576847871145, 0.8805625379085541, 0.9760619501272837, 0.8472960392634074, 0.7521942118803661, 0.7001296659310658], "train_acc": [0.6274074074074074, 0.8985185185185187, 0.9044444444444447, 0.9155555555555558, 0.9400000000000001, 0.9407407407407409, 0.9755555555555556, 0.9770370370370371, 0.9881481481481482], "val_loss": [1.6216988563537598, 1.4887187480926514, 1.5470311403274537, 1.4426603436470031, 1.8269743919372559, 1.4863183856010438, 1.7614194869995117, 1.4861889600753784, 1.6510273575782777], "val_acc": [0.6444444444444445, 0.7555555555555555, 0.6666666666666667, 0.7777777777777778, 0.6555555555555554, 0.6666666666666666, 0.6333333333333333, 0.6444444444444445, 0.6444444444444445], "contrastive_loss": [0.4258961242934068, 0.2611910005410512, 0.2949110232293606, 0.20402000506098072, 0.14974149035212272, 0.18402706353614728, 0.1429419779451564, 0.05665880043313033, 0.03880868925678745], "learning_rates": [0.0002, 0.0002, 0.0002, 0.0002, 0.0002, 0.0002, 0.0002, 0.0001, 0.0001], "batch_learning_rates": []}, "args": {"train_csv": "/home/<USER>/balanced_task_sampling/split_result/train_set.csv", "val_csv": "/home/<USER>/balanced_task_sampling/split_result/val_set.csv", "test_csv": "/home/<USER>/balanced_task_sampling/split_result/test_set.csv", "save_dir": "results_optimized_feature_separation_optimized_basic_separation_20250525_160747_gpu0", "device": "cuda:0", "proto_counts": "2,4,2", "feature_dim": 512, "inner_lr": 0.3, "inner_steps": 2, "pretrained": true, "dropout": 0.5, "use_normalized_features": false, "normalize_scale": 10.0, "use_feature_separation": true, "use_enhanced_separation": false, "use_adaptive_separation": false, "separation_dim": 128, "separation_weight": 0.1, "use_attention_in_separation": true, "epochs": 30, "lr": 0.0002, "weight_decay": 0.0005, "n_way": 3, "n_shot": 5, "n_query": 15, "tasks_per_epoch": 30, "meta_batch_size": 8, "val_frequency": 1, "early_stopping": 5, "use_lr_scheduler": true, "scheduler_type": "plateau", "max_lr": 0.001, "div_factor": 25.0, "final_div_factor": 10000.0, "pct_start": 0.1, "plateau_factor": 0.5, "plateau_patience": 2, "min_lr": 1e-06, "record_batch_lr": false, "no_augmentation": false, "early_kc_augment": false, "kc_augment": false, "advanced_augment": false, "mixup_alpha": 0.2, "cutmix_prob": 0.3, "early_kc_specific_augment": false, "augment_factor": 6, "kc_augment_factor": 3, "use_enhanced_gpu_augment": false, "early_kc_weight": 8.0, "kc_weight": 4.0, "focal_gamma": 2.0, "use_contrastive": true, "contrastive_weight": 0.5, "temperature": 0.07, "hard_mining_ratio": 0.7, "early_normal_weight": 2.0, "kc_normal_weight": 1.0, "use_balanced_task_sampler": true, "kc_shot_multiplier": 3.0, "early_kc_shot_multiplier": 1.5, "seed": 42, "num_workers": 2, "use_separate_test_sampler": false}}
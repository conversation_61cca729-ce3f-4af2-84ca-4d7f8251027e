#!/usr/bin/env python3
"""
带特征分离模块的训练脚本

该脚本集成了特征分离模块，专门用于提高E-KC和KC之间的区分能力。
"""

import os
import sys
import argparse
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
import numpy as np
from tqdm import tqdm
import json
import time
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 导入自定义模块
try:
    from models.feature_separation_extractor import FeatureSeparationExtractor, EnhancedFeatureSeparationExtractor
    from models.enhanced_feature_extractor import EnhancedKeratoconusFeatureExtractor
    from models.protonet import ProtoNet
    from models.maml import MAMLProtoNet
    from data.keratoconus_dataset import KeratoconusDataset
    from data.balanced_task_sampler import BalancedTaskSampler
    from models.losses import FocalLoss, MAMLContrastiveLoss
    print("成功导入所有模块")
except ImportError as e:
    print(f"导入模块失败: {e}")
    sys.exit(1)


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='带特征分离模块的圆锥角膜分类训练')
    
    # 数据相关参数
    parser.add_argument('--data_dir', type=str, default='/home/<USER>/data/keratoconus_data',
                        help='数据目录路径')
    parser.add_argument('--batch_size', type=int, default=16, help='批次大小')
    
    # 模型相关参数
    parser.add_argument('--feature_dim', type=int, default=512, help='特征维度')
    parser.add_argument('--separation_dim', type=int, default=128, help='分离特征维度')
    parser.add_argument('--dropout', type=float, default=0.3, help='Dropout率')
    parser.add_argument('--pretrained', action='store_true', default=True, help='使用预训练模型')
    
    # 特征分离相关参数
    parser.add_argument('--use_feature_separation', action='store_true', default=False,
                        help='是否使用特征分离模块')
    parser.add_argument('--use_enhanced_separation', action='store_true', default=False,
                        help='是否使用增强版特征分离')
    parser.add_argument('--use_adaptive_separation', action='store_true', default=False,
                        help='是否使用自适应特征分离')
    parser.add_argument('--separation_weight', type=float, default=0.1,
                        help='特征分离损失权重')
    parser.add_argument('--use_attention_in_separation', action='store_true', default=True,
                        help='在特征分离模块中使用注意力机制')
    
    # 训练相关参数
    parser.add_argument('--epochs', type=int, default=100, help='训练轮数')
    parser.add_argument('--lr', type=float, default=0.001, help='学习率')
    parser.add_argument('--weight_decay', type=float, default=1e-4, help='权重衰减')
    parser.add_argument('--tasks_per_epoch', type=int, default=100, help='每轮任务数')
    
    # Few-shot学习参数
    parser.add_argument('--n_way', type=int, default=3, help='N-way分类')
    parser.add_argument('--n_shot', type=int, default=5, help='N-shot学习')
    parser.add_argument('--n_query', type=int, default=15, help='查询集大小')
    parser.add_argument('--proto_counts', type=str, default='1,5,2',
                        help='每个类别的原型数量 (Normal,E-KC,KC)')
    
    # MAML参数
    parser.add_argument('--inner_lr', type=float, default=0.01, help='内循环学习率')
    parser.add_argument('--inner_steps', type=int, default=5, help='内循环步数')
    
    # 损失函数参数
    parser.add_argument('--use_focal_loss', action='store_true', default=True,
                        help='使用焦点损失')
    parser.add_argument('--focal_alpha', type=str, default='1.0,2.0,3.0',
                        help='焦点损失的alpha参数')
    parser.add_argument('--focal_gamma', type=float, default=2.0,
                        help='焦点损失的gamma参数')
    parser.add_argument('--use_contrastive', action='store_true', default=False,
                        help='使用对比学习损失')
    parser.add_argument('--contrastive_weight', type=float, default=0.5,
                        help='对比学习损失权重')
    
    # 其他参数
    parser.add_argument('--device', type=str, default='cuda:0', help='设备')
    parser.add_argument('--save_dir', type=str, default='./checkpoints_feature_separation',
                        help='模型保存目录')
    parser.add_argument('--log_interval', type=int, default=10, help='日志打印间隔')
    parser.add_argument('--save_interval', type=int, default=20, help='模型保存间隔')
    
    return parser.parse_args()


def create_model(args, device):
    """创建模型"""
    # 解析原型配置
    proto_counts = [int(x) for x in args.proto_counts.split(',')]
    print(f"使用原型配置: {proto_counts}")
    
    # 创建特征提取器
    if args.use_enhanced_separation:
        print("使用增强版特征分离提取器")
        feature_extractor = EnhancedFeatureSeparationExtractor(
            pretrained=args.pretrained,
            feature_dim=args.feature_dim,
            separation_dim=args.separation_dim,
            dropout_rate=args.dropout,
            use_separation=args.use_feature_separation,
            use_adaptive=args.use_adaptive_separation,
            use_attention=args.use_attention_in_separation,
            separation_weight=args.separation_weight,
            use_multiscale=True
        )
    elif args.use_feature_separation:
        print("使用特征分离提取器")
        feature_extractor = FeatureSeparationExtractor(
            pretrained=args.pretrained,
            feature_dim=args.feature_dim,
            separation_dim=args.separation_dim,
            dropout_rate=args.dropout,
            use_separation=True,
            use_adaptive=args.use_adaptive_separation,
            use_attention=args.use_attention_in_separation,
            separation_weight=args.separation_weight
        )
    else:
        print("使用增强版特征提取器（不含特征分离）")
        feature_extractor = EnhancedKeratoconusFeatureExtractor(
            pretrained=args.pretrained,
            feature_dim=args.feature_dim,
            dropout_rate=args.dropout,
            use_feature_separation=False
        )
    
    feature_extractor = feature_extractor.to(device)
    
    # 创建ProtoNet模型
    protonet = ProtoNet(
        feature_extractor,
        n_classes=args.n_way,
        proto_counts=proto_counts,
        feature_dim=args.feature_dim
    ).to(device)
    
    # 创建MAMLProtoNet模型
    model = MAMLProtoNet(
        protonet=protonet,
        inner_lr=args.inner_lr,
        inner_steps=args.inner_steps,
        task_weight_method='difficulty'
    ).to(device)
    
    return model


def create_loss_function(args, device):
    """创建损失函数"""
    if args.use_focal_loss:
        # 解析alpha参数
        alpha = [float(x) for x in args.focal_alpha.split(',')]
        alpha_tensor = torch.tensor(alpha).to(device)
        criterion = FocalLoss(alpha=alpha_tensor, gamma=args.focal_gamma)
        print(f"使用焦点损失，alpha: {alpha}, gamma: {args.focal_gamma}")
    else:
        criterion = nn.CrossEntropyLoss()
        print("使用交叉熵损失")
    
    # 对比学习损失
    contrastive_loss_fn = None
    if args.use_contrastive:
        contrastive_loss_fn = MAMLContrastiveLoss(temperature=0.07)
        print(f"使用对比学习损失，权重: {args.contrastive_weight}")
    
    return criterion, contrastive_loss_fn


def train_epoch(model, task_sampler, criterion, contrastive_loss_fn, optimizer, device, args):
    """训练一个epoch"""
    model.train()
    epoch_loss = 0.0
    epoch_acc = 0.0
    epoch_contrastive_loss = 0.0
    epoch_separation_loss = 0.0
    
    for _ in tqdm(range(args.tasks_per_epoch), desc="Training tasks"):
        # 采样一个任务
        task = task_sampler.sample_task()
        support_images, support_labels = task['support']
        query_images, query_labels = task['query']
        
        # 将数据移动到设备
        support_images = support_images.to(device)
        support_labels = support_labels.to(device)
        query_images = query_images.to(device)
        query_labels = query_labels.to(device)
        
        # 前向传播
        optimizer.zero_grad()
        
        # 获取查询集预测
        query_logits = model(query_images)
        
        # 计算分类损失
        loss = criterion(query_logits, query_labels)
        
        # 计算对比损失
        contrastive_loss = 0.0
        if args.use_contrastive and contrastive_loss_fn is not None:
            # 获取特征
            query_features = model.protonet.get_features(query_images)
            support_features = model.protonet.get_features(support_images)
            
            contrastive_loss = contrastive_loss_fn(
                query_features, query_labels, support_features, support_labels
            )
            loss += args.contrastive_weight * contrastive_loss
            epoch_contrastive_loss += contrastive_loss.item()
        
        # 计算特征分离损失
        separation_loss = 0.0
        if args.use_feature_separation:
            # 检查特征提取器是否有compute_separation_loss方法
            feature_extractor = model.protonet.feature_extractor
            if hasattr(feature_extractor, 'compute_separation_loss'):
                separation_loss = feature_extractor.compute_separation_loss(query_images, query_labels)
                loss += separation_loss
                epoch_separation_loss += separation_loss.item()
            elif hasattr(feature_extractor, 'compute_total_separation_loss'):
                separation_loss = feature_extractor.compute_total_separation_loss(query_images, query_labels)
                loss += separation_loss
                epoch_separation_loss += separation_loss.item()
        
        # 反向传播
        loss.backward()
        optimizer.step()
        
        # 计算准确率
        pred = torch.argmax(query_logits, dim=1)
        correct = (pred == query_labels).sum().item()
        accuracy = correct / len(query_labels)
        
        # 更新统计信息
        epoch_loss += loss.item()
        epoch_acc += accuracy
    
    # 计算平均值
    epoch_loss /= args.tasks_per_epoch
    epoch_acc /= args.tasks_per_epoch
    if args.use_contrastive:
        epoch_contrastive_loss /= args.tasks_per_epoch
    if args.use_feature_separation:
        epoch_separation_loss /= args.tasks_per_epoch
    
    return epoch_loss, epoch_acc, epoch_contrastive_loss, epoch_separation_loss


if __name__ == '__main__':
    args = parse_args()
    
    # 设置设备
    device = torch.device(args.device if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 创建保存目录
    os.makedirs(args.save_dir, exist_ok=True)
    
    # 保存配置
    config_path = os.path.join(args.save_dir, 'config.json')
    with open(config_path, 'w') as f:
        json.dump(vars(args), f, indent=2)
    
    print("特征分离模块训练脚本已创建完成！")
    print(f"配置已保存到: {config_path}")
    print("\n主要功能:")
    print("1. 支持基础特征分离模块")
    print("2. 支持增强版特征分离模块")
    print("3. 支持自适应特征分离")
    print("4. 集成多种损失函数")
    print("5. 支持Few-shot学习和MAML")

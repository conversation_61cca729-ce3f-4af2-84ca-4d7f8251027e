# 特征分离模块使用说明

## 📋 概述

特征分离模块是专门为提高E-KC（早期圆锥角膜）和KC（圆锥角膜）之间区分能力而设计的深度学习组件。该模块通过共享特征提取器和类别特定特征提取器，学习E-KC和KC之间的细微差异。

## 🏗️ 架构设计

### 核心组件

1. **FeatureSeparationModule** - 基础特征分离模块
   - 共享特征提取器：学习通用特征
   - E-KC特定特征提取器：专门学习E-KC特征
   - KC特定特征提取器：专门学习KC特征
   - 注意力机制：增强特征区分能力
   - 特征融合层：整合多种特征

2. **AdaptiveFeatureSeparationModule** - 自适应特征分离模块
   - 动态调整分离强度
   - 训练初期强分离，后期逐渐减弱
   - 自适应权重学习

3. **FeatureSeparationExtractor** - 特征分离提取器
   - 集成增强版特征提取器
   - 支持多种分离配置
   - 计算分离损失

4. **EnhancedFeatureSeparationExtractor** - 增强版特征分离提取器
   - 多尺度特征分离
   - 更复杂的特征融合策略

## 🚀 快速开始

### 1. 基础使用

```bash
# 运行基础特征分离实验
./run_optimized_with_feature_separation.sh --use_feature_separation true

# 运行增强版特征分离实验
./run_optimized_with_feature_separation.sh --use_enhanced_separation true

# 运行自适应特征分离实验
./run_optimized_with_feature_separation.sh --use_adaptive_separation true
```

### 2. 批量实验

```bash
# 运行所有配置的对比实验
chmod +x run_feature_separation_experiments.sh
./run_feature_separation_experiments.sh
```

### 3. 测试模块功能

```bash
# 测试特征分离模块是否正常工作
python test_feature_separation.py
```

## 📊 脚本对比

### run_optimized.sh vs run_feature_separation.sh vs run_optimized_with_feature_separation.sh

| 特性 | run_optimized.sh | run_feature_separation.sh | run_optimized_with_feature_separation.sh |
|------|------------------|---------------------------|------------------------------------------|
| 优化配置 | ✅ 完整的优化参数 | ❌ 基础配置 | ✅ 继承优化配置 |
| 特征分离 | ❌ 不支持 | ✅ 专门支持 | ✅ 完整支持 |
| 生产级功能 | ✅ 后台运行、日志管理 | ❌ 简单脚本 | ✅ 完整功能 |
| 实验设计 | ❌ 单一配置 | ✅ 多种对比 | ✅ 灵活配置 |
| 推荐使用 | 基线实验 | 特征分离测试 | **生产使用** |

## ⚙️ 参数配置

### 特征分离参数

```bash
# 基础参数
--use_feature_separation true/false     # 是否启用特征分离
--separation_dim 128                    # 分离特征维度
--separation_weight 0.1                 # 分离损失权重

# 高级参数
--use_enhanced_separation true/false    # 是否使用增强版分离
--use_adaptive_separation true/false    # 是否使用自适应分离
--use_attention_in_separation true/false # 是否使用注意力机制
```

### 优化参数（继承自run_optimized.sh）

```bash
# 模型参数
--proto_counts "2,4,2"                  # 原型配置 (Normal,E-KC,KC)
--kc_weight 4.0                         # KC类别权重
--early_kc_weight 8.0                   # E-KC类别权重

# 训练参数
--epochs 30                             # 训练轮数
--lr 0.0002                            # 学习率
--inner_lr 0.3                         # MAML内循环学习率
--tasks_per_epoch 30                    # 每轮任务数

# 对比学习
--use_contrastive true                  # 启用对比学习
--contrastive_weight 0.5                # 对比学习权重
```

## 📈 实验配置

### 推荐的实验序列

1. **基线实验**（无特征分离）
   ```bash
   ./run_optimized_with_feature_separation.sh --use_feature_separation false
   ```

2. **基础特征分离**
   ```bash
   ./run_optimized_with_feature_separation.sh --use_feature_separation true
   ```

3. **增强版特征分离**
   ```bash
   ./run_optimized_with_feature_separation.sh --use_enhanced_separation true
   ```

4. **自适应特征分离**
   ```bash
   ./run_optimized_with_feature_separation.sh --use_adaptive_separation true
   ```

5. **权重调优**
   ```bash
   ./run_optimized_with_feature_separation.sh --separation_weight 0.05
   ./run_optimized_with_feature_separation.sh --separation_weight 0.2
   ```

## 📊 结果分析

### 关键指标

1. **整体准确率**：模型的总体分类性能
2. **KC类别准确率**：圆锥角膜的识别能力
3. **E-KC类别准确率**：早期圆锥角膜的识别能力
4. **KC vs E-KC混淆**：两类之间的误分类情况
5. **特征分离损失**：分离模块的收敛情况

### 预期效果

- **基线 → 基础分离**：KC和E-KC准确率应有提升
- **基础 → 增强版**：多尺度特征应进一步改善性能
- **基础 → 自适应**：训练稳定性应有改善
- **权重调优**：找到最佳的分离损失权重

## 🔧 故障排除

### 常见问题

1. **导入错误**
   ```
   ImportError: cannot import name 'FeatureSeparationModule'
   ```
   **解决**：确保运行了 `python test_feature_separation.py` 验证模块

2. **CUDA内存不足**
   ```
   RuntimeError: CUDA out of memory
   ```
   **解决**：减少batch_size或使用单GPU

3. **特征分离损失为0**
   ```
   separation_loss: 0.0000
   ```
   **解决**：检查标签是否包含E-KC(1)和KC(2)类别

### 调试建议

1. **检查模块功能**：
   ```bash
   python test_feature_separation.py
   ```

2. **监控训练日志**：
   ```bash
   tail -f logs/optimized_basic_separation_*.log
   ```

3. **验证数据**：
   ```bash
   # 检查数据集中的类别分布
   python -c "
   import pandas as pd
   df = pd.read_csv('/home/<USER>/balanced_task_sampling/split_result/train_set.csv')
   print(df['label'].value_counts())
   "
   ```

## 🎯 最佳实践

1. **从基线开始**：先运行无特征分离的基线实验
2. **逐步增加复杂度**：基础 → 增强版 → 自适应
3. **权重调优**：在0.05-0.2范围内调整separation_weight
4. **监控过拟合**：注意验证集性能变化
5. **多次运行**：使用不同随机种子验证结果稳定性

## 📝 引用

如果您在研究中使用了特征分离模块，请引用：

```
特征分离模块用于圆锥角膜分类
- 共享特征提取器和类别特定特征提取器
- 注意力机制增强特征区分
- 自适应训练强度调整
```

---

**作者**：Augment Agent  
**更新时间**：2024年12月  
**版本**：1.0

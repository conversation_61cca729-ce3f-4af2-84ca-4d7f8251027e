{"test_acc": 91.38888888888889, "class_accuracies": {"kc": 77.5, "e-kc": 99.16666666666667, "normal": 97.5}, "confusion_matrix": [[117, 3, 0], [1, 119, 0], [7, 20, 93]], "best_epoch": 2, "best_val_acc": 0.8222222222222223, "history": {"train_loss": [1.8869367996851603, 1.425059425830841, 1.0843268473943075, 1.0487453500429789, 1.083295645316442, 0.9767829457918803, 0.8309653103351593, 0.7220126827557881], "train_acc": [0.5251851851851852, 0.6088888888888888, 0.8725925925925928, 0.9044444444444447, 0.8874074074074075, 0.9251851851851853, 0.942962962962963, 0.971851851851852], "val_loss": [1.901685655117035, 1.9407511949539185, 1.3895515859127046, 1.6425093054771422, 1.8828041672706604, 1.8254920482635497, 1.8708117723464965, 1.816410744190216], "val_acc": [0.5333333333333333, 0.5555555555555556, 0.8222222222222223, 0.6111111111111112, 0.5444444444444444, 0.5444444444444445, 0.45555555555555555, 0.5444444444444445], "contrastive_loss": [0.6229365020990372, 0.34097900390625, 0.29650186871488887, 0.3215876261393229, 0.2881339196115732, 0.2280523879143099, 0.14362895612139254, 0.07227230875663129], "learning_rates": [0.0002, 0.0002, 0.0002, 0.0002, 0.0002, 0.0002, 0.0001, 0.0001], "batch_learning_rates": []}, "args": {"train_csv": "/home/<USER>/balanced_task_sampling/split_result/train_set.csv", "val_csv": "/home/<USER>/balanced_task_sampling/split_result/val_set.csv", "test_csv": "/home/<USER>/balanced_task_sampling/split_result/test_set.csv", "save_dir": "results_optimized_20250525_142053_gpu0,1", "device": "cuda", "proto_counts": "2,4,2", "feature_dim": 512, "inner_lr": 0.3, "inner_steps": 2, "pretrained": true, "dropout": 0.5, "use_normalized_features": false, "normalize_scale": 10.0, "epochs": 30, "lr": 0.0002, "weight_decay": 0.0005, "n_way": 3, "n_shot": 5, "n_query": 15, "tasks_per_epoch": 30, "meta_batch_size": 8, "val_frequency": 1, "early_stopping": 5, "use_lr_scheduler": true, "scheduler_type": "plateau", "max_lr": 0.001, "div_factor": 25.0, "final_div_factor": 10000.0, "pct_start": 0.1, "plateau_factor": 0.5, "plateau_patience": 2, "min_lr": 1e-06, "record_batch_lr": false, "no_augmentation": true, "early_kc_augment": false, "kc_augment": false, "advanced_augment": false, "mixup_alpha": 0.0, "cutmix_prob": 0.0, "early_kc_specific_augment": false, "augment_factor": 1, "kc_augment_factor": 1, "use_enhanced_gpu_augment": false, "early_kc_weight": 8.0, "kc_weight": 4.0, "focal_gamma": 2.0, "use_contrastive": true, "contrastive_weight": 0.5, "temperature": 0.07, "hard_mining_ratio": 0.7, "early_normal_weight": 2.0, "kc_normal_weight": 1.0, "use_balanced_task_sampler": true, "kc_shot_multiplier": 3.0, "early_kc_shot_multiplier": 1.5, "seed": 42, "num_workers": 4, "use_separate_test_sampler": true}}
{"test_acc": 50.83333333333333, "class_accuracies": {"kc": 67.5, "e-kc": 85.0, "normal": 0.0}, "confusion_matrix": [[0, 120, 0], [0, 102, 18], [0, 39, 81]], "best_epoch": 1, "best_val_acc": 0.5444444444444445, "history": {"train_loss": [0.5733911693096161, 0.011830063660939534, 0.01023788849512736, 0.027227477232615153, 0.10274454156557719, 0.07991153001785278, 0.04376692175865173], "train_acc": [0.5162962962962963, 0.5466666666666665, 0.5503703703703703, 0.5599999999999999, 0.5318518518518518, 0.5296296296296296, 0.5459259259259259], "val_loss": [2.206619989871979, 1.5941972017288208, 1.7550769805908204, 1.8309471130371093, 1.9621444463729858, 2.2215079784393312, 2.0731781721115112], "val_acc": [0.4222222222222222, 0.5444444444444445, 0.4666666666666666, 0.48888888888888893, 0.4222222222222222, 0.3333333333333333, 0.39999999999999997], "contrastive_loss": [-1.8767662833134333, -2.756923770904541, -2.7614529927571616, -2.6731417655944822, -2.6579400261243182, -2.7064196427663165, -2.679389528433482], "learning_rates": [0.0002, 0.0002, 0.0002, 0.0002, 0.0002, 0.0001, 0.0001], "batch_learning_rates": []}, "args": {"train_csv": "/home/<USER>/balanced_task_sampling/split_result/train_set.csv", "val_csv": "/home/<USER>/balanced_task_sampling/split_result/val_set.csv", "test_csv": "/home/<USER>/balanced_task_sampling/split_result/test_set.csv", "save_dir": "results_optimized_20250525_152539_gpu0,1", "device": "cuda", "proto_counts": "2,4,2", "feature_dim": 512, "inner_lr": 0.3, "inner_steps": 2, "pretrained": true, "dropout": 0.5, "use_normalized_features": false, "normalize_scale": 10.0, "epochs": 30, "lr": 0.0002, "weight_decay": 0.0005, "n_way": 3, "n_shot": 5, "n_query": 15, "tasks_per_epoch": 30, "meta_batch_size": 8, "val_frequency": 1, "early_stopping": 5, "use_lr_scheduler": true, "scheduler_type": "plateau", "max_lr": 0.001, "div_factor": 25.0, "final_div_factor": 10000.0, "pct_start": 0.1, "plateau_factor": 0.5, "plateau_patience": 2, "min_lr": 1e-06, "record_batch_lr": false, "no_augmentation": true, "early_kc_augment": false, "kc_augment": false, "advanced_augment": false, "mixup_alpha": 0.0, "cutmix_prob": 0.0, "early_kc_specific_augment": false, "augment_factor": 1, "kc_augment_factor": 1, "use_enhanced_gpu_augment": false, "early_kc_weight": 8.0, "kc_weight": 4.0, "focal_gamma": 2.0, "use_contrastive": true, "contrastive_weight": 0.5, "temperature": 0.07, "hard_mining_ratio": 0.7, "early_normal_weight": 3.0, "kc_normal_weight": 1.0, "use_balanced_task_sampler": true, "kc_shot_multiplier": 3.0, "early_kc_shot_multiplier": 1.5, "seed": 42, "num_workers": 4, "use_separate_test_sampler": true}}
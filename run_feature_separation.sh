#!/bin/bash

# 带特征分离模块的训练脚本
# 专门用于提高E-KC和KC之间的区分能力

echo "开始带特征分离模块的训练..."

# 设置CUDA设备
export CUDA_VISIBLE_DEVICES=0

# 基础参数
DATA_DIR="/home/<USER>/data/keratoconus_data"
SAVE_DIR="./checkpoints_feature_separation"
DEVICE="cuda:0"

# 模型参数
FEATURE_DIM=512
SEPARATION_DIM=128
DROPOUT=0.3

# 特征分离参数
USE_FEATURE_SEPARATION="--use_feature_separation"
USE_ENHANCED_SEPARATION=""  # 可选: "--use_enhanced_separation"
USE_ADAPTIVE_SEPARATION=""  # 可选: "--use_adaptive_separation"
SEPARATION_WEIGHT=0.1
USE_ATTENTION_IN_SEPARATION="--use_attention_in_separation"

# 训练参数
EPOCHS=100
LR=0.001
WEIGHT_DECAY=1e-4
TASKS_PER_EPOCH=100

# Few-shot参数
N_WAY=3
N_SHOT=5
N_QUERY=15
PROTO_COUNTS="1,5,2"  # Normal,E-KC,KC

# MAML参数
INNER_LR=0.01
INNER_STEPS=5

# 损失函数参数
USE_FOCAL_LOSS="--use_focal_loss"
FOCAL_ALPHA="1.0,2.0,3.0"  # Normal,E-KC,KC
FOCAL_GAMMA=2.0
USE_CONTRASTIVE=""  # 可选: "--use_contrastive"
CONTRASTIVE_WEIGHT=0.5

# 创建保存目录
mkdir -p $SAVE_DIR

# 运行训练（基础特征分离）
echo "运行基础特征分离训练..."
python train_with_feature_separation.py \
    --data_dir $DATA_DIR \
    --save_dir "${SAVE_DIR}/basic_separation" \
    --device $DEVICE \
    --feature_dim $FEATURE_DIM \
    --separation_dim $SEPARATION_DIM \
    --dropout $DROPOUT \
    $USE_FEATURE_SEPARATION \
    --separation_weight $SEPARATION_WEIGHT \
    $USE_ATTENTION_IN_SEPARATION \
    --epochs $EPOCHS \
    --lr $LR \
    --weight_decay $WEIGHT_DECAY \
    --tasks_per_epoch $TASKS_PER_EPOCH \
    --n_way $N_WAY \
    --n_shot $N_SHOT \
    --n_query $N_QUERY \
    --proto_counts $PROTO_COUNTS \
    --inner_lr $INNER_LR \
    --inner_steps $INNER_STEPS \
    $USE_FOCAL_LOSS \
    --focal_alpha $FOCAL_ALPHA \
    --focal_gamma $FOCAL_GAMMA \
    --pretrained

echo "基础特征分离训练完成！"

# 运行训练（增强版特征分离）
echo "运行增强版特征分离训练..."
python train_with_feature_separation.py \
    --data_dir $DATA_DIR \
    --save_dir "${SAVE_DIR}/enhanced_separation" \
    --device $DEVICE \
    --feature_dim $FEATURE_DIM \
    --separation_dim $SEPARATION_DIM \
    --dropout $DROPOUT \
    $USE_FEATURE_SEPARATION \
    --use_enhanced_separation \
    --separation_weight $SEPARATION_WEIGHT \
    $USE_ATTENTION_IN_SEPARATION \
    --epochs $EPOCHS \
    --lr $LR \
    --weight_decay $WEIGHT_DECAY \
    --tasks_per_epoch $TASKS_PER_EPOCH \
    --n_way $N_WAY \
    --n_shot $N_SHOT \
    --n_query $N_QUERY \
    --proto_counts $PROTO_COUNTS \
    --inner_lr $INNER_LR \
    --inner_steps $INNER_STEPS \
    $USE_FOCAL_LOSS \
    --focal_alpha $FOCAL_ALPHA \
    --focal_gamma $FOCAL_GAMMA \
    --pretrained

echo "增强版特征分离训练完成！"

# 运行训练（自适应特征分离）
echo "运行自适应特征分离训练..."
python train_with_feature_separation.py \
    --data_dir $DATA_DIR \
    --save_dir "${SAVE_DIR}/adaptive_separation" \
    --device $DEVICE \
    --feature_dim $FEATURE_DIM \
    --separation_dim $SEPARATION_DIM \
    --dropout $DROPOUT \
    $USE_FEATURE_SEPARATION \
    --use_adaptive_separation \
    --separation_weight $SEPARATION_WEIGHT \
    $USE_ATTENTION_IN_SEPARATION \
    --epochs $EPOCHS \
    --lr $LR \
    --weight_decay $WEIGHT_DECAY \
    --tasks_per_epoch $TASKS_PER_EPOCH \
    --n_way $N_WAY \
    --n_shot $N_SHOT \
    --n_query $N_QUERY \
    --proto_counts $PROTO_COUNTS \
    --inner_lr $INNER_LR \
    --inner_steps $INNER_STEPS \
    $USE_FOCAL_LOSS \
    --focal_alpha $FOCAL_ALPHA \
    --focal_gamma $FOCAL_GAMMA \
    --pretrained

echo "自适应特征分离训练完成！"

# 运行对比实验（不使用特征分离）
echo "运行对比实验（不使用特征分离）..."
python train_with_feature_separation.py \
    --data_dir $DATA_DIR \
    --save_dir "${SAVE_DIR}/no_separation" \
    --device $DEVICE \
    --feature_dim $FEATURE_DIM \
    --dropout $DROPOUT \
    --epochs $EPOCHS \
    --lr $LR \
    --weight_decay $WEIGHT_DECAY \
    --tasks_per_epoch $TASKS_PER_EPOCH \
    --n_way $N_WAY \
    --n_shot $N_SHOT \
    --n_query $N_QUERY \
    --proto_counts $PROTO_COUNTS \
    --inner_lr $INNER_LR \
    --inner_steps $INNER_STEPS \
    $USE_FOCAL_LOSS \
    --focal_alpha $FOCAL_ALPHA \
    --focal_gamma $FOCAL_GAMMA \
    --pretrained

echo "对比实验完成！"

echo "所有实验完成！结果保存在 $SAVE_DIR 目录中。"

# 显示结果目录结构
echo "结果目录结构:"
ls -la $SAVE_DIR/

echo "特征分离模块训练完成！"
echo ""
echo "实验配置:"
echo "- 基础特征分离: ${SAVE_DIR}/basic_separation"
echo "- 增强版特征分离: ${SAVE_DIR}/enhanced_separation"
echo "- 自适应特征分离: ${SAVE_DIR}/adaptive_separation"
echo "- 对比实验(无分离): ${SAVE_DIR}/no_separation"
echo ""
echo "主要特性:"
echo "1. 专门的E-KC和KC特征分离"
echo "2. 共享特征提取器和类别特定提取器"
echo "3. 注意力机制增强特征区分"
echo "4. 自适应训练强度调整"
echo "5. 多尺度特征分离（增强版）"

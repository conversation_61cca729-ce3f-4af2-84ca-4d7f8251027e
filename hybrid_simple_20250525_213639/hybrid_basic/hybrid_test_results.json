{"test_acc": 77.22222222222223, "class_accuracies": {"normal": 86.66666666666667, "e-kc": 57.49999999999999, "kc": 87.5}, "confusion_matrix": [[104, 16, 0], [32, 69, 19], [0, 15, 105]], "best_epoch": 11, "best_val_acc": 0.7222222222222222, "history": {"train_loss": [1.593191784620285, 1.1123816460371017, 0.9476862102746964, 0.8579029679298401, 0.8630454272031785, 0.7920110285282135, 0.8089940279722214, 0.7892831116914749, 0.8008724331855774, 0.7282621055841446, 0.6991749972105026, 0.6942594856023788, 0.6461122959852219, 0.7116223514080048, 0.6761496603488922], "train_acc": [0.5566666666666665, 0.8444444444444447, 0.8622222222222226, 0.9100000000000001, 0.9077777777777779, 0.9277777777777778, 0.9233333333333335, 0.9022222222222224, 0.9211111111111112, 0.9400000000000001, 0.9533333333333334, 0.9533333333333334, 0.9666666666666666, 0.9533333333333334, 0.9477777777777778], "val_loss": [1.91643545627594, 2.0220858097076415, 1.9973444938659668, 1.95942063331604, 1.9170178651809693, 1.4762397527694702, 1.8199840188026428], "val_acc": [0.4888888888888888, 0.5777777777777777, 0.6, 0.48888888888888893, 0.5777777777777777, 0.7222222222222222, 0.5999999999999999], "loss_components": {"main_loss": [1.593191784620285, 1.1123816460371017, 0.9476862102746964, 0.8579029679298401, 0.8630454272031785, 0.7920110285282135, 0.8089940279722214, 0.7892831116914749, 0.8008724331855774, 0.7282621055841446, 0.6991749972105026, 0.6942594856023788, 0.6461122959852219, 0.7116223514080048, 0.6761496603488922], "prototype_loss": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "classifier_loss": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "consistency_loss": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "total_loss": [1.593191784620285, 1.1123816460371017, 0.9476862102746964, 0.8579029679298401, 0.8630454272031785, 0.7920110285282135, 0.8089940279722214, 0.7892831116914749, 0.8008724331855774, 0.7282621055841446, 0.6991749972105026, 0.6942594856023788, 0.6461122959852219, 0.7116223514080048, 0.6761496603488922]}}, "args": {"train_csv": "/home/<USER>/balanced_task_sampling/split_result/train_set.csv", "val_csv": "/home/<USER>/balanced_task_sampling/split_result/val_set.csv", "test_csv": "/home/<USER>/balanced_task_sampling/split_result/test_set.csv", "save_dir": "hybrid_simple_20250525_213639/hybrid_basic", "device": "cuda:0", "proto_counts": "2,4,2", "feature_dim": 512, "inner_lr": 0.3, "inner_steps": 2, "pretrained": true, "dropout": 0.5, "hybrid_type": "basic", "hybrid_hidden_dim": 256, "prototype_weight": 0.7, "classifier_weight": 0.3, "consistency_weight": 0.1, "epochs": 15, "lr": 0.0002, "weight_decay": 0.0005, "n_way": 3, "n_shot": 5, "n_query": 15, "tasks_per_epoch": 20, "early_kc_weight": 8.0, "kc_weight": 4.0, "focal_gamma": 2.0, "use_balanced_task_sampler": true, "kc_shot_multiplier": 3.0, "early_kc_shot_multiplier": 1.5, "seed": 42, "val_frequency": 2, "early_stopping": 3}}
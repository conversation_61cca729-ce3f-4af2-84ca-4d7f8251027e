{"test_acc": 75.0, "class_accuracies": {"normal": 84.16666666666667, "e-kc": 89.16666666666667, "kc": 51.66666666666667}, "confusion_matrix": [[101, 19, 0], [13, 107, 0], [1, 57, 62]], "best_epoch": 7, "best_val_acc": 0.7777777777777777, "history": {"train_loss": [1.446932053565979, 0.9955746740102768, 0.9788392543792724, 1.088197335600853, 0.7717924803495407, 0.8045669198036194, 0.781921136379242, 0.7750630497932434, 0.7682388484477997, 0.7938520610332489, 0.7009789168834686, 0.6723903745412827, 0.671141830086708, 0.7195269882678985], "train_acc": [0.5766666666666664, 0.8277777777777781, 0.8455555555555557, 0.8677777777777779, 0.93, 0.9388888888888889, 0.8966666666666668, 0.9311111111111112, 0.9277777777777778, 0.9255555555555557, 0.9522222222222222, 0.9588888888888889, 0.9533333333333334, 0.9444444444444446], "val_loss": [2.0471693515777587, 1.8707487106323242, 1.6771217346191407, 1.3564907371997834, 1.5654548525810241, 1.8396936774253845, 1.5711347341537476], "val_acc": [0.4666666666666666, 0.6222222222222222, 0.6222222222222222, 0.7777777777777777, 0.7222222222222221, 0.611111111111111, 0.611111111111111], "loss_components": {"main_loss": [1.446932053565979, 0.9955746740102768, 0.9788392543792724, 1.088197335600853, 0.7717924803495407, 0.8045669198036194, 0.781921136379242, 0.7750630497932434, 0.7682388484477997, 0.7938520610332489, 0.7009789168834686, 0.6723903745412827, 0.671141830086708, 0.7195269882678985], "prototype_loss": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "classifier_loss": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "consistency_loss": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "total_loss": [1.446932053565979, 0.9955746740102768, 0.9788392543792724, 1.088197335600853, 0.7717924803495407, 0.8045669198036194, 0.781921136379242, 0.7750630497932434, 0.7682388484477997, 0.7938520610332489, 0.7009789168834686, 0.6723903745412827, 0.671141830086708, 0.7195269882678985]}}, "args": {"train_csv": "/home/<USER>/balanced_task_sampling/split_result/train_set.csv", "val_csv": "/home/<USER>/balanced_task_sampling/split_result/val_set.csv", "test_csv": "/home/<USER>/balanced_task_sampling/split_result/test_set.csv", "save_dir": "hybrid_simple_20250525_213639/hybrid_adaptive", "device": "cuda:0", "proto_counts": "2,4,2", "feature_dim": 512, "inner_lr": 0.3, "inner_steps": 2, "pretrained": true, "dropout": 0.5, "hybrid_type": "adaptive", "hybrid_hidden_dim": 256, "prototype_weight": 0.6, "classifier_weight": 0.4, "consistency_weight": 0.15, "epochs": 15, "lr": 0.0002, "weight_decay": 0.0005, "n_way": 3, "n_shot": 5, "n_query": 15, "tasks_per_epoch": 20, "early_kc_weight": 8.0, "kc_weight": 4.0, "focal_gamma": 2.0, "use_balanced_task_sampler": true, "kc_shot_multiplier": 3.0, "early_kc_shot_multiplier": 1.5, "seed": 42, "val_frequency": 2, "early_stopping": 3}}
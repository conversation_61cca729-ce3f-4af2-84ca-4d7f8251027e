#!/usr/bin/env python3
"""
简单校准演示

使用模拟数据演示校准功能的效果。
"""

import torch
import torch.nn.functional as F
import numpy as np
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def generate_realistic_predictions():
    """生成符合实际情况的模拟预测数据"""
    np.random.seed(42)
    torch.manual_seed(42)
    
    # 模拟100个样本的预测结果
    n_samples = 100
    
    # 真实标签分布：Normal(40%), E-KC(35%), KC(25%)
    true_labels = []
    true_labels.extend([0] * 40)  # Normal
    true_labels.extend([1] * 35)  # E-KC  
    true_labels.extend([2] * 25)  # KC
    true_labels = torch.tensor(true_labels)
    
    # 生成预测概率，模拟实际模型的行为
    probs = []
    
    for label in true_labels:
        if label == 0:  # Normal样本
            # Normal样本通常预测正确，但偶尔被误分类为E-KC
            if np.random.random() < 0.9:  # 90%正确率
                prob = np.array([0.7 + np.random.random() * 0.25, 
                               0.1 + np.random.random() * 0.15, 
                               0.05 + np.random.random() * 0.1])
            else:  # 误分类为E-KC
                prob = np.array([0.3 + np.random.random() * 0.2, 
                               0.4 + np.random.random() * 0.3, 
                               0.1 + np.random.random() * 0.15])
        
        elif label == 1:  # E-KC样本
            # E-KC样本容易被误分类为Normal或KC
            rand = np.random.random()
            if rand < 0.7:  # 70%正确率
                prob = np.array([0.1 + np.random.random() * 0.2, 
                               0.5 + np.random.random() * 0.3, 
                               0.1 + np.random.random() * 0.2])
            elif rand < 0.85:  # 15%误分类为Normal
                prob = np.array([0.4 + np.random.random() * 0.3, 
                               0.3 + np.random.random() * 0.2, 
                               0.1 + np.random.random() * 0.15])
            else:  # 15%误分类为KC
                prob = np.array([0.1 + np.random.random() * 0.15, 
                               0.25 + np.random.random() * 0.2, 
                               0.4 + np.random.random() * 0.3])
        
        else:  # KC样本 (label == 2)
            # KC样本经常被误分类为E-KC，这是我们要解决的主要问题
            if np.random.random() < 0.6:  # 只有60%正确率
                prob = np.array([0.05 + np.random.random() * 0.1, 
                               0.1 + np.random.random() * 0.2, 
                               0.6 + np.random.random() * 0.25])
            else:  # 40%误分类为E-KC
                prob = np.array([0.1 + np.random.random() * 0.15, 
                               0.5 + np.random.random() * 0.3, 
                               0.2 + np.random.random() * 0.25])
        
        # 归一化概率
        prob = prob / prob.sum()
        probs.append(prob)
    
    probs = torch.tensor(probs, dtype=torch.float32)
    
    return probs, true_labels


def evaluate_performance(probs, labels, name=""):
    """评估性能"""
    preds = torch.argmax(probs, dim=1)
    
    # 总体准确率
    overall_acc = (preds == labels).float().mean().item() * 100
    
    # 类别准确率
    class_names = ['Normal', 'E-KC', 'KC']
    class_accs = {}
    
    for i, class_name in enumerate(class_names):
        class_mask = (labels == i)
        if class_mask.any():
            class_acc = (preds[class_mask] == labels[class_mask]).float().mean().item() * 100
            class_accs[class_name] = class_acc
        else:
            class_accs[class_name] = 0.0
    
    print(f"{name}性能:")
    print(f"  总体准确率: {overall_acc:.1f}%")
    for class_name, acc in class_accs.items():
        print(f"  {class_name}: {acc:.1f}%")
    
    return overall_acc, class_accs


def demo_kc_ekc_boundary_calibration():
    """演示KC-EKC边界校准"""
    print("🔬 KC-EKC边界校准演示")
    print("=" * 50)
    
    # 生成模拟数据
    probs, labels = generate_realistic_predictions()
    
    # 分割数据：前50个用于校准，后50个用于测试
    cal_probs, cal_labels = probs[:50], labels[:50]
    test_probs, test_labels = probs[50:], labels[50:]
    
    print("原始模型性能:")
    original_acc, original_class_accs = evaluate_performance(test_probs, test_labels, "原始")
    
    # 导入并应用KC-EKC边界校准
    try:
        from models.calibration import KCEKCBoundaryCalibration
        
        # 创建校准器
        calibrator = KCEKCBoundaryCalibration()
        
        # 在校准集上拟合
        print(f"\n在校准集上拟合校准器...")
        calibrator.fit(cal_probs, cal_labels, target_kc_recall=0.8)
        
        # 应用校准
        calibrated_probs = calibrator.calibrate(test_probs)
        
        print(f"\n校准后模型性能:")
        calibrated_acc, calibrated_class_accs = evaluate_performance(calibrated_probs, test_labels, "校准后")
        
        # 计算改进
        print(f"\n📊 改进效果:")
        print(f"  总体准确率: {original_acc:.1f}% → {calibrated_acc:.1f}% ({calibrated_acc - original_acc:+.1f}%)")
        
        for class_name in ['Normal', 'E-KC', 'KC']:
            original = original_class_accs[class_name]
            calibrated = calibrated_class_accs[class_name]
            improvement = calibrated - original
            print(f"  {class_name}: {original:.1f}% → {calibrated:.1f}% ({improvement:+.1f}%)")
        
        # 特别关注KC类别
        kc_improvement = calibrated_class_accs['KC'] - original_class_accs['KC']
        if kc_improvement > 5:
            print(f"\n✅ KC类别准确率显著提升 {kc_improvement:.1f}%！")
        elif kc_improvement > 0:
            print(f"\n🟡 KC类别准确率有所提升 {kc_improvement:.1f}%")
        else:
            print(f"\n❌ KC类别准确率未提升")
        
        # 显示校准参数
        print(f"\n🔧 校准参数:")
        print(f"  KC增强因子: {calibrator.kc_boost_factor:.2f}")
        print(f"  E-KC抑制因子: {calibrator.ekc_suppress_factor:.2f}")
        
        return True
        
    except Exception as e:
        print(f"❌ 校准演示失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def demo_temperature_scaling():
    """演示温度缩放校准"""
    print("\n🌡️ 温度缩放校准演示")
    print("=" * 50)
    
    # 生成模拟数据
    probs, labels = generate_realistic_predictions()
    
    # 分割数据
    cal_probs, cal_labels = probs[:50], labels[:50]
    test_probs, test_labels = probs[50:], labels[50:]
    
    try:
        from models.calibration import TemperatureScaling
        
        # 创建校准器
        calibrator = TemperatureScaling()
        
        # 转换为logits
        cal_logits = torch.log(cal_probs + 1e-8)
        test_logits = torch.log(test_probs + 1e-8)
        
        # 拟合温度参数
        temperature = calibrator.fit(cal_logits, cal_labels)
        print(f"学习到的温度参数: {temperature:.3f}")
        
        # 应用校准
        calibrated_probs = calibrator(test_logits)
        
        # 评估性能
        original_acc, original_class_accs = evaluate_performance(test_probs, test_labels, "原始")
        calibrated_acc, calibrated_class_accs = evaluate_performance(calibrated_probs, test_labels, "温度校准后")
        
        # 计算改进
        print(f"\n📊 温度缩放改进效果:")
        overall_improvement = calibrated_acc - original_acc
        print(f"  总体准确率改进: {overall_improvement:+.1f}%")
        
        if overall_improvement > 0:
            print(f"✅ 温度缩放提升了整体性能")
        else:
            print(f"🟡 温度缩放对整体性能影响较小")
        
        return True
        
    except Exception as e:
        print(f"❌ 温度缩放演示失败: {e}")
        return False


def main():
    """主演示函数"""
    print("🎯 校准方法演示")
    print("=" * 60)
    print("该演示使用模拟数据展示校准方法如何改善KC分类性能")
    print("=" * 60)
    
    # 演示KC-EKC边界校准
    success1 = demo_kc_ekc_boundary_calibration()
    
    # 演示温度缩放
    success2 = demo_temperature_scaling()
    
    print("\n" + "=" * 60)
    print("📋 演示总结")
    print("=" * 60)
    
    if success1:
        print("✅ KC-EKC边界校准：专门优化KC vs E-KC决策边界")
        print("   - 通过调整KC增强因子和E-KC抑制因子")
        print("   - 特别适合解决KC被误分类为E-KC的问题")
    
    if success2:
        print("✅ 温度缩放校准：整体概率校准")
        print("   - 通过学习温度参数调整预测置信度")
        print("   - 适合改善整体校准质量")
    
    print("\n💡 实际使用建议:")
    print("1. 对于KC分类问题，优先尝试KC-EKC边界校准")
    print("2. 可以结合多种校准方法获得更好效果")
    print("3. 在真实模型上运行: python evaluate_with_calibration.py")
    
    if success1 or success2:
        print("\n🎉 校准模块工作正常，可以在真实模型上使用！")
        return True
    else:
        print("\n❌ 校准模块存在问题，需要检查")
        return False


if __name__ == '__main__':
    main()

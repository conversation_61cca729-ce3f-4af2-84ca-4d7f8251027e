#!/bin/bash

#############################################################
# 优化模型 + 特征分离模块训练脚本
# 结合了run_optimized.sh的优化配置和特征分离功能
# 专门用于提高E-KC和KC之间的区分能力
#############################################################

# ==================== 默认参数设置 ====================

# ----- 运行模式参数 -----
RUN_MODE="nohup"          # 运行模式: nohup(后台运行), direct(直接运行)
SETUP_FIRST=false         # 是否先运行setup.sh脚本准备环境

# ----- GPU和硬件参数 -----
GPU_IDS="0"               # 使用单GPU，根据用户偏好
NUM_WORKERS=4             # 数据加载的工作线程数

# ----- 训练基本参数（来自优化配置）-----
EPOCHS=30                 # 训练轮数
LR=0.0002                 # 基础学习率
WEIGHT_DECAY=5e-4         # 权重衰减系数
FEATURE_DIM=512           # 特征维度
DROPOUT=0.5               # Dropout比率
SEED=42                   # 随机种子
OUTPUT_DIR="results_optimized_feature_separation"  # 输出目录

# ----- MAML算法参数（来自优化配置）-----
INNER_STEPS=2             # 内循环步数
INNER_LR=0.3              # 内循环学习率
TASKS_PER_EPOCH=30        # 每个epoch的任务数
META_BATCH_SIZE=8         # 元批次大小
N_WAY=3                   # 任务中的类别数
N_SHOT=5                  # 支持集中每个类别的样本数
N_QUERY=15                # 查询集中每个类别的样本数

# ----- 模型架构参数（来自优化配置）-----
PROTO_COUNTS="2,4,2"      # 每个类别的原型数量
PRETRAINED=true           # 是否使用预训练的特征提取器

# ----- 类别权重和损失函数参数（来自优化配置）-----
EARLY_KC_WEIGHT=8.0       # 早期圆锥角膜样本权重
KC_WEIGHT=4.0             # 圆锥角膜类别权重
FOCAL_GAMMA=2.0           # Focal Loss的gamma参数

# ----- 特征分离模块参数（新增）-----
USE_FEATURE_SEPARATION=true      # 是否使用特征分离模块
USE_ENHANCED_SEPARATION=false    # 是否使用增强版特征分离
USE_ADAPTIVE_SEPARATION=false    # 是否使用自适应特征分离
SEPARATION_DIM=128               # 分离特征维度
SEPARATION_WEIGHT=0.1            # 特征分离损失权重
USE_ATTENTION_IN_SEPARATION=true # 在特征分离模块中使用注意力机制

# ----- 对比学习参数（来自优化配置）-----
USE_CONTRASTIVE=true      # 是否使用对比学习
CONTRASTIVE_WEIGHT=0.5    # 对比学习损失权重
TEMPERATURE=0.07          # 对比学习的温度参数
HARD_MINING_RATIO=0.7     # 硬负样本挖掘比例
EARLY_NORMAL_WEIGHT=2.0   # 早期圆锥角膜与正常样本对比权重
KC_NORMAL_WEIGHT=1.0

# ----- 任务采样参数（来自优化配置）-----
USE_SEPARATE_TEST_SAMPLER=true   # 是否使用单独的测试集任务采样器
USE_BALANCED_TASK_SAMPLER=true   # 是否使用平衡任务采样器
KC_SHOT_MULTIPLIER=3.0           # 圆锥角膜样本在支持集中的倍数
EARLY_KC_SHOT_MULTIPLIER=1.5     # 早期圆锥角膜样本在支持集中的倍数

# ----- 验证和早停参数（来自优化配置）-----
VAL_FREQUENCY=1           # 验证频率
EARLY_STOPPING=5          # 早停耐心值

# ----- 学习率调度器参数（来自优化配置）-----
USE_LR_SCHEDULER=true     # 是否使用学习率调度器
SCHEDULER_TYPE="plateau"  # 学习率调度器类型
PLATEAU_FACTOR=0.5        # ReduceLROnPlateau的学习率衰减因子
PLATEAU_PATIENCE=2        # ReduceLROnPlateau的耐心值
MIN_LR=1e-6               # ReduceLROnPlateau的最小学习率

# ----- 数据路径参数 -----
TRAIN_CSV="/home/<USER>/balanced_task_sampling/split_result/train_set.csv"
VAL_CSV="/home/<USER>/balanced_task_sampling/split_result/val_set.csv"
TEST_CSV="/home/<USER>/balanced_task_sampling/split_result/test_set.csv"

# ==================== 命令行参数解析 ====================
while [[ $# -gt 0 ]]; do
  case $1 in
    --run_mode)
      RUN_MODE="$2"
      shift 2
      ;;
    --gpu_ids)
      GPU_IDS="$2"
      shift 2
      ;;
    --use_feature_separation)
      USE_FEATURE_SEPARATION="$2"
      shift 2
      ;;
    --use_enhanced_separation)
      USE_ENHANCED_SEPARATION="$2"
      shift 2
      ;;
    --use_adaptive_separation)
      USE_ADAPTIVE_SEPARATION="$2"
      shift 2
      ;;
    --separation_weight)
      SEPARATION_WEIGHT="$2"
      shift 2
      ;;
    --epochs)
      EPOCHS="$2"
      shift 2
      ;;
    *)
      echo "未知参数: $1"
      echo "支持的参数: --run_mode, --gpu_ids, --use_feature_separation, --use_enhanced_separation, --use_adaptive_separation, --separation_weight, --epochs"
      exit 1
      ;;
  esac
done

# ==================== 环境准备 ====================
# 如果需要先运行setup.sh
if [ "$SETUP_FIRST" = "true" ]; then
  echo "步骤1: 运行setup.sh准备环境..."
  bash setup.sh
fi

# 创建日志目录
mkdir -p logs

# 获取当前时间戳
TIMESTAMP=$(date +%Y%m%d_%H%M%S)

# 根据特征分离配置确定实验名称
EXPERIMENT_NAME="optimized"
if [ "$USE_FEATURE_SEPARATION" = "true" ]; then
  if [ "$USE_ENHANCED_SEPARATION" = "true" ]; then
    EXPERIMENT_NAME="${EXPERIMENT_NAME}_enhanced_separation"
  elif [ "$USE_ADAPTIVE_SEPARATION" = "true" ]; then
    EXPERIMENT_NAME="${EXPERIMENT_NAME}_adaptive_separation"
  else
    EXPERIMENT_NAME="${EXPERIMENT_NAME}_basic_separation"
  fi
else
  EXPERIMENT_NAME="${EXPERIMENT_NAME}_no_separation"
fi

LOG_FILE="logs/${EXPERIMENT_NAME}_${TIMESTAMP}_gpu${GPU_IDS}.log"
FINAL_OUTPUT_DIR="${OUTPUT_DIR}_${EXPERIMENT_NAME}_${TIMESTAMP}_gpu${GPU_IDS}"

echo "实验名称: $EXPERIMENT_NAME"
echo "日志文件: $LOG_FILE"
echo "输出目录: $FINAL_OUTPUT_DIR"

# 设置环境变量
export CUDA_VISIBLE_DEVICES=$GPU_IDS
export OMP_NUM_THREADS=1
export MKL_NUM_THREADS=1
export MPLBACKEND=Agg

# 创建输出目录
mkdir -p $FINAL_OUTPUT_DIR

# ==================== 构建命令参数 ====================
# 构建预训练参数
PRETRAINED_ARGS=""
if [ "$PRETRAINED" = "true" ]; then
  PRETRAINED_ARGS="--pretrained"
fi

# 构建特征分离参数
FEATURE_SEPARATION_ARGS=""
if [ "$USE_FEATURE_SEPARATION" = "true" ]; then
  FEATURE_SEPARATION_ARGS="--use_feature_separation --separation_dim $SEPARATION_DIM --separation_weight $SEPARATION_WEIGHT"

  if [ "$USE_ENHANCED_SEPARATION" = "true" ]; then
    FEATURE_SEPARATION_ARGS="$FEATURE_SEPARATION_ARGS --use_enhanced_separation"
  fi

  if [ "$USE_ADAPTIVE_SEPARATION" = "true" ]; then
    FEATURE_SEPARATION_ARGS="$FEATURE_SEPARATION_ARGS --use_adaptive_separation"
  fi

  if [ "$USE_ATTENTION_IN_SEPARATION" = "true" ]; then
    FEATURE_SEPARATION_ARGS="$FEATURE_SEPARATION_ARGS --use_attention_in_separation"
  fi
fi

# 构建对比学习参数
CONTRASTIVE_ARGS=""
if [ "$USE_CONTRASTIVE" = "true" ]; then
  CONTRASTIVE_ARGS="--use_contrastive --contrastive_weight $CONTRASTIVE_WEIGHT"
fi

# 构建焦点损失参数
FOCAL_ARGS="--use_focal_loss --focal_alpha 1.0,2.0,3.0 --focal_gamma $FOCAL_GAMMA"

# 构建学习率调度器参数
LR_SCHEDULER_ARGS=""
if [ "$USE_LR_SCHEDULER" = "true" ]; then
  LR_SCHEDULER_ARGS="--use_lr_scheduler --scheduler_type $SCHEDULER_TYPE --plateau_factor $PLATEAU_FACTOR --plateau_patience $PLATEAU_PATIENCE --min_lr $MIN_LR"
fi

# ==================== 选择训练脚本 ====================
# 根据是否使用特征分离选择不同的训练脚本
if [ "$USE_FEATURE_SEPARATION" = "true" ]; then
  TRAIN_SCRIPT="train_with_feature_separation.py"
  echo "使用特征分离训练脚本: $TRAIN_SCRIPT"
else
  TRAIN_SCRIPT="train_balanced_task_sampling.py"
  echo "使用标准训练脚本: $TRAIN_SCRIPT"
fi

# ==================== 构建完整命令 ====================
BASE_CMD="python $TRAIN_SCRIPT \
  --data_dir /home/<USER>/data/keratoconus_data \
  --save_dir $FINAL_OUTPUT_DIR \
  --device cuda:0 \
  --epochs $EPOCHS \
  --lr $LR \
  --weight_decay $WEIGHT_DECAY \
  --feature_dim $FEATURE_DIM \
  --dropout $DROPOUT \
  --tasks_per_epoch $TASKS_PER_EPOCH \
  --n_way $N_WAY \
  --n_shot $N_SHOT \
  --n_query $N_QUERY \
  --proto_counts $PROTO_COUNTS \
  --inner_lr $INNER_LR \
  --inner_steps $INNER_STEPS \
  $PRETRAINED_ARGS \
  $FEATURE_SEPARATION_ARGS \
  $FOCAL_ARGS \
  $CONTRASTIVE_ARGS"

# ==================== 保存配置信息 ====================
echo "优化配置 + 特征分离:" > "${FINAL_OUTPUT_DIR}/experiment_config.txt"
echo "  实验名称: $EXPERIMENT_NAME" >> "${FINAL_OUTPUT_DIR}/experiment_config.txt"
echo "  训练脚本: $TRAIN_SCRIPT" >> "${FINAL_OUTPUT_DIR}/experiment_config.txt"
echo "  PROTO_COUNTS: $PROTO_COUNTS" >> "${FINAL_OUTPUT_DIR}/experiment_config.txt"
echo "  EARLY_KC_WEIGHT: $EARLY_KC_WEIGHT" >> "${FINAL_OUTPUT_DIR}/experiment_config.txt"
echo "  KC_WEIGHT: $KC_WEIGHT" >> "${FINAL_OUTPUT_DIR}/experiment_config.txt"
echo "  USE_FEATURE_SEPARATION: $USE_FEATURE_SEPARATION" >> "${FINAL_OUTPUT_DIR}/experiment_config.txt"
echo "  USE_ENHANCED_SEPARATION: $USE_ENHANCED_SEPARATION" >> "${FINAL_OUTPUT_DIR}/experiment_config.txt"
echo "  USE_ADAPTIVE_SEPARATION: $USE_ADAPTIVE_SEPARATION" >> "${FINAL_OUTPUT_DIR}/experiment_config.txt"
echo "  SEPARATION_WEIGHT: $SEPARATION_WEIGHT" >> "${FINAL_OUTPUT_DIR}/experiment_config.txt"
echo "  USE_CONTRASTIVE: $USE_CONTRASTIVE" >> "${FINAL_OUTPUT_DIR}/experiment_config.txt"
echo "  CONTRASTIVE_WEIGHT: $CONTRASTIVE_WEIGHT" >> "${FINAL_OUTPUT_DIR}/experiment_config.txt"

# ==================== 执行命令 ====================
echo "开始优化模型 + 特征分离训练..."
echo "使用GPU: $GPU_IDS"
echo "实验配置: $EXPERIMENT_NAME"

# 根据运行模式选择执行方式
if [ "$RUN_MODE" = "nohup" ]; then
  # 使用nohup在后台运行
  echo "使用nohup在后台运行..."
  echo "$BASE_CMD" > $LOG_FILE
  nohup bash -c "$BASE_CMD" >> $LOG_FILE 2>&1 &

  # 获取进程ID
  PID=$!
  echo "进程已启动，PID: $PID"
  echo "您可以使用以下命令监控训练进度:"
  echo "  tail -f $LOG_FILE"
  echo "要停止训练，请使用:"
  echo "  kill $PID"

  # 将PID保存到文件中，方便后续管理
  echo $PID > "logs/pid_${EXPERIMENT_NAME}_${TIMESTAMP}.txt"
else
  # 直接运行
  echo "直接运行训练脚本..."
  eval $BASE_CMD
fi

echo ""
echo "实验完成！"
echo "结果保存在: $FINAL_OUTPUT_DIR"
echo "配置信息: ${FINAL_OUTPUT_DIR}/experiment_config.txt"

# ==================== 实验说明 ====================
echo ""
echo "🔬 实验配置说明:"
echo "1. 基础优化配置 (来自run_optimized.sh):"
echo "   - 原型配置: $PROTO_COUNTS (Normal,E-KC,KC)"
echo "   - 类别权重: KC=$KC_WEIGHT, E-KC=$EARLY_KC_WEIGHT"
echo "   - 对比学习: $USE_CONTRASTIVE (权重: $CONTRASTIVE_WEIGHT)"
echo "   - 学习率调度: $SCHEDULER_TYPE"
echo ""
echo "2. 特征分离配置 (新增功能):"
echo "   - 特征分离: $USE_FEATURE_SEPARATION"
echo "   - 增强版分离: $USE_ENHANCED_SEPARATION"
echo "   - 自适应分离: $USE_ADAPTIVE_SEPARATION"
echo "   - 分离损失权重: $SEPARATION_WEIGHT"
echo "   - 注意力机制: $USE_ATTENTION_IN_SEPARATION"
echo ""
echo "💡 使用建议:"
echo "1. 运行不同配置的对比实验:"
echo "   ./run_optimized_with_feature_separation.sh --use_feature_separation true"
echo "   ./run_optimized_with_feature_separation.sh --use_enhanced_separation true"
echo "   ./run_optimized_with_feature_separation.sh --use_feature_separation false"
echo ""
echo "2. 调整特征分离权重:"
echo "   ./run_optimized_with_feature_separation.sh --separation_weight 0.05"
echo "   ./run_optimized_with_feature_separation.sh --separation_weight 0.2"
echo ""
echo "3. 监控训练进度:"
echo "   tail -f $LOG_FILE"

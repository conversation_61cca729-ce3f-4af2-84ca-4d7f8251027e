#!/usr/bin/env python3
"""
混合决策策略训练脚本

该脚本实现了结合原型网络和分类器的混合决策策略训练。
"""

import os
import sys
import argparse
import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
from tqdm import tqdm
import json
import matplotlib.pyplot as plt

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 导入自定义模块
from models.hybrid_decision import create_hybrid_protonet
from models.enhanced_feature_extractor import EnhancedKeratoconusFeatureExtractor
from models.protonet import ProtoNet
from models.maml import MAMLProtoNet
from models.losses import FocalLoss
from datasets.keratoconus_dataset import KeratoconusDataset, get_transforms
from datasets.balanced_task_sampler import BalancedTaskSampler


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='Train with Hybrid Decision Strategy')
    
    # 基本参数
    parser.add_argument('--train_csv', type=str, required=True, help='Path to training CSV file')
    parser.add_argument('--val_csv', type=str, required=True, help='Path to validation CSV file')
    parser.add_argument('--test_csv', type=str, required=True, help='Path to test CSV file')
    parser.add_argument('--save_dir', type=str, default='hybrid_results', help='Directory to save results')
    parser.add_argument('--device', type=str, default='cuda', help='Device to use')
    
    # 模型参数
    parser.add_argument('--proto_counts', type=str, default='2,4,2', help='Prototype counts for each class')
    parser.add_argument('--feature_dim', type=int, default=512, help='Feature dimension')
    parser.add_argument('--inner_lr', type=float, default=0.3, help='Inner learning rate')
    parser.add_argument('--inner_steps', type=int, default=2, help='Inner update steps')
    parser.add_argument('--pretrained', action='store_true', default=True, help='Use pretrained feature extractor')
    parser.add_argument('--dropout', type=float, default=0.5, help='Dropout rate')
    
    # 混合决策参数
    parser.add_argument('--hybrid_type', type=str, default='basic', choices=['basic', 'adaptive'],
                        help='Type of hybrid classifier')
    parser.add_argument('--hybrid_hidden_dim', type=int, default=256, help='Hidden dimension for hybrid classifier')
    parser.add_argument('--prototype_weight', type=float, default=0.7, help='Weight for prototype loss')
    parser.add_argument('--classifier_weight', type=float, default=0.3, help='Weight for classifier loss')
    parser.add_argument('--consistency_weight', type=float, default=0.1, help='Weight for consistency loss')
    
    # 训练参数
    parser.add_argument('--epochs', type=int, default=30, help='Number of epochs')
    parser.add_argument('--lr', type=float, default=0.0002, help='Learning rate')
    parser.add_argument('--weight_decay', type=float, default=5e-4, help='Weight decay')
    parser.add_argument('--n_way', type=int, default=3, help='Number of classes per task')
    parser.add_argument('--n_shot', type=int, default=5, help='Number of support samples per class')
    parser.add_argument('--n_query', type=int, default=15, help='Number of query samples per class')
    parser.add_argument('--tasks_per_epoch', type=int, default=30, help='Number of tasks per epoch')
    
    # 损失函数参数
    parser.add_argument('--early_kc_weight', type=float, default=8.0, help='Weight for early KC samples')
    parser.add_argument('--kc_weight', type=float, default=4.0, help='Weight for KC samples')
    parser.add_argument('--focal_gamma', type=float, default=2.0, help='Gamma for focal loss')
    
    # 平衡任务采样器参数
    parser.add_argument('--use_balanced_task_sampler', action='store_true', default=True,
                        help='Use balanced task sampler')
    parser.add_argument('--kc_shot_multiplier', type=float, default=3.0, help='Multiplier for KC shot count')
    parser.add_argument('--early_kc_shot_multiplier', type=float, default=1.5, help='Multiplier for early KC shot count')
    
    # 其他参数
    parser.add_argument('--seed', type=int, default=42, help='Random seed')
    parser.add_argument('--val_frequency', type=int, default=1, help='Validation frequency in epochs')
    parser.add_argument('--early_stopping', type=int, default=5, help='Early stopping patience')
    
    return parser.parse_args()


def set_seed(seed):
    """设置随机种子"""
    torch.manual_seed(seed)
    np.random.seed(seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed(seed)


def create_model(args, device):
    """创建混合决策模型"""
    # 解析原型配置
    proto_counts = [int(x) for x in args.proto_counts.split(',')]
    print(f"使用原型配置: {proto_counts}")
    
    # 创建特征提取器
    feature_extractor = EnhancedKeratoconusFeatureExtractor(
        pretrained=args.pretrained,
        feature_dim=args.feature_dim,
        dropout_rate=args.dropout
    ).to(device)
    print(f"使用增强版特征提取器，dropout率: {args.dropout}")
    
    # 创建基础ProtoNet模型
    base_protonet = ProtoNet(
        feature_extractor,
        n_classes=3,
        proto_counts=proto_counts,
        feature_dim=args.feature_dim
    ).to(device)
    
    # 创建混合ProtoNet模型
    hybrid_protonet = create_hybrid_protonet(
        base_protonet,
        hybrid_type=args.hybrid_type,
        hidden_dim=args.hybrid_hidden_dim,
        dropout_rate=args.dropout
    )
    
    # 创建MAML模型
    model = MAMLProtoNet(
        hybrid_protonet,
        inner_lr=args.inner_lr,
        inner_steps=args.inner_steps
    ).to(device)
    
    print(f"使用{args.hybrid_type}混合决策策略")
    print(f"MAML参数: 内循环学习率={args.inner_lr}, 内循环步数={args.inner_steps}")
    
    return model


def train_epoch(model, task_sampler, optimizer, device, args):
    """训练一个epoch"""
    model.train()
    epoch_loss = 0.0
    epoch_acc = 0.0
    epoch_loss_components = {
        'main_loss': 0.0,
        'prototype_loss': 0.0,
        'classifier_loss': 0.0,
        'consistency_loss': 0.0
    }
    
    # 创建损失函数
    class_weights = torch.tensor([1.0, args.early_kc_weight, args.kc_weight], device=device)
    criterion = FocalLoss(gamma=args.focal_gamma, alpha=class_weights)
    
    for _ in tqdm(range(args.tasks_per_epoch), desc="Training tasks"):
        # 采样一个任务
        task = task_sampler.sample_task()
        support_images, support_labels = task['support']
        query_images, query_labels = task['query']
        
        # 将数据移动到设备
        support_images = support_images.to(device)
        support_labels = support_labels.to(device)
        query_images = query_images.to(device)
        query_labels = query_labels.to(device)
        
        # 前向传播
        optimizer.zero_grad()
        
        # 获取混合决策结果和决策信息
        hybrid_logits, decision_info = model.protonet(
            support_images, support_labels, query_images, return_decision_info=True
        )
        
        # 计算混合损失
        total_loss, loss_components = model.protonet.compute_hybrid_loss(
            hybrid_logits,
            decision_info['prototype_logits'],
            decision_info['classifier_logits'],
            query_labels,
            prototype_weight=args.prototype_weight,
            classifier_weight=args.classifier_weight,
            consistency_weight=args.consistency_weight
        )
        
        # 反向传播
        total_loss.backward()
        optimizer.step()
        
        # 计算准确率
        pred = torch.argmax(hybrid_logits, dim=1)
        correct = (pred == query_labels).sum().item()
        accuracy = correct / len(query_labels)
        
        # 更新统计信息
        epoch_loss += total_loss.item()
        epoch_acc += accuracy
        for key, value in loss_components.items():
            epoch_loss_components[key] += value
    
    # 计算平均值
    epoch_loss /= args.tasks_per_epoch
    epoch_acc /= args.tasks_per_epoch
    for key in epoch_loss_components:
        epoch_loss_components[key] /= args.tasks_per_epoch
    
    return epoch_loss, epoch_acc, epoch_loss_components


def validate(model, val_dataset, device, args):
    """验证模型"""
    model.eval()
    val_loss = 0.0
    val_acc = 0.0
    
    # 创建验证任务采样器
    val_task_sampler = BalancedTaskSampler(
        val_dataset,
        n_way=args.n_way,
        n_shot=args.n_shot,
        n_query=args.n_query,
        kc_shot_multiplier=args.kc_shot_multiplier,
        early_kc_shot_multiplier=args.early_kc_shot_multiplier
    )
    
    # 创建损失函数
    class_weights = torch.tensor([1.0, args.early_kc_weight, args.kc_weight], device=device)
    criterion = FocalLoss(gamma=args.focal_gamma, alpha=class_weights)
    
    n_tasks = max(10, args.tasks_per_epoch // 3)
    
    with torch.no_grad():
        for _ in tqdm(range(n_tasks), desc="Validating tasks"):
            # 采样一个任务
            task = val_task_sampler.sample_task()
            support_images, support_labels = task['support']
            query_images, query_labels = task['query']
            
            # 将数据移动到设备
            support_images = support_images.to(device)
            support_labels = support_labels.to(device)
            query_images = query_images.to(device)
            query_labels = query_labels.to(device)
            
            # 前向传播
            hybrid_logits = model(support_images, support_labels, query_images)
            
            # 计算损失
            loss = criterion(hybrid_logits, query_labels)
            
            # 计算准确率
            pred = torch.argmax(hybrid_logits, dim=1)
            correct = (pred == query_labels).sum().item()
            accuracy = correct / len(query_labels)
            
            # 更新统计信息
            val_loss += loss.item()
            val_acc += accuracy
    
    # 计算平均值
    val_loss /= n_tasks
    val_acc /= n_tasks
    
    return val_loss, val_acc


def test(model, test_dataset, device, args):
    """测试模型"""
    model.eval()
    test_acc = 0.0
    class_accuracies = {'normal': [], 'e-kc': [], 'kc': []}
    confusion_matrix = np.zeros((3, 3), dtype=int)
    
    # 创建测试任务采样器
    test_task_sampler = BalancedTaskSampler(
        test_dataset,
        n_way=args.n_way,
        n_shot=args.n_shot,
        n_query=args.n_query,
        kc_shot_multiplier=args.kc_shot_multiplier,
        early_kc_shot_multiplier=args.early_kc_shot_multiplier
    )
    
    n_tasks = 30
    
    with torch.no_grad():
        for _ in tqdm(range(n_tasks), desc="Testing tasks"):
            # 采样一个任务
            task = test_task_sampler.sample_task()
            support_images, support_labels = task['support']
            query_images, query_labels = task['query']
            
            # 将数据移动到设备
            support_images = support_images.to(device)
            support_labels = support_labels.to(device)
            query_images = query_images.to(device)
            query_labels = query_labels.to(device)
            
            # 前向传播
            hybrid_logits = model(support_images, support_labels, query_images)
            
            # 计算准确率
            pred = torch.argmax(hybrid_logits, dim=1)
            correct = (pred == query_labels).sum().item()
            accuracy = correct / len(query_labels)
            
            # 更新统计信息
            test_acc += accuracy
            
            # 按类别统计准确率
            for i, label in enumerate(query_labels.cpu().numpy()):
                class_name = ['normal', 'e-kc', 'kc'][label]
                is_correct = int(pred[i].cpu().numpy() == label)
                class_accuracies[class_name].append(is_correct)
                
                # 更新混淆矩阵
                confusion_matrix[label][pred[i].cpu().numpy()] += 1
    
    # 计算平均值
    test_acc /= n_tasks
    
    # 计算每个类别的准确率
    class_avg_accuracies = {}
    for class_name, accs in class_accuracies.items():
        class_avg_accuracies[class_name] = np.mean(accs) * 100
    
    return test_acc, class_avg_accuracies, confusion_matrix


def main():
    args = parse_args()
    
    # 设置随机种子
    set_seed(args.seed)
    
    # 创建输出目录
    os.makedirs(args.save_dir, exist_ok=True)
    
    # 设置设备
    device = torch.device(args.device if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 加载数据集
    train_transforms, val_transforms = get_transforms()
    
    train_dataset = KeratoconusDataset(args.train_csv, transform=train_transforms)
    val_dataset = KeratoconusDataset(args.val_csv, transform=val_transforms)
    test_dataset = KeratoconusDataset(args.test_csv, transform=val_transforms)
    
    print(f"训练集样本数: {len(train_dataset)}")
    print(f"验证集样本数: {len(val_dataset)}")
    print(f"测试集样本数: {len(test_dataset)}")
    
    # 创建任务采样器
    train_task_sampler = BalancedTaskSampler(
        train_dataset,
        n_way=args.n_way,
        n_shot=args.n_shot,
        n_query=args.n_query,
        kc_shot_multiplier=args.kc_shot_multiplier,
        early_kc_shot_multiplier=args.early_kc_shot_multiplier
    )
    
    # 创建模型
    model = create_model(args, device)
    
    # 创建优化器
    optimizer = optim.Adam(model.parameters(), lr=args.lr, weight_decay=args.weight_decay)
    
    # 训练循环
    best_val_acc = 0.0
    best_epoch = 0
    patience_counter = 0
    history = {
        'train_loss': [],
        'train_acc': [],
        'val_loss': [],
        'val_acc': [],
        'loss_components': {
            'main_loss': [],
            'prototype_loss': [],
            'classifier_loss': [],
            'consistency_loss': []
        }
    }
    
    print(f"开始训练，共{args.epochs}个epoch")
    for epoch in range(args.epochs):
        print(f"Epoch {epoch+1}/{args.epochs}")
        
        # 训练
        train_loss, train_acc, loss_components = train_epoch(
            model, train_task_sampler, optimizer, device, args
        )
        
        # 记录训练历史
        history['train_loss'].append(train_loss)
        history['train_acc'].append(train_acc)
        for key, value in loss_components.items():
            history['loss_components'][key].append(value)
        
        # 验证
        if (epoch + 1) % args.val_frequency == 0:
            val_loss, val_acc = validate(model, val_dataset, device, args)
            history['val_loss'].append(val_loss)
            history['val_acc'].append(val_acc)
            
            print(f"Epoch {epoch+1}: Train Loss: {train_loss:.4f}, Train Acc: {train_acc:.4f}, "
                  f"Val Loss: {val_loss:.4f}, Val Acc: {val_acc:.4f}")
            print(f"Loss Components: Main: {loss_components['main_loss']:.4f}, "
                  f"Proto: {loss_components['prototype_loss']:.4f}, "
                  f"Classifier: {loss_components['classifier_loss']:.4f}, "
                  f"Consistency: {loss_components['consistency_loss']:.4f}")
            
            # 保存最佳模型
            if val_acc > best_val_acc:
                best_val_acc = val_acc
                best_epoch = epoch
                patience_counter = 0
                
                # 保存模型
                torch.save({
                    'model_state_dict': model.state_dict(),
                    'optimizer_state_dict': optimizer.state_dict(),
                    'epoch': epoch,
                    'val_acc': val_acc,
                    'history': history,
                    'args': vars(args)
                }, os.path.join(args.save_dir, 'best_hybrid_model.pth'))
                
                print(f"保存最佳模型，验证准确率: {val_acc:.4f}")
            else:
                patience_counter += 1
                print(f"验证准确率未提高，当前耐心值: {patience_counter}/{args.early_stopping}")
                
                # 早停
                if patience_counter >= args.early_stopping:
                    print(f"早停触发，停止训练")
                    break
        else:
            print(f"Epoch {epoch+1}: Train Loss: {train_loss:.4f}, Train Acc: {train_acc:.4f}")
    
    # 加载最佳模型
    checkpoint = torch.load(os.path.join(args.save_dir, 'best_hybrid_model.pth'))
    model.load_state_dict(checkpoint['model_state_dict'])
    
    # 测试最佳模型
    test_acc, class_accuracies, confusion_matrix = test(model, test_dataset, device, args)
    
    # 打印测试结果
    print(f"\n测试结果:")
    print(f"总体准确率: {test_acc*100:.2f}%")
    print("类别准确率:")
    for class_name, acc in class_accuracies.items():
        print(f"  {class_name}: {acc:.2f}%")
    
    # 保存测试结果
    results = {
        'test_acc': test_acc * 100,
        'class_accuracies': class_accuracies,
        'confusion_matrix': confusion_matrix.tolist(),
        'best_epoch': best_epoch,
        'best_val_acc': best_val_acc,
        'history': history,
        'args': vars(args)
    }
    
    with open(os.path.join(args.save_dir, 'hybrid_test_results.json'), 'w') as f:
        json.dump(results, f, indent=4)
    
    print(f"混合决策训练完成，结果已保存到 {args.save_dir}")


if __name__ == "__main__":
    main()

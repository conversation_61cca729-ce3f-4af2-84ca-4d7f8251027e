#!/usr/bin/env python3
"""
测试校准和混合决策模块

验证所有新实现的模块是否正常工作。
"""

import torch
import torch.nn.functional as F
import numpy as np
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_calibration_modules():
    """测试校准模块"""
    print("=" * 50)
    print("测试校准模块")
    print("=" * 50)
    
    try:
        from models.calibration import (
            TemperatureScaling, 
            ClassSpecificCalibration,
            KCEKCBoundaryCalibration,
            AdaptiveThresholdCalibration,
            create_calibrator
        )
        
        # 创建测试数据
        batch_size = 20
        num_classes = 3
        logits = torch.randn(batch_size, num_classes)
        probs = F.softmax(logits, dim=1)
        labels = torch.randint(0, num_classes, (batch_size,))
        
        print(f"测试数据: logits shape={logits.shape}, labels shape={labels.shape}")
        
        # 测试温度缩放
        print("\n1. 测试温度缩放...")
        temp_calibrator = TemperatureScaling()
        temp_calibrator.fit(logits, labels)
        calibrated_probs = temp_calibrator(logits)
        print(f"   温度参数: {temp_calibrator.temperature.item():.3f}")
        print(f"   校准前概率范围: [{probs.min():.3f}, {probs.max():.3f}]")
        print(f"   校准后概率范围: [{calibrated_probs.min():.3f}, {calibrated_probs.max():.3f}]")
        
        # 测试类别特定校准
        print("\n2. 测试类别特定校准...")
        class_calibrator = ClassSpecificCalibration()
        temps, biases = class_calibrator.fit(logits, labels)
        calibrated_probs = class_calibrator(logits)
        print(f"   类别温度: {temps}")
        print(f"   类别偏置: {biases}")
        
        # 测试KC-EKC边界校准
        print("\n3. 测试KC-EKC边界校准...")
        boundary_calibrator = KCEKCBoundaryCalibration()
        boundary_calibrator.fit(probs, labels, target_kc_recall=0.8)
        calibrated_probs = boundary_calibrator.calibrate(probs)
        print(f"   KC增强因子: {boundary_calibrator.kc_boost_factor:.2f}")
        print(f"   E-KC抑制因子: {boundary_calibrator.ekc_suppress_factor:.2f}")
        
        # 测试自适应阈值校准
        print("\n4. 测试自适应阈值校准...")
        adaptive_calibrator = AdaptiveThresholdCalibration()
        adaptive_calibrator.fit(probs, labels)
        calibrated_probs = adaptive_calibrator.calibrate(probs)
        print(f"   置信度阈值: {adaptive_calibrator.confidence_thresholds}")
        print(f"   KC增强因子: {adaptive_calibrator.kc_boost_factors}")
        
        # 测试工厂函数
        print("\n5. 测试工厂函数...")
        for method in ['temperature', 'class_specific', 'kc_ekc_boundary', 'adaptive_threshold']:
            calibrator = create_calibrator(method)
            print(f"   {method}: {type(calibrator).__name__}")
        
        print("\n✅ 校准模块测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 校准模块测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_hybrid_decision_modules():
    """测试混合决策模块"""
    print("\n" + "=" * 50)
    print("测试混合决策模块")
    print("=" * 50)
    
    try:
        from models.hybrid_decision import (
            HybridClassifier,
            AdaptiveHybridClassifier,
            HybridProtoNet,
            create_hybrid_protonet
        )
        
        # 创建测试数据
        batch_size = 8
        feature_dim = 512
        num_classes = 3
        
        query_features = torch.randn(batch_size, feature_dim)
        prototype_logits = torch.randn(batch_size, num_classes)
        
        print(f"测试数据: features shape={query_features.shape}, logits shape={prototype_logits.shape}")
        
        # 测试基础混合分类器
        print("\n1. 测试基础混合分类器...")
        hybrid_classifier = HybridClassifier(feature_dim=feature_dim, num_classes=num_classes)
        hybrid_logits, decision_info = hybrid_classifier(query_features, prototype_logits)
        
        print(f"   混合logits shape: {hybrid_logits.shape}")
        print(f"   决策信息键: {list(decision_info.keys())}")
        print(f"   融合权重: {decision_info['fusion_weights']}")
        print(f"   KC增强权重: {decision_info['kc_enhancement_weight']}")
        
        # 测试自适应混合分类器
        print("\n2. 测试自适应混合分类器...")
        adaptive_classifier = AdaptiveHybridClassifier(feature_dim=feature_dim, num_classes=num_classes)
        hybrid_logits, decision_info = adaptive_classifier(query_features, prototype_logits)
        
        print(f"   自适应权重 shape: {decision_info['adaptive_weights'].shape}")
        print(f"   难度分数 shape: {decision_info['difficulty_scores'].shape}")
        print(f"   调整后权重 shape: {decision_info['difficulty_adjusted_weights'].shape}")
        
        # 测试损失计算
        print("\n3. 测试混合损失计算...")
        labels = torch.randint(0, num_classes, (batch_size,))
        
        # 创建一个简单的ProtoNet来测试
        from models.enhanced_feature_extractor import EnhancedKeratoconusFeatureExtractor
        from models.protonet import ProtoNet
        
        feature_extractor = EnhancedKeratoconusFeatureExtractor(
            pretrained=False, feature_dim=feature_dim, dropout_rate=0.3
        )
        base_protonet = ProtoNet(feature_extractor, n_classes=num_classes, feature_dim=feature_dim)
        
        # 创建混合ProtoNet
        hybrid_protonet = create_hybrid_protonet(base_protonet, hybrid_type='basic')
        
        # 测试损失计算
        total_loss, loss_components = hybrid_protonet.compute_hybrid_loss(
            hybrid_logits, prototype_logits, decision_info['classifier_logits'], labels
        )
        
        print(f"   总损失: {total_loss.item():.4f}")
        print(f"   损失组件: {loss_components}")
        
        print("\n✅ 混合决策模块测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 混合决策模块测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_integration():
    """测试模块集成"""
    print("\n" + "=" * 50)
    print("测试模块集成")
    print("=" * 50)
    
    try:
        # 测试校准和混合决策的结合使用
        from models.calibration import create_calibrator
        from models.hybrid_decision import create_hybrid_protonet
        from models.enhanced_feature_extractor import EnhancedKeratoconusFeatureExtractor
        from models.protonet import ProtoNet
        
        # 创建测试数据
        batch_size = 8
        feature_dim = 512
        num_classes = 3
        
        # 创建基础模型
        feature_extractor = EnhancedKeratoconusFeatureExtractor(
            pretrained=False, feature_dim=feature_dim, dropout_rate=0.3
        )
        base_protonet = ProtoNet(feature_extractor, n_classes=num_classes, feature_dim=feature_dim)
        hybrid_protonet = create_hybrid_protonet(base_protonet, hybrid_type='basic')
        
        # 模拟前向传播
        support_images = torch.randn(15, 3, 224, 224)  # 5 shot * 3 classes
        support_labels = torch.tensor([0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 2, 2, 2, 2, 2])
        query_images = torch.randn(batch_size, 3, 224, 224)
        query_labels = torch.randint(0, num_classes, (batch_size,))
        
        print("1. 测试混合决策前向传播...")
        hybrid_protonet.set_hybrid_mode(True)
        hybrid_logits, decision_info = hybrid_protonet(
            support_images, support_labels, query_images, return_decision_info=True
        )
        print(f"   混合logits shape: {hybrid_logits.shape}")
        
        # 测试校准
        print("\n2. 测试校准应用...")
        probs = F.softmax(hybrid_logits, dim=1)
        
        calibrator = create_calibrator('kc_ekc_boundary')
        calibrator.fit(probs, query_labels)
        calibrated_probs = calibrator.calibrate(probs)
        
        print(f"   原始概率 shape: {probs.shape}")
        print(f"   校准后概率 shape: {calibrated_probs.shape}")
        print(f"   概率和检查: {calibrated_probs.sum(dim=1)}")  # 应该都接近1
        
        # 测试预测变化
        original_preds = torch.argmax(probs, dim=1)
        calibrated_preds = torch.argmax(calibrated_probs, dim=1)
        changed_predictions = (original_preds != calibrated_preds).sum().item()
        
        print(f"   改变的预测数量: {changed_predictions}/{batch_size}")
        
        print("\n✅ 模块集成测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 模块集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("开始测试校准和混合决策模块...")
    
    # 设置随机种子
    torch.manual_seed(42)
    np.random.seed(42)
    
    # 运行所有测试
    tests = [
        test_calibration_modules,
        test_hybrid_decision_modules,
        test_integration
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        if test_func():
            passed += 1
    
    print("\n" + "=" * 50)
    print("测试总结")
    print("=" * 50)
    print(f"通过测试: {passed}/{total}")
    
    if passed == total:
        print("🎉 所有测试通过！校准和混合决策模块工作正常。")
        print("\n💡 下一步建议:")
        print("1. 运行校准评估: python evaluate_with_calibration.py")
        print("2. 运行混合决策训练: python train_hybrid_decision.py")
        print("3. 运行完整实验: ./run_calibration_and_hybrid.sh")
        return True
    else:
        print("❌ 部分测试失败，请检查错误信息。")
        return False


if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)

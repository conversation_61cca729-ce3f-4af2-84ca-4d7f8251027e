STDOUT:
===================== 训练配置 =====================
GPU: 0
输出目录: /home/<USER>/balanced_task_sampling/results_trial_20250525_001109
日志文件: /home/<USER>/balanced_task_sampling/results_trial_20250525_001109/main_training_trial_23_20250525_001109.log

--------- 优化参数 ---------
内部学习率: 0.347389
训练轮数: 50
每轮任务数: 30

--------- 模型参数 ---------
使用类别权重: 圆锥角膜=6.544715712226761, 早期圆锥角膜=4.041393755876385
温度参数: 0.03602343195913314
硬样本挖掘比例: 0.5760290066895447
对比学习权重: 0.6081568031163989
=============================================

创建目录: /home/<USER>/balanced_task_sampling/results_trial_20250525_001109
总用量 4
-rw-rw-r-- 1 <USER> <GROUP> 657 5月  25 00:11 main_training_trial_23_20250525_001109.log
开始无数据增强的平衡任务采样训练...
使用GPU: 0
直接运行训练脚本...
成功导入增强版特征提取器
成功导入归一化特征提取器
成功导入增强版损失函数
成功导入增强版MAML模型
成功导入KC特定增强模块
成功导入增强版GPU数据增强模块
使用设备: cuda
禁用所有数据增强，直接加载原始数据集...
使用平衡任务采样器
类别 normal (标签 0): 164个样本
类别 e-kc (标签 1): 26个样本
类别 kc (标签 2): 133个样本
使用原型配置: [3, 6, 4]
使用增强版特征提取器，dropout率: 0.5
使用增强版MAML-ProtoNet模型，内循环学习率: 0.347389, 内循环步数: 5
使用ReduceLROnPlateau学习率调度器，因子: 0.5, 耐心值: 2
开始训练，共50个epoch
Epoch 1/50
当前学习率: 0.000200
使用类别权重: 正常=1.0, 早期圆锥角膜=4.041393755876385, 圆锥角膜=6.544715712226761
使用增强版对比损失函数，温度: 0.03602343195913314, 硬负样本挖掘比例: 0.5760290066895447, 早期圆锥角膜与正常样本对比权重: 1.221228162167176, 圆锥角膜与正常样本对比权重: 1.0
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
警告: 没有足够的有效类别进行3-way任务采样，尝试调整参数...
类别样本数量: {0: 33, 1: 5, 2: 29}
调整后的参数: n_shot=2, n_query=3
类别 normal (标签 0): 33个样本
类别 e-kc (标签 1): 5个样本
类别 kc (标签 2): 29个样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
Epoch 1: Train Loss: 2.3930, Train Acc: 0.4726, Val Loss: 1.7885, Val Acc: 0.6556
保存最佳模型，验证准确率: 0.6556
Epoch 2/50
当前学习率: 0.000200
使用类别权重: 正常=1.0, 早期圆锥角膜=4.041393755876385, 圆锥角膜=6.544715712226761
使用增强版对比损失函数，温度: 0.03602343195913314, 硬负样本挖掘比例: 0.5760290066895447, 早期圆锥角膜与正常样本对比权重: 1.221228162167176, 圆锥角膜与正常样本对比权重: 1.0
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
警告: 没有足够的有效类别进行3-way任务采样，尝试调整参数...
类别样本数量: {0: 33, 1: 5, 2: 29}
调整后的参数: n_shot=2, n_query=3
类别 normal (标签 0): 33个样本
类别 e-kc (标签 1): 5个样本
类别 kc (标签 2): 29个样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
Epoch 2: Train Loss: 1.9202, Train Acc: 0.6370, Val Loss: 1.8883, Val Acc: 0.6778
保存最佳模型，验证准确率: 0.6778
Epoch 3/50
当前学习率: 0.000200
使用类别权重: 正常=1.0, 早期圆锥角膜=4.041393755876385, 圆锥角膜=6.544715712226761
使用增强版对比损失函数，温度: 0.03602343195913314, 硬负样本挖掘比例: 0.5760290066895447, 早期圆锥角膜与正常样本对比权重: 1.221228162167176, 圆锥角膜与正常样本对比权重: 1.0
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
警告: 没有足够的有效类别进行3-way任务采样，尝试调整参数...
类别样本数量: {0: 33, 1: 5, 2: 29}
调整后的参数: n_shot=2, n_query=3
类别 normal (标签 0): 33个样本
类别 e-kc (标签 1): 5个样本
类别 kc (标签 2): 29个样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
Epoch 3: Train Loss: 1.6361, Train Acc: 0.7844, Val Loss: 1.8387, Val Acc: 0.6444
验证准确率未提高，当前耐心值: 1/3
Epoch 4/50
当前学习率: 0.000200
使用类别权重: 正常=1.0, 早期圆锥角膜=4.041393755876385, 圆锥角膜=6.544715712226761
使用增强版对比损失函数，温度: 0.03602343195913314, 硬负样本挖掘比例: 0.5760290066895447, 早期圆锥角膜与正常样本对比权重: 1.221228162167176, 圆锥角膜与正常样本对比权重: 1.0
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
警告: 没有足够的有效类别进行3-way任务采样，尝试调整参数...
类别样本数量: {0: 33, 1: 5, 2: 29}
调整后的参数: n_shot=2, n_query=3
类别 normal (标签 0): 33个样本
类别 e-kc (标签 1): 5个样本
类别 kc (标签 2): 29个样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
Epoch 4: Train Loss: 1.5134, Train Acc: 0.8148, Val Loss: 1.7980, Val Acc: 0.7000
保存最佳模型，验证准确率: 0.7000
Epoch 5/50
当前学习率: 0.000200
使用类别权重: 正常=1.0, 早期圆锥角膜=4.041393755876385, 圆锥角膜=6.544715712226761
使用增强版对比损失函数，温度: 0.03602343195913314, 硬负样本挖掘比例: 0.5760290066895447, 早期圆锥角膜与正常样本对比权重: 1.221228162167176, 圆锥角膜与正常样本对比权重: 1.0
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
警告: 没有足够的有效类别进行3-way任务采样，尝试调整参数...
类别样本数量: {0: 33, 1: 5, 2: 29}
调整后的参数: n_shot=2, n_query=3
类别 normal (标签 0): 33个样本
类别 e-kc (标签 1): 5个样本
类别 kc (标签 2): 29个样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
Epoch 5: Train Loss: 1.3747, Train Acc: 0.8674, Val Loss: 1.7235, Val Acc: 0.6222
验证准确率未提高，当前耐心值: 1/3
Epoch 6/50
当前学习率: 0.000200
使用类别权重: 正常=1.0, 早期圆锥角膜=4.041393755876385, 圆锥角膜=6.544715712226761
使用增强版对比损失函数，温度: 0.03602343195913314, 硬负样本挖掘比例: 0.5760290066895447, 早期圆锥角膜与正常样本对比权重: 1.221228162167176, 圆锥角膜与正常样本对比权重: 1.0
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
警告: 没有足够的有效类别进行3-way任务采样，尝试调整参数...
类别样本数量: {0: 33, 1: 5, 2: 29}
调整后的参数: n_shot=2, n_query=3
类别 normal (标签 0): 33个样本
类别 e-kc (标签 1): 5个样本
类别 kc (标签 2): 29个样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
Epoch 6: Train Loss: 1.3197, Train Acc: 0.8874, Val Loss: 1.9536, Val Acc: 0.6667
验证准确率未提高，当前耐心值: 2/3
Epoch 7/50
当前学习率: 0.000200
使用类别权重: 正常=1.0, 早期圆锥角膜=4.041393755876385, 圆锥角膜=6.544715712226761
使用增强版对比损失函数，温度: 0.03602343195913314, 硬负样本挖掘比例: 0.5760290066895447, 早期圆锥角膜与正常样本对比权重: 1.221228162167176, 圆锥角膜与正常样本对比权重: 1.0
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
警告: 没有足够的有效类别进行3-way任务采样，尝试调整参数...
类别样本数量: {0: 33, 1: 5, 2: 29}
调整后的参数: n_shot=2, n_query=3
类别 normal (标签 0): 33个样本
类别 e-kc (标签 1): 5个样本
类别 kc (标签 2): 29个样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
Epoch 7: Train Loss: 1.3182, Train Acc: 0.8726, Val Loss: 2.0158, Val Acc: 0.5667
验证准确率未提高，当前耐心值: 3/3
早停触发，停止训练
警告: 没有足够的有效类别进行3-way任务采样，尝试调整参数...
类别样本数量: {0: 37, 1: 7, 2: 27}
调整后的参数: n_shot=3, n_query=4
类别 normal (标签 0): 37个样本
类别 e-kc (标签 1): 7个样本
类别 kc (标签 2): 27个样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本

测试结果:
总体准确率: 81.67%
类别准确率:
  kc: 69.17%
  e-kc: 75.83%
  normal: 100.00%
训练完成，结果已保存到 /home/<USER>/balanced_task_sampling/results_trial_20250525_001109

STDERR:
+ TIMESTAMP=20250525_001109
+ RESULT_DIR=/home/<USER>/balanced_task_sampling/results_trial_20250525_001109
+ '[' -n /home/<USER>/balanced_task_sampling/results_trial_20250525_001109/main_training_trial_23_20250525_001109.log ']'
+ LOG_FILE=/home/<USER>/balanced_task_sampling/results_trial_20250525_001109/main_training_trial_23_20250525_001109.log
++ dirname /home/<USER>/balanced_task_sampling/results_trial_20250525_001109/main_training_trial_23_20250525_001109.log
+ mkdir -p /home/<USER>/balanced_task_sampling/results_trial_20250525_001109
+ mkdir -p /home/<USER>/balanced_task_sampling/results_trial_20250525_001109
+ echo '===================== 训练配置 ====================='
+ echo 'GPU: 0'
+ echo '输出目录: /home/<USER>/balanced_task_sampling/results_trial_20250525_001109'
+ echo '日志文件: /home/<USER>/balanced_task_sampling/results_trial_20250525_001109/main_training_trial_23_20250525_001109.log'
+ echo ''
+ echo '--------- 优化参数 ---------'
+ echo '内部学习率: 0.347389'
+ echo '训练轮数: 50'
+ echo '每轮任务数: 30'
+ echo ''
+ echo '--------- 模型参数 ---------'
+ tee -a /home/<USER>/balanced_task_sampling/results_trial_20250525_001109/main_training_trial_23_20250525_001109.log
+ echo '使用类别权重: 圆锥角膜=6.544715712226761, 早期圆锥角膜=4.041393755876385'
+ echo '温度参数: 0.03602343195913314'
+ echo '硬样本挖掘比例: 0.5760290066895447'
+ echo '对比学习权重: 0.6081568031163989'
+ echo '=============================================
'
+ validate_params
+ local error_count=0
++ dirname /home/<USER>/balanced_task_sampling/results_trial_20250525_001109/main_training_trial_23_20250525_001109.log
++ dirname /home/<USER>/balanced_task_sampling/split_result/train_set.csv
+ for dir in "$RESULT_DIR" "$(dirname "$LOG_FILE")" "$(dirname "$TRAIN_CSV")"
+ mkdir -p /home/<USER>/balanced_task_sampling/results_trial_20250525_001109
+ for dir in "$RESULT_DIR" "$(dirname "$LOG_FILE")" "$(dirname "$TRAIN_CSV")"
+ mkdir -p /home/<USER>/balanced_task_sampling/results_trial_20250525_001109
+ for dir in "$RESULT_DIR" "$(dirname "$LOG_FILE")" "$(dirname "$TRAIN_CSV")"
+ mkdir -p /home/<USER>/balanced_task_sampling/split_result
+ for csv in "$TRAIN_CSV" "$VAL_CSV" "$TEST_CSV"
+ '[' '!' -f /home/<USER>/balanced_task_sampling/split_result/train_set.csv ']'
+ for csv in "$TRAIN_CSV" "$VAL_CSV" "$TEST_CSV"
+ '[' '!' -f /home/<USER>/balanced_task_sampling/split_result/val_set.csv ']'
+ for csv in "$TRAIN_CSV" "$VAL_CSV" "$TEST_CSV"
+ '[' '!' -f /home/<USER>/balanced_task_sampling/split_result/test_set.csv ']'
+ [[ 0.0002 =~ ^[0-9.]+$ ]]
+ [[ 0.347389 =~ ^[0-9.]+$ ]]
+ '[' 0 -gt 0 ']'
+ return 0
+ '[' false = true ']'
+ export CUDA_VISIBLE_DEVICES=0
+ CUDA_VISIBLE_DEVICES=0
+ export OMP_NUM_THREADS=1
+ OMP_NUM_THREADS=1
+ export MKL_NUM_THREADS=1
+ MKL_NUM_THREADS=1
+ export MPLBACKEND=Agg
+ MPLBACKEND=Agg
+ mkdir -p /home/<USER>/balanced_task_sampling/results_trial_20250525_001109
+ echo '创建目录: /home/<USER>/balanced_task_sampling/results_trial_20250525_001109'
+ ls -l /home/<USER>/balanced_task_sampling/results_trial_20250525_001109
+ PRETRAINED_ARGS=
+ '[' true = true ']'
+ PRETRAINED_ARGS=--pretrained
+ AUGMENT_ARGS=--no_augmentation
+ CONTRASTIVE_ARGS=
+ '[' true = true ']'
+ CONTRASTIVE_ARGS='--use_contrastive --contrastive_weight 0.6081568031163989 --temperature 0.03602343195913314 --hard_mining_ratio 0.5760290066895447 --early_normal_weight 1.221228162167176 --kc_normal_weight 1.0'
+ NORMALIZE_ARGS=
+ '[' false = true ']'
+ SAMPLER_ARGS=
+ '[' true = true ']'
+ SAMPLER_ARGS=' --use_separate_test_sampler'
+ '[' true = true ']'
+ SAMPLER_ARGS=' --use_separate_test_sampler --use_balanced_task_sampler --kc_shot_multiplier 3.697553668592402 --early_kc_shot_multiplier 1.701777968555318'
+ LR_SCHEDULER_ARGS=
+ '[' true = true ']'
+ LR_SCHEDULER_ARGS='--use_lr_scheduler --scheduler_type plateau'
+ '[' plateau = onecycle ']'
+ '[' plateau = plateau ']'
+ LR_SCHEDULER_ARGS='--use_lr_scheduler --scheduler_type plateau --plateau_factor 0.5 --plateau_patience 2 --min_lr 1e-6'
+ BASE_CMD='python train_balanced_task_sampling.py   --train_csv /home/<USER>/balanced_task_sampling/split_result/train_set.csv   --val_csv /home/<USER>/balanced_task_sampling/split_result/val_set.csv   --test_csv /home/<USER>/balanced_task_sampling/split_result/test_set.csv   --epochs 50   --lr 0.0002   --weight_decay 5e-4   --feature_dim 512   --save_dir /home/<USER>/balanced_task_sampling/results_trial_20250525_001109   --proto_counts 3,6,4   --pretrained   --inner_lr 0.347389   --inner_steps 5   --n_way 3   --n_shot 5   --n_query 15   --tasks_per_epoch 30   --meta_batch_size 8   --no_augmentation   --mixup_alpha 0.0   --cutmix_prob 0.0   --num_workers 4   --augment_factor 1   --kc_augment_factor 1   --seed 42   --early_kc_weight 4.041393755876385   --kc_weight 6.544715712226761   --focal_gamma 1.4162050444380962   --val_frequency 1   --early_stopping 3   --dropout 0.5    --use_separate_test_sampler --use_balanced_task_sampler --kc_shot_multiplier 3.697553668592402 --early_kc_shot_multiplier 1.701777968555318   --use_contrastive --contrastive_weight 0.6081568031163989 --temperature 0.03602343195913314 --hard_mining_ratio 0.5760290066895447 --early_normal_weight 1.221228162167176 --kc_normal_weight 1.0      --use_lr_scheduler --scheduler_type plateau --plateau_factor 0.5 --plateau_patience 2 --min_lr 1e-6'
+ echo 开始无数据增强的平衡任务采样训练...
+ echo '使用GPU: 0'
+ '[' direct = nohup ']'
+ echo 直接运行训练脚本...
+ eval python train_balanced_task_sampling.py --train_csv /home/<USER>/balanced_task_sampling/split_result/train_set.csv --val_csv /home/<USER>/balanced_task_sampling/split_result/val_set.csv --test_csv /home/<USER>/balanced_task_sampling/split_result/test_set.csv --epochs 50 --lr 0.0002 --weight_decay 5e-4 --feature_dim 512 --save_dir /home/<USER>/balanced_task_sampling/results_trial_20250525_001109 --proto_counts 3,6,4 --pretrained --inner_lr 0.347389 --inner_steps 5 --n_way 3 --n_shot 5 --n_query 15 --tasks_per_epoch 30 --meta_batch_size 8 --no_augmentation --mixup_alpha 0.0 --cutmix_prob 0.0 --num_workers 4 --augment_factor 1 --kc_augment_factor 1 --seed 42 --early_kc_weight 4.041393755876385 --kc_weight 6.544715712226761 --focal_gamma 1.4162050444380962 --val_frequency 1 --early_stopping 3 --dropout 0.5 --use_separate_test_sampler --use_balanced_task_sampler --kc_shot_multiplier 3.697553668592402 --early_kc_shot_multiplier 1.701777968555318 --use_contrastive --contrastive_weight 0.6081568031163989 --temperature 0.03602343195913314 --hard_mining_ratio 0.5760290066895447 --early_normal_weight 1.221228162167176 --kc_normal_weight 1.0 --use_lr_scheduler --scheduler_type plateau --plateau_factor 0.5 --plateau_patience 2 --min_lr 1e-6
++ python train_balanced_task_sampling.py --train_csv /home/<USER>/balanced_task_sampling/split_result/train_set.csv --val_csv /home/<USER>/balanced_task_sampling/split_result/val_set.csv --test_csv /home/<USER>/balanced_task_sampling/split_result/test_set.csv --epochs 50 --lr 0.0002 --weight_decay 5e-4 --feature_dim 512 --save_dir /home/<USER>/balanced_task_sampling/results_trial_20250525_001109 --proto_counts 3,6,4 --pretrained --inner_lr 0.347389 --inner_steps 5 --n_way 3 --n_shot 5 --n_query 15 --tasks_per_epoch 30 --meta_batch_size 8 --no_augmentation --mixup_alpha 0.0 --cutmix_prob 0.0 --num_workers 4 --augment_factor 1 --kc_augment_factor 1 --seed 42 --early_kc_weight 4.041393755876385 --kc_weight 6.544715712226761 --focal_gamma 1.4162050444380962 --val_frequency 1 --early_stopping 3 --dropout 0.5 --use_separate_test_sampler --use_balanced_task_sampler --kc_shot_multiplier 3.697553668592402 --early_kc_shot_multiplier 1.701777968555318 --use_contrastive --contrastive_weight 0.6081568031163989 --temperature 0.03602343195913314 --hard_mining_ratio 0.5760290066895447 --early_normal_weight 1.221228162167176 --kc_normal_weight 1.0 --use_lr_scheduler --scheduler_type plateau --plateau_factor 0.5 --plateau_patience 2 --min_lr 1e-6
/home/<USER>/anaconda3/envs/kc1/lib/python3.10/site-packages/torchvision/models/_utils.py:208: UserWarning: The parameter 'pretrained' is deprecated since 0.13 and may be removed in the future, please use 'weights' instead.
  warnings.warn(
/home/<USER>/anaconda3/envs/kc1/lib/python3.10/site-packages/torchvision/models/_utils.py:223: UserWarning: Arguments other than a weight enum or `None` for 'weights' are deprecated since 0.13 and may be removed in the future. The current behavior is equivalent to passing `weights=ResNet34_Weights.IMAGENET1K_V1`. You can also use `weights=ResNet34_Weights.DEFAULT` to get the most up-to-date weights.
  warnings.warn(msg)
/home/<USER>/anaconda3/envs/kc1/lib/python3.10/site-packages/torch/optim/lr_scheduler.py:62: UserWarning: The verbose parameter is deprecated. Please use get_last_lr() to access the learning rate.
  warnings.warn(

Training tasks:   0%|          | 0/30 [00:00<?, ?it/s]
Training tasks:   3%|▎         | 1/30 [00:07<03:44,  7.74s/it]
Training tasks:   7%|▋         | 2/30 [00:09<02:02,  4.37s/it]
Training tasks:  10%|█         | 3/30 [00:11<01:28,  3.28s/it]
Training tasks:  13%|█▎        | 4/30 [00:13<01:13,  2.82s/it]
Training tasks:  17%|█▋        | 5/30 [00:15<01:03,  2.53s/it]
Training tasks:  20%|██        | 6/30 [00:17<00:55,  2.30s/it]
Training tasks:  23%|██▎       | 7/30 [00:19<00:50,  2.21s/it]
Training tasks:  27%|██▋       | 8/30 [00:21<00:47,  2.17s/it]
Training tasks:  30%|███       | 9/30 [00:23<00:42,  2.04s/it]
Training tasks:  33%|███▎      | 10/30 [00:25<00:40,  2.00s/it]
Training tasks:  37%|███▋      | 11/30 [00:27<00:37,  1.96s/it]
Training tasks:  40%|████      | 12/30 [00:29<00:34,  1.89s/it]
Training tasks:  43%|████▎     | 13/30 [00:30<00:30,  1.78s/it]
Training tasks:  47%|████▋     | 14/30 [00:32<00:29,  1.83s/it]
Training tasks:  50%|█████     | 15/30 [00:34<00:27,  1.86s/it]
Training tasks:  53%|█████▎    | 16/30 [00:36<00:26,  1.92s/it]
Training tasks:  57%|█████▋    | 17/30 [00:38<00:25,  1.93s/it]
Training tasks:  60%|██████    | 18/30 [00:40<00:23,  1.97s/it]
Training tasks:  63%|██████▎   | 19/30 [00:42<00:21,  1.97s/it]
Training tasks:  67%|██████▋   | 20/30 [00:44<00:19,  1.95s/it]
Training tasks:  70%|███████   | 21/30 [00:46<00:16,  1.84s/it]
Training tasks:  73%|███████▎  | 22/30 [00:47<00:13,  1.74s/it]
Training tasks:  77%|███████▋  | 23/30 [00:49<00:12,  1.78s/it]
Training tasks:  80%|████████  | 24/30 [00:51<00:11,  1.83s/it]
Training tasks:  83%|████████▎ | 25/30 [00:53<00:09,  1.84s/it]
Training tasks:  87%|████████▋ | 26/30 [00:55<00:07,  1.90s/it]
Training tasks:  90%|█████████ | 27/30 [00:57<00:05,  1.95s/it]
Training tasks:  93%|█████████▎| 28/30 [00:59<00:03,  1.95s/it]
Training tasks:  97%|█████████▋| 29/30 [01:01<00:01,  1.97s/it]
Training tasks: 100%|██████████| 30/30 [01:02<00:00,  1.87s/it]
Training tasks: 100%|██████████| 30/30 [01:02<00:00,  2.10s/it]

Validating tasks:   0%|          | 0/10 [00:00<?, ?it/s]
Validating tasks:  10%|█         | 1/10 [00:00<00:03,  2.49it/s]
Validating tasks:  20%|██        | 2/10 [00:00<00:02,  2.78it/s]
Validating tasks:  30%|███       | 3/10 [00:01<00:02,  2.69it/s]
Validating tasks:  40%|████      | 4/10 [00:01<00:02,  2.92it/s]
Validating tasks:  50%|█████     | 5/10 [00:01<00:01,  3.10it/s]
Validating tasks:  60%|██████    | 6/10 [00:02<00:01,  3.09it/s]
Validating tasks:  70%|███████   | 7/10 [00:02<00:00,  3.26it/s]
Validating tasks:  80%|████████  | 8/10 [00:02<00:00,  3.20it/s]
Validating tasks:  90%|█████████ | 9/10 [00:02<00:00,  3.15it/s]
Validating tasks: 100%|██████████| 10/10 [00:03<00:00,  3.13it/s]
Validating tasks: 100%|██████████| 10/10 [00:03<00:00,  3.05it/s]

Training tasks:   0%|          | 0/30 [00:00<?, ?it/s]
Training tasks:   3%|▎         | 1/30 [00:01<00:56,  1.96s/it]
Training tasks:   7%|▋         | 2/30 [00:03<00:47,  1.68s/it]
Training tasks:  10%|█         | 3/30 [00:05<00:44,  1.65s/it]
Training tasks:  13%|█▎        | 4/30 [00:06<00:43,  1.66s/it]
Training tasks:  17%|█▋        | 5/30 [00:08<00:41,  1.65s/it]
Training tasks:  20%|██        | 6/30 [00:09<00:39,  1.63s/it]
Training tasks:  23%|██▎       | 7/30 [00:11<00:38,  1.66s/it]
Training tasks:  27%|██▋       | 8/30 [00:13<00:39,  1.77s/it]
Training tasks:  30%|███       | 9/30 [00:15<00:38,  1.85s/it]
Training tasks:  33%|███▎      | 10/30 [00:17<00:36,  1.82s/it]
Training tasks:  37%|███▋      | 11/30 [00:19<00:34,  1.80s/it]
Training tasks:  40%|████      | 12/30 [00:20<00:30,  1.71s/it]
Training tasks:  43%|████▎     | 13/30 [00:22<00:29,  1.73s/it]
Training tasks:  47%|████▋     | 14/30 [00:24<00:28,  1.79s/it]
Training tasks:  50%|█████     | 15/30 [00:26<00:27,  1.82s/it]
Training tasks:  53%|█████▎    | 16/30 [00:28<00:25,  1.85s/it]
Training tasks:  57%|█████▋    | 17/30 [00:30<00:24,  1.86s/it]
Training tasks:  60%|██████    | 18/30 [00:31<00:22,  1.86s/it]
Training tasks:  63%|██████▎   | 19/30 [00:33<00:20,  1.88s/it]
Training tasks:  67%|██████▋   | 20/30 [00:35<00:18,  1.87s/it]
Training tasks:  70%|███████   | 21/30 [00:37<00:17,  1.93s/it]
Training tasks:  73%|███████▎  | 22/30 [00:39<00:15,  1.94s/it]
Training tasks:  77%|███████▋  | 23/30 [00:41<00:13,  1.91s/it]
Training tasks:  80%|████████  | 24/30 [00:43<00:11,  1.91s/it]
Training tasks:  83%|████████▎ | 25/30 [00:45<00:08,  1.79s/it]
Training tasks:  87%|████████▋ | 26/30 [00:46<00:07,  1.78s/it]
Training tasks:  90%|█████████ | 27/30 [00:48<00:05,  1.83s/it]
Training tasks:  93%|█████████▎| 28/30 [00:50<00:03,  1.83s/it]
Training tasks:  97%|█████████▋| 29/30 [00:52<00:01,  1.81s/it]
Training tasks: 100%|██████████| 30/30 [00:54<00:00,  1.81s/it]
Training tasks: 100%|██████████| 30/30 [00:54<00:00,  1.80s/it]

Validating tasks:   0%|          | 0/10 [00:00<?, ?it/s]
Validating tasks:  10%|█         | 1/10 [00:00<00:02,  3.40it/s]
Validating tasks:  20%|██        | 2/10 [00:00<00:01,  4.03it/s]
Validating tasks:  30%|███       | 3/10 [00:00<00:01,  4.09it/s]
Validating tasks:  40%|████      | 4/10 [00:01<00:01,  3.92it/s]
Validating tasks:  50%|█████     | 5/10 [00:01<00:01,  3.83it/s]
Validating tasks:  60%|██████    | 6/10 [00:01<00:01,  3.74it/s]
Validating tasks:  70%|███████   | 7/10 [00:01<00:00,  3.70it/s]
Validating tasks:  80%|████████  | 8/10 [00:02<00:00,  3.62it/s]
Validating tasks:  90%|█████████ | 9/10 [00:02<00:00,  3.68it/s]
Validating tasks: 100%|██████████| 10/10 [00:02<00:00,  3.56it/s]
Validating tasks: 100%|██████████| 10/10 [00:02<00:00,  3.70it/s]

Training tasks:   0%|          | 0/30 [00:00<?, ?it/s]
Training tasks:   3%|▎         | 1/30 [00:01<00:47,  1.65s/it]
Training tasks:   7%|▋         | 2/30 [00:03<00:48,  1.74s/it]
Training tasks:  10%|█         | 3/30 [00:05<00:48,  1.79s/it]
Training tasks:  13%|█▎        | 4/30 [00:07<00:46,  1.81s/it]
Training tasks:  17%|█▋        | 5/30 [00:08<00:45,  1.82s/it]
Training tasks:  20%|██        | 6/30 [00:10<00:45,  1.88s/it]
Training tasks:  23%|██▎       | 7/30 [00:12<00:42,  1.86s/it]
Training tasks:  27%|██▋       | 8/30 [00:14<00:38,  1.76s/it]
Training tasks:  30%|███       | 9/30 [00:16<00:38,  1.83s/it]
Training tasks:  33%|███▎      | 10/30 [00:18<00:37,  1.85s/it]
Training tasks:  37%|███▋      | 11/30 [00:20<00:35,  1.86s/it]
Training tasks:  40%|████      | 12/30 [00:21<00:32,  1.83s/it]
Training tasks:  43%|████▎     | 13/30 [00:23<00:30,  1.79s/it]
Training tasks:  47%|████▋     | 14/30 [00:25<00:28,  1.75s/it]
Training tasks:  50%|█████     | 15/30 [00:27<00:26,  1.76s/it]
Training tasks:  53%|█████▎    | 16/30 [00:28<00:24,  1.78s/it]
Training tasks:  57%|█████▋    | 17/30 [00:30<00:23,  1.84s/it]
Training tasks:  60%|██████    | 18/30 [00:32<00:21,  1.79s/it]
Training tasks:  63%|██████▎   | 19/30 [00:34<00:19,  1.81s/it]
Training tasks:  67%|██████▋   | 20/30 [00:36<00:18,  1.82s/it]
Training tasks:  70%|███████   | 21/30 [00:38<00:16,  1.82s/it]
Training tasks:  73%|███████▎  | 22/30 [00:39<00:14,  1.77s/it]
Training tasks:  77%|███████▋  | 23/30 [00:41<00:12,  1.77s/it]
Training tasks:  80%|████████  | 24/30 [00:43<00:10,  1.73s/it]
Training tasks:  83%|████████▎ | 25/30 [00:44<00:08,  1.76s/it]
Training tasks:  87%|████████▋ | 26/30 [00:46<00:07,  1.78s/it]
Training tasks:  90%|█████████ | 27/30 [00:48<00:05,  1.78s/it]
Training tasks:  93%|█████████▎| 28/30 [00:50<00:03,  1.77s/it]
Training tasks:  97%|█████████▋| 29/30 [00:52<00:01,  1.78s/it]
Training tasks: 100%|██████████| 30/30 [00:53<00:00,  1.81s/it]
Training tasks: 100%|██████████| 30/30 [00:53<00:00,  1.80s/it]

Validating tasks:   0%|          | 0/10 [00:00<?, ?it/s]
Validating tasks:  10%|█         | 1/10 [00:00<00:02,  3.54it/s]
Validating tasks:  20%|██        | 2/10 [00:00<00:02,  3.70it/s]
Validating tasks:  30%|███       | 3/10 [00:00<00:01,  3.92it/s]
Validating tasks:  40%|████      | 4/10 [00:01<00:01,  3.88it/s]
Validating tasks:  50%|█████     | 5/10 [00:01<00:01,  3.58it/s]
Validating tasks:  60%|██████    | 6/10 [00:01<00:01,  3.75it/s]
Validating tasks:  70%|███████   | 7/10 [00:01<00:00,  4.18it/s]
Validating tasks:  80%|████████  | 8/10 [00:02<00:00,  4.28it/s]
Validating tasks:  90%|█████████ | 9/10 [00:02<00:00,  3.92it/s]
Validating tasks: 100%|██████████| 10/10 [00:02<00:00,  3.65it/s]
Validating tasks: 100%|██████████| 10/10 [00:02<00:00,  3.81it/s]

Training tasks:   0%|          | 0/30 [00:00<?, ?it/s]
Training tasks:   3%|▎         | 1/30 [00:01<00:55,  1.92s/it]
Training tasks:   7%|▋         | 2/30 [00:03<00:52,  1.86s/it]
Training tasks:  10%|█         | 3/30 [00:05<00:46,  1.71s/it]
Training tasks:  13%|█▎        | 4/30 [00:06<00:42,  1.62s/it]
Training tasks:  17%|█▋        | 5/30 [00:08<00:41,  1.65s/it]
Training tasks:  20%|██        | 6/30 [00:10<00:41,  1.72s/it]
Training tasks:  23%|██▎       | 7/30 [00:11<00:39,  1.71s/it]
Training tasks:  27%|██▋       | 8/30 [00:13<00:39,  1.79s/it]
Training tasks:  30%|███       | 9/30 [00:15<00:38,  1.83s/it]
Training tasks:  33%|███▎      | 10/30 [00:17<00:36,  1.82s/it]
Training tasks:  37%|███▋      | 11/30 [00:19<00:34,  1.80s/it]
Training tasks:  40%|████      | 12/30 [00:21<00:32,  1.78s/it]
Training tasks:  43%|████▎     | 13/30 [00:22<00:28,  1.70s/it]
Training tasks:  47%|████▋     | 14/30 [00:24<00:26,  1.65s/it]
Training tasks:  50%|█████     | 15/30 [00:26<00:26,  1.75s/it]
Training tasks:  53%|█████▎    | 16/30 [00:28<00:25,  1.82s/it]
Training tasks:  57%|█████▋    | 17/30 [00:30<00:23,  1.84s/it]
Training tasks:  60%|██████    | 18/30 [00:32<00:22,  1.89s/it]
Training tasks:  63%|██████▎   | 19/30 [00:33<00:20,  1.90s/it]
Training tasks:  67%|██████▋   | 20/30 [00:35<00:19,  1.93s/it]
Training tasks:  70%|███████   | 21/30 [00:37<00:17,  1.94s/it]
Training tasks:  73%|███████▎  | 22/30 [00:40<00:15,  1.99s/it]
Training tasks:  77%|███████▋  | 23/30 [00:41<00:13,  1.93s/it]
Training tasks:  80%|████████  | 24/30 [00:43<00:11,  1.98s/it]
Training tasks:  83%|████████▎ | 25/30 [00:45<00:09,  1.86s/it]
Training tasks:  87%|████████▋ | 26/30 [00:47<00:07,  1.77s/it]
Training tasks:  90%|█████████ | 27/30 [00:48<00:05,  1.76s/it]
Training tasks:  93%|█████████▎| 28/30 [00:50<00:03,  1.76s/it]
Training tasks:  97%|█████████▋| 29/30 [00:52<00:01,  1.74s/it]
Training tasks: 100%|██████████| 30/30 [00:53<00:00,  1.58s/it]
Training tasks: 100%|██████████| 30/30 [00:53<00:00,  1.78s/it]

Validating tasks:   0%|          | 0/10 [00:00<?, ?it/s]
Validating tasks:  10%|█         | 1/10 [00:00<00:02,  4.07it/s]
Validating tasks:  20%|██        | 2/10 [00:00<00:01,  4.61it/s]
Validating tasks:  30%|███       | 3/10 [00:00<00:01,  4.52it/s]
Validating tasks:  40%|████      | 4/10 [00:00<00:01,  4.35it/s]
Validating tasks:  50%|█████     | 5/10 [00:01<00:01,  4.44it/s]
Validating tasks:  60%|██████    | 6/10 [00:01<00:00,  4.15it/s]
Validating tasks:  70%|███████   | 7/10 [00:01<00:00,  4.01it/s]
Validating tasks:  80%|████████  | 8/10 [00:01<00:00,  3.92it/s]
Validating tasks:  90%|█████████ | 9/10 [00:02<00:00,  3.99it/s]
Validating tasks: 100%|██████████| 10/10 [00:02<00:00,  3.94it/s]
Validating tasks: 100%|██████████| 10/10 [00:02<00:00,  4.10it/s]

Training tasks:   0%|          | 0/30 [00:00<?, ?it/s]
Training tasks:   3%|▎         | 1/30 [00:01<00:45,  1.58s/it]
Training tasks:   7%|▋         | 2/30 [00:03<00:42,  1.51s/it]
Training tasks:  10%|█         | 3/30 [00:04<00:41,  1.55s/it]
Training tasks:  13%|█▎        | 4/30 [00:06<00:40,  1.58s/it]
Training tasks:  17%|█▋        | 5/30 [00:08<00:41,  1.67s/it]
Training tasks:  20%|██        | 6/30 [00:09<00:40,  1.67s/it]
Training tasks:  23%|██▎       | 7/30 [00:11<00:38,  1.65s/it]
Training tasks:  27%|██▋       | 8/30 [00:13<00:38,  1.75s/it]
Training tasks:  30%|███       | 9/30 [00:15<00:37,  1.80s/it]
Training tasks:  33%|███▎      | 10/30 [00:16<00:34,  1.75s/it]
Training tasks:  37%|███▋      | 11/30 [00:18<00:33,  1.76s/it]
Training tasks:  40%|████      | 12/30 [00:20<00:32,  1.81s/it]
Training tasks:  43%|████▎     | 13/30 [00:22<00:32,  1.88s/it]
Training tasks:  47%|████▋     | 14/30 [00:24<00:29,  1.87s/it]
Training tasks:  50%|█████     | 15/30 [00:26<00:28,  1.92s/it]
Training tasks:  53%|█████▎    | 16/30 [00:28<00:26,  1.86s/it]
Training tasks:  57%|█████▋    | 17/30 [00:30<00:24,  1.87s/it]
Training tasks:  60%|██████    | 18/30 [00:32<00:22,  1.89s/it]
Training tasks:  63%|██████▎   | 19/30 [00:33<00:20,  1.84s/it]
Training tasks:  67%|██████▋   | 20/30 [00:35<00:17,  1.78s/it]
Training tasks:  70%|███████   | 21/30 [00:36<00:14,  1.64s/it]
Training tasks:  73%|███████▎  | 22/30 [00:38<00:12,  1.58s/it]
Training tasks:  77%|███████▋  | 23/30 [00:39<00:10,  1.44s/it]
Training tasks:  80%|████████  | 24/30 [00:40<00:08,  1.34s/it]
Training tasks:  83%|████████▎ | 25/30 [00:41<00:06,  1.37s/it]
Training tasks:  87%|████████▋ | 26/30 [00:43<00:05,  1.44s/it]
Training tasks:  90%|█████████ | 27/30 [00:45<00:04,  1.51s/it]
Training tasks:  93%|█████████▎| 28/30 [00:47<00:03,  1.66s/it]
Training tasks:  97%|█████████▋| 29/30 [00:49<00:01,  1.75s/it]
Training tasks: 100%|██████████| 30/30 [00:51<00:00,  1.81s/it]
Training tasks: 100%|██████████| 30/30 [00:51<00:00,  1.70s/it]

Validating tasks:   0%|          | 0/10 [00:00<?, ?it/s]
Validating tasks:  10%|█         | 1/10 [00:00<00:02,  3.26it/s]
Validating tasks:  20%|██        | 2/10 [00:00<00:02,  3.19it/s]
Validating tasks:  30%|███       | 3/10 [00:00<00:02,  3.21it/s]
Validating tasks:  40%|████      | 4/10 [00:01<00:01,  3.22it/s]
Validating tasks:  50%|█████     | 5/10 [00:01<00:01,  3.17it/s]
Validating tasks:  60%|██████    | 6/10 [00:01<00:01,  3.10it/s]
Validating tasks:  70%|███████   | 7/10 [00:02<00:01,  2.99it/s]
Validating tasks:  80%|████████  | 8/10 [00:02<00:00,  3.16it/s]
Validating tasks:  90%|█████████ | 9/10 [00:02<00:00,  3.29it/s]
Validating tasks: 100%|██████████| 10/10 [00:03<00:00,  3.38it/s]
Validating tasks: 100%|██████████| 10/10 [00:03<00:00,  3.23it/s]

Training tasks:   0%|          | 0/30 [00:00<?, ?it/s]
Training tasks:   3%|▎         | 1/30 [00:02<00:58,  2.01s/it]
Training tasks:   7%|▋         | 2/30 [00:03<00:53,  1.90s/it]
Training tasks:  10%|█         | 3/30 [00:05<00:53,  2.00s/it]
Training tasks:  13%|█▎        | 4/30 [00:07<00:49,  1.90s/it]
Training tasks:  17%|█▋        | 5/30 [00:09<00:44,  1.78s/it]
Training tasks:  20%|██        | 6/30 [00:10<00:41,  1.71s/it]
Training tasks:  23%|██▎       | 7/30 [00:12<00:39,  1.70s/it]
Training tasks:  27%|██▋       | 8/30 [00:14<00:36,  1.65s/it]
Training tasks:  30%|███       | 9/30 [00:15<00:34,  1.62s/it]
Training tasks:  33%|███▎      | 10/30 [00:17<00:32,  1.64s/it]
Training tasks:  37%|███▋      | 11/30 [00:19<00:31,  1.67s/it]
Training tasks:  40%|████      | 12/30 [00:20<00:31,  1.72s/it]
Training tasks:  43%|████▎     | 13/30 [00:22<00:29,  1.76s/it]
Training tasks:  47%|████▋     | 14/30 [00:24<00:26,  1.68s/it]
Training tasks:  50%|█████     | 15/30 [00:25<00:22,  1.51s/it]
Training tasks:  53%|█████▎    | 16/30 [00:26<00:19,  1.39s/it]
Training tasks:  57%|█████▋    | 17/30 [00:28<00:19,  1.52s/it]
Training tasks:  60%|██████    | 18/30 [00:29<00:18,  1.58s/it]
Training tasks:  63%|██████▎   | 19/30 [00:31<00:17,  1.57s/it]
Training tasks:  67%|██████▋   | 20/30 [00:33<00:16,  1.66s/it]
Training tasks:  70%|███████   | 21/30 [00:35<00:15,  1.72s/it]
Training tasks:  73%|███████▎  | 22/30 [00:37<00:14,  1.81s/it]
Training tasks:  77%|███████▋  | 23/30 [00:39<00:12,  1.85s/it]
Training tasks:  80%|████████  | 24/30 [00:41<00:11,  1.86s/it]
Training tasks:  83%|████████▎ | 25/30 [00:42<00:08,  1.79s/it]
Training tasks:  87%|████████▋ | 26/30 [00:44<00:07,  1.86s/it]
Training tasks:  90%|█████████ | 27/30 [00:46<00:05,  1.88s/it]
Training tasks:  93%|█████████▎| 28/30 [00:48<00:03,  1.89s/it]
Training tasks:  97%|█████████▋| 29/30 [00:50<00:01,  1.90s/it]
Training tasks: 100%|██████████| 30/30 [00:52<00:00,  1.94s/it]
Training tasks: 100%|██████████| 30/30 [00:52<00:00,  1.75s/it]

Validating tasks:   0%|          | 0/10 [00:00<?, ?it/s]
Validating tasks:  10%|█         | 1/10 [00:00<00:02,  3.41it/s]
Validating tasks:  20%|██        | 2/10 [00:00<00:02,  3.36it/s]
Validating tasks:  30%|███       | 3/10 [00:00<00:01,  3.70it/s]
Validating tasks:  40%|████      | 4/10 [00:01<00:01,  3.91it/s]
Validating tasks:  50%|█████     | 5/10 [00:01<00:01,  4.01it/s]
Validating tasks:  60%|██████    | 6/10 [00:01<00:00,  4.06it/s]
Validating tasks:  70%|███████   | 7/10 [00:01<00:00,  4.10it/s]
Validating tasks:  80%|████████  | 8/10 [00:02<00:00,  4.09it/s]
Validating tasks:  90%|█████████ | 9/10 [00:02<00:00,  3.99it/s]
Validating tasks: 100%|██████████| 10/10 [00:02<00:00,  4.00it/s]
Validating tasks: 100%|██████████| 10/10 [00:02<00:00,  3.93it/s]

Training tasks:   0%|          | 0/30 [00:00<?, ?it/s]
Training tasks:   3%|▎         | 1/30 [00:01<00:50,  1.75s/it]
Training tasks:   7%|▋         | 2/30 [00:03<00:52,  1.89s/it]
Training tasks:  10%|█         | 3/30 [00:05<00:52,  1.94s/it]
Training tasks:  13%|█▎        | 4/30 [00:07<00:49,  1.89s/it]
Training tasks:  17%|█▋        | 5/30 [00:09<00:48,  1.95s/it]
Training tasks:  20%|██        | 6/30 [00:11<00:48,  2.00s/it]
Training tasks:  23%|██▎       | 7/30 [00:13<00:41,  1.81s/it]
Training tasks:  27%|██▋       | 8/30 [00:14<00:38,  1.77s/it]
Training tasks:  30%|███       | 9/30 [00:16<00:37,  1.79s/it]
Training tasks:  33%|███▎      | 10/30 [00:18<00:36,  1.84s/it]
Training tasks:  37%|███▋      | 11/30 [00:20<00:34,  1.83s/it]
Training tasks:  40%|████      | 12/30 [00:22<00:34,  1.90s/it]
Training tasks:  43%|████▎     | 13/30 [00:24<00:31,  1.86s/it]
Training tasks:  47%|████▋     | 14/30 [00:25<00:29,  1.83s/it]
Training tasks:  50%|█████     | 15/30 [00:27<00:27,  1.82s/it]
Training tasks:  53%|█████▎    | 16/30 [00:29<00:25,  1.84s/it]
Training tasks:  57%|█████▋    | 17/30 [00:31<00:23,  1.82s/it]
Training tasks:  60%|██████    | 18/30 [00:33<00:22,  1.88s/it]
Training tasks:  63%|██████▎   | 19/30 [00:35<00:21,  1.94s/it]
Training tasks:  67%|██████▋   | 20/30 [00:37<00:19,  1.92s/it]
Training tasks:  70%|███████   | 21/30 [00:39<00:17,  1.90s/it]
Training tasks:  73%|███████▎  | 22/30 [00:40<00:14,  1.81s/it]
Training tasks:  77%|███████▋  | 23/30 [00:42<00:12,  1.76s/it]
Training tasks:  80%|████████  | 24/30 [00:44<00:10,  1.75s/it]
Training tasks:  83%|████████▎ | 25/30 [00:46<00:08,  1.77s/it]
Training tasks:  87%|████████▋ | 26/30 [00:48<00:07,  1.87s/it]
Training tasks:  90%|█████████ | 27/30 [00:50<00:05,  1.94s/it]
Training tasks:  93%|█████████▎| 28/30 [00:52<00:03,  1.90s/it]
Training tasks:  97%|█████████▋| 29/30 [00:53<00:01,  1.80s/it]
Training tasks: 100%|██████████| 30/30 [00:54<00:00,  1.66s/it]
Training tasks: 100%|██████████| 30/30 [00:54<00:00,  1.83s/it]

Validating tasks:   0%|          | 0/10 [00:00<?, ?it/s]
Validating tasks:  10%|█         | 1/10 [00:00<00:01,  4.53it/s]
Validating tasks:  20%|██        | 2/10 [00:00<00:01,  4.63it/s]
Validating tasks:  30%|███       | 3/10 [00:00<00:01,  4.34it/s]
Validating tasks:  40%|████      | 4/10 [00:00<00:01,  4.06it/s]
Validating tasks:  50%|█████     | 5/10 [00:01<00:01,  3.73it/s]
Validating tasks:  60%|██████    | 6/10 [00:01<00:01,  3.59it/s]
Validating tasks:  70%|███████   | 7/10 [00:01<00:00,  3.66it/s]
Validating tasks:  80%|████████  | 8/10 [00:02<00:00,  3.74it/s]
Validating tasks:  90%|█████████ | 9/10 [00:02<00:00,  3.79it/s]
Validating tasks: 100%|██████████| 10/10 [00:02<00:00,  3.68it/s]
Validating tasks: 100%|██████████| 10/10 [00:02<00:00,  3.82it/s]

Testing tasks:   0%|          | 0/30 [00:00<?, ?it/s]
Testing tasks:   3%|▎         | 1/30 [00:00<00:11,  2.44it/s]
Testing tasks:   7%|▋         | 2/30 [00:00<00:10,  2.58it/s]
Testing tasks:  10%|█         | 3/30 [00:01<00:10,  2.50it/s]
Testing tasks:  13%|█▎        | 4/30 [00:01<00:10,  2.55it/s]
Testing tasks:  17%|█▋        | 5/30 [00:01<00:09,  2.57it/s]
Testing tasks:  20%|██        | 6/30 [00:02<00:08,  2.84it/s]
Testing tasks:  23%|██▎       | 7/30 [00:02<00:07,  2.95it/s]
Testing tasks:  27%|██▋       | 8/30 [00:02<00:07,  2.79it/s]
Testing tasks:  30%|███       | 9/30 [00:03<00:07,  2.67it/s]
Testing tasks:  33%|███▎      | 10/30 [00:03<00:07,  2.80it/s]
Testing tasks:  37%|███▋      | 11/30 [00:04<00:06,  2.79it/s]
Testing tasks:  40%|████      | 12/30 [00:04<00:06,  2.86it/s]
Testing tasks:  43%|████▎     | 13/30 [00:04<00:05,  2.89it/s]
Testing tasks:  47%|████▋     | 14/30 [00:05<00:05,  2.96it/s]
Testing tasks:  50%|█████     | 15/30 [00:05<00:05,  2.95it/s]
Testing tasks:  53%|█████▎    | 16/30 [00:05<00:04,  2.98it/s]
Testing tasks:  57%|█████▋    | 17/30 [00:06<00:04,  3.01it/s]
Testing tasks:  60%|██████    | 18/30 [00:06<00:04,  2.91it/s]
Testing tasks:  63%|██████▎   | 19/30 [00:06<00:03,  2.78it/s]
Testing tasks:  67%|██████▋   | 20/30 [00:07<00:03,  2.79it/s]
Testing tasks:  70%|███████   | 21/30 [00:07<00:03,  2.72it/s]
Testing tasks:  73%|███████▎  | 22/30 [00:07<00:02,  2.69it/s]
Testing tasks:  77%|███████▋  | 23/30 [00:08<00:02,  2.65it/s]
Testing tasks:  80%|████████  | 24/30 [00:08<00:02,  2.80it/s]
Testing tasks:  83%|████████▎ | 25/30 [00:08<00:01,  2.82it/s]
Testing tasks:  87%|████████▋ | 26/30 [00:09<00:01,  2.96it/s]
Testing tasks:  90%|█████████ | 27/30 [00:09<00:00,  3.01it/s]
Testing tasks:  93%|█████████▎| 28/30 [00:09<00:00,  3.04it/s]
Testing tasks:  97%|█████████▋| 29/30 [00:10<00:00,  2.86it/s]
Testing tasks: 100%|██████████| 30/30 [00:10<00:00,  2.90it/s]
Testing tasks: 100%|██████████| 30/30 [00:10<00:00,  2.82it/s]

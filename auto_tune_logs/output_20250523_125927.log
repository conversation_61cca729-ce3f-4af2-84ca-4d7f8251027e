STDOUT:
===================== 训练配置 =====================
GPU: 0
输出目录: /home/<USER>/balanced_task_sampling/results_trial_20250523_125927
日志文件: /home/<USER>/balanced_task_sampling/results_trial_20250523_125927/main_training_trial_13_20250523_125927.log

--------- 优化参数 ---------
学习率: 0.0003645187414302882
内部学习率: 0.347389
训练轮数: 50
每轮任务数: 30

--------- 模型参数 ---------
使用类别权重: 圆锥角膜=3.959217742292199, 早期圆锥角膜=7.939627382451082
温度参数: 0.044411613454426346
硬样本挖掘比例: 0.7043331220120156
自定义惩罚权重: 0.0020626204511044787
自定义惩罚边界: 0.451723150444279
对比学习权重: 0.7928219608514536
=============================================

创建目录: /home/<USER>/balanced_task_sampling/results_trial_20250523_125927
总用量 4
-rw-rw-r-- 1 <USER> <GROUP> 777 5月  23 12:59 main_training_trial_13_20250523_125927.log
开始无数据增强的平衡任务采样训练...
使用GPU: 0
直接运行训练脚本...
成功导入增强版特征提取器
成功导入归一化特征提取器
成功导入增强版损失函数
成功导入增强版MAML模型
成功导入KC特定增强模块
成功导入增强版GPU数据增强模块
成功导入增强版KC数据增强模块
使用设备: cuda
禁用所有数据增强，直接加载原始数据集...
使用平衡任务采样器
类别 normal (标签 0): 164个样本
类别 e-kc (标签 1): 26个样本
类别 kc (标签 2): 133个样本
使用原型配置: [2, 4, 2]
使用增强版特征提取器，dropout率: 0.5
使用增强版MAML-ProtoNet模型，内循环学习率: 0.347389, 内循环步数: 5
使用CosineAnnealingLR学习率调度器，T_max: 50, eta_min: 1e-07
开始训练，共50个epoch
Epoch 1/50
当前学习率: 0.000365
使用类别权重: 正常=1.0, 早期圆锥角膜=7.939627382451082, 圆锥角膜=3.959217742292199
使用增强版对比损失函数，温度: 0.044411613454426346, 硬负样本挖掘比例: 0.7043331220120156, 早期圆锥角膜与正常样本对比权重: 0.0020626204511044787
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
警告: 没有足够的有效类别进行3-way任务采样，尝试调整参数...
类别样本数量: {0: 33, 1: 5, 2: 29}
调整后的参数: n_shot=2, n_query=3
类别 normal (标签 0): 33个样本
类别 e-kc (标签 1): 5个样本
类别 kc (标签 2): 29个样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
Epoch 1: Train Loss: 1.6637, Train Acc: 0.5933, Val Loss: 1.5649, Val Acc: 0.3556
保存最佳模型，验证准确率: 0.3556
Epoch 2/50
当前学习率: 0.000365
使用类别权重: 正常=1.0, 早期圆锥角膜=7.939627382451082, 圆锥角膜=3.959217742292199
使用增强版对比损失函数，温度: 0.044411613454426346, 硬负样本挖掘比例: 0.7043331220120156, 早期圆锥角膜与正常样本对比权重: 0.0020626204511044787
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
警告: 没有足够的有效类别进行3-way任务采样，尝试调整参数...
类别样本数量: {0: 33, 1: 5, 2: 29}
调整后的参数: n_shot=2, n_query=3
类别 normal (标签 0): 33个样本
类别 e-kc (标签 1): 5个样本
类别 kc (标签 2): 29个样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
Epoch 2: Train Loss: 1.4343, Train Acc: 0.6711, Val Loss: 1.2701, Val Acc: 0.5778
保存最佳模型，验证准确率: 0.5778
Epoch 3/50
当前学习率: 0.000365
使用类别权重: 正常=1.0, 早期圆锥角膜=7.939627382451082, 圆锥角膜=3.959217742292199
使用增强版对比损失函数，温度: 0.044411613454426346, 硬负样本挖掘比例: 0.7043331220120156, 早期圆锥角膜与正常样本对比权重: 0.0020626204511044787
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
警告: 没有足够的有效类别进行3-way任务采样，尝试调整参数...
类别样本数量: {0: 33, 1: 5, 2: 29}
调整后的参数: n_shot=2, n_query=3
类别 normal (标签 0): 33个样本
类别 e-kc (标签 1): 5个样本
类别 kc (标签 2): 29个样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
Epoch 3: Train Loss: 1.4620, Train Acc: 0.6793, Val Loss: 1.5361, Val Acc: 0.5556
验证准确率未提高，当前耐心值: 1/3
Epoch 4/50
当前学习率: 0.000365
使用类别权重: 正常=1.0, 早期圆锥角膜=7.939627382451082, 圆锥角膜=3.959217742292199
使用增强版对比损失函数，温度: 0.044411613454426346, 硬负样本挖掘比例: 0.7043331220120156, 早期圆锥角膜与正常样本对比权重: 0.0020626204511044787
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
警告: 没有足够的有效类别进行3-way任务采样，尝试调整参数...
类别样本数量: {0: 33, 1: 5, 2: 29}
调整后的参数: n_shot=2, n_query=3
类别 normal (标签 0): 33个样本
类别 e-kc (标签 1): 5个样本
类别 kc (标签 2): 29个样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
Epoch 4: Train Loss: 1.2138, Train Acc: 0.7556, Val Loss: 1.3625, Val Acc: 0.5333
验证准确率未提高，当前耐心值: 2/3
Epoch 5/50
当前学习率: 0.000365
使用类别权重: 正常=1.0, 早期圆锥角膜=7.939627382451082, 圆锥角膜=3.959217742292199
使用增强版对比损失函数，温度: 0.044411613454426346, 硬负样本挖掘比例: 0.7043331220120156, 早期圆锥角膜与正常样本对比权重: 0.0020626204511044787
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 12 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
警告: 没有足够的有效类别进行3-way任务采样，尝试调整参数...
类别样本数量: {0: 33, 1: 5, 2: 29}
调整后的参数: n_shot=2, n_query=3
类别 normal (标签 0): 33个样本
类别 e-kc (标签 1): 5个样本
类别 kc (标签 2): 29个样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
Epoch 5: Train Loss: 0.9294, Train Acc: 0.8104, Val Loss: 1.6525, Val Acc: 0.5000
验证准确率未提高，当前耐心值: 3/3
早停触发，停止训练
警告: 没有足够的有效类别进行3-way任务采样，尝试调整参数...
类别样本数量: {0: 37, 1: 7, 2: 27}
调整后的参数: n_shot=3, n_query=4
类别 normal (标签 0): 37个样本
类别 e-kc (标签 1): 7个样本
类别 kc (标签 2): 27个样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本

测试结果:
总体准确率: 60.00%
类别准确率:
  kc: 5.83%
  e-kc: 100.00%
  normal: 74.17%
训练完成，结果已保存到 /home/<USER>/balanced_task_sampling/results_trial_20250523_125927

STDERR:
+ TIMESTAMP=20250523_125927
+ RESULT_DIR=/home/<USER>/balanced_task_sampling/results_trial_20250523_125927
+ '[' -n /home/<USER>/balanced_task_sampling/results_trial_20250523_125927/main_training_trial_13_20250523_125927.log ']'
+ LOG_FILE=/home/<USER>/balanced_task_sampling/results_trial_20250523_125927/main_training_trial_13_20250523_125927.log
++ dirname /home/<USER>/balanced_task_sampling/results_trial_20250523_125927/main_training_trial_13_20250523_125927.log
+ mkdir -p /home/<USER>/balanced_task_sampling/results_trial_20250523_125927
+ mkdir -p /home/<USER>/balanced_task_sampling/results_trial_20250523_125927
+ echo '===================== 训练配置 ====================='
+ echo 'GPU: 0'
+ echo '输出目录: /home/<USER>/balanced_task_sampling/results_trial_20250523_125927'
+ echo '日志文件: /home/<USER>/balanced_task_sampling/results_trial_20250523_125927/main_training_trial_13_20250523_125927.log'
+ echo ''
+ echo '--------- 优化参数 ---------'
+ echo '学习率: 0.0003645187414302882'
+ echo '内部学习率: 0.347389'
+ echo '训练轮数: 50'
+ echo '每轮任务数: 30'
+ echo ''
+ echo '--------- 模型参数 ---------'
+ echo '使用类别权重: 圆锥角膜=3.959217742292199, 早期圆锥角膜=7.939627382451082'
+ echo '温度参数: 0.044411613454426346'
+ tee -a /home/<USER>/balanced_task_sampling/results_trial_20250523_125927/main_training_trial_13_20250523_125927.log
+ echo '硬样本挖掘比例: 0.7043331220120156'
+ echo '自定义惩罚权重: 0.0020626204511044787'
+ echo '自定义惩罚边界: 0.451723150444279'
+ echo '对比学习权重: 0.7928219608514536'
+ echo '=============================================
'
+ validate_params
+ local error_count=0
++ dirname /home/<USER>/balanced_task_sampling/results_trial_20250523_125927/main_training_trial_13_20250523_125927.log
++ dirname /home/<USER>/balanced_task_sampling/split_result/train_set.csv
+ for dir in "$RESULT_DIR" "$(dirname "$LOG_FILE")" "$(dirname "$TRAIN_CSV")"
+ mkdir -p /home/<USER>/balanced_task_sampling/results_trial_20250523_125927
+ for dir in "$RESULT_DIR" "$(dirname "$LOG_FILE")" "$(dirname "$TRAIN_CSV")"
+ mkdir -p /home/<USER>/balanced_task_sampling/results_trial_20250523_125927
+ for dir in "$RESULT_DIR" "$(dirname "$LOG_FILE")" "$(dirname "$TRAIN_CSV")"
+ mkdir -p /home/<USER>/balanced_task_sampling/split_result
+ for csv in "$TRAIN_CSV" "$VAL_CSV" "$TEST_CSV"
+ '[' '!' -f /home/<USER>/balanced_task_sampling/split_result/train_set.csv ']'
+ for csv in "$TRAIN_CSV" "$VAL_CSV" "$TEST_CSV"
+ '[' '!' -f /home/<USER>/balanced_task_sampling/split_result/val_set.csv ']'
+ for csv in "$TRAIN_CSV" "$VAL_CSV" "$TEST_CSV"
+ '[' '!' -f /home/<USER>/balanced_task_sampling/split_result/test_set.csv ']'
+ [[ 0.0003645187414302882 =~ ^[0-9.]+$ ]]
+ [[ 0.347389 =~ ^[0-9.]+$ ]]
+ '[' 0 -gt 0 ']'
+ return 0
+ '[' false = true ']'
+ export CUDA_VISIBLE_DEVICES=0
+ CUDA_VISIBLE_DEVICES=0
+ export OMP_NUM_THREADS=1
+ OMP_NUM_THREADS=1
+ export MKL_NUM_THREADS=1
+ MKL_NUM_THREADS=1
+ export MPLBACKEND=Agg
+ MPLBACKEND=Agg
+ mkdir -p /home/<USER>/balanced_task_sampling/results_trial_20250523_125927
+ echo '创建目录: /home/<USER>/balanced_task_sampling/results_trial_20250523_125927'
+ ls -l /home/<USER>/balanced_task_sampling/results_trial_20250523_125927
+ PRETRAINED_ARGS=
+ '[' true = true ']'
+ PRETRAINED_ARGS=--pretrained
+ AUGMENT_ARGS=--no_augmentation
+ CONTRASTIVE_ARGS=
+ '[' true = true ']'
+ CONTRASTIVE_ARGS='--use_contrastive --contrastive_weight 0.7928219608514536 --temperature 0.044411613454426346 --hard_mining_ratio 0.7043331220120156 --early_normal_weight 0.0020626204511044787'
+ NORMALIZE_ARGS=
+ '[' false = true ']'
+ SAMPLER_ARGS=
+ '[' true = true ']'
+ SAMPLER_ARGS=' --use_separate_test_sampler'
+ '[' true = true ']'
+ SAMPLER_ARGS=' --use_separate_test_sampler --use_balanced_task_sampler --kc_shot_multiplier 2.5639736609095722 --early_kc_shot_multiplier 1.1944983515975605'
+ LR_SCHEDULER_ARGS=
+ '[' true = true ']'
+ LR_SCHEDULER_ARGS='--use_lr_scheduler --scheduler_type cosine_annealing'
+ '[' cosine_annealing = onecycle ']'
+ '[' cosine_annealing = plateau ']'
+ '[' cosine_annealing = cosine_annealing ']'
+ LR_SCHEDULER_ARGS='--use_lr_scheduler --scheduler_type cosine_annealing --cosine_t_max 50 --cosine_eta_min 1e-7'
+ BASE_CMD='python train_balanced_task_sampling.py   --train_csv /home/<USER>/balanced_task_sampling/split_result/train_set.csv   --val_csv /home/<USER>/balanced_task_sampling/split_result/val_set.csv   --test_csv /home/<USER>/balanced_task_sampling/split_result/test_set.csv   --epochs 50   --lr 0.0003645187414302882   --weight_decay 5e-4   --feature_dim 512   --save_dir /home/<USER>/balanced_task_sampling/results_trial_20250523_125927   --proto_counts 2,4,2   --pretrained   --inner_lr 0.347389   --inner_steps 5   --n_way 3   --n_shot 5   --n_query 15   --tasks_per_epoch 30   --meta_batch_size 8   --no_augmentation   --mixup_alpha 0.0   --cutmix_prob 0.0   --num_workers 4   --augment_factor 1   --kc_augment_factor 1   --seed 42   --early_kc_weight 7.939627382451082   --kc_weight 3.959217742292199   --focal_gamma 2.409854165203021   --val_frequency 1   --early_stopping 3   --dropout 0.5    --use_separate_test_sampler --use_balanced_task_sampler --kc_shot_multiplier 2.5639736609095722 --early_kc_shot_multiplier 1.1944983515975605   --use_contrastive --contrastive_weight 0.7928219608514536 --temperature 0.044411613454426346 --hard_mining_ratio 0.7043331220120156 --early_normal_weight 0.0020626204511044787      --custom_penalty_margin 0.451723150444279   --custom_penalty_weight 0.0020626204511044787   --use_lr_scheduler --scheduler_type cosine_annealing --cosine_t_max 50 --cosine_eta_min 1e-7'
+ echo 开始无数据增强的平衡任务采样训练...
+ echo '使用GPU: 0'
+ '[' direct = nohup ']'
+ echo 直接运行训练脚本...
+ eval python train_balanced_task_sampling.py --train_csv /home/<USER>/balanced_task_sampling/split_result/train_set.csv --val_csv /home/<USER>/balanced_task_sampling/split_result/val_set.csv --test_csv /home/<USER>/balanced_task_sampling/split_result/test_set.csv --epochs 50 --lr 0.0003645187414302882 --weight_decay 5e-4 --feature_dim 512 --save_dir /home/<USER>/balanced_task_sampling/results_trial_20250523_125927 --proto_counts 2,4,2 --pretrained --inner_lr 0.347389 --inner_steps 5 --n_way 3 --n_shot 5 --n_query 15 --tasks_per_epoch 30 --meta_batch_size 8 --no_augmentation --mixup_alpha 0.0 --cutmix_prob 0.0 --num_workers 4 --augment_factor 1 --kc_augment_factor 1 --seed 42 --early_kc_weight 7.939627382451082 --kc_weight 3.959217742292199 --focal_gamma 2.409854165203021 --val_frequency 1 --early_stopping 3 --dropout 0.5 --use_separate_test_sampler --use_balanced_task_sampler --kc_shot_multiplier 2.5639736609095722 --early_kc_shot_multiplier 1.1944983515975605 --use_contrastive --contrastive_weight 0.7928219608514536 --temperature 0.044411613454426346 --hard_mining_ratio 0.7043331220120156 --early_normal_weight 0.0020626204511044787 --custom_penalty_margin 0.451723150444279 --custom_penalty_weight 0.0020626204511044787 --use_lr_scheduler --scheduler_type cosine_annealing --cosine_t_max 50 --cosine_eta_min 1e-7
++ python train_balanced_task_sampling.py --train_csv /home/<USER>/balanced_task_sampling/split_result/train_set.csv --val_csv /home/<USER>/balanced_task_sampling/split_result/val_set.csv --test_csv /home/<USER>/balanced_task_sampling/split_result/test_set.csv --epochs 50 --lr 0.0003645187414302882 --weight_decay 5e-4 --feature_dim 512 --save_dir /home/<USER>/balanced_task_sampling/results_trial_20250523_125927 --proto_counts 2,4,2 --pretrained --inner_lr 0.347389 --inner_steps 5 --n_way 3 --n_shot 5 --n_query 15 --tasks_per_epoch 30 --meta_batch_size 8 --no_augmentation --mixup_alpha 0.0 --cutmix_prob 0.0 --num_workers 4 --augment_factor 1 --kc_augment_factor 1 --seed 42 --early_kc_weight 7.939627382451082 --kc_weight 3.959217742292199 --focal_gamma 2.409854165203021 --val_frequency 1 --early_stopping 3 --dropout 0.5 --use_separate_test_sampler --use_balanced_task_sampler --kc_shot_multiplier 2.5639736609095722 --early_kc_shot_multiplier 1.1944983515975605 --use_contrastive --contrastive_weight 0.7928219608514536 --temperature 0.044411613454426346 --hard_mining_ratio 0.7043331220120156 --early_normal_weight 0.0020626204511044787 --custom_penalty_margin 0.451723150444279 --custom_penalty_weight 0.0020626204511044787 --use_lr_scheduler --scheduler_type cosine_annealing --cosine_t_max 50 --cosine_eta_min 1e-7
/home/<USER>/anaconda3/envs/kc1/lib/python3.10/site-packages/torchvision/models/_utils.py:208: UserWarning: The parameter 'pretrained' is deprecated since 0.13 and may be removed in the future, please use 'weights' instead.
  warnings.warn(
/home/<USER>/anaconda3/envs/kc1/lib/python3.10/site-packages/torchvision/models/_utils.py:223: UserWarning: Arguments other than a weight enum or `None` for 'weights' are deprecated since 0.13 and may be removed in the future. The current behavior is equivalent to passing `weights=ResNet34_Weights.IMAGENET1K_V1`. You can also use `weights=ResNet34_Weights.DEFAULT` to get the most up-to-date weights.
  warnings.warn(msg)

Training tasks:   0%|          | 0/30 [00:00<?, ?it/s]
Training tasks:   3%|▎         | 1/30 [00:07<03:38,  7.52s/it]
Training tasks:   7%|▋         | 2/30 [00:09<01:55,  4.12s/it]
Training tasks:  10%|█         | 3/30 [00:10<01:19,  2.95s/it]
Training tasks:  13%|█▎        | 4/30 [00:12<01:02,  2.41s/it]
Training tasks:  17%|█▋        | 5/30 [00:14<00:53,  2.15s/it]
Training tasks:  20%|██        | 6/30 [00:15<00:48,  2.03s/it]
Training tasks:  23%|██▎       | 7/30 [00:17<00:42,  1.84s/it]
Training tasks:  27%|██▋       | 8/30 [00:18<00:37,  1.70s/it]
Training tasks:  30%|███       | 9/30 [00:20<00:33,  1.62s/it]
Training tasks:  33%|███▎      | 10/30 [00:21<00:31,  1.59s/it]
Training tasks:  37%|███▋      | 11/30 [00:23<00:30,  1.62s/it]
Training tasks:  40%|████      | 12/30 [00:25<00:30,  1.67s/it]
Training tasks:  43%|████▎     | 13/30 [00:26<00:28,  1.68s/it]
Training tasks:  47%|████▋     | 14/30 [00:28<00:26,  1.65s/it]
Training tasks:  50%|█████     | 15/30 [00:29<00:23,  1.58s/it]
Training tasks:  53%|█████▎    | 16/30 [00:31<00:21,  1.56s/it]
Training tasks:  57%|█████▋    | 17/30 [00:32<00:19,  1.52s/it]
Training tasks:  60%|██████    | 18/30 [00:34<00:19,  1.58s/it]
Training tasks:  63%|██████▎   | 19/30 [00:36<00:17,  1.59s/it]
Training tasks:  67%|██████▋   | 20/30 [00:37<00:15,  1.56s/it]
Training tasks:  70%|███████   | 21/30 [00:39<00:14,  1.56s/it]
Training tasks:  73%|███████▎  | 22/30 [00:40<00:12,  1.53s/it]
Training tasks:  77%|███████▋  | 23/30 [00:42<00:10,  1.50s/it]
Training tasks:  80%|████████  | 24/30 [00:43<00:08,  1.45s/it]
Training tasks:  83%|████████▎ | 25/30 [00:45<00:07,  1.51s/it]
Training tasks:  87%|████████▋ | 26/30 [00:46<00:06,  1.58s/it]
Training tasks:  90%|█████████ | 27/30 [00:48<00:04,  1.61s/it]
Training tasks:  93%|█████████▎| 28/30 [00:50<00:03,  1.59s/it]
Training tasks:  97%|█████████▋| 29/30 [00:51<00:01,  1.53s/it]
Training tasks: 100%|██████████| 30/30 [00:52<00:00,  1.50s/it]
Training tasks: 100%|██████████| 30/30 [00:52<00:00,  1.76s/it]

Validating tasks:   0%|          | 0/10 [00:00<?, ?it/s]
Validating tasks:  10%|█         | 1/10 [00:00<00:01,  4.50it/s]
Validating tasks:  20%|██        | 2/10 [00:00<00:01,  4.74it/s]
Validating tasks:  30%|███       | 3/10 [00:00<00:01,  4.67it/s]
Validating tasks:  40%|████      | 4/10 [00:00<00:01,  4.32it/s]
Validating tasks:  50%|█████     | 5/10 [00:01<00:01,  4.38it/s]
Validating tasks:  60%|██████    | 6/10 [00:01<00:00,  4.34it/s]
Validating tasks:  70%|███████   | 7/10 [00:01<00:00,  4.51it/s]
Validating tasks:  80%|████████  | 8/10 [00:01<00:00,  4.53it/s]
Validating tasks:  90%|█████████ | 9/10 [00:02<00:00,  4.47it/s]
Validating tasks: 100%|██████████| 10/10 [00:02<00:00,  4.77it/s]
Validating tasks: 100%|██████████| 10/10 [00:02<00:00,  4.57it/s]

Training tasks:   0%|          | 0/30 [00:00<?, ?it/s]
Training tasks:   3%|▎         | 1/30 [00:01<00:45,  1.59s/it]
Training tasks:   7%|▋         | 2/30 [00:03<00:43,  1.54s/it]
Training tasks:  10%|█         | 3/30 [00:04<00:41,  1.54s/it]
Training tasks:  13%|█▎        | 4/30 [00:06<00:41,  1.60s/it]
Training tasks:  17%|█▋        | 5/30 [00:07<00:39,  1.57s/it]
Training tasks:  20%|██        | 6/30 [00:09<00:37,  1.57s/it]
Training tasks:  23%|██▎       | 7/30 [00:10<00:36,  1.58s/it]
Training tasks:  27%|██▋       | 8/30 [00:12<00:34,  1.57s/it]
Training tasks:  30%|███       | 9/30 [00:13<00:31,  1.49s/it]
Training tasks:  33%|███▎      | 10/30 [00:15<00:28,  1.45s/it]
Training tasks:  37%|███▋      | 11/30 [00:16<00:27,  1.47s/it]
Training tasks:  40%|████      | 12/30 [00:18<00:26,  1.47s/it]
Training tasks:  43%|████▎     | 13/30 [00:19<00:25,  1.49s/it]
Training tasks:  47%|████▋     | 14/30 [00:21<00:25,  1.57s/it]
Training tasks:  50%|█████     | 15/30 [00:22<00:22,  1.48s/it]
Training tasks:  53%|█████▎    | 16/30 [00:24<00:20,  1.43s/it]
Training tasks:  57%|█████▋    | 17/30 [00:25<00:17,  1.37s/it]
Training tasks:  60%|██████    | 18/30 [00:26<00:16,  1.35s/it]
Training tasks:  63%|██████▎   | 19/30 [00:28<00:15,  1.37s/it]
Training tasks:  67%|██████▋   | 20/30 [00:29<00:14,  1.46s/it]
Training tasks:  70%|███████   | 21/30 [00:31<00:13,  1.48s/it]
Training tasks:  73%|███████▎  | 22/30 [00:32<00:11,  1.49s/it]
Training tasks:  77%|███████▋  | 23/30 [00:34<00:10,  1.50s/it]
Training tasks:  80%|████████  | 24/30 [00:35<00:08,  1.49s/it]
Training tasks:  83%|████████▎ | 25/30 [00:37<00:07,  1.54s/it]
Training tasks:  87%|████████▋ | 26/30 [00:38<00:06,  1.51s/it]
Training tasks:  90%|█████████ | 27/30 [00:40<00:04,  1.53s/it]
Training tasks:  93%|█████████▎| 28/30 [00:42<00:03,  1.59s/it]
Training tasks:  97%|█████████▋| 29/30 [00:43<00:01,  1.55s/it]
Training tasks: 100%|██████████| 30/30 [00:45<00:00,  1.54s/it]
Training tasks: 100%|██████████| 30/30 [00:45<00:00,  1.50s/it]

Validating tasks:   0%|          | 0/10 [00:00<?, ?it/s]
Validating tasks:  10%|█         | 1/10 [00:00<00:02,  4.46it/s]
Validating tasks:  20%|██        | 2/10 [00:00<00:01,  4.64it/s]
Validating tasks:  30%|███       | 3/10 [00:00<00:01,  4.56it/s]
Validating tasks:  40%|████      | 4/10 [00:00<00:01,  4.73it/s]
Validating tasks:  50%|█████     | 5/10 [00:01<00:00,  5.10it/s]
Validating tasks:  60%|██████    | 6/10 [00:01<00:00,  4.88it/s]
Validating tasks:  70%|███████   | 7/10 [00:01<00:00,  4.83it/s]
Validating tasks:  80%|████████  | 8/10 [00:01<00:00,  4.79it/s]
Validating tasks:  90%|█████████ | 9/10 [00:01<00:00,  4.64it/s]
Validating tasks: 100%|██████████| 10/10 [00:02<00:00,  4.62it/s]
Validating tasks: 100%|██████████| 10/10 [00:02<00:00,  4.71it/s]

Training tasks:   0%|          | 0/30 [00:00<?, ?it/s]
Training tasks:   3%|▎         | 1/30 [00:01<00:48,  1.68s/it]
Training tasks:   7%|▋         | 2/30 [00:03<00:46,  1.66s/it]
Training tasks:  10%|█         | 3/30 [00:05<00:45,  1.67s/it]
Training tasks:  13%|█▎        | 4/30 [00:06<00:42,  1.65s/it]
Training tasks:  17%|█▋        | 5/30 [00:08<00:39,  1.57s/it]
Training tasks:  20%|██        | 6/30 [00:09<00:37,  1.55s/it]
Training tasks:  23%|██▎       | 7/30 [00:11<00:35,  1.55s/it]
Training tasks:  27%|██▋       | 8/30 [00:12<00:35,  1.62s/it]
Training tasks:  30%|███       | 9/30 [00:14<00:34,  1.62s/it]
Training tasks:  33%|███▎      | 10/30 [00:16<00:31,  1.60s/it]
Training tasks:  37%|███▋      | 11/30 [00:17<00:31,  1.63s/it]
Training tasks:  40%|████      | 12/30 [00:19<00:30,  1.68s/it]
Training tasks:  43%|████▎     | 13/30 [00:21<00:28,  1.69s/it]
Training tasks:  47%|████▋     | 14/30 [00:22<00:26,  1.68s/it]
Training tasks:  50%|█████     | 15/30 [00:24<00:25,  1.69s/it]
Training tasks:  53%|█████▎    | 16/30 [00:26<00:23,  1.66s/it]
Training tasks:  57%|█████▋    | 17/30 [00:28<00:22,  1.70s/it]
Training tasks:  60%|██████    | 18/30 [00:29<00:20,  1.67s/it]
Training tasks:  63%|██████▎   | 19/30 [00:31<00:18,  1.68s/it]
Training tasks:  67%|██████▋   | 20/30 [00:33<00:17,  1.73s/it]
Training tasks:  70%|███████   | 21/30 [00:34<00:15,  1.72s/it]
Training tasks:  73%|███████▎  | 22/30 [00:36<00:13,  1.64s/it]
Training tasks:  77%|███████▋  | 23/30 [00:38<00:11,  1.68s/it]
Training tasks:  80%|████████  | 24/30 [00:39<00:10,  1.67s/it]
Training tasks:  83%|████████▎ | 25/30 [00:41<00:08,  1.69s/it]
Training tasks:  87%|████████▋ | 26/30 [00:43<00:06,  1.66s/it]
Training tasks:  90%|█████████ | 27/30 [00:44<00:04,  1.63s/it]
Training tasks:  93%|█████████▎| 28/30 [00:46<00:03,  1.69s/it]
Training tasks:  97%|█████████▋| 29/30 [00:47<00:01,  1.64s/it]
Training tasks: 100%|██████████| 30/30 [00:49<00:00,  1.67s/it]
Training tasks: 100%|██████████| 30/30 [00:49<00:00,  1.66s/it]

Validating tasks:   0%|          | 0/10 [00:00<?, ?it/s]
Validating tasks:  10%|█         | 1/10 [00:00<00:02,  4.23it/s]
Validating tasks:  20%|██        | 2/10 [00:00<00:02,  3.96it/s]
Validating tasks:  30%|███       | 3/10 [00:00<00:01,  3.91it/s]
Validating tasks:  40%|████      | 4/10 [00:00<00:01,  4.30it/s]
Validating tasks:  50%|█████     | 5/10 [00:01<00:01,  4.53it/s]
Validating tasks:  60%|██████    | 6/10 [00:01<00:00,  4.42it/s]
Validating tasks:  70%|███████   | 7/10 [00:01<00:00,  4.46it/s]
Validating tasks:  80%|████████  | 8/10 [00:01<00:00,  4.66it/s]
Validating tasks:  90%|█████████ | 9/10 [00:02<00:00,  4.35it/s]
Validating tasks: 100%|██████████| 10/10 [00:02<00:00,  4.36it/s]
Validating tasks: 100%|██████████| 10/10 [00:02<00:00,  4.35it/s]

Training tasks:   0%|          | 0/30 [00:00<?, ?it/s]
Training tasks:   3%|▎         | 1/30 [00:01<00:46,  1.62s/it]
Training tasks:   7%|▋         | 2/30 [00:03<00:51,  1.83s/it]
Training tasks:  10%|█         | 3/30 [00:05<00:48,  1.79s/it]
Training tasks:  13%|█▎        | 4/30 [00:06<00:43,  1.66s/it]
Training tasks:  17%|█▋        | 5/30 [00:08<00:41,  1.66s/it]
Training tasks:  20%|██        | 6/30 [00:10<00:43,  1.81s/it]
Training tasks:  23%|██▎       | 7/30 [00:12<00:40,  1.77s/it]
Training tasks:  27%|██▋       | 8/30 [00:13<00:36,  1.66s/it]
Training tasks:  30%|███       | 9/30 [00:15<00:33,  1.59s/it]
Training tasks:  33%|███▎      | 10/30 [00:16<00:30,  1.53s/it]
Training tasks:  37%|███▋      | 11/30 [00:18<00:28,  1.53s/it]
Training tasks:  40%|████      | 12/30 [00:19<00:28,  1.60s/it]
Training tasks:  43%|████▎     | 13/30 [00:21<00:28,  1.70s/it]
Training tasks:  47%|████▋     | 14/30 [00:23<00:25,  1.62s/it]
Training tasks:  50%|█████     | 15/30 [00:24<00:24,  1.64s/it]
Training tasks:  53%|█████▎    | 16/30 [00:26<00:22,  1.61s/it]
Training tasks:  57%|█████▋    | 17/30 [00:27<00:19,  1.51s/it]
Training tasks:  60%|██████    | 18/30 [00:29<00:17,  1.46s/it]
Training tasks:  63%|██████▎   | 19/30 [00:30<00:16,  1.46s/it]
Training tasks:  67%|██████▋   | 20/30 [00:32<00:14,  1.50s/it]
Training tasks:  70%|███████   | 21/30 [00:33<00:12,  1.44s/it]
Training tasks:  73%|███████▎  | 22/30 [00:34<00:11,  1.40s/it]
Training tasks:  77%|███████▋  | 23/30 [00:35<00:09,  1.37s/it]
Training tasks:  80%|████████  | 24/30 [00:37<00:08,  1.44s/it]
Training tasks:  83%|████████▎ | 25/30 [00:39<00:07,  1.50s/it]
Training tasks:  87%|████████▋ | 26/30 [00:40<00:06,  1.50s/it]
Training tasks:  90%|█████████ | 27/30 [00:42<00:04,  1.55s/it]
Training tasks:  93%|█████████▎| 28/30 [00:43<00:03,  1.55s/it]
Training tasks:  97%|█████████▋| 29/30 [00:44<00:01,  1.38s/it]
Training tasks: 100%|██████████| 30/30 [00:46<00:00,  1.36s/it]
Training tasks: 100%|██████████| 30/30 [00:46<00:00,  1.54s/it]

Validating tasks:   0%|          | 0/10 [00:00<?, ?it/s]
Validating tasks:  10%|█         | 1/10 [00:00<00:01,  5.67it/s]
Validating tasks:  20%|██        | 2/10 [00:00<00:01,  5.73it/s]
Validating tasks:  30%|███       | 3/10 [00:00<00:01,  5.28it/s]
Validating tasks:  40%|████      | 4/10 [00:00<00:01,  5.12it/s]
Validating tasks:  50%|█████     | 5/10 [00:00<00:00,  5.14it/s]
Validating tasks:  60%|██████    | 6/10 [00:01<00:00,  5.41it/s]
Validating tasks:  70%|███████   | 7/10 [00:01<00:00,  5.12it/s]
Validating tasks:  80%|████████  | 8/10 [00:01<00:00,  5.13it/s]
Validating tasks:  90%|█████████ | 9/10 [00:01<00:00,  4.59it/s]
Validating tasks: 100%|██████████| 10/10 [00:02<00:00,  4.10it/s]
Validating tasks: 100%|██████████| 10/10 [00:02<00:00,  4.76it/s]

Training tasks:   0%|          | 0/30 [00:00<?, ?it/s]
Training tasks:   3%|▎         | 1/30 [00:01<00:48,  1.68s/it]
Training tasks:   7%|▋         | 2/30 [00:03<00:42,  1.52s/it]
Training tasks:  10%|█         | 3/30 [00:04<00:39,  1.48s/it]
Training tasks:  13%|█▎        | 4/30 [00:05<00:37,  1.44s/it]
Training tasks:  17%|█▋        | 5/30 [00:07<00:35,  1.42s/it]
Training tasks:  20%|██        | 6/30 [00:08<00:35,  1.46s/it]
Training tasks:  23%|██▎       | 7/30 [00:10<00:34,  1.49s/it]
Training tasks:  27%|██▋       | 8/30 [00:12<00:35,  1.63s/it]
Training tasks:  30%|███       | 9/30 [00:13<00:34,  1.63s/it]
Training tasks:  33%|███▎      | 10/30 [00:15<00:33,  1.68s/it]
Training tasks:  37%|███▋      | 11/30 [00:17<00:31,  1.66s/it]
Training tasks:  40%|████      | 12/30 [00:19<00:29,  1.67s/it]
Training tasks:  43%|████▎     | 13/30 [00:20<00:27,  1.64s/it]
Training tasks:  47%|████▋     | 14/30 [00:22<00:25,  1.60s/it]
Training tasks:  50%|█████     | 15/30 [00:23<00:23,  1.55s/it]
Training tasks:  53%|█████▎    | 16/30 [00:25<00:22,  1.63s/it]
Training tasks:  57%|█████▋    | 17/30 [00:26<00:20,  1.60s/it]
Training tasks:  60%|██████    | 18/30 [00:28<00:19,  1.59s/it]
Training tasks:  63%|██████▎   | 19/30 [00:30<00:17,  1.62s/it]
Training tasks:  67%|██████▋   | 20/30 [00:31<00:15,  1.58s/it]
Training tasks:  70%|███████   | 21/30 [00:33<00:14,  1.60s/it]
Training tasks:  73%|███████▎  | 22/30 [00:34<00:12,  1.56s/it]
Training tasks:  77%|███████▋  | 23/30 [00:36<00:11,  1.60s/it]
Training tasks:  80%|████████  | 24/30 [00:38<00:09,  1.59s/it]
Training tasks:  83%|████████▎ | 25/30 [00:39<00:07,  1.59s/it]
Training tasks:  87%|████████▋ | 26/30 [00:41<00:06,  1.61s/it]
Training tasks:  90%|█████████ | 27/30 [00:42<00:04,  1.61s/it]
Training tasks:  93%|█████████▎| 28/30 [00:44<00:03,  1.63s/it]
Training tasks:  97%|█████████▋| 29/30 [00:46<00:01,  1.66s/it]
Training tasks: 100%|██████████| 30/30 [00:48<00:00,  1.70s/it]
Training tasks: 100%|██████████| 30/30 [00:48<00:00,  1.60s/it]

Validating tasks:   0%|          | 0/10 [00:00<?, ?it/s]
Validating tasks:  10%|█         | 1/10 [00:00<00:02,  4.14it/s]
Validating tasks:  20%|██        | 2/10 [00:00<00:01,  4.11it/s]
Validating tasks:  30%|███       | 3/10 [00:00<00:01,  3.95it/s]
Validating tasks:  40%|████      | 4/10 [00:01<00:01,  3.91it/s]
Validating tasks:  50%|█████     | 5/10 [00:01<00:01,  3.84it/s]
Validating tasks:  60%|██████    | 6/10 [00:01<00:01,  3.93it/s]
Validating tasks:  70%|███████   | 7/10 [00:01<00:00,  4.07it/s]
Validating tasks:  80%|████████  | 8/10 [00:01<00:00,  4.12it/s]
Validating tasks:  90%|█████████ | 9/10 [00:02<00:00,  4.20it/s]
Validating tasks: 100%|██████████| 10/10 [00:02<00:00,  4.17it/s]
Validating tasks: 100%|██████████| 10/10 [00:02<00:00,  4.07it/s]

Testing tasks:   0%|          | 0/30 [00:00<?, ?it/s]
Testing tasks:   3%|▎         | 1/30 [00:00<00:09,  2.99it/s]
Testing tasks:   7%|▋         | 2/30 [00:00<00:08,  3.24it/s]
Testing tasks:  10%|█         | 3/30 [00:00<00:08,  3.35it/s]
Testing tasks:  13%|█▎        | 4/30 [00:01<00:07,  3.42it/s]
Testing tasks:  17%|█▋        | 5/30 [00:01<00:07,  3.29it/s]
Testing tasks:  20%|██        | 6/30 [00:01<00:08,  2.95it/s]
Testing tasks:  23%|██▎       | 7/30 [00:02<00:08,  2.77it/s]
Testing tasks:  27%|██▋       | 8/30 [00:02<00:08,  2.68it/s]
Testing tasks:  30%|███       | 9/30 [00:03<00:07,  2.77it/s]
Testing tasks:  33%|███▎      | 10/30 [00:03<00:07,  2.80it/s]
Testing tasks:  37%|███▋      | 11/30 [00:03<00:06,  2.81it/s]
Testing tasks:  40%|████      | 12/30 [00:04<00:06,  2.81it/s]
Testing tasks:  43%|████▎     | 13/30 [00:04<00:06,  2.80it/s]
Testing tasks:  47%|████▋     | 14/30 [00:04<00:05,  2.94it/s]
Testing tasks:  50%|█████     | 15/30 [00:05<00:05,  2.91it/s]
Testing tasks:  53%|█████▎    | 16/30 [00:05<00:04,  2.89it/s]
Testing tasks:  57%|█████▋    | 17/30 [00:05<00:04,  2.97it/s]
Testing tasks:  60%|██████    | 18/30 [00:06<00:04,  2.94it/s]
Testing tasks:  63%|██████▎   | 19/30 [00:06<00:03,  2.94it/s]
Testing tasks:  67%|██████▋   | 20/30 [00:06<00:03,  2.69it/s]
Testing tasks:  70%|███████   | 21/30 [00:07<00:03,  2.73it/s]
Testing tasks:  73%|███████▎  | 22/30 [00:07<00:02,  2.89it/s]
Testing tasks:  77%|███████▋  | 23/30 [00:07<00:02,  2.93it/s]
Testing tasks:  80%|████████  | 24/30 [00:08<00:02,  2.94it/s]
Testing tasks:  83%|████████▎ | 25/30 [00:08<00:01,  2.96it/s]
Testing tasks:  87%|████████▋ | 26/30 [00:08<00:01,  2.98it/s]
Testing tasks:  90%|█████████ | 27/30 [00:09<00:01,  2.94it/s]
Testing tasks:  93%|█████████▎| 28/30 [00:09<00:00,  2.75it/s]
Testing tasks:  97%|█████████▋| 29/30 [00:10<00:00,  2.71it/s]
Testing tasks: 100%|██████████| 30/30 [00:10<00:00,  2.71it/s]
Testing tasks: 100%|██████████| 30/30 [00:10<00:00,  2.87it/s]

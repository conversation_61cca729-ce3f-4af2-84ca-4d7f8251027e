STDOUT:
===================== 训练配置 =====================
GPU: 0
输出目录: /home/<USER>/balanced_task_sampling/results_trial_20250524_012522
日志文件: /home/<USER>/balanced_task_sampling/results_trial_20250524_012522/main_training_trial_51_20250524_012522.log

--------- 优化参数 ---------
内部学习率: 0.347389
训练轮数: 50
每轮任务数: 30

--------- 模型参数 ---------
使用类别权重: 圆锥角膜=5.497871178587208, 早期圆锥角膜=4.9223883460253735
温度参数: 0.053384006899807164
硬样本挖掘比例: 0.6592558493862402
对比学习权重: 0.32254998111010447
=============================================

创建目录: /home/<USER>/balanced_task_sampling/results_trial_20250524_012522
总用量 4
-rw-rw-r-- 1 <USER> <GROUP> 660 5月  24 01:25 main_training_trial_51_20250524_012522.log
开始无数据增强的平衡任务采样训练...
使用GPU: 0
直接运行训练脚本...
成功导入增强版特征提取器
成功导入归一化特征提取器
成功导入增强版损失函数
成功导入增强版MAML模型
成功导入KC特定增强模块
成功导入增强版GPU数据增强模块
使用设备: cuda
禁用所有数据增强，直接加载原始数据集...
使用平衡任务采样器
类别 normal (标签 0): 164个样本
类别 e-kc (标签 1): 26个样本
类别 kc (标签 2): 133个样本
使用原型配置: [2, 3, 4]
使用增强版特征提取器，dropout率: 0.5
使用增强版MAML-ProtoNet模型，内循环学习率: 0.347389, 内循环步数: 5
使用ReduceLROnPlateau学习率调度器，因子: 0.5, 耐心值: 2
开始训练，共50个epoch
Epoch 1/50
当前学习率: 0.000200
使用类别权重: 正常=1.0, 早期圆锥角膜=4.9223883460253735, 圆锥角膜=5.497871178587208
使用增强版对比损失函数，温度: 0.053384006899807164, 硬负样本挖掘比例: 0.6592558493862402, 早期圆锥角膜与正常样本对比权重: 1.0199155110379972, 圆锥角膜与正常样本对比权重: 1.0
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
警告: 没有足够的有效类别进行3-way任务采样，尝试调整参数...
类别样本数量: {0: 33, 1: 5, 2: 29}
调整后的参数: n_shot=2, n_query=3
类别 normal (标签 0): 33个样本
类别 e-kc (标签 1): 5个样本
类别 kc (标签 2): 29个样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
Epoch 1: Train Loss: 2.2081, Train Acc: 0.6015, Val Loss: 2.2967, Val Acc: 0.6000
保存最佳模型，验证准确率: 0.6000
Epoch 2/50
当前学习率: 0.000200
使用类别权重: 正常=1.0, 早期圆锥角膜=4.9223883460253735, 圆锥角膜=5.497871178587208
使用增强版对比损失函数，温度: 0.053384006899807164, 硬负样本挖掘比例: 0.6592558493862402, 早期圆锥角膜与正常样本对比权重: 1.0199155110379972, 圆锥角膜与正常样本对比权重: 1.0
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
警告: 没有足够的有效类别进行3-way任务采样，尝试调整参数...
类别样本数量: {0: 33, 1: 5, 2: 29}
调整后的参数: n_shot=2, n_query=3
类别 normal (标签 0): 33个样本
类别 e-kc (标签 1): 5个样本
类别 kc (标签 2): 29个样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
Epoch 2: Train Loss: 1.6965, Train Acc: 0.8526, Val Loss: 2.1111, Val Acc: 0.7000
保存最佳模型，验证准确率: 0.7000
Epoch 3/50
当前学习率: 0.000200
使用类别权重: 正常=1.0, 早期圆锥角膜=4.9223883460253735, 圆锥角膜=5.497871178587208
使用增强版对比损失函数，温度: 0.053384006899807164, 硬负样本挖掘比例: 0.6592558493862402, 早期圆锥角膜与正常样本对比权重: 1.0199155110379972, 圆锥角膜与正常样本对比权重: 1.0
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
警告: 没有足够的有效类别进行3-way任务采样，尝试调整参数...
类别样本数量: {0: 33, 1: 5, 2: 29}
调整后的参数: n_shot=2, n_query=3
类别 normal (标签 0): 33个样本
类别 e-kc (标签 1): 5个样本
类别 kc (标签 2): 29个样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
Epoch 3: Train Loss: 1.5663, Train Acc: 0.8704, Val Loss: 2.2652, Val Acc: 0.6333
验证准确率未提高，当前耐心值: 1/3
Epoch 4/50
当前学习率: 0.000200
使用类别权重: 正常=1.0, 早期圆锥角膜=4.9223883460253735, 圆锥角膜=5.497871178587208
使用增强版对比损失函数，温度: 0.053384006899807164, 硬负样本挖掘比例: 0.6592558493862402, 早期圆锥角膜与正常样本对比权重: 1.0199155110379972, 圆锥角膜与正常样本对比权重: 1.0
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
警告: 没有足够的有效类别进行3-way任务采样，尝试调整参数...
类别样本数量: {0: 33, 1: 5, 2: 29}
调整后的参数: n_shot=2, n_query=3
类别 normal (标签 0): 33个样本
类别 e-kc (标签 1): 5个样本
类别 kc (标签 2): 29个样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
Epoch 4: Train Loss: 1.3917, Train Acc: 0.9104, Val Loss: 2.0670, Val Acc: 0.6444
验证准确率未提高，当前耐心值: 2/3
Epoch 5/50
当前学习率: 0.000200
使用类别权重: 正常=1.0, 早期圆锥角膜=4.9223883460253735, 圆锥角膜=5.497871178587208
使用增强版对比损失函数，温度: 0.053384006899807164, 硬负样本挖掘比例: 0.6592558493862402, 早期圆锥角膜与正常样本对比权重: 1.0199155110379972, 圆锥角膜与正常样本对比权重: 1.0
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 7 个支持集样本
警告: 没有足够的有效类别进行3-way任务采样，尝试调整参数...
类别样本数量: {0: 33, 1: 5, 2: 29}
调整后的参数: n_shot=2, n_query=3
类别 normal (标签 0): 33个样本
类别 e-kc (标签 1): 5个样本
类别 kc (标签 2): 29个样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
Epoch 5: Train Loss: 1.3230, Train Acc: 0.9459, Val Loss: 2.2089, Val Acc: 0.6667
验证准确率未提高，当前耐心值: 3/3
早停触发，停止训练
警告: 没有足够的有效类别进行3-way任务采样，尝试调整参数...
类别样本数量: {0: 37, 1: 7, 2: 27}
调整后的参数: n_shot=3, n_query=4
类别 normal (标签 0): 37个样本
类别 e-kc (标签 1): 7个样本
类别 kc (标签 2): 27个样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 10 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 10 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 10 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 10 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 10 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 10 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 10 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 10 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 10 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 10 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 10 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 10 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 10 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 10 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 10 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 10 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 10 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 10 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 10 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 10 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 10 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 10 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 10 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 10 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 10 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 10 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 10 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 10 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 10 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 10 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本

测试结果:
总体准确率: 71.67%
类别准确率:
  kc: 84.17%
  e-kc: 38.33%
  normal: 92.50%
训练完成，结果已保存到 /home/<USER>/balanced_task_sampling/results_trial_20250524_012522

STDERR:
+ TIMESTAMP=20250524_012522
+ RESULT_DIR=/home/<USER>/balanced_task_sampling/results_trial_20250524_012522
+ '[' -n /home/<USER>/balanced_task_sampling/results_trial_20250524_012522/main_training_trial_51_20250524_012522.log ']'
+ LOG_FILE=/home/<USER>/balanced_task_sampling/results_trial_20250524_012522/main_training_trial_51_20250524_012522.log
++ dirname /home/<USER>/balanced_task_sampling/results_trial_20250524_012522/main_training_trial_51_20250524_012522.log
+ mkdir -p /home/<USER>/balanced_task_sampling/results_trial_20250524_012522
+ mkdir -p /home/<USER>/balanced_task_sampling/results_trial_20250524_012522
+ echo '===================== 训练配置 ====================='
+ echo 'GPU: 0'
+ echo '输出目录: /home/<USER>/balanced_task_sampling/results_trial_20250524_012522'
+ echo '日志文件: /home/<USER>/balanced_task_sampling/results_trial_20250524_012522/main_training_trial_51_20250524_012522.log'
+ echo ''
+ echo '--------- 优化参数 ---------'
+ echo '内部学习率: 0.347389'
+ echo '训练轮数: 50'
+ echo '每轮任务数: 30'
+ echo ''
+ echo '--------- 模型参数 ---------'
+ echo '使用类别权重: 圆锥角膜=5.497871178587208, 早期圆锥角膜=4.9223883460253735'
+ echo '温度参数: 0.053384006899807164'
+ echo '硬样本挖掘比例: 0.6592558493862402'
+ echo '对比学习权重: 0.32254998111010447'
+ echo '=============================================
'
+ tee -a /home/<USER>/balanced_task_sampling/results_trial_20250524_012522/main_training_trial_51_20250524_012522.log
+ validate_params
+ local error_count=0
++ dirname /home/<USER>/balanced_task_sampling/results_trial_20250524_012522/main_training_trial_51_20250524_012522.log
++ dirname /home/<USER>/balanced_task_sampling/split_result/train_set.csv
+ for dir in "$RESULT_DIR" "$(dirname "$LOG_FILE")" "$(dirname "$TRAIN_CSV")"
+ mkdir -p /home/<USER>/balanced_task_sampling/results_trial_20250524_012522
+ for dir in "$RESULT_DIR" "$(dirname "$LOG_FILE")" "$(dirname "$TRAIN_CSV")"
+ mkdir -p /home/<USER>/balanced_task_sampling/results_trial_20250524_012522
+ for dir in "$RESULT_DIR" "$(dirname "$LOG_FILE")" "$(dirname "$TRAIN_CSV")"
+ mkdir -p /home/<USER>/balanced_task_sampling/split_result
+ for csv in "$TRAIN_CSV" "$VAL_CSV" "$TEST_CSV"
+ '[' '!' -f /home/<USER>/balanced_task_sampling/split_result/train_set.csv ']'
+ for csv in "$TRAIN_CSV" "$VAL_CSV" "$TEST_CSV"
+ '[' '!' -f /home/<USER>/balanced_task_sampling/split_result/val_set.csv ']'
+ for csv in "$TRAIN_CSV" "$VAL_CSV" "$TEST_CSV"
+ '[' '!' -f /home/<USER>/balanced_task_sampling/split_result/test_set.csv ']'
+ [[ 0.0002 =~ ^[0-9.]+$ ]]
+ [[ 0.347389 =~ ^[0-9.]+$ ]]
+ '[' 0 -gt 0 ']'
+ return 0
+ '[' false = true ']'
+ export CUDA_VISIBLE_DEVICES=0
+ CUDA_VISIBLE_DEVICES=0
+ export OMP_NUM_THREADS=1
+ OMP_NUM_THREADS=1
+ export MKL_NUM_THREADS=1
+ MKL_NUM_THREADS=1
+ export MPLBACKEND=Agg
+ MPLBACKEND=Agg
+ mkdir -p /home/<USER>/balanced_task_sampling/results_trial_20250524_012522
+ echo '创建目录: /home/<USER>/balanced_task_sampling/results_trial_20250524_012522'
+ ls -l /home/<USER>/balanced_task_sampling/results_trial_20250524_012522
+ PRETRAINED_ARGS=
+ '[' true = true ']'
+ PRETRAINED_ARGS=--pretrained
+ AUGMENT_ARGS=--no_augmentation
+ CONTRASTIVE_ARGS=
+ '[' true = true ']'
+ CONTRASTIVE_ARGS='--use_contrastive --contrastive_weight 0.32254998111010447 --temperature 0.053384006899807164 --hard_mining_ratio 0.6592558493862402 --early_normal_weight 1.0199155110379972 --kc_normal_weight 1.0'
+ NORMALIZE_ARGS=
+ '[' false = true ']'
+ SAMPLER_ARGS=
+ '[' true = true ']'
+ SAMPLER_ARGS=' --use_separate_test_sampler'
+ '[' true = true ']'
+ SAMPLER_ARGS=' --use_separate_test_sampler --use_balanced_task_sampler --kc_shot_multiplier 3.6409336783933974 --early_kc_shot_multiplier 1.565595830172888'
+ LR_SCHEDULER_ARGS=
+ '[' true = true ']'
+ LR_SCHEDULER_ARGS='--use_lr_scheduler --scheduler_type plateau'
+ '[' plateau = onecycle ']'
+ '[' plateau = plateau ']'
+ LR_SCHEDULER_ARGS='--use_lr_scheduler --scheduler_type plateau --plateau_factor 0.5 --plateau_patience 2 --min_lr 1e-6'
+ BASE_CMD='python train_balanced_task_sampling.py   --train_csv /home/<USER>/balanced_task_sampling/split_result/train_set.csv   --val_csv /home/<USER>/balanced_task_sampling/split_result/val_set.csv   --test_csv /home/<USER>/balanced_task_sampling/split_result/test_set.csv   --epochs 50   --lr 0.0002   --weight_decay 5e-4   --feature_dim 512   --save_dir /home/<USER>/balanced_task_sampling/results_trial_20250524_012522   --proto_counts 2,3,4   --pretrained   --inner_lr 0.347389   --inner_steps 5   --n_way 3   --n_shot 5   --n_query 15   --tasks_per_epoch 30   --meta_batch_size 8   --no_augmentation   --mixup_alpha 0.0   --cutmix_prob 0.0   --num_workers 4   --augment_factor 1   --kc_augment_factor 1   --seed 42   --early_kc_weight 4.9223883460253735   --kc_weight 5.497871178587208   --focal_gamma 1.1659127628337098   --val_frequency 1   --early_stopping 3   --dropout 0.5    --use_separate_test_sampler --use_balanced_task_sampler --kc_shot_multiplier 3.6409336783933974 --early_kc_shot_multiplier 1.565595830172888   --use_contrastive --contrastive_weight 0.32254998111010447 --temperature 0.053384006899807164 --hard_mining_ratio 0.6592558493862402 --early_normal_weight 1.0199155110379972 --kc_normal_weight 1.0      --use_lr_scheduler --scheduler_type plateau --plateau_factor 0.5 --plateau_patience 2 --min_lr 1e-6'
+ echo 开始无数据增强的平衡任务采样训练...
+ echo '使用GPU: 0'
+ '[' direct = nohup ']'
+ echo 直接运行训练脚本...
+ eval python train_balanced_task_sampling.py --train_csv /home/<USER>/balanced_task_sampling/split_result/train_set.csv --val_csv /home/<USER>/balanced_task_sampling/split_result/val_set.csv --test_csv /home/<USER>/balanced_task_sampling/split_result/test_set.csv --epochs 50 --lr 0.0002 --weight_decay 5e-4 --feature_dim 512 --save_dir /home/<USER>/balanced_task_sampling/results_trial_20250524_012522 --proto_counts 2,3,4 --pretrained --inner_lr 0.347389 --inner_steps 5 --n_way 3 --n_shot 5 --n_query 15 --tasks_per_epoch 30 --meta_batch_size 8 --no_augmentation --mixup_alpha 0.0 --cutmix_prob 0.0 --num_workers 4 --augment_factor 1 --kc_augment_factor 1 --seed 42 --early_kc_weight 4.9223883460253735 --kc_weight 5.497871178587208 --focal_gamma 1.1659127628337098 --val_frequency 1 --early_stopping 3 --dropout 0.5 --use_separate_test_sampler --use_balanced_task_sampler --kc_shot_multiplier 3.6409336783933974 --early_kc_shot_multiplier 1.565595830172888 --use_contrastive --contrastive_weight 0.32254998111010447 --temperature 0.053384006899807164 --hard_mining_ratio 0.6592558493862402 --early_normal_weight 1.0199155110379972 --kc_normal_weight 1.0 --use_lr_scheduler --scheduler_type plateau --plateau_factor 0.5 --plateau_patience 2 --min_lr 1e-6
++ python train_balanced_task_sampling.py --train_csv /home/<USER>/balanced_task_sampling/split_result/train_set.csv --val_csv /home/<USER>/balanced_task_sampling/split_result/val_set.csv --test_csv /home/<USER>/balanced_task_sampling/split_result/test_set.csv --epochs 50 --lr 0.0002 --weight_decay 5e-4 --feature_dim 512 --save_dir /home/<USER>/balanced_task_sampling/results_trial_20250524_012522 --proto_counts 2,3,4 --pretrained --inner_lr 0.347389 --inner_steps 5 --n_way 3 --n_shot 5 --n_query 15 --tasks_per_epoch 30 --meta_batch_size 8 --no_augmentation --mixup_alpha 0.0 --cutmix_prob 0.0 --num_workers 4 --augment_factor 1 --kc_augment_factor 1 --seed 42 --early_kc_weight 4.9223883460253735 --kc_weight 5.497871178587208 --focal_gamma 1.1659127628337098 --val_frequency 1 --early_stopping 3 --dropout 0.5 --use_separate_test_sampler --use_balanced_task_sampler --kc_shot_multiplier 3.6409336783933974 --early_kc_shot_multiplier 1.565595830172888 --use_contrastive --contrastive_weight 0.32254998111010447 --temperature 0.053384006899807164 --hard_mining_ratio 0.6592558493862402 --early_normal_weight 1.0199155110379972 --kc_normal_weight 1.0 --use_lr_scheduler --scheduler_type plateau --plateau_factor 0.5 --plateau_patience 2 --min_lr 1e-6
/home/<USER>/anaconda3/envs/kc1/lib/python3.10/site-packages/torchvision/models/_utils.py:208: UserWarning: The parameter 'pretrained' is deprecated since 0.13 and may be removed in the future, please use 'weights' instead.
  warnings.warn(
/home/<USER>/anaconda3/envs/kc1/lib/python3.10/site-packages/torchvision/models/_utils.py:223: UserWarning: Arguments other than a weight enum or `None` for 'weights' are deprecated since 0.13 and may be removed in the future. The current behavior is equivalent to passing `weights=ResNet34_Weights.IMAGENET1K_V1`. You can also use `weights=ResNet34_Weights.DEFAULT` to get the most up-to-date weights.
  warnings.warn(msg)
/home/<USER>/anaconda3/envs/kc1/lib/python3.10/site-packages/torch/optim/lr_scheduler.py:62: UserWarning: The verbose parameter is deprecated. Please use get_last_lr() to access the learning rate.
  warnings.warn(

Training tasks:   0%|          | 0/30 [00:00<?, ?it/s]
Training tasks:   3%|▎         | 1/30 [00:05<02:44,  5.67s/it]
Training tasks:   7%|▋         | 2/30 [00:07<01:30,  3.22s/it]
Training tasks:  10%|█         | 3/30 [00:08<01:07,  2.50s/it]
Training tasks:  13%|█▎        | 4/30 [00:10<00:57,  2.21s/it]
Training tasks:  17%|█▋        | 5/30 [00:11<00:47,  1.89s/it]
Training tasks:  20%|██        | 6/30 [00:13<00:41,  1.74s/it]
Training tasks:  23%|██▎       | 7/30 [00:14<00:37,  1.64s/it]
Training tasks:  27%|██▋       | 8/30 [00:16<00:34,  1.58s/it]
Training tasks:  30%|███       | 9/30 [00:17<00:31,  1.52s/it]
Training tasks:  33%|███▎      | 10/30 [00:19<00:30,  1.51s/it]
Training tasks:  37%|███▋      | 11/30 [00:20<00:29,  1.57s/it]
Training tasks:  40%|████      | 12/30 [00:22<00:27,  1.55s/it]
Training tasks:  43%|████▎     | 13/30 [00:23<00:25,  1.53s/it]
Training tasks:  47%|████▋     | 14/30 [00:25<00:24,  1.52s/it]
Training tasks:  50%|█████     | 15/30 [00:26<00:22,  1.52s/it]
Training tasks:  53%|█████▎    | 16/30 [00:28<00:21,  1.52s/it]
Training tasks:  57%|█████▋    | 17/30 [00:29<00:20,  1.56s/it]
Training tasks:  60%|██████    | 18/30 [00:31<00:17,  1.49s/it]
Training tasks:  63%|██████▎   | 19/30 [00:32<00:15,  1.41s/it]
Training tasks:  67%|██████▋   | 20/30 [00:34<00:14,  1.48s/it]
Training tasks:  70%|███████   | 21/30 [00:35<00:13,  1.46s/it]
Training tasks:  73%|███████▎  | 22/30 [00:37<00:12,  1.52s/it]
Training tasks:  77%|███████▋  | 23/30 [00:38<00:10,  1.50s/it]
Training tasks:  80%|████████  | 24/30 [00:40<00:08,  1.47s/it]
Training tasks:  83%|████████▎ | 25/30 [00:41<00:07,  1.45s/it]
Training tasks:  87%|████████▋ | 26/30 [00:42<00:05,  1.43s/it]
Training tasks:  90%|█████████ | 27/30 [00:44<00:04,  1.47s/it]
Training tasks:  93%|█████████▎| 28/30 [00:46<00:03,  1.52s/it]
Training tasks:  97%|█████████▋| 29/30 [00:47<00:01,  1.53s/it]
Training tasks: 100%|██████████| 30/30 [00:49<00:00,  1.52s/it]
Training tasks: 100%|██████████| 30/30 [00:49<00:00,  1.64s/it]

Validating tasks:   0%|          | 0/10 [00:00<?, ?it/s]
Validating tasks:  10%|█         | 1/10 [00:00<00:02,  3.65it/s]
Validating tasks:  20%|██        | 2/10 [00:00<00:02,  3.99it/s]
Validating tasks:  30%|███       | 3/10 [00:00<00:01,  4.42it/s]
Validating tasks:  40%|████      | 4/10 [00:00<00:01,  4.58it/s]
Validating tasks:  50%|█████     | 5/10 [00:01<00:01,  4.71it/s]
Validating tasks:  60%|██████    | 6/10 [00:01<00:00,  4.75it/s]
Validating tasks:  70%|███████   | 7/10 [00:01<00:00,  4.81it/s]
Validating tasks:  80%|████████  | 8/10 [00:01<00:00,  4.91it/s]
Validating tasks:  90%|█████████ | 9/10 [00:01<00:00,  4.96it/s]
Validating tasks: 100%|██████████| 10/10 [00:02<00:00,  5.09it/s]
Validating tasks: 100%|██████████| 10/10 [00:02<00:00,  4.76it/s]

Training tasks:   0%|          | 0/30 [00:00<?, ?it/s]
Training tasks:   3%|▎         | 1/30 [00:01<00:47,  1.62s/it]
Training tasks:   7%|▋         | 2/30 [00:03<00:41,  1.50s/it]
Training tasks:  10%|█         | 3/30 [00:04<00:39,  1.47s/it]
Training tasks:  13%|█▎        | 4/30 [00:05<00:37,  1.43s/it]
Training tasks:  17%|█▋        | 5/30 [00:07<00:34,  1.37s/it]
Training tasks:  20%|██        | 6/30 [00:08<00:34,  1.43s/it]
Training tasks:  23%|██▎       | 7/30 [00:10<00:33,  1.46s/it]
Training tasks:  27%|██▋       | 8/30 [00:11<00:32,  1.49s/it]
Training tasks:  30%|███       | 9/30 [00:13<00:32,  1.54s/it]
Training tasks:  33%|███▎      | 10/30 [00:15<00:31,  1.57s/it]
Training tasks:  37%|███▋      | 11/30 [00:16<00:28,  1.48s/it]
Training tasks:  40%|████      | 12/30 [00:17<00:26,  1.47s/it]
Training tasks:  43%|████▎     | 13/30 [00:19<00:24,  1.43s/it]
Training tasks:  47%|████▋     | 14/30 [00:20<00:22,  1.39s/it]
Training tasks:  50%|█████     | 15/30 [00:21<00:20,  1.38s/it]
Training tasks:  53%|█████▎    | 16/30 [00:23<00:18,  1.36s/it]
Training tasks:  57%|█████▋    | 17/30 [00:24<00:17,  1.33s/it]
Training tasks:  60%|██████    | 18/30 [00:25<00:16,  1.41s/it]
Training tasks:  63%|██████▎   | 19/30 [00:27<00:16,  1.46s/it]
Training tasks:  67%|██████▋   | 20/30 [00:28<00:14,  1.42s/it]
Training tasks:  70%|███████   | 21/30 [00:30<00:13,  1.48s/it]
Training tasks:  73%|███████▎  | 22/30 [00:31<00:11,  1.49s/it]
Training tasks:  77%|███████▋  | 23/30 [00:33<00:10,  1.53s/it]
Training tasks:  80%|████████  | 24/30 [00:35<00:09,  1.52s/it]
Training tasks:  83%|████████▎ | 25/30 [00:36<00:07,  1.50s/it]
Training tasks:  87%|████████▋ | 26/30 [00:38<00:06,  1.58s/it]
Training tasks:  90%|█████████ | 27/30 [00:39<00:04,  1.60s/it]
Training tasks:  93%|█████████▎| 28/30 [00:41<00:03,  1.62s/it]
Training tasks:  97%|█████████▋| 29/30 [00:43<00:01,  1.64s/it]
Training tasks: 100%|██████████| 30/30 [00:44<00:00,  1.65s/it]
Training tasks: 100%|██████████| 30/30 [00:44<00:00,  1.50s/it]

Validating tasks:   0%|          | 0/10 [00:00<?, ?it/s]
Validating tasks:  10%|█         | 1/10 [00:00<00:02,  3.90it/s]
Validating tasks:  20%|██        | 2/10 [00:00<00:01,  4.27it/s]
Validating tasks:  30%|███       | 3/10 [00:00<00:01,  4.69it/s]
Validating tasks:  40%|████      | 4/10 [00:00<00:01,  4.91it/s]
Validating tasks:  50%|█████     | 5/10 [00:01<00:00,  5.08it/s]
Validating tasks:  60%|██████    | 6/10 [00:01<00:00,  5.21it/s]
Validating tasks:  70%|███████   | 7/10 [00:01<00:00,  5.20it/s]
Validating tasks:  80%|████████  | 8/10 [00:01<00:00,  5.22it/s]
Validating tasks:  90%|█████████ | 9/10 [00:01<00:00,  5.26it/s]
Validating tasks: 100%|██████████| 10/10 [00:02<00:00,  5.04it/s]
Validating tasks: 100%|██████████| 10/10 [00:02<00:00,  4.98it/s]

Training tasks:   0%|          | 0/30 [00:00<?, ?it/s]
Training tasks:   3%|▎         | 1/30 [00:01<00:44,  1.55s/it]
Training tasks:   7%|▋         | 2/30 [00:03<00:42,  1.53s/it]
Training tasks:  10%|█         | 3/30 [00:04<00:38,  1.42s/it]
Training tasks:  13%|█▎        | 4/30 [00:05<00:35,  1.36s/it]
Training tasks:  17%|█▋        | 5/30 [00:07<00:35,  1.40s/it]
Training tasks:  20%|██        | 6/30 [00:08<00:33,  1.41s/it]
Training tasks:  23%|██▎       | 7/30 [00:10<00:34,  1.49s/it]
Training tasks:  27%|██▋       | 8/30 [00:11<00:33,  1.52s/it]
Training tasks:  30%|███       | 9/30 [00:13<00:31,  1.51s/it]
Training tasks:  33%|███▎      | 10/30 [00:14<00:30,  1.51s/it]
Training tasks:  37%|███▋      | 11/30 [00:16<00:29,  1.57s/it]
Training tasks:  40%|████      | 12/30 [00:18<00:28,  1.57s/it]
Training tasks:  43%|████▎     | 13/30 [00:19<00:27,  1.59s/it]
Training tasks:  47%|████▋     | 14/30 [00:21<00:25,  1.61s/it]
Training tasks:  50%|█████     | 15/30 [00:22<00:22,  1.52s/it]
Training tasks:  53%|█████▎    | 16/30 [00:24<00:21,  1.54s/it]
Training tasks:  57%|█████▋    | 17/30 [00:25<00:20,  1.58s/it]
Training tasks:  60%|██████    | 18/30 [00:27<00:19,  1.64s/it]
Training tasks:  63%|██████▎   | 19/30 [00:29<00:17,  1.58s/it]
Training tasks:  67%|██████▋   | 20/30 [00:30<00:15,  1.57s/it]
Training tasks:  70%|███████   | 21/30 [00:32<00:14,  1.57s/it]
Training tasks:  73%|███████▎  | 22/30 [00:33<00:12,  1.61s/it]
Training tasks:  77%|███████▋  | 23/30 [00:35<00:11,  1.64s/it]
Training tasks:  80%|████████  | 24/30 [00:37<00:09,  1.66s/it]
Training tasks:  83%|████████▎ | 25/30 [00:38<00:08,  1.62s/it]
Training tasks:  87%|████████▋ | 26/30 [00:40<00:06,  1.57s/it]
Training tasks:  90%|█████████ | 27/30 [00:41<00:04,  1.52s/it]
Training tasks:  93%|█████████▎| 28/30 [00:43<00:02,  1.50s/it]
Training tasks:  97%|█████████▋| 29/30 [00:44<00:01,  1.55s/it]
Training tasks: 100%|██████████| 30/30 [00:46<00:00,  1.61s/it]
Training tasks: 100%|██████████| 30/30 [00:46<00:00,  1.55s/it]

Validating tasks:   0%|          | 0/10 [00:00<?, ?it/s]
Validating tasks:  10%|█         | 1/10 [00:00<00:02,  3.39it/s]
Validating tasks:  20%|██        | 2/10 [00:00<00:02,  3.72it/s]
Validating tasks:  30%|███       | 3/10 [00:00<00:01,  3.58it/s]
Validating tasks:  40%|████      | 4/10 [00:01<00:01,  3.45it/s]
Validating tasks:  50%|█████     | 5/10 [00:01<00:01,  3.49it/s]
Validating tasks:  60%|██████    | 6/10 [00:01<00:01,  3.87it/s]
Validating tasks:  70%|███████   | 7/10 [00:01<00:00,  3.72it/s]
Validating tasks:  80%|████████  | 8/10 [00:02<00:00,  3.63it/s]
Validating tasks:  90%|█████████ | 9/10 [00:02<00:00,  3.75it/s]
Validating tasks: 100%|██████████| 10/10 [00:02<00:00,  3.91it/s]
Validating tasks: 100%|██████████| 10/10 [00:02<00:00,  3.72it/s]

Training tasks:   0%|          | 0/30 [00:00<?, ?it/s]
Training tasks:   3%|▎         | 1/30 [00:01<00:42,  1.48s/it]
Training tasks:   7%|▋         | 2/30 [00:02<00:40,  1.46s/it]
Training tasks:  10%|█         | 3/30 [00:04<00:38,  1.43s/it]
Training tasks:  13%|█▎        | 4/30 [00:06<00:39,  1.53s/it]
Training tasks:  17%|█▋        | 5/30 [00:07<00:38,  1.53s/it]
Training tasks:  20%|██        | 6/30 [00:08<00:35,  1.47s/it]
Training tasks:  23%|██▎       | 7/30 [00:10<00:35,  1.53s/it]
Training tasks:  27%|██▋       | 8/30 [00:12<00:34,  1.55s/it]
Training tasks:  30%|███       | 9/30 [00:13<00:31,  1.51s/it]
Training tasks:  33%|███▎      | 10/30 [00:15<00:30,  1.54s/it]
Training tasks:  37%|███▋      | 11/30 [00:17<00:31,  1.65s/it]
Training tasks:  40%|████      | 12/30 [00:18<00:30,  1.67s/it]
Training tasks:  43%|████▎     | 13/30 [00:20<00:26,  1.55s/it]
Training tasks:  47%|████▋     | 14/30 [00:21<00:23,  1.46s/it]
Training tasks:  50%|█████     | 15/30 [00:22<00:20,  1.39s/it]
Training tasks:  53%|█████▎    | 16/30 [00:24<00:19,  1.41s/it]
Training tasks:  57%|█████▋    | 17/30 [00:25<00:18,  1.44s/it]
Training tasks:  60%|██████    | 18/30 [00:27<00:18,  1.50s/it]
Training tasks:  63%|██████▎   | 19/30 [00:28<00:15,  1.45s/it]
Training tasks:  67%|██████▋   | 20/30 [00:29<00:14,  1.40s/it]
Training tasks:  70%|███████   | 21/30 [00:31<00:13,  1.45s/it]
Training tasks:  73%|███████▎  | 22/30 [00:32<00:11,  1.40s/it]
Training tasks:  77%|███████▋  | 23/30 [00:33<00:09,  1.39s/it]
Training tasks:  80%|████████  | 24/30 [00:35<00:08,  1.49s/it]
Training tasks:  83%|████████▎ | 25/30 [00:37<00:07,  1.58s/it]
Training tasks:  87%|████████▋ | 26/30 [00:39<00:06,  1.60s/it]
Training tasks:  90%|█████████ | 27/30 [00:41<00:05,  1.71s/it]
Training tasks:  93%|█████████▎| 28/30 [00:42<00:03,  1.59s/it]
Training tasks:  97%|█████████▋| 29/30 [00:43<00:01,  1.55s/it]
Training tasks: 100%|██████████| 30/30 [00:45<00:00,  1.47s/it]
Training tasks: 100%|██████████| 30/30 [00:45<00:00,  1.51s/it]

Validating tasks:   0%|          | 0/10 [00:00<?, ?it/s]
Validating tasks:  10%|█         | 1/10 [00:00<00:01,  5.52it/s]
Validating tasks:  20%|██        | 2/10 [00:00<00:01,  5.41it/s]
Validating tasks:  30%|███       | 3/10 [00:00<00:01,  5.46it/s]
Validating tasks:  40%|████      | 4/10 [00:00<00:01,  5.27it/s]
Validating tasks:  50%|█████     | 5/10 [00:00<00:00,  5.32it/s]
Validating tasks:  60%|██████    | 6/10 [00:01<00:00,  5.27it/s]
Validating tasks:  70%|███████   | 7/10 [00:01<00:00,  5.38it/s]
Validating tasks:  80%|████████  | 8/10 [00:01<00:00,  5.31it/s]
Validating tasks:  90%|█████████ | 9/10 [00:01<00:00,  5.56it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  5.64it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  5.46it/s]

Training tasks:   0%|          | 0/30 [00:00<?, ?it/s]
Training tasks:   3%|▎         | 1/30 [00:01<00:45,  1.55s/it]
Training tasks:   7%|▋         | 2/30 [00:03<00:47,  1.68s/it]
Training tasks:  10%|█         | 3/30 [00:05<00:45,  1.68s/it]
Training tasks:  13%|█▎        | 4/30 [00:06<00:43,  1.67s/it]
Training tasks:  17%|█▋        | 5/30 [00:08<00:42,  1.69s/it]
Training tasks:  20%|██        | 6/30 [00:10<00:40,  1.67s/it]
Training tasks:  23%|██▎       | 7/30 [00:11<00:38,  1.67s/it]
Training tasks:  27%|██▋       | 8/30 [00:13<00:35,  1.61s/it]
Training tasks:  30%|███       | 9/30 [00:14<00:34,  1.64s/it]
Training tasks:  33%|███▎      | 10/30 [00:16<00:32,  1.65s/it]
Training tasks:  37%|███▋      | 11/30 [00:18<00:31,  1.65s/it]
Training tasks:  40%|████      | 12/30 [00:20<00:31,  1.73s/it]
Training tasks:  43%|████▎     | 13/30 [00:22<00:30,  1.79s/it]
Training tasks:  47%|████▋     | 14/30 [00:23<00:28,  1.76s/it]
Training tasks:  50%|█████     | 15/30 [00:25<00:24,  1.66s/it]
Training tasks:  53%|█████▎    | 16/30 [00:26<00:23,  1.65s/it]
Training tasks:  57%|█████▋    | 17/30 [00:28<00:20,  1.56s/it]
Training tasks:  60%|██████    | 18/30 [00:29<00:18,  1.57s/it]
Training tasks:  63%|██████▎   | 19/30 [00:31<00:17,  1.60s/it]
Training tasks:  67%|██████▋   | 20/30 [00:33<00:16,  1.60s/it]
Training tasks:  70%|███████   | 21/30 [00:34<00:14,  1.61s/it]
Training tasks:  73%|███████▎  | 22/30 [00:36<00:13,  1.63s/it]
Training tasks:  77%|███████▋  | 23/30 [00:37<00:11,  1.59s/it]
Training tasks:  80%|████████  | 24/30 [00:39<00:09,  1.55s/it]
Training tasks:  83%|████████▎ | 25/30 [00:40<00:07,  1.56s/it]
Training tasks:  87%|████████▋ | 26/30 [00:42<00:06,  1.58s/it]
Training tasks:  90%|█████████ | 27/30 [00:43<00:04,  1.56s/it]
Training tasks:  93%|█████████▎| 28/30 [00:45<00:02,  1.48s/it]
Training tasks:  97%|█████████▋| 29/30 [00:46<00:01,  1.43s/it]
Training tasks: 100%|██████████| 30/30 [00:48<00:00,  1.44s/it]
Training tasks: 100%|██████████| 30/30 [00:48<00:00,  1.60s/it]

Validating tasks:   0%|          | 0/10 [00:00<?, ?it/s]
Validating tasks:  10%|█         | 1/10 [00:00<00:02,  4.01it/s]
Validating tasks:  20%|██        | 2/10 [00:00<00:01,  4.37it/s]
Validating tasks:  30%|███       | 3/10 [00:00<00:01,  4.26it/s]
Validating tasks:  40%|████      | 4/10 [00:00<00:01,  4.34it/s]
Validating tasks:  50%|█████     | 5/10 [00:01<00:01,  4.49it/s]
Validating tasks:  60%|██████    | 6/10 [00:01<00:00,  4.46it/s]
Validating tasks:  70%|███████   | 7/10 [00:01<00:00,  4.55it/s]
Validating tasks:  80%|████████  | 8/10 [00:01<00:00,  4.57it/s]
Validating tasks:  90%|█████████ | 9/10 [00:01<00:00,  4.66it/s]
Validating tasks: 100%|██████████| 10/10 [00:02<00:00,  4.63it/s]
Validating tasks: 100%|██████████| 10/10 [00:02<00:00,  4.51it/s]

Testing tasks:   0%|          | 0/30 [00:00<?, ?it/s]
Testing tasks:   3%|▎         | 1/30 [00:00<00:10,  2.88it/s]
Testing tasks:   7%|▋         | 2/30 [00:00<00:10,  2.67it/s]
Testing tasks:  10%|█         | 3/30 [00:01<00:10,  2.64it/s]
Testing tasks:  13%|█▎        | 4/30 [00:01<00:09,  2.80it/s]
Testing tasks:  17%|█▋        | 5/30 [00:01<00:09,  2.72it/s]
Testing tasks:  20%|██        | 6/30 [00:02<00:08,  2.79it/s]
Testing tasks:  23%|██▎       | 7/30 [00:02<00:08,  2.76it/s]
Testing tasks:  27%|██▋       | 8/30 [00:02<00:07,  2.78it/s]
Testing tasks:  30%|███       | 9/30 [00:03<00:07,  2.77it/s]
Testing tasks:  33%|███▎      | 10/30 [00:03<00:07,  2.64it/s]
Testing tasks:  37%|███▋      | 11/30 [00:04<00:07,  2.56it/s]
Testing tasks:  40%|████      | 12/30 [00:04<00:07,  2.57it/s]
Testing tasks:  43%|████▎     | 13/30 [00:04<00:06,  2.65it/s]
Testing tasks:  47%|████▋     | 14/30 [00:05<00:05,  2.68it/s]
Testing tasks:  50%|█████     | 15/30 [00:05<00:05,  2.65it/s]
Testing tasks:  53%|█████▎    | 16/30 [00:06<00:05,  2.56it/s]
Testing tasks:  57%|█████▋    | 17/30 [00:06<00:05,  2.58it/s]
Testing tasks:  60%|██████    | 18/30 [00:06<00:04,  2.56it/s]
Testing tasks:  63%|██████▎   | 19/30 [00:06<00:03,  2.99it/s]
Testing tasks:  67%|██████▋   | 20/30 [00:07<00:02,  3.36it/s]
Testing tasks:  70%|███████   | 21/30 [00:07<00:02,  3.67it/s]
Testing tasks:  73%|███████▎  | 22/30 [00:07<00:02,  3.93it/s]
Testing tasks:  77%|███████▋  | 23/30 [00:07<00:01,  4.11it/s]
Testing tasks:  80%|████████  | 24/30 [00:08<00:01,  4.16it/s]
Testing tasks:  83%|████████▎ | 25/30 [00:08<00:01,  4.32it/s]
Testing tasks:  87%|████████▋ | 26/30 [00:08<00:00,  4.42it/s]
Testing tasks:  90%|█████████ | 27/30 [00:08<00:00,  4.41it/s]
Testing tasks:  93%|█████████▎| 28/30 [00:08<00:00,  4.43it/s]
Testing tasks:  97%|█████████▋| 29/30 [00:09<00:00,  4.54it/s]
Testing tasks: 100%|██████████| 30/30 [00:09<00:00,  4.37it/s]
Testing tasks: 100%|██████████| 30/30 [00:09<00:00,  3.19it/s]

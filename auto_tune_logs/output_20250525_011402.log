STDOUT:
===================== 训练配置 =====================
GPU: 0
输出目录: /home/<USER>/balanced_task_sampling/results_trial_20250525_011402
日志文件: /home/<USER>/balanced_task_sampling/results_trial_20250525_011402/main_training_trial_34_20250525_011402.log

--------- 优化参数 ---------
内部学习率: 0.347389
训练轮数: 50
每轮任务数: 30

--------- 模型参数 ---------
使用类别权重: 圆锥角膜=6.692145717702664, 早期圆锥角膜=6.577460642490939
温度参数: 0.05122677626768014
硬样本挖掘比例: 0.5689979814103232
对比学习权重: 0.4016268828367995
=============================================

创建目录: /home/<USER>/balanced_task_sampling/results_trial_20250525_011402
总用量 4
-rw-rw-r-- 1 <USER> <GROUP> 657 5月  25 01:14 main_training_trial_34_20250525_011402.log
开始无数据增强的平衡任务采样训练...
使用GPU: 0
直接运行训练脚本...
成功导入增强版特征提取器
成功导入归一化特征提取器
成功导入增强版损失函数
成功导入增强版MAML模型
成功导入KC特定增强模块
成功导入增强版GPU数据增强模块
使用设备: cuda
禁用所有数据增强，直接加载原始数据集...
使用平衡任务采样器
类别 normal (标签 0): 164个样本
类别 e-kc (标签 1): 26个样本
类别 kc (标签 2): 133个样本
使用原型配置: [3, 5, 4]
使用增强版特征提取器，dropout率: 0.5
使用增强版MAML-ProtoNet模型，内循环学习率: 0.347389, 内循环步数: 5
使用ReduceLROnPlateau学习率调度器，因子: 0.5, 耐心值: 2
开始训练，共50个epoch
Epoch 1/50
当前学习率: 0.000200
使用类别权重: 正常=1.0, 早期圆锥角膜=6.577460642490939, 圆锥角膜=6.692145717702664
使用增强版对比损失函数，温度: 0.05122677626768014, 硬负样本挖掘比例: 0.5689979814103232, 早期圆锥角膜与正常样本对比权重: 1.9002649378055503, 圆锥角膜与正常样本对比权重: 1.0
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
警告: 没有足够的有效类别进行3-way任务采样，尝试调整参数...
类别样本数量: {0: 33, 1: 5, 2: 29}
调整后的参数: n_shot=2, n_query=3
类别 normal (标签 0): 33个样本
类别 e-kc (标签 1): 5个样本
类别 kc (标签 2): 29个样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
Epoch 1: Train Loss: 2.3355, Train Acc: 0.5519, Val Loss: 2.5359, Val Acc: 0.5333
保存最佳模型，验证准确率: 0.5333
Epoch 2/50
当前学习率: 0.000200
使用类别权重: 正常=1.0, 早期圆锥角膜=6.577460642490939, 圆锥角膜=6.692145717702664
使用增强版对比损失函数，温度: 0.05122677626768014, 硬负样本挖掘比例: 0.5689979814103232, 早期圆锥角膜与正常样本对比权重: 1.9002649378055503, 圆锥角膜与正常样本对比权重: 1.0
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
警告: 没有足够的有效类别进行3-way任务采样，尝试调整参数...
类别样本数量: {0: 33, 1: 5, 2: 29}
调整后的参数: n_shot=2, n_query=3
类别 normal (标签 0): 33个样本
类别 e-kc (标签 1): 5个样本
类别 kc (标签 2): 29个样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
Epoch 2: Train Loss: 1.6382, Train Acc: 0.8363, Val Loss: 2.1985, Val Acc: 0.6000
保存最佳模型，验证准确率: 0.6000
Epoch 3/50
当前学习率: 0.000200
使用类别权重: 正常=1.0, 早期圆锥角膜=6.577460642490939, 圆锥角膜=6.692145717702664
使用增强版对比损失函数，温度: 0.05122677626768014, 硬负样本挖掘比例: 0.5689979814103232, 早期圆锥角膜与正常样本对比权重: 1.9002649378055503, 圆锥角膜与正常样本对比权重: 1.0
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
警告: 没有足够的有效类别进行3-way任务采样，尝试调整参数...
类别样本数量: {0: 33, 1: 5, 2: 29}
调整后的参数: n_shot=2, n_query=3
类别 normal (标签 0): 33个样本
类别 e-kc (标签 1): 5个样本
类别 kc (标签 2): 29个样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
Epoch 3: Train Loss: 1.4628, Train Acc: 0.9067, Val Loss: 2.2181, Val Acc: 0.5556
验证准确率未提高，当前耐心值: 1/3
Epoch 4/50
当前学习率: 0.000200
使用类别权重: 正常=1.0, 早期圆锥角膜=6.577460642490939, 圆锥角膜=6.692145717702664
使用增强版对比损失函数，温度: 0.05122677626768014, 硬负样本挖掘比例: 0.5689979814103232, 早期圆锥角膜与正常样本对比权重: 1.9002649378055503, 圆锥角膜与正常样本对比权重: 1.0
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
警告: 没有足够的有效类别进行3-way任务采样，尝试调整参数...
类别样本数量: {0: 33, 1: 5, 2: 29}
调整后的参数: n_shot=2, n_query=3
类别 normal (标签 0): 33个样本
类别 e-kc (标签 1): 5个样本
类别 kc (标签 2): 29个样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
Epoch 4: Train Loss: 1.3065, Train Acc: 0.9133, Val Loss: 2.3877, Val Acc: 0.5222
验证准确率未提高，当前耐心值: 2/3
Epoch 5/50
当前学习率: 0.000200
使用类别权重: 正常=1.0, 早期圆锥角膜=6.577460642490939, 圆锥角膜=6.692145717702664
使用增强版对比损失函数，温度: 0.05122677626768014, 硬负样本挖掘比例: 0.5689979814103232, 早期圆锥角膜与正常样本对比权重: 1.9002649378055503, 圆锥角膜与正常样本对比权重: 1.0
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 17 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
警告: 没有足够的有效类别进行3-way任务采样，尝试调整参数...
类别样本数量: {0: 33, 1: 5, 2: 29}
调整后的参数: n_shot=2, n_query=3
类别 normal (标签 0): 33个样本
类别 e-kc (标签 1): 5个样本
类别 kc (标签 2): 29个样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
Epoch 5: Train Loss: 1.2159, Train Acc: 0.9430, Val Loss: 2.1831, Val Acc: 0.5778
验证准确率未提高，当前耐心值: 3/3
早停触发，停止训练
警告: 没有足够的有效类别进行3-way任务采样，尝试调整参数...
类别样本数量: {0: 37, 1: 7, 2: 27}
调整后的参数: n_shot=3, n_query=4
类别 normal (标签 0): 37个样本
类别 e-kc (标签 1): 7个样本
类别 kc (标签 2): 27个样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 10 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 10 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 10 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 10 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 10 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 10 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 10 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 10 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 10 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 10 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 10 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 10 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 10 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 10 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 10 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 10 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 10 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 10 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 10 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 10 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 10 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 10 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 10 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 10 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 10 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 10 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 10 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 10 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 10 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 10 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本

测试结果:
总体准确率: 74.17%
类别准确率:
  kc: 67.50%
  e-kc: 65.00%
  normal: 90.00%
训练完成，结果已保存到 /home/<USER>/balanced_task_sampling/results_trial_20250525_011402

STDERR:
+ TIMESTAMP=20250525_011402
+ RESULT_DIR=/home/<USER>/balanced_task_sampling/results_trial_20250525_011402
+ '[' -n /home/<USER>/balanced_task_sampling/results_trial_20250525_011402/main_training_trial_34_20250525_011402.log ']'
+ LOG_FILE=/home/<USER>/balanced_task_sampling/results_trial_20250525_011402/main_training_trial_34_20250525_011402.log
++ dirname /home/<USER>/balanced_task_sampling/results_trial_20250525_011402/main_training_trial_34_20250525_011402.log
+ mkdir -p /home/<USER>/balanced_task_sampling/results_trial_20250525_011402
+ mkdir -p /home/<USER>/balanced_task_sampling/results_trial_20250525_011402
+ echo '===================== 训练配置 ====================='
+ echo 'GPU: 0'
+ echo '输出目录: /home/<USER>/balanced_task_sampling/results_trial_20250525_011402'
+ echo '日志文件: /home/<USER>/balanced_task_sampling/results_trial_20250525_011402/main_training_trial_34_20250525_011402.log'
+ echo ''
+ echo '--------- 优化参数 ---------'
+ echo '内部学习率: 0.347389'
+ echo '训练轮数: 50'
+ echo '每轮任务数: 30'
+ echo ''
+ echo '--------- 模型参数 ---------'
+ echo '使用类别权重: 圆锥角膜=6.692145717702664, 早期圆锥角膜=6.577460642490939'
+ echo '温度参数: 0.05122677626768014'
+ echo '硬样本挖掘比例: 0.5689979814103232'
+ echo '对比学习权重: 0.4016268828367995'
+ echo '=============================================
'
+ tee -a /home/<USER>/balanced_task_sampling/results_trial_20250525_011402/main_training_trial_34_20250525_011402.log
+ validate_params
+ local error_count=0
++ dirname /home/<USER>/balanced_task_sampling/results_trial_20250525_011402/main_training_trial_34_20250525_011402.log
++ dirname /home/<USER>/balanced_task_sampling/split_result/train_set.csv
+ for dir in "$RESULT_DIR" "$(dirname "$LOG_FILE")" "$(dirname "$TRAIN_CSV")"
+ mkdir -p /home/<USER>/balanced_task_sampling/results_trial_20250525_011402
+ for dir in "$RESULT_DIR" "$(dirname "$LOG_FILE")" "$(dirname "$TRAIN_CSV")"
+ mkdir -p /home/<USER>/balanced_task_sampling/results_trial_20250525_011402
+ for dir in "$RESULT_DIR" "$(dirname "$LOG_FILE")" "$(dirname "$TRAIN_CSV")"
+ mkdir -p /home/<USER>/balanced_task_sampling/split_result
+ for csv in "$TRAIN_CSV" "$VAL_CSV" "$TEST_CSV"
+ '[' '!' -f /home/<USER>/balanced_task_sampling/split_result/train_set.csv ']'
+ for csv in "$TRAIN_CSV" "$VAL_CSV" "$TEST_CSV"
+ '[' '!' -f /home/<USER>/balanced_task_sampling/split_result/val_set.csv ']'
+ for csv in "$TRAIN_CSV" "$VAL_CSV" "$TEST_CSV"
+ '[' '!' -f /home/<USER>/balanced_task_sampling/split_result/test_set.csv ']'
+ [[ 0.0002 =~ ^[0-9.]+$ ]]
+ [[ 0.347389 =~ ^[0-9.]+$ ]]
+ '[' 0 -gt 0 ']'
+ return 0
+ '[' false = true ']'
+ export CUDA_VISIBLE_DEVICES=0
+ CUDA_VISIBLE_DEVICES=0
+ export OMP_NUM_THREADS=1
+ OMP_NUM_THREADS=1
+ export MKL_NUM_THREADS=1
+ MKL_NUM_THREADS=1
+ export MPLBACKEND=Agg
+ MPLBACKEND=Agg
+ mkdir -p /home/<USER>/balanced_task_sampling/results_trial_20250525_011402
+ echo '创建目录: /home/<USER>/balanced_task_sampling/results_trial_20250525_011402'
+ ls -l /home/<USER>/balanced_task_sampling/results_trial_20250525_011402
+ PRETRAINED_ARGS=
+ '[' true = true ']'
+ PRETRAINED_ARGS=--pretrained
+ AUGMENT_ARGS=--no_augmentation
+ CONTRASTIVE_ARGS=
+ '[' true = true ']'
+ CONTRASTIVE_ARGS='--use_contrastive --contrastive_weight 0.4016268828367995 --temperature 0.05122677626768014 --hard_mining_ratio 0.5689979814103232 --early_normal_weight 1.9002649378055503 --kc_normal_weight 1.0'
+ NORMALIZE_ARGS=
+ '[' false = true ']'
+ SAMPLER_ARGS=
+ '[' true = true ']'
+ SAMPLER_ARGS=' --use_separate_test_sampler'
+ '[' true = true ']'
+ SAMPLER_ARGS=' --use_separate_test_sampler --use_balanced_task_sampler --kc_shot_multiplier 3.5234595306793652 --early_kc_shot_multiplier 1.6389031411673125'
+ LR_SCHEDULER_ARGS=
+ '[' true = true ']'
+ LR_SCHEDULER_ARGS='--use_lr_scheduler --scheduler_type plateau'
+ '[' plateau = onecycle ']'
+ '[' plateau = plateau ']'
+ LR_SCHEDULER_ARGS='--use_lr_scheduler --scheduler_type plateau --plateau_factor 0.5 --plateau_patience 2 --min_lr 1e-6'
+ BASE_CMD='python train_balanced_task_sampling.py   --train_csv /home/<USER>/balanced_task_sampling/split_result/train_set.csv   --val_csv /home/<USER>/balanced_task_sampling/split_result/val_set.csv   --test_csv /home/<USER>/balanced_task_sampling/split_result/test_set.csv   --epochs 50   --lr 0.0002   --weight_decay 5e-4   --feature_dim 512   --save_dir /home/<USER>/balanced_task_sampling/results_trial_20250525_011402   --proto_counts 3,5,4   --pretrained   --inner_lr 0.347389   --inner_steps 5   --n_way 3   --n_shot 5   --n_query 15   --tasks_per_epoch 30   --meta_batch_size 8   --no_augmentation   --mixup_alpha 0.0   --cutmix_prob 0.0   --num_workers 4   --augment_factor 1   --kc_augment_factor 1   --seed 42   --early_kc_weight 6.577460642490939   --kc_weight 6.692145717702664   --focal_gamma 1.660480208949328   --val_frequency 1   --early_stopping 3   --dropout 0.5    --use_separate_test_sampler --use_balanced_task_sampler --kc_shot_multiplier 3.5234595306793652 --early_kc_shot_multiplier 1.6389031411673125   --use_contrastive --contrastive_weight 0.4016268828367995 --temperature 0.05122677626768014 --hard_mining_ratio 0.5689979814103232 --early_normal_weight 1.9002649378055503 --kc_normal_weight 1.0      --use_lr_scheduler --scheduler_type plateau --plateau_factor 0.5 --plateau_patience 2 --min_lr 1e-6'
+ echo 开始无数据增强的平衡任务采样训练...
+ echo '使用GPU: 0'
+ '[' direct = nohup ']'
+ echo 直接运行训练脚本...
+ eval python train_balanced_task_sampling.py --train_csv /home/<USER>/balanced_task_sampling/split_result/train_set.csv --val_csv /home/<USER>/balanced_task_sampling/split_result/val_set.csv --test_csv /home/<USER>/balanced_task_sampling/split_result/test_set.csv --epochs 50 --lr 0.0002 --weight_decay 5e-4 --feature_dim 512 --save_dir /home/<USER>/balanced_task_sampling/results_trial_20250525_011402 --proto_counts 3,5,4 --pretrained --inner_lr 0.347389 --inner_steps 5 --n_way 3 --n_shot 5 --n_query 15 --tasks_per_epoch 30 --meta_batch_size 8 --no_augmentation --mixup_alpha 0.0 --cutmix_prob 0.0 --num_workers 4 --augment_factor 1 --kc_augment_factor 1 --seed 42 --early_kc_weight 6.577460642490939 --kc_weight 6.692145717702664 --focal_gamma 1.660480208949328 --val_frequency 1 --early_stopping 3 --dropout 0.5 --use_separate_test_sampler --use_balanced_task_sampler --kc_shot_multiplier 3.5234595306793652 --early_kc_shot_multiplier 1.6389031411673125 --use_contrastive --contrastive_weight 0.4016268828367995 --temperature 0.05122677626768014 --hard_mining_ratio 0.5689979814103232 --early_normal_weight 1.9002649378055503 --kc_normal_weight 1.0 --use_lr_scheduler --scheduler_type plateau --plateau_factor 0.5 --plateau_patience 2 --min_lr 1e-6
++ python train_balanced_task_sampling.py --train_csv /home/<USER>/balanced_task_sampling/split_result/train_set.csv --val_csv /home/<USER>/balanced_task_sampling/split_result/val_set.csv --test_csv /home/<USER>/balanced_task_sampling/split_result/test_set.csv --epochs 50 --lr 0.0002 --weight_decay 5e-4 --feature_dim 512 --save_dir /home/<USER>/balanced_task_sampling/results_trial_20250525_011402 --proto_counts 3,5,4 --pretrained --inner_lr 0.347389 --inner_steps 5 --n_way 3 --n_shot 5 --n_query 15 --tasks_per_epoch 30 --meta_batch_size 8 --no_augmentation --mixup_alpha 0.0 --cutmix_prob 0.0 --num_workers 4 --augment_factor 1 --kc_augment_factor 1 --seed 42 --early_kc_weight 6.577460642490939 --kc_weight 6.692145717702664 --focal_gamma 1.660480208949328 --val_frequency 1 --early_stopping 3 --dropout 0.5 --use_separate_test_sampler --use_balanced_task_sampler --kc_shot_multiplier 3.5234595306793652 --early_kc_shot_multiplier 1.6389031411673125 --use_contrastive --contrastive_weight 0.4016268828367995 --temperature 0.05122677626768014 --hard_mining_ratio 0.5689979814103232 --early_normal_weight 1.9002649378055503 --kc_normal_weight 1.0 --use_lr_scheduler --scheduler_type plateau --plateau_factor 0.5 --plateau_patience 2 --min_lr 1e-6
/home/<USER>/anaconda3/envs/kc1/lib/python3.10/site-packages/torchvision/models/_utils.py:208: UserWarning: The parameter 'pretrained' is deprecated since 0.13 and may be removed in the future, please use 'weights' instead.
  warnings.warn(
/home/<USER>/anaconda3/envs/kc1/lib/python3.10/site-packages/torchvision/models/_utils.py:223: UserWarning: Arguments other than a weight enum or `None` for 'weights' are deprecated since 0.13 and may be removed in the future. The current behavior is equivalent to passing `weights=ResNet34_Weights.IMAGENET1K_V1`. You can also use `weights=ResNet34_Weights.DEFAULT` to get the most up-to-date weights.
  warnings.warn(msg)
/home/<USER>/anaconda3/envs/kc1/lib/python3.10/site-packages/torch/optim/lr_scheduler.py:62: UserWarning: The verbose parameter is deprecated. Please use get_last_lr() to access the learning rate.
  warnings.warn(

Training tasks:   0%|          | 0/30 [00:00<?, ?it/s]
Training tasks:   3%|▎         | 1/30 [00:04<02:14,  4.64s/it]
Training tasks:   7%|▋         | 2/30 [00:06<01:17,  2.75s/it]
Training tasks:  10%|█         | 3/30 [00:07<00:56,  2.09s/it]
Training tasks:  13%|█▎        | 4/30 [00:08<00:45,  1.77s/it]
Training tasks:  17%|█▋        | 5/30 [00:09<00:39,  1.59s/it]
Training tasks:  20%|██        | 6/30 [00:11<00:34,  1.43s/it]
Training tasks:  23%|██▎       | 7/30 [00:12<00:30,  1.33s/it]
Training tasks:  27%|██▋       | 8/30 [00:13<00:29,  1.33s/it]
Training tasks:  30%|███       | 9/30 [00:14<00:27,  1.30s/it]
Training tasks:  33%|███▎      | 10/30 [00:16<00:26,  1.31s/it]
Training tasks:  37%|███▋      | 11/30 [00:17<00:24,  1.28s/it]
Training tasks:  40%|████      | 12/30 [00:18<00:22,  1.22s/it]
Training tasks:  43%|████▎     | 13/30 [00:19<00:20,  1.20s/it]
Training tasks:  47%|████▋     | 14/30 [00:20<00:19,  1.21s/it]
Training tasks:  50%|█████     | 15/30 [00:21<00:18,  1.21s/it]
Training tasks:  53%|█████▎    | 16/30 [00:23<00:16,  1.17s/it]
Training tasks:  57%|█████▋    | 17/30 [00:24<00:14,  1.15s/it]
Training tasks:  60%|██████    | 18/30 [00:25<00:14,  1.19s/it]
Training tasks:  63%|██████▎   | 19/30 [00:26<00:12,  1.13s/it]
Training tasks:  67%|██████▋   | 20/30 [00:27<00:11,  1.12s/it]
Training tasks:  70%|███████   | 21/30 [00:28<00:10,  1.16s/it]
Training tasks:  73%|███████▎  | 22/30 [00:29<00:09,  1.16s/it]
Training tasks:  77%|███████▋  | 23/30 [00:30<00:07,  1.12s/it]
Training tasks:  80%|████████  | 24/30 [00:32<00:06,  1.11s/it]
Training tasks:  83%|████████▎ | 25/30 [00:33<00:05,  1.11s/it]
Training tasks:  87%|████████▋ | 26/30 [00:34<00:04,  1.12s/it]
Training tasks:  90%|█████████ | 27/30 [00:35<00:03,  1.10s/it]
Training tasks:  93%|█████████▎| 28/30 [00:36<00:02,  1.10s/it]
Training tasks:  97%|█████████▋| 29/30 [00:37<00:01,  1.10s/it]
Training tasks: 100%|██████████| 30/30 [00:38<00:00,  1.11s/it]
Training tasks: 100%|██████████| 30/30 [00:38<00:00,  1.29s/it]

Validating tasks:   0%|          | 0/10 [00:00<?, ?it/s]
Validating tasks:  10%|█         | 1/10 [00:00<00:01,  5.53it/s]
Validating tasks:  20%|██        | 2/10 [00:00<00:01,  6.12it/s]
Validating tasks:  30%|███       | 3/10 [00:00<00:01,  6.57it/s]
Validating tasks:  40%|████      | 4/10 [00:00<00:00,  6.91it/s]
Validating tasks:  50%|█████     | 5/10 [00:00<00:00,  6.96it/s]
Validating tasks:  60%|██████    | 6/10 [00:00<00:00,  7.09it/s]
Validating tasks:  70%|███████   | 7/10 [00:01<00:00,  6.63it/s]
Validating tasks:  80%|████████  | 8/10 [00:01<00:00,  6.47it/s]
Validating tasks:  90%|█████████ | 9/10 [00:01<00:00,  6.30it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  6.54it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  6.57it/s]

Training tasks:   0%|          | 0/30 [00:00<?, ?it/s]
Training tasks:   3%|▎         | 1/30 [00:01<00:33,  1.15s/it]
Training tasks:   7%|▋         | 2/30 [00:02<00:33,  1.18s/it]
Training tasks:  10%|█         | 3/30 [00:03<00:30,  1.14s/it]
Training tasks:  13%|█▎        | 4/30 [00:04<00:29,  1.15s/it]
Training tasks:  17%|█▋        | 5/30 [00:06<00:30,  1.24s/it]
Training tasks:  20%|██        | 6/30 [00:07<00:29,  1.24s/it]
Training tasks:  23%|██▎       | 7/30 [00:08<00:27,  1.20s/it]
Training tasks:  27%|██▋       | 8/30 [00:09<00:25,  1.17s/it]
Training tasks:  30%|███       | 9/30 [00:10<00:25,  1.20s/it]
Training tasks:  33%|███▎      | 10/30 [00:11<00:23,  1.17s/it]
Training tasks:  37%|███▋      | 11/30 [00:12<00:21,  1.13s/it]
Training tasks:  40%|████      | 12/30 [00:13<00:19,  1.10s/it]
Training tasks:  43%|████▎     | 13/30 [00:14<00:18,  1.09s/it]
Training tasks:  47%|████▋     | 14/30 [00:16<00:17,  1.08s/it]
Training tasks:  50%|█████     | 15/30 [00:17<00:16,  1.08s/it]
Training tasks:  53%|█████▎    | 16/30 [00:18<00:14,  1.07s/it]
Training tasks:  57%|█████▋    | 17/30 [00:19<00:14,  1.11s/it]
Training tasks:  60%|██████    | 18/30 [00:20<00:13,  1.12s/it]
Training tasks:  63%|██████▎   | 19/30 [00:21<00:12,  1.10s/it]
Training tasks:  67%|██████▋   | 20/30 [00:22<00:10,  1.08s/it]
Training tasks:  70%|███████   | 21/30 [00:23<00:10,  1.12s/it]
Training tasks:  73%|███████▎  | 22/30 [00:24<00:09,  1.14s/it]
Training tasks:  77%|███████▋  | 23/30 [00:26<00:08,  1.17s/it]
Training tasks:  80%|████████  | 24/30 [00:27<00:06,  1.13s/it]
Training tasks:  83%|████████▎ | 25/30 [00:28<00:05,  1.11s/it]
Training tasks:  87%|████████▋ | 26/30 [00:29<00:04,  1.15s/it]
Training tasks:  90%|█████████ | 27/30 [00:30<00:03,  1.12s/it]
Training tasks:  93%|█████████▎| 28/30 [00:31<00:02,  1.15s/it]
Training tasks:  97%|█████████▋| 29/30 [00:32<00:01,  1.12s/it]
Training tasks: 100%|██████████| 30/30 [00:33<00:00,  1.10s/it]
Training tasks: 100%|██████████| 30/30 [00:33<00:00,  1.13s/it]

Validating tasks:   0%|          | 0/10 [00:00<?, ?it/s]
Validating tasks:  10%|█         | 1/10 [00:00<00:01,  6.13it/s]
Validating tasks:  20%|██        | 2/10 [00:00<00:01,  5.92it/s]
Validating tasks:  30%|███       | 3/10 [00:00<00:01,  6.42it/s]
Validating tasks:  40%|████      | 4/10 [00:00<00:00,  6.76it/s]
Validating tasks:  50%|█████     | 5/10 [00:00<00:00,  6.96it/s]
Validating tasks:  60%|██████    | 6/10 [00:00<00:00,  7.11it/s]
Validating tasks:  70%|███████   | 7/10 [00:01<00:00,  7.24it/s]
Validating tasks:  80%|████████  | 8/10 [00:01<00:00,  7.30it/s]
Validating tasks:  90%|█████████ | 9/10 [00:01<00:00,  7.35it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  7.04it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  6.94it/s]

Training tasks:   0%|          | 0/30 [00:00<?, ?it/s]
Training tasks:   3%|▎         | 1/30 [00:01<00:33,  1.14s/it]
Training tasks:   7%|▋         | 2/30 [00:02<00:32,  1.17s/it]
Training tasks:  10%|█         | 3/30 [00:03<00:31,  1.15s/it]
Training tasks:  13%|█▎        | 4/30 [00:04<00:28,  1.11s/it]
Training tasks:  17%|█▋        | 5/30 [00:05<00:27,  1.08s/it]
Training tasks:  20%|██        | 6/30 [00:06<00:26,  1.09s/it]
Training tasks:  23%|██▎       | 7/30 [00:07<00:26,  1.15s/it]
Training tasks:  27%|██▋       | 8/30 [00:08<00:24,  1.12s/it]
Training tasks:  30%|███       | 9/30 [00:10<00:22,  1.09s/it]
Training tasks:  33%|███▎      | 10/30 [00:11<00:21,  1.08s/it]
Training tasks:  37%|███▋      | 11/30 [00:12<00:20,  1.06s/it]
Training tasks:  40%|████      | 12/30 [00:13<00:19,  1.06s/it]
Training tasks:  43%|████▎     | 13/30 [00:14<00:18,  1.07s/it]
Training tasks:  47%|████▋     | 14/30 [00:15<00:17,  1.08s/it]
Training tasks:  50%|█████     | 15/30 [00:16<00:16,  1.07s/it]
Training tasks:  53%|█████▎    | 16/30 [00:17<00:15,  1.10s/it]
Training tasks:  57%|█████▋    | 17/30 [00:18<00:15,  1.16s/it]
Training tasks:  60%|██████    | 18/30 [00:19<00:13,  1.13s/it]
Training tasks:  63%|██████▎   | 19/30 [00:21<00:12,  1.17s/it]
Training tasks:  67%|██████▋   | 20/30 [00:22<00:11,  1.14s/it]
Training tasks:  70%|███████   | 21/30 [00:23<00:10,  1.12s/it]
Training tasks:  73%|███████▎  | 22/30 [00:24<00:08,  1.11s/it]
Training tasks:  77%|███████▋  | 23/30 [00:25<00:07,  1.14s/it]
Training tasks:  80%|████████  | 24/30 [00:26<00:06,  1.12s/it]
Training tasks:  83%|████████▎ | 25/30 [00:27<00:05,  1.10s/it]
Training tasks:  87%|████████▋ | 26/30 [00:29<00:04,  1.15s/it]
Training tasks:  90%|█████████ | 27/30 [00:30<00:03,  1.15s/it]
Training tasks:  93%|█████████▎| 28/30 [00:31<00:02,  1.13s/it]
Training tasks:  97%|█████████▋| 29/30 [00:32<00:01,  1.15s/it]
Training tasks: 100%|██████████| 30/30 [00:33<00:00,  1.17s/it]
Training tasks: 100%|██████████| 30/30 [00:33<00:00,  1.12s/it]

Validating tasks:   0%|          | 0/10 [00:00<?, ?it/s]
Validating tasks:  10%|█         | 1/10 [00:00<00:01,  6.84it/s]
Validating tasks:  20%|██        | 2/10 [00:00<00:01,  6.93it/s]
Validating tasks:  30%|███       | 3/10 [00:00<00:01,  6.90it/s]
Validating tasks:  40%|████      | 4/10 [00:00<00:00,  6.56it/s]
Validating tasks:  50%|█████     | 5/10 [00:00<00:00,  6.15it/s]
Validating tasks:  60%|██████    | 6/10 [00:00<00:00,  6.05it/s]
Validating tasks:  70%|███████   | 7/10 [00:01<00:00,  6.34it/s]
Validating tasks:  80%|████████  | 8/10 [00:01<00:00,  6.63it/s]
Validating tasks:  90%|█████████ | 9/10 [00:01<00:00,  6.85it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  7.04it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  6.68it/s]

Training tasks:   0%|          | 0/30 [00:00<?, ?it/s]
Training tasks:   3%|▎         | 1/30 [00:01<00:32,  1.11s/it]
Training tasks:   7%|▋         | 2/30 [00:02<00:29,  1.06s/it]
Training tasks:  10%|█         | 3/30 [00:03<00:30,  1.14s/it]
Training tasks:  13%|█▎        | 4/30 [00:04<00:30,  1.16s/it]
Training tasks:  17%|█▋        | 5/30 [00:05<00:27,  1.10s/it]
Training tasks:  20%|██        | 6/30 [00:06<00:27,  1.13s/it]
Training tasks:  23%|██▎       | 7/30 [00:07<00:26,  1.14s/it]
Training tasks:  27%|██▋       | 8/30 [00:08<00:24,  1.10s/it]
Training tasks:  30%|███       | 9/30 [00:09<00:22,  1.08s/it]
Training tasks:  33%|███▎      | 10/30 [00:11<00:21,  1.09s/it]
Training tasks:  37%|███▋      | 11/30 [00:12<00:20,  1.07s/it]
Training tasks:  40%|████      | 12/30 [00:13<00:20,  1.13s/it]
Training tasks:  43%|████▎     | 13/30 [00:14<00:20,  1.19s/it]
Training tasks:  47%|████▋     | 14/30 [00:15<00:18,  1.17s/it]
Training tasks:  50%|█████     | 15/30 [00:16<00:17,  1.16s/it]
Training tasks:  53%|█████▎    | 16/30 [00:17<00:15,  1.12s/it]
Training tasks:  57%|█████▋    | 17/30 [00:19<00:14,  1.10s/it]
Training tasks:  60%|██████    | 18/30 [00:20<00:13,  1.12s/it]
Training tasks:  63%|██████▎   | 19/30 [00:21<00:12,  1.09s/it]
Training tasks:  67%|██████▋   | 20/30 [00:22<00:10,  1.08s/it]
Training tasks:  70%|███████   | 21/30 [00:23<00:09,  1.07s/it]
Training tasks:  73%|███████▎  | 22/30 [00:24<00:08,  1.06s/it]
Training tasks:  77%|███████▋  | 23/30 [00:25<00:07,  1.10s/it]
Training tasks:  80%|████████  | 24/30 [00:26<00:06,  1.09s/it]
Training tasks:  83%|████████▎ | 25/30 [00:27<00:05,  1.08s/it]
Training tasks:  87%|████████▋ | 26/30 [00:28<00:04,  1.13s/it]
Training tasks:  90%|█████████ | 27/30 [00:29<00:03,  1.11s/it]
Training tasks:  93%|█████████▎| 28/30 [00:31<00:02,  1.10s/it]
Training tasks:  97%|█████████▋| 29/30 [00:32<00:01,  1.09s/it]
Training tasks: 100%|██████████| 30/30 [00:33<00:00,  1.14s/it]
Training tasks: 100%|██████████| 30/30 [00:33<00:00,  1.11s/it]

Validating tasks:   0%|          | 0/10 [00:00<?, ?it/s]
Validating tasks:  10%|█         | 1/10 [00:00<00:01,  6.92it/s]
Validating tasks:  20%|██        | 2/10 [00:00<00:01,  7.03it/s]
Validating tasks:  30%|███       | 3/10 [00:00<00:00,  7.16it/s]
Validating tasks:  40%|████      | 4/10 [00:00<00:00,  6.39it/s]
Validating tasks:  50%|█████     | 5/10 [00:00<00:00,  6.66it/s]
Validating tasks:  60%|██████    | 6/10 [00:00<00:00,  6.81it/s]
Validating tasks:  70%|███████   | 7/10 [00:01<00:00,  6.87it/s]
Validating tasks:  80%|████████  | 8/10 [00:01<00:00,  6.87it/s]
Validating tasks:  90%|█████████ | 9/10 [00:01<00:00,  6.97it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  6.98it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  6.88it/s]

Training tasks:   0%|          | 0/30 [00:00<?, ?it/s]
Training tasks:   3%|▎         | 1/30 [00:01<00:35,  1.21s/it]
Training tasks:   7%|▋         | 2/30 [00:02<00:31,  1.12s/it]
Training tasks:  10%|█         | 3/30 [00:03<00:30,  1.13s/it]
Training tasks:  13%|█▎        | 4/30 [00:04<00:29,  1.14s/it]
Training tasks:  17%|█▋        | 5/30 [00:05<00:27,  1.11s/it]
Training tasks:  20%|██        | 6/30 [00:06<00:26,  1.10s/it]
Training tasks:  23%|██▎       | 7/30 [00:07<00:25,  1.09s/it]
Training tasks:  27%|██▋       | 8/30 [00:08<00:24,  1.13s/it]
Training tasks:  30%|███       | 9/30 [00:10<00:23,  1.11s/it]
Training tasks:  33%|███▎      | 10/30 [00:11<00:21,  1.09s/it]
Training tasks:  37%|███▋      | 11/30 [00:12<00:21,  1.11s/it]
Training tasks:  40%|████      | 12/30 [00:13<00:20,  1.11s/it]
Training tasks:  43%|████▎     | 13/30 [00:14<00:19,  1.15s/it]
Training tasks:  47%|████▋     | 14/30 [00:15<00:18,  1.16s/it]
Training tasks:  50%|█████     | 15/30 [00:16<00:16,  1.13s/it]
Training tasks:  53%|█████▎    | 16/30 [00:18<00:16,  1.16s/it]
Training tasks:  57%|█████▋    | 17/30 [00:19<00:15,  1.19s/it]
Training tasks:  60%|██████    | 18/30 [00:20<00:13,  1.15s/it]
Training tasks:  63%|██████▎   | 19/30 [00:21<00:12,  1.13s/it]
Training tasks:  67%|██████▋   | 20/30 [00:22<00:11,  1.11s/it]
Training tasks:  70%|███████   | 21/30 [00:23<00:09,  1.10s/it]
Training tasks:  73%|███████▎  | 22/30 [00:24<00:09,  1.14s/it]
Training tasks:  77%|███████▋  | 23/30 [00:26<00:08,  1.15s/it]
Training tasks:  80%|████████  | 24/30 [00:27<00:07,  1.18s/it]
Training tasks:  83%|████████▎ | 25/30 [00:28<00:05,  1.15s/it]
Training tasks:  87%|████████▋ | 26/30 [00:29<00:04,  1.13s/it]
Training tasks:  90%|█████████ | 27/30 [00:30<00:03,  1.11s/it]
Training tasks:  93%|█████████▎| 28/30 [00:31<00:02,  1.12s/it]
Training tasks:  97%|█████████▋| 29/30 [00:32<00:01,  1.16s/it]
Training tasks: 100%|██████████| 30/30 [00:34<00:00,  1.16s/it]
Training tasks: 100%|██████████| 30/30 [00:34<00:00,  1.14s/it]

Validating tasks:   0%|          | 0/10 [00:00<?, ?it/s]
Validating tasks:  10%|█         | 1/10 [00:00<00:01,  7.18it/s]
Validating tasks:  20%|██        | 2/10 [00:00<00:01,  7.09it/s]
Validating tasks:  30%|███       | 3/10 [00:00<00:00,  7.11it/s]
Validating tasks:  40%|████      | 4/10 [00:00<00:00,  7.02it/s]
Validating tasks:  50%|█████     | 5/10 [00:00<00:00,  6.71it/s]
Validating tasks:  60%|██████    | 6/10 [00:00<00:00,  6.86it/s]
Validating tasks:  70%|███████   | 7/10 [00:01<00:00,  6.96it/s]
Validating tasks:  80%|████████  | 8/10 [00:01<00:00,  6.66it/s]
Validating tasks:  90%|█████████ | 9/10 [00:01<00:00,  6.58it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  6.61it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  6.77it/s]

Testing tasks:   0%|          | 0/30 [00:00<?, ?it/s]
Testing tasks:   3%|▎         | 1/30 [00:00<00:06,  4.16it/s]
Testing tasks:   7%|▋         | 2/30 [00:00<00:06,  4.15it/s]
Testing tasks:  10%|█         | 3/30 [00:00<00:05,  4.57it/s]
Testing tasks:  13%|█▎        | 4/30 [00:00<00:05,  4.75it/s]
Testing tasks:  17%|█▋        | 5/30 [00:01<00:05,  4.84it/s]
Testing tasks:  20%|██        | 6/30 [00:01<00:05,  4.73it/s]
Testing tasks:  23%|██▎       | 7/30 [00:01<00:04,  4.82it/s]
Testing tasks:  27%|██▋       | 8/30 [00:01<00:04,  4.74it/s]
Testing tasks:  30%|███       | 9/30 [00:01<00:04,  4.69it/s]
Testing tasks:  33%|███▎      | 10/30 [00:02<00:04,  4.80it/s]
Testing tasks:  37%|███▋      | 11/30 [00:02<00:03,  4.91it/s]
Testing tasks:  40%|████      | 12/30 [00:02<00:03,  4.93it/s]
Testing tasks:  43%|████▎     | 13/30 [00:02<00:03,  4.97it/s]
Testing tasks:  47%|████▋     | 14/30 [00:02<00:03,  4.95it/s]
Testing tasks:  50%|█████     | 15/30 [00:03<00:02,  5.02it/s]
Testing tasks:  53%|█████▎    | 16/30 [00:03<00:02,  4.86it/s]
Testing tasks:  57%|█████▋    | 17/30 [00:03<00:02,  4.66it/s]
Testing tasks:  60%|██████    | 18/30 [00:03<00:02,  4.47it/s]
Testing tasks:  63%|██████▎   | 19/30 [00:04<00:02,  4.63it/s]
Testing tasks:  67%|██████▋   | 20/30 [00:04<00:02,  4.60it/s]
Testing tasks:  70%|███████   | 21/30 [00:04<00:01,  4.79it/s]
Testing tasks:  73%|███████▎  | 22/30 [00:04<00:01,  4.60it/s]
Testing tasks:  77%|███████▋  | 23/30 [00:04<00:01,  4.73it/s]
Testing tasks:  80%|████████  | 24/30 [00:05<00:01,  4.79it/s]
Testing tasks:  83%|████████▎ | 25/30 [00:05<00:01,  4.92it/s]
Testing tasks:  87%|████████▋ | 26/30 [00:05<00:00,  4.95it/s]
Testing tasks:  90%|█████████ | 27/30 [00:05<00:00,  4.90it/s]
Testing tasks:  93%|█████████▎| 28/30 [00:05<00:00,  4.69it/s]
Testing tasks:  97%|█████████▋| 29/30 [00:06<00:00,  4.67it/s]
Testing tasks: 100%|██████████| 30/30 [00:06<00:00,  4.72it/s]
Testing tasks: 100%|██████████| 30/30 [00:06<00:00,  4.75it/s]

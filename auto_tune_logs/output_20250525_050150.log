STDOUT:
===================== 训练配置 =====================
GPU: 0
输出目录: /home/<USER>/balanced_task_sampling/results_trial_20250525_050150
日志文件: /home/<USER>/balanced_task_sampling/results_trial_20250525_050150/main_training_trial_91_20250525_050150.log

--------- 优化参数 ---------
内部学习率: 0.347389
训练轮数: 50
每轮任务数: 30

--------- 模型参数 ---------
使用类别权重: 圆锥角膜=6.812536223468256, 早期圆锥角膜=4.716860796358321
温度参数: 0.021494862108303127
硬样本挖掘比例: 0.4426803149548197
对比学习权重: 0.315240842031719
=============================================

创建目录: /home/<USER>/balanced_task_sampling/results_trial_20250525_050150
总用量 4
-rw-rw-r-- 1 <USER> <GROUP> 657 5月  25 05:01 main_training_trial_91_20250525_050150.log
开始无数据增强的平衡任务采样训练...
使用GPU: 0
直接运行训练脚本...
成功导入增强版特征提取器
成功导入归一化特征提取器
成功导入增强版损失函数
成功导入增强版MAML模型
成功导入KC特定增强模块
成功导入增强版GPU数据增强模块
使用设备: cuda
禁用所有数据增强，直接加载原始数据集...
使用平衡任务采样器
类别 normal (标签 0): 164个样本
类别 e-kc (标签 1): 26个样本
类别 kc (标签 2): 133个样本
使用原型配置: [2, 6, 4]
使用增强版特征提取器，dropout率: 0.5
使用增强版MAML-ProtoNet模型，内循环学习率: 0.347389, 内循环步数: 5
使用ReduceLROnPlateau学习率调度器，因子: 0.5, 耐心值: 2
开始训练，共50个epoch
Epoch 1/50
当前学习率: 0.000200
使用类别权重: 正常=1.0, 早期圆锥角膜=4.716860796358321, 圆锥角膜=6.812536223468256
使用增强版对比损失函数，温度: 0.021494862108303127, 硬负样本挖掘比例: 0.4426803149548197, 早期圆锥角膜与正常样本对比权重: 1.190756084848058, 圆锥角膜与正常样本对比权重: 1.0
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
警告: 没有足够的有效类别进行3-way任务采样，尝试调整参数...
类别样本数量: {0: 33, 1: 5, 2: 29}
调整后的参数: n_shot=2, n_query=3
类别 normal (标签 0): 33个样本
类别 e-kc (标签 1): 5个样本
类别 kc (标签 2): 29个样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
Epoch 1: Train Loss: 2.9823, Train Acc: 0.4400, Val Loss: 2.4195, Val Acc: 0.5889
保存最佳模型，验证准确率: 0.5889
Epoch 2/50
当前学习率: 0.000200
使用类别权重: 正常=1.0, 早期圆锥角膜=4.716860796358321, 圆锥角膜=6.812536223468256
使用增强版对比损失函数，温度: 0.021494862108303127, 硬负样本挖掘比例: 0.4426803149548197, 早期圆锥角膜与正常样本对比权重: 1.190756084848058, 圆锥角膜与正常样本对比权重: 1.0
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
警告: 没有足够的有效类别进行3-way任务采样，尝试调整参数...
类别样本数量: {0: 33, 1: 5, 2: 29}
调整后的参数: n_shot=2, n_query=3
类别 normal (标签 0): 33个样本
类别 e-kc (标签 1): 5个样本
类别 kc (标签 2): 29个样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
Epoch 2: Train Loss: 2.5418, Train Acc: 0.5585, Val Loss: 2.3369, Val Acc: 0.4778
验证准确率未提高，当前耐心值: 1/3
Epoch 3/50
当前学习率: 0.000200
使用类别权重: 正常=1.0, 早期圆锥角膜=4.716860796358321, 圆锥角膜=6.812536223468256
使用增强版对比损失函数，温度: 0.021494862108303127, 硬负样本挖掘比例: 0.4426803149548197, 早期圆锥角膜与正常样本对比权重: 1.190756084848058, 圆锥角膜与正常样本对比权重: 1.0
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
警告: 没有足够的有效类别进行3-way任务采样，尝试调整参数...
类别样本数量: {0: 33, 1: 5, 2: 29}
调整后的参数: n_shot=2, n_query=3
类别 normal (标签 0): 33个样本
类别 e-kc (标签 1): 5个样本
类别 kc (标签 2): 29个样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
Epoch 3: Train Loss: 2.6100, Train Acc: 0.5481, Val Loss: 2.3183, Val Acc: 0.6000
保存最佳模型，验证准确率: 0.6000
Epoch 4/50
当前学习率: 0.000200
使用类别权重: 正常=1.0, 早期圆锥角膜=4.716860796358321, 圆锥角膜=6.812536223468256
使用增强版对比损失函数，温度: 0.021494862108303127, 硬负样本挖掘比例: 0.4426803149548197, 早期圆锥角膜与正常样本对比权重: 1.190756084848058, 圆锥角膜与正常样本对比权重: 1.0
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
警告: 没有足够的有效类别进行3-way任务采样，尝试调整参数...
类别样本数量: {0: 33, 1: 5, 2: 29}
调整后的参数: n_shot=2, n_query=3
类别 normal (标签 0): 33个样本
类别 e-kc (标签 1): 5个样本
类别 kc (标签 2): 29个样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
Epoch 4: Train Loss: 2.4997, Train Acc: 0.6859, Val Loss: 2.5031, Val Acc: 0.5889
验证准确率未提高，当前耐心值: 1/3
Epoch 5/50
当前学习率: 0.000200
使用类别权重: 正常=1.0, 早期圆锥角膜=4.716860796358321, 圆锥角膜=6.812536223468256
使用增强版对比损失函数，温度: 0.021494862108303127, 硬负样本挖掘比例: 0.4426803149548197, 早期圆锥角膜与正常样本对比权重: 1.190756084848058, 圆锥角膜与正常样本对比权重: 1.0
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
警告: 没有足够的有效类别进行3-way任务采样，尝试调整参数...
类别样本数量: {0: 33, 1: 5, 2: 29}
调整后的参数: n_shot=2, n_query=3
类别 normal (标签 0): 33个样本
类别 e-kc (标签 1): 5个样本
类别 kc (标签 2): 29个样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
Epoch 5: Train Loss: 2.3931, Train Acc: 0.7548, Val Loss: 2.4345, Val Acc: 0.5889
验证准确率未提高，当前耐心值: 2/3
Epoch 6/50
当前学习率: 0.000200
使用类别权重: 正常=1.0, 早期圆锥角膜=4.716860796358321, 圆锥角膜=6.812536223468256
使用增强版对比损失函数，温度: 0.021494862108303127, 硬负样本挖掘比例: 0.4426803149548197, 早期圆锥角膜与正常样本对比权重: 1.190756084848058, 圆锥角膜与正常样本对比权重: 1.0
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
警告: 没有足够的有效类别进行3-way任务采样，尝试调整参数...
类别样本数量: {0: 33, 1: 5, 2: 29}
调整后的参数: n_shot=2, n_query=3
类别 normal (标签 0): 33个样本
类别 e-kc (标签 1): 5个样本
类别 kc (标签 2): 29个样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
Epoch 6: Train Loss: 2.4491, Train Acc: 0.7356, Val Loss: 2.5050, Val Acc: 0.5333
验证准确率未提高，当前耐心值: 3/3
早停触发，停止训练
警告: 没有足够的有效类别进行3-way任务采样，尝试调整参数...
类别样本数量: {0: 37, 1: 7, 2: 27}
调整后的参数: n_shot=3, n_query=4
类别 normal (标签 0): 37个样本
类别 e-kc (标签 1): 7个样本
类别 kc (标签 2): 27个样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 4 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 4 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 4 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 4 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 4 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 4 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 4 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 4 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 4 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 4 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 4 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 4 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 4 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 4 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 4 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 4 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 4 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 4 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 4 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 4 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 4 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 4 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 4 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 4 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 4 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 4 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 4 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 4 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 4 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 4 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本

测试结果:
总体准确率: 64.17%
类别准确率:
  kc: 91.67%
  e-kc: 55.00%
  normal: 45.83%
训练完成，结果已保存到 /home/<USER>/balanced_task_sampling/results_trial_20250525_050150

STDERR:
+ TIMESTAMP=20250525_050150
+ RESULT_DIR=/home/<USER>/balanced_task_sampling/results_trial_20250525_050150
+ '[' -n /home/<USER>/balanced_task_sampling/results_trial_20250525_050150/main_training_trial_91_20250525_050150.log ']'
+ LOG_FILE=/home/<USER>/balanced_task_sampling/results_trial_20250525_050150/main_training_trial_91_20250525_050150.log
++ dirname /home/<USER>/balanced_task_sampling/results_trial_20250525_050150/main_training_trial_91_20250525_050150.log
+ mkdir -p /home/<USER>/balanced_task_sampling/results_trial_20250525_050150
+ mkdir -p /home/<USER>/balanced_task_sampling/results_trial_20250525_050150
+ echo '===================== 训练配置 ====================='
+ echo 'GPU: 0'
+ echo '输出目录: /home/<USER>/balanced_task_sampling/results_trial_20250525_050150'
+ echo '日志文件: /home/<USER>/balanced_task_sampling/results_trial_20250525_050150/main_training_trial_91_20250525_050150.log'
+ echo ''
+ echo '--------- 优化参数 ---------'
+ tee -a /home/<USER>/balanced_task_sampling/results_trial_20250525_050150/main_training_trial_91_20250525_050150.log
+ echo '内部学习率: 0.347389'
+ echo '训练轮数: 50'
+ echo '每轮任务数: 30'
+ echo ''
+ echo '--------- 模型参数 ---------'
+ echo '使用类别权重: 圆锥角膜=6.812536223468256, 早期圆锥角膜=4.716860796358321'
+ echo '温度参数: 0.021494862108303127'
+ echo '硬样本挖掘比例: 0.4426803149548197'
+ echo '对比学习权重: 0.315240842031719'
+ echo '=============================================
'
+ validate_params
+ local error_count=0
++ dirname /home/<USER>/balanced_task_sampling/results_trial_20250525_050150/main_training_trial_91_20250525_050150.log
++ dirname /home/<USER>/balanced_task_sampling/split_result/train_set.csv
+ for dir in "$RESULT_DIR" "$(dirname "$LOG_FILE")" "$(dirname "$TRAIN_CSV")"
+ mkdir -p /home/<USER>/balanced_task_sampling/results_trial_20250525_050150
+ for dir in "$RESULT_DIR" "$(dirname "$LOG_FILE")" "$(dirname "$TRAIN_CSV")"
+ mkdir -p /home/<USER>/balanced_task_sampling/results_trial_20250525_050150
+ for dir in "$RESULT_DIR" "$(dirname "$LOG_FILE")" "$(dirname "$TRAIN_CSV")"
+ mkdir -p /home/<USER>/balanced_task_sampling/split_result
+ for csv in "$TRAIN_CSV" "$VAL_CSV" "$TEST_CSV"
+ '[' '!' -f /home/<USER>/balanced_task_sampling/split_result/train_set.csv ']'
+ for csv in "$TRAIN_CSV" "$VAL_CSV" "$TEST_CSV"
+ '[' '!' -f /home/<USER>/balanced_task_sampling/split_result/val_set.csv ']'
+ for csv in "$TRAIN_CSV" "$VAL_CSV" "$TEST_CSV"
+ '[' '!' -f /home/<USER>/balanced_task_sampling/split_result/test_set.csv ']'
+ [[ 0.0002 =~ ^[0-9.]+$ ]]
+ [[ 0.347389 =~ ^[0-9.]+$ ]]
+ '[' 0 -gt 0 ']'
+ return 0
+ '[' false = true ']'
+ export CUDA_VISIBLE_DEVICES=0
+ CUDA_VISIBLE_DEVICES=0
+ export OMP_NUM_THREADS=1
+ OMP_NUM_THREADS=1
+ export MKL_NUM_THREADS=1
+ MKL_NUM_THREADS=1
+ export MPLBACKEND=Agg
+ MPLBACKEND=Agg
+ mkdir -p /home/<USER>/balanced_task_sampling/results_trial_20250525_050150
+ echo '创建目录: /home/<USER>/balanced_task_sampling/results_trial_20250525_050150'
+ ls -l /home/<USER>/balanced_task_sampling/results_trial_20250525_050150
+ PRETRAINED_ARGS=
+ '[' true = true ']'
+ PRETRAINED_ARGS=--pretrained
+ AUGMENT_ARGS=--no_augmentation
+ CONTRASTIVE_ARGS=
+ '[' true = true ']'
+ CONTRASTIVE_ARGS='--use_contrastive --contrastive_weight 0.315240842031719 --temperature 0.021494862108303127 --hard_mining_ratio 0.4426803149548197 --early_normal_weight 1.190756084848058 --kc_normal_weight 1.0'
+ NORMALIZE_ARGS=
+ '[' false = true ']'
+ SAMPLER_ARGS=
+ '[' true = true ']'
+ SAMPLER_ARGS=' --use_separate_test_sampler'
+ '[' true = true ']'
+ SAMPLER_ARGS=' --use_separate_test_sampler --use_balanced_task_sampler --kc_shot_multiplier 1.5426702091981206 --early_kc_shot_multiplier 1.8195508684673496'
+ LR_SCHEDULER_ARGS=
+ '[' true = true ']'
+ LR_SCHEDULER_ARGS='--use_lr_scheduler --scheduler_type plateau'
+ '[' plateau = onecycle ']'
+ '[' plateau = plateau ']'
+ LR_SCHEDULER_ARGS='--use_lr_scheduler --scheduler_type plateau --plateau_factor 0.5 --plateau_patience 2 --min_lr 1e-6'
+ BASE_CMD='python train_balanced_task_sampling.py   --train_csv /home/<USER>/balanced_task_sampling/split_result/train_set.csv   --val_csv /home/<USER>/balanced_task_sampling/split_result/val_set.csv   --test_csv /home/<USER>/balanced_task_sampling/split_result/test_set.csv   --epochs 50   --lr 0.0002   --weight_decay 5e-4   --feature_dim 512   --save_dir /home/<USER>/balanced_task_sampling/results_trial_20250525_050150   --proto_counts 2,6,4   --pretrained   --inner_lr 0.347389   --inner_steps 5   --n_way 3   --n_shot 5   --n_query 15   --tasks_per_epoch 30   --meta_batch_size 8   --no_augmentation   --mixup_alpha 0.0   --cutmix_prob 0.0   --num_workers 4   --augment_factor 1   --kc_augment_factor 1   --seed 42   --early_kc_weight 4.716860796358321   --kc_weight 6.812536223468256   --focal_gamma 1.08119417519286   --val_frequency 1   --early_stopping 3   --dropout 0.5    --use_separate_test_sampler --use_balanced_task_sampler --kc_shot_multiplier 1.5426702091981206 --early_kc_shot_multiplier 1.8195508684673496   --use_contrastive --contrastive_weight 0.315240842031719 --temperature 0.021494862108303127 --hard_mining_ratio 0.4426803149548197 --early_normal_weight 1.190756084848058 --kc_normal_weight 1.0      --use_lr_scheduler --scheduler_type plateau --plateau_factor 0.5 --plateau_patience 2 --min_lr 1e-6'
+ echo 开始无数据增强的平衡任务采样训练...
+ echo '使用GPU: 0'
+ '[' direct = nohup ']'
+ echo 直接运行训练脚本...
+ eval python train_balanced_task_sampling.py --train_csv /home/<USER>/balanced_task_sampling/split_result/train_set.csv --val_csv /home/<USER>/balanced_task_sampling/split_result/val_set.csv --test_csv /home/<USER>/balanced_task_sampling/split_result/test_set.csv --epochs 50 --lr 0.0002 --weight_decay 5e-4 --feature_dim 512 --save_dir /home/<USER>/balanced_task_sampling/results_trial_20250525_050150 --proto_counts 2,6,4 --pretrained --inner_lr 0.347389 --inner_steps 5 --n_way 3 --n_shot 5 --n_query 15 --tasks_per_epoch 30 --meta_batch_size 8 --no_augmentation --mixup_alpha 0.0 --cutmix_prob 0.0 --num_workers 4 --augment_factor 1 --kc_augment_factor 1 --seed 42 --early_kc_weight 4.716860796358321 --kc_weight 6.812536223468256 --focal_gamma 1.08119417519286 --val_frequency 1 --early_stopping 3 --dropout 0.5 --use_separate_test_sampler --use_balanced_task_sampler --kc_shot_multiplier 1.5426702091981206 --early_kc_shot_multiplier 1.8195508684673496 --use_contrastive --contrastive_weight 0.315240842031719 --temperature 0.021494862108303127 --hard_mining_ratio 0.4426803149548197 --early_normal_weight 1.190756084848058 --kc_normal_weight 1.0 --use_lr_scheduler --scheduler_type plateau --plateau_factor 0.5 --plateau_patience 2 --min_lr 1e-6
++ python train_balanced_task_sampling.py --train_csv /home/<USER>/balanced_task_sampling/split_result/train_set.csv --val_csv /home/<USER>/balanced_task_sampling/split_result/val_set.csv --test_csv /home/<USER>/balanced_task_sampling/split_result/test_set.csv --epochs 50 --lr 0.0002 --weight_decay 5e-4 --feature_dim 512 --save_dir /home/<USER>/balanced_task_sampling/results_trial_20250525_050150 --proto_counts 2,6,4 --pretrained --inner_lr 0.347389 --inner_steps 5 --n_way 3 --n_shot 5 --n_query 15 --tasks_per_epoch 30 --meta_batch_size 8 --no_augmentation --mixup_alpha 0.0 --cutmix_prob 0.0 --num_workers 4 --augment_factor 1 --kc_augment_factor 1 --seed 42 --early_kc_weight 4.716860796358321 --kc_weight 6.812536223468256 --focal_gamma 1.08119417519286 --val_frequency 1 --early_stopping 3 --dropout 0.5 --use_separate_test_sampler --use_balanced_task_sampler --kc_shot_multiplier 1.5426702091981206 --early_kc_shot_multiplier 1.8195508684673496 --use_contrastive --contrastive_weight 0.315240842031719 --temperature 0.021494862108303127 --hard_mining_ratio 0.4426803149548197 --early_normal_weight 1.190756084848058 --kc_normal_weight 1.0 --use_lr_scheduler --scheduler_type plateau --plateau_factor 0.5 --plateau_patience 2 --min_lr 1e-6
/home/<USER>/anaconda3/envs/kc1/lib/python3.10/site-packages/torchvision/models/_utils.py:208: UserWarning: The parameter 'pretrained' is deprecated since 0.13 and may be removed in the future, please use 'weights' instead.
  warnings.warn(
/home/<USER>/anaconda3/envs/kc1/lib/python3.10/site-packages/torchvision/models/_utils.py:223: UserWarning: Arguments other than a weight enum or `None` for 'weights' are deprecated since 0.13 and may be removed in the future. The current behavior is equivalent to passing `weights=ResNet34_Weights.IMAGENET1K_V1`. You can also use `weights=ResNet34_Weights.DEFAULT` to get the most up-to-date weights.
  warnings.warn(msg)
/home/<USER>/anaconda3/envs/kc1/lib/python3.10/site-packages/torch/optim/lr_scheduler.py:62: UserWarning: The verbose parameter is deprecated. Please use get_last_lr() to access the learning rate.
  warnings.warn(

Training tasks:   0%|          | 0/30 [00:00<?, ?it/s]
Training tasks:   3%|▎         | 1/30 [00:03<01:52,  3.89s/it]
Training tasks:   7%|▋         | 2/30 [00:04<01:00,  2.17s/it]
Training tasks:  10%|█         | 3/30 [00:05<00:43,  1.61s/it]
Training tasks:  13%|█▎        | 4/30 [00:06<00:34,  1.34s/it]
Training tasks:  17%|█▋        | 5/30 [00:07<00:29,  1.19s/it]
Training tasks:  20%|██        | 6/30 [00:08<00:26,  1.10s/it]
Training tasks:  23%|██▎       | 7/30 [00:09<00:24,  1.04s/it]
Training tasks:  27%|██▋       | 8/30 [00:10<00:22,  1.01s/it]
Training tasks:  30%|███       | 9/30 [00:11<00:20,  1.01it/s]
Training tasks:  33%|███▎      | 10/30 [00:12<00:19,  1.03it/s]
Training tasks:  37%|███▋      | 11/30 [00:13<00:18,  1.05it/s]
Training tasks:  40%|████      | 12/30 [00:14<00:16,  1.06it/s]
Training tasks:  43%|████▎     | 13/30 [00:15<00:15,  1.07it/s]
Training tasks:  47%|████▋     | 14/30 [00:15<00:14,  1.07it/s]
Training tasks:  50%|█████     | 15/30 [00:16<00:13,  1.07it/s]
Training tasks:  53%|█████▎    | 16/30 [00:17<00:12,  1.09it/s]
Training tasks:  57%|█████▋    | 17/30 [00:18<00:11,  1.09it/s]
Training tasks:  60%|██████    | 18/30 [00:19<00:11,  1.09it/s]
Training tasks:  63%|██████▎   | 19/30 [00:20<00:10,  1.09it/s]
Training tasks:  67%|██████▋   | 20/30 [00:21<00:09,  1.09it/s]
Training tasks:  70%|███████   | 21/30 [00:22<00:08,  1.09it/s]
Training tasks:  73%|███████▎  | 22/30 [00:23<00:07,  1.09it/s]
Training tasks:  77%|███████▋  | 23/30 [00:24<00:06,  1.09it/s]
Training tasks:  80%|████████  | 24/30 [00:25<00:05,  1.09it/s]
Training tasks:  83%|████████▎ | 25/30 [00:26<00:04,  1.09it/s]
Training tasks:  87%|████████▋ | 26/30 [00:26<00:03,  1.10it/s]
Training tasks:  90%|█████████ | 27/30 [00:27<00:02,  1.09it/s]
Training tasks:  93%|█████████▎| 28/30 [00:28<00:01,  1.09it/s]
Training tasks:  97%|█████████▋| 29/30 [00:29<00:00,  1.09it/s]
Training tasks: 100%|██████████| 30/30 [00:30<00:00,  1.10it/s]
Training tasks: 100%|██████████| 30/30 [00:30<00:00,  1.02s/it]

Validating tasks:   0%|          | 0/10 [00:00<?, ?it/s]
Validating tasks:  10%|█         | 1/10 [00:00<00:01,  6.79it/s]
Validating tasks:  20%|██        | 2/10 [00:00<00:01,  7.71it/s]
Validating tasks:  30%|███       | 3/10 [00:00<00:00,  8.02it/s]
Validating tasks:  40%|████      | 4/10 [00:00<00:00,  8.19it/s]
Validating tasks:  50%|█████     | 5/10 [00:00<00:00,  8.34it/s]
Validating tasks:  60%|██████    | 6/10 [00:00<00:00,  8.40it/s]
Validating tasks:  70%|███████   | 7/10 [00:00<00:00,  8.44it/s]
Validating tasks:  80%|████████  | 8/10 [00:00<00:00,  8.49it/s]
Validating tasks:  90%|█████████ | 9/10 [00:01<00:00,  8.48it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  8.52it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  8.31it/s]

Training tasks:   0%|          | 0/30 [00:00<?, ?it/s]
Training tasks:   3%|▎         | 1/30 [00:00<00:28,  1.03it/s]
Training tasks:   7%|▋         | 2/30 [00:01<00:26,  1.06it/s]
Training tasks:  10%|█         | 3/30 [00:02<00:25,  1.07it/s]
Training tasks:  13%|█▎        | 4/30 [00:03<00:23,  1.08it/s]
Training tasks:  17%|█▋        | 5/30 [00:04<00:22,  1.09it/s]
Training tasks:  20%|██        | 6/30 [00:05<00:22,  1.09it/s]
Training tasks:  23%|██▎       | 7/30 [00:06<00:21,  1.09it/s]
Training tasks:  27%|██▋       | 8/30 [00:07<00:20,  1.08it/s]
Training tasks:  30%|███       | 9/30 [00:08<00:19,  1.09it/s]
Training tasks:  33%|███▎      | 10/30 [00:09<00:18,  1.09it/s]
Training tasks:  37%|███▋      | 11/30 [00:10<00:17,  1.08it/s]
Training tasks:  40%|████      | 12/30 [00:11<00:16,  1.08it/s]
Training tasks:  43%|████▎     | 13/30 [00:12<00:15,  1.09it/s]
Training tasks:  47%|████▋     | 14/30 [00:12<00:14,  1.09it/s]
Training tasks:  50%|█████     | 15/30 [00:13<00:13,  1.09it/s]
Training tasks:  53%|█████▎    | 16/30 [00:14<00:12,  1.08it/s]
Training tasks:  57%|█████▋    | 17/30 [00:15<00:11,  1.09it/s]
Training tasks:  60%|██████    | 18/30 [00:16<00:11,  1.08it/s]
Training tasks:  63%|██████▎   | 19/30 [00:17<00:10,  1.08it/s]
Training tasks:  67%|██████▋   | 20/30 [00:18<00:09,  1.08it/s]
Training tasks:  70%|███████   | 21/30 [00:19<00:08,  1.09it/s]
Training tasks:  73%|███████▎  | 22/30 [00:20<00:07,  1.09it/s]
Training tasks:  77%|███████▋  | 23/30 [00:21<00:06,  1.09it/s]
Training tasks:  80%|████████  | 24/30 [00:22<00:05,  1.09it/s]
Training tasks:  83%|████████▎ | 25/30 [00:23<00:04,  1.10it/s]
Training tasks:  87%|████████▋ | 26/30 [00:23<00:03,  1.10it/s]
Training tasks:  90%|█████████ | 27/30 [00:24<00:02,  1.10it/s]
Training tasks:  93%|█████████▎| 28/30 [00:25<00:01,  1.11it/s]
Training tasks:  97%|█████████▋| 29/30 [00:26<00:00,  1.10it/s]
Training tasks: 100%|██████████| 30/30 [00:27<00:00,  1.10it/s]
Training tasks: 100%|██████████| 30/30 [00:27<00:00,  1.09it/s]

Validating tasks:   0%|          | 0/10 [00:00<?, ?it/s]
Validating tasks:  10%|█         | 1/10 [00:00<00:00,  9.04it/s]
Validating tasks:  20%|██        | 2/10 [00:00<00:00,  8.90it/s]
Validating tasks:  30%|███       | 3/10 [00:00<00:00,  8.92it/s]
Validating tasks:  40%|████      | 4/10 [00:00<00:00,  8.83it/s]
Validating tasks:  50%|█████     | 5/10 [00:00<00:00,  8.79it/s]
Validating tasks:  60%|██████    | 6/10 [00:00<00:00,  8.87it/s]
Validating tasks:  70%|███████   | 7/10 [00:00<00:00,  8.90it/s]
Validating tasks:  80%|████████  | 8/10 [00:00<00:00,  8.81it/s]
Validating tasks:  90%|█████████ | 9/10 [00:01<00:00,  8.87it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  8.84it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  8.85it/s]

Training tasks:   0%|          | 0/30 [00:00<?, ?it/s]
Training tasks:   3%|▎         | 1/30 [00:00<00:26,  1.09it/s]
Training tasks:   7%|▋         | 2/30 [00:01<00:25,  1.09it/s]
Training tasks:  10%|█         | 3/30 [00:02<00:24,  1.10it/s]
Training tasks:  13%|█▎        | 4/30 [00:03<00:23,  1.11it/s]
Training tasks:  17%|█▋        | 5/30 [00:04<00:22,  1.12it/s]
Training tasks:  20%|██        | 6/30 [00:05<00:21,  1.12it/s]
Training tasks:  23%|██▎       | 7/30 [00:06<00:20,  1.12it/s]
Training tasks:  27%|██▋       | 8/30 [00:07<00:19,  1.12it/s]
Training tasks:  30%|███       | 9/30 [00:08<00:18,  1.12it/s]
Training tasks:  33%|███▎      | 10/30 [00:08<00:17,  1.12it/s]
Training tasks:  37%|███▋      | 11/30 [00:09<00:16,  1.12it/s]
Training tasks:  40%|████      | 12/30 [00:10<00:16,  1.12it/s]
Training tasks:  43%|████▎     | 13/30 [00:11<00:15,  1.12it/s]
Training tasks:  47%|████▋     | 14/30 [00:12<00:14,  1.12it/s]
Training tasks:  50%|█████     | 15/30 [00:13<00:13,  1.12it/s]
Training tasks:  53%|█████▎    | 16/30 [00:14<00:12,  1.12it/s]
Training tasks:  57%|█████▋    | 17/30 [00:15<00:11,  1.12it/s]
Training tasks:  60%|██████    | 18/30 [00:16<00:10,  1.12it/s]
Training tasks:  63%|██████▎   | 19/30 [00:16<00:09,  1.12it/s]
Training tasks:  67%|██████▋   | 20/30 [00:17<00:08,  1.12it/s]
Training tasks:  70%|███████   | 21/30 [00:18<00:08,  1.12it/s]
Training tasks:  73%|███████▎  | 22/30 [00:19<00:07,  1.13it/s]
Training tasks:  77%|███████▋  | 23/30 [00:20<00:06,  1.13it/s]
Training tasks:  80%|████████  | 24/30 [00:21<00:05,  1.13it/s]
Training tasks:  83%|████████▎ | 25/30 [00:22<00:04,  1.13it/s]
Training tasks:  87%|████████▋ | 26/30 [00:23<00:03,  1.13it/s]
Training tasks:  90%|█████████ | 27/30 [00:24<00:02,  1.13it/s]
Training tasks:  93%|█████████▎| 28/30 [00:24<00:01,  1.13it/s]
Training tasks:  97%|█████████▋| 29/30 [00:25<00:00,  1.13it/s]
Training tasks: 100%|██████████| 30/30 [00:26<00:00,  1.12it/s]
Training tasks: 100%|██████████| 30/30 [00:26<00:00,  1.12it/s]

Validating tasks:   0%|          | 0/10 [00:00<?, ?it/s]
Validating tasks:  10%|█         | 1/10 [00:00<00:00,  9.10it/s]
Validating tasks:  20%|██        | 2/10 [00:00<00:00,  9.22it/s]
Validating tasks:  30%|███       | 3/10 [00:00<00:00,  9.10it/s]
Validating tasks:  40%|████      | 4/10 [00:00<00:00,  9.20it/s]
Validating tasks:  50%|█████     | 5/10 [00:00<00:00,  9.09it/s]
Validating tasks:  60%|██████    | 6/10 [00:00<00:00,  9.15it/s]
Validating tasks:  70%|███████   | 7/10 [00:00<00:00,  9.08it/s]
Validating tasks:  80%|████████  | 8/10 [00:00<00:00,  9.14it/s]
Validating tasks:  90%|█████████ | 9/10 [00:00<00:00,  9.07it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  9.14it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  9.13it/s]

Training tasks:   0%|          | 0/30 [00:00<?, ?it/s]
Training tasks:   3%|▎         | 1/30 [00:00<00:27,  1.06it/s]
Training tasks:   7%|▋         | 2/30 [00:01<00:26,  1.08it/s]
Training tasks:  10%|█         | 3/30 [00:02<00:24,  1.10it/s]
Training tasks:  13%|█▎        | 4/30 [00:03<00:23,  1.10it/s]
Training tasks:  17%|█▋        | 5/30 [00:04<00:22,  1.10it/s]
Training tasks:  20%|██        | 6/30 [00:05<00:21,  1.10it/s]
Training tasks:  23%|██▎       | 7/30 [00:06<00:20,  1.10it/s]
Training tasks:  27%|██▋       | 8/30 [00:07<00:19,  1.10it/s]
Training tasks:  30%|███       | 9/30 [00:08<00:19,  1.10it/s]
Training tasks:  33%|███▎      | 10/30 [00:09<00:18,  1.10it/s]
Training tasks:  37%|███▋      | 11/30 [00:09<00:17,  1.11it/s]
Training tasks:  40%|████      | 12/30 [00:10<00:16,  1.12it/s]
Training tasks:  43%|████▎     | 13/30 [00:11<00:15,  1.11it/s]
Training tasks:  47%|████▋     | 14/30 [00:12<00:14,  1.09it/s]
Training tasks:  50%|█████     | 15/30 [00:13<00:13,  1.09it/s]
Training tasks:  53%|█████▎    | 16/30 [00:14<00:12,  1.10it/s]
Training tasks:  57%|█████▋    | 17/30 [00:15<00:11,  1.10it/s]
Training tasks:  60%|██████    | 18/30 [00:16<00:10,  1.11it/s]
Training tasks:  63%|██████▎   | 19/30 [00:17<00:09,  1.12it/s]
Training tasks:  67%|██████▋   | 20/30 [00:18<00:08,  1.12it/s]
Training tasks:  70%|███████   | 21/30 [00:18<00:08,  1.12it/s]
Training tasks:  73%|███████▎  | 22/30 [00:19<00:07,  1.12it/s]
Training tasks:  77%|███████▋  | 23/30 [00:20<00:06,  1.12it/s]
Training tasks:  80%|████████  | 24/30 [00:21<00:05,  1.12it/s]
Training tasks:  83%|████████▎ | 25/30 [00:22<00:04,  1.12it/s]
Training tasks:  87%|████████▋ | 26/30 [00:23<00:03,  1.12it/s]
Training tasks:  90%|█████████ | 27/30 [00:24<00:02,  1.13it/s]
Training tasks:  93%|█████████▎| 28/30 [00:25<00:01,  1.12it/s]
Training tasks:  97%|█████████▋| 29/30 [00:26<00:00,  1.13it/s]
Training tasks: 100%|██████████| 30/30 [00:26<00:00,  1.12it/s]
Training tasks: 100%|██████████| 30/30 [00:26<00:00,  1.11it/s]

Validating tasks:   0%|          | 0/10 [00:00<?, ?it/s]
Validating tasks:  10%|█         | 1/10 [00:00<00:01,  8.82it/s]
Validating tasks:  20%|██        | 2/10 [00:00<00:00,  9.00it/s]
Validating tasks:  30%|███       | 3/10 [00:00<00:00,  8.92it/s]
Validating tasks:  40%|████      | 4/10 [00:00<00:00,  9.07it/s]
Validating tasks:  50%|█████     | 5/10 [00:00<00:00,  9.03it/s]
Validating tasks:  60%|██████    | 6/10 [00:00<00:00,  9.15it/s]
Validating tasks:  70%|███████   | 7/10 [00:00<00:00,  9.09it/s]
Validating tasks:  80%|████████  | 8/10 [00:00<00:00,  9.04it/s]
Validating tasks:  90%|█████████ | 9/10 [00:00<00:00,  8.95it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  9.06it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  9.03it/s]

Training tasks:   0%|          | 0/30 [00:00<?, ?it/s]
Training tasks:   3%|▎         | 1/30 [00:00<00:26,  1.09it/s]
Training tasks:   7%|▋         | 2/30 [00:01<00:25,  1.10it/s]
Training tasks:  10%|█         | 3/30 [00:02<00:24,  1.12it/s]
Training tasks:  13%|█▎        | 4/30 [00:03<00:23,  1.12it/s]
Training tasks:  17%|█▋        | 5/30 [00:04<00:22,  1.12it/s]
Training tasks:  20%|██        | 6/30 [00:05<00:21,  1.12it/s]
Training tasks:  23%|██▎       | 7/30 [00:06<00:20,  1.12it/s]
Training tasks:  27%|██▋       | 8/30 [00:07<00:19,  1.12it/s]
Training tasks:  30%|███       | 9/30 [00:08<00:18,  1.12it/s]
Training tasks:  33%|███▎      | 10/30 [00:08<00:17,  1.12it/s]
Training tasks:  37%|███▋      | 11/30 [00:09<00:17,  1.11it/s]
Training tasks:  40%|████      | 12/30 [00:10<00:16,  1.12it/s]
Training tasks:  43%|████▎     | 13/30 [00:11<00:15,  1.12it/s]
Training tasks:  47%|████▋     | 14/30 [00:12<00:14,  1.12it/s]
Training tasks:  50%|█████     | 15/30 [00:13<00:13,  1.10it/s]
Training tasks:  53%|█████▎    | 16/30 [00:14<00:12,  1.09it/s]
Training tasks:  57%|█████▋    | 17/30 [00:15<00:11,  1.09it/s]
Training tasks:  60%|██████    | 18/30 [00:16<00:11,  1.08it/s]
Training tasks:  63%|██████▎   | 19/30 [00:17<00:10,  1.09it/s]
Training tasks:  67%|██████▋   | 20/30 [00:18<00:09,  1.10it/s]
Training tasks:  70%|███████   | 21/30 [00:18<00:08,  1.10it/s]
Training tasks:  73%|███████▎  | 22/30 [00:19<00:07,  1.10it/s]
Training tasks:  77%|███████▋  | 23/30 [00:20<00:06,  1.11it/s]
Training tasks:  80%|████████  | 24/30 [00:21<00:05,  1.11it/s]
Training tasks:  83%|████████▎ | 25/30 [00:22<00:04,  1.11it/s]
Training tasks:  87%|████████▋ | 26/30 [00:23<00:03,  1.11it/s]
Training tasks:  90%|█████████ | 27/30 [00:24<00:02,  1.11it/s]
Training tasks:  93%|█████████▎| 28/30 [00:25<00:01,  1.10it/s]
Training tasks:  97%|█████████▋| 29/30 [00:26<00:00,  1.11it/s]
Training tasks: 100%|██████████| 30/30 [00:27<00:00,  1.12it/s]
Training tasks: 100%|██████████| 30/30 [00:27<00:00,  1.11it/s]

Validating tasks:   0%|          | 0/10 [00:00<?, ?it/s]
Validating tasks:  10%|█         | 1/10 [00:00<00:00,  9.24it/s]
Validating tasks:  20%|██        | 2/10 [00:00<00:00,  9.06it/s]
Validating tasks:  30%|███       | 3/10 [00:00<00:00,  9.10it/s]
Validating tasks:  40%|████      | 4/10 [00:00<00:00,  9.12it/s]
Validating tasks:  50%|█████     | 5/10 [00:00<00:00,  9.12it/s]
Validating tasks:  60%|██████    | 6/10 [00:00<00:00,  9.16it/s]
Validating tasks:  70%|███████   | 7/10 [00:00<00:00,  9.10it/s]
Validating tasks:  80%|████████  | 8/10 [00:00<00:00,  9.15it/s]
Validating tasks:  90%|█████████ | 9/10 [00:00<00:00,  9.18it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  9.14it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  9.14it/s]

Training tasks:   0%|          | 0/30 [00:00<?, ?it/s]
Training tasks:   3%|▎         | 1/30 [00:00<00:26,  1.10it/s]
Training tasks:   7%|▋         | 2/30 [00:01<00:25,  1.12it/s]
Training tasks:  10%|█         | 3/30 [00:02<00:24,  1.11it/s]
Training tasks:  13%|█▎        | 4/30 [00:03<00:23,  1.12it/s]
Training tasks:  17%|█▋        | 5/30 [00:04<00:22,  1.13it/s]
Training tasks:  20%|██        | 6/30 [00:05<00:21,  1.12it/s]
Training tasks:  23%|██▎       | 7/30 [00:06<00:20,  1.12it/s]
Training tasks:  27%|██▋       | 8/30 [00:07<00:19,  1.13it/s]
Training tasks:  30%|███       | 9/30 [00:08<00:18,  1.13it/s]
Training tasks:  33%|███▎      | 10/30 [00:08<00:17,  1.13it/s]
Training tasks:  37%|███▋      | 11/30 [00:09<00:16,  1.13it/s]
Training tasks:  40%|████      | 12/30 [00:10<00:15,  1.13it/s]
Training tasks:  43%|████▎     | 13/30 [00:11<00:15,  1.13it/s]
Training tasks:  47%|████▋     | 14/30 [00:12<00:14,  1.14it/s]
Training tasks:  50%|█████     | 15/30 [00:13<00:13,  1.14it/s]
Training tasks:  53%|█████▎    | 16/30 [00:14<00:12,  1.12it/s]
Training tasks:  57%|█████▋    | 17/30 [00:15<00:11,  1.12it/s]
Training tasks:  60%|██████    | 18/30 [00:15<00:10,  1.13it/s]
Training tasks:  63%|██████▎   | 19/30 [00:16<00:09,  1.14it/s]
Training tasks:  67%|██████▋   | 20/30 [00:17<00:08,  1.13it/s]
Training tasks:  70%|███████   | 21/30 [00:18<00:07,  1.13it/s]
Training tasks:  73%|███████▎  | 22/30 [00:19<00:07,  1.13it/s]
Training tasks:  77%|███████▋  | 23/30 [00:20<00:06,  1.13it/s]
Training tasks:  80%|████████  | 24/30 [00:21<00:05,  1.13it/s]
Training tasks:  83%|████████▎ | 25/30 [00:22<00:04,  1.12it/s]
Training tasks:  87%|████████▋ | 26/30 [00:23<00:03,  1.12it/s]
Training tasks:  90%|█████████ | 27/30 [00:23<00:02,  1.13it/s]
Training tasks:  93%|█████████▎| 28/30 [00:24<00:01,  1.13it/s]
Training tasks:  97%|█████████▋| 29/30 [00:25<00:00,  1.14it/s]
Training tasks: 100%|██████████| 30/30 [00:26<00:00,  1.14it/s]
Training tasks: 100%|██████████| 30/30 [00:26<00:00,  1.13it/s]

Validating tasks:   0%|          | 0/10 [00:00<?, ?it/s]
Validating tasks:  10%|█         | 1/10 [00:00<00:00,  9.36it/s]
Validating tasks:  20%|██        | 2/10 [00:00<00:00,  8.98it/s]
Validating tasks:  30%|███       | 3/10 [00:00<00:00,  8.72it/s]
Validating tasks:  40%|████      | 4/10 [00:00<00:00,  8.75it/s]
Validating tasks:  50%|█████     | 5/10 [00:00<00:00,  8.73it/s]
Validating tasks:  60%|██████    | 6/10 [00:00<00:00,  8.87it/s]
Validating tasks:  70%|███████   | 7/10 [00:00<00:00,  8.75it/s]
Validating tasks:  80%|████████  | 8/10 [00:00<00:00,  8.88it/s]
Validating tasks:  90%|█████████ | 9/10 [00:01<00:00,  8.85it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  8.95it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  8.87it/s]

Testing tasks:   0%|          | 0/30 [00:00<?, ?it/s]
Testing tasks:   3%|▎         | 1/30 [00:00<00:05,  5.68it/s]
Testing tasks:   7%|▋         | 2/30 [00:00<00:04,  6.24it/s]
Testing tasks:  10%|█         | 3/30 [00:00<00:04,  6.44it/s]
Testing tasks:  13%|█▎        | 4/30 [00:00<00:03,  6.51it/s]
Testing tasks:  17%|█▋        | 5/30 [00:00<00:03,  6.54it/s]
Testing tasks:  20%|██        | 6/30 [00:00<00:03,  6.56it/s]
Testing tasks:  23%|██▎       | 7/30 [00:01<00:03,  6.58it/s]
Testing tasks:  27%|██▋       | 8/30 [00:01<00:03,  6.57it/s]
Testing tasks:  30%|███       | 9/30 [00:01<00:03,  6.58it/s]
Testing tasks:  33%|███▎      | 10/30 [00:01<00:02,  6.69it/s]
Testing tasks:  37%|███▋      | 11/30 [00:01<00:02,  6.75it/s]
Testing tasks:  40%|████      | 12/30 [00:01<00:02,  6.79it/s]
Testing tasks:  43%|████▎     | 13/30 [00:01<00:02,  6.72it/s]
Testing tasks:  47%|████▋     | 14/30 [00:02<00:02,  6.78it/s]
Testing tasks:  50%|█████     | 15/30 [00:02<00:02,  6.81it/s]
Testing tasks:  53%|█████▎    | 16/30 [00:02<00:02,  6.85it/s]
Testing tasks:  57%|█████▋    | 17/30 [00:02<00:01,  6.88it/s]
Testing tasks:  60%|██████    | 18/30 [00:02<00:01,  6.90it/s]
Testing tasks:  63%|██████▎   | 19/30 [00:02<00:01,  6.92it/s]
Testing tasks:  67%|██████▋   | 20/30 [00:02<00:01,  6.94it/s]
Testing tasks:  70%|███████   | 21/30 [00:03<00:01,  6.94it/s]
Testing tasks:  73%|███████▎  | 22/30 [00:03<00:01,  6.95it/s]
Testing tasks:  77%|███████▋  | 23/30 [00:03<00:01,  6.94it/s]
Testing tasks:  80%|████████  | 24/30 [00:03<00:00,  6.94it/s]
Testing tasks:  83%|████████▎ | 25/30 [00:03<00:00,  6.95it/s]
Testing tasks:  87%|████████▋ | 26/30 [00:03<00:00,  6.96it/s]
Testing tasks:  90%|█████████ | 27/30 [00:03<00:00,  6.95it/s]
Testing tasks:  93%|█████████▎| 28/30 [00:04<00:00,  6.95it/s]
Testing tasks:  97%|█████████▋| 29/30 [00:04<00:00,  6.94it/s]
Testing tasks: 100%|██████████| 30/30 [00:04<00:00,  6.95it/s]
Testing tasks: 100%|██████████| 30/30 [00:04<00:00,  6.79it/s]

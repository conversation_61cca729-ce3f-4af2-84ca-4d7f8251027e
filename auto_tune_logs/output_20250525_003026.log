STDOUT:
===================== 训练配置 =====================
GPU: 0
输出目录: /home/<USER>/balanced_task_sampling/results_trial_20250525_003026
日志文件: /home/<USER>/balanced_task_sampling/results_trial_20250525_003026/main_training_trial_26_20250525_003026.log

--------- 优化参数 ---------
内部学习率: 0.347389
训练轮数: 50
每轮任务数: 30

--------- 模型参数 ---------
使用类别权重: 圆锥角膜=6.88631981995826, 早期圆锥角膜=4.465037338622979
温度参数: 0.07136091213566449
硬样本挖掘比例: 0.5107925561204784
对比学习权重: 0.6330396760937711
=============================================

创建目录: /home/<USER>/balanced_task_sampling/results_trial_20250525_003026
总用量 4
-rw-rw-r-- 1 <USER> <GROUP> 656 5月  25 00:30 main_training_trial_26_20250525_003026.log
开始无数据增强的平衡任务采样训练...
使用GPU: 0
直接运行训练脚本...
成功导入增强版特征提取器
成功导入归一化特征提取器
成功导入增强版损失函数
成功导入增强版MAML模型
成功导入KC特定增强模块
成功导入增强版GPU数据增强模块
使用设备: cuda
禁用所有数据增强，直接加载原始数据集...
使用平衡任务采样器
类别 normal (标签 0): 164个样本
类别 e-kc (标签 1): 26个样本
类别 kc (标签 2): 133个样本
使用原型配置: [3, 6, 3]
使用增强版特征提取器，dropout率: 0.5
使用增强版MAML-ProtoNet模型，内循环学习率: 0.347389, 内循环步数: 5
使用ReduceLROnPlateau学习率调度器，因子: 0.5, 耐心值: 2
开始训练，共50个epoch
Epoch 1/50
当前学习率: 0.000200
使用类别权重: 正常=1.0, 早期圆锥角膜=4.465037338622979, 圆锥角膜=6.88631981995826
使用增强版对比损失函数，温度: 0.07136091213566449, 硬负样本挖掘比例: 0.5107925561204784, 早期圆锥角膜与正常样本对比权重: 1.6079647764558842, 圆锥角膜与正常样本对比权重: 1.0
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
警告: 没有足够的有效类别进行3-way任务采样，尝试调整参数...
类别样本数量: {0: 33, 1: 5, 2: 29}
调整后的参数: n_shot=2, n_query=3
类别 normal (标签 0): 33个样本
类别 e-kc (标签 1): 5个样本
类别 kc (标签 2): 29个样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
Epoch 1: Train Loss: 2.3814, Train Acc: 0.6615, Val Loss: 2.3874, Val Acc: 0.5667
保存最佳模型，验证准确率: 0.5667
Epoch 2/50
当前学习率: 0.000200
使用类别权重: 正常=1.0, 早期圆锥角膜=4.465037338622979, 圆锥角膜=6.88631981995826
使用增强版对比损失函数，温度: 0.07136091213566449, 硬负样本挖掘比例: 0.5107925561204784, 早期圆锥角膜与正常样本对比权重: 1.6079647764558842, 圆锥角膜与正常样本对比权重: 1.0
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
警告: 没有足够的有效类别进行3-way任务采样，尝试调整参数...
类别样本数量: {0: 33, 1: 5, 2: 29}
调整后的参数: n_shot=2, n_query=3
类别 normal (标签 0): 33个样本
类别 e-kc (标签 1): 5个样本
类别 kc (标签 2): 29个样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
Epoch 2: Train Loss: 1.9448, Train Acc: 0.8622, Val Loss: 2.3171, Val Acc: 0.6000
保存最佳模型，验证准确率: 0.6000
Epoch 3/50
当前学习率: 0.000200
使用类别权重: 正常=1.0, 早期圆锥角膜=4.465037338622979, 圆锥角膜=6.88631981995826
使用增强版对比损失函数，温度: 0.07136091213566449, 硬负样本挖掘比例: 0.5107925561204784, 早期圆锥角膜与正常样本对比权重: 1.6079647764558842, 圆锥角膜与正常样本对比权重: 1.0
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
警告: 没有足够的有效类别进行3-way任务采样，尝试调整参数...
类别样本数量: {0: 33, 1: 5, 2: 29}
调整后的参数: n_shot=2, n_query=3
类别 normal (标签 0): 33个样本
类别 e-kc (标签 1): 5个样本
类别 kc (标签 2): 29个样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
Epoch 3: Train Loss: 1.5113, Train Acc: 0.9200, Val Loss: 2.2053, Val Acc: 0.5111
验证准确率未提高，当前耐心值: 1/3
Epoch 4/50
当前学习率: 0.000200
使用类别权重: 正常=1.0, 早期圆锥角膜=4.465037338622979, 圆锥角膜=6.88631981995826
使用增强版对比损失函数，温度: 0.07136091213566449, 硬负样本挖掘比例: 0.5107925561204784, 早期圆锥角膜与正常样本对比权重: 1.6079647764558842, 圆锥角膜与正常样本对比权重: 1.0
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
警告: 没有足够的有效类别进行3-way任务采样，尝试调整参数...
类别样本数量: {0: 33, 1: 5, 2: 29}
调整后的参数: n_shot=2, n_query=3
类别 normal (标签 0): 33个样本
类别 e-kc (标签 1): 5个样本
类别 kc (标签 2): 29个样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
Epoch 4: Train Loss: 1.5769, Train Acc: 0.9089, Val Loss: 2.3814, Val Acc: 0.5222
验证准确率未提高，当前耐心值: 2/3
Epoch 5/50
当前学习率: 0.000200
使用类别权重: 正常=1.0, 早期圆锥角膜=4.465037338622979, 圆锥角膜=6.88631981995826
使用增强版对比损失函数，温度: 0.07136091213566449, 硬负样本挖掘比例: 0.5107925561204784, 早期圆锥角膜与正常样本对比权重: 1.6079647764558842, 圆锥角膜与正常样本对比权重: 1.0
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
警告: 没有足够的有效类别进行3-way任务采样，尝试调整参数...
类别样本数量: {0: 33, 1: 5, 2: 29}
调整后的参数: n_shot=2, n_query=3
类别 normal (标签 0): 33个样本
类别 e-kc (标签 1): 5个样本
类别 kc (标签 2): 29个样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
Epoch 5: Train Loss: 1.4216, Train Acc: 0.9467, Val Loss: 2.1902, Val Acc: 0.5778
验证准确率未提高，当前耐心值: 3/3
早停触发，停止训练
警告: 没有足够的有效类别进行3-way任务采样，尝试调整参数...
类别样本数量: {0: 37, 1: 7, 2: 27}
调整后的参数: n_shot=3, n_query=4
类别 normal (标签 0): 37个样本
类别 e-kc (标签 1): 7个样本
类别 kc (标签 2): 27个样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本

测试结果:
总体准确率: 65.00%
类别准确率:
  kc: 57.50%
  e-kc: 43.33%
  normal: 94.17%
训练完成，结果已保存到 /home/<USER>/balanced_task_sampling/results_trial_20250525_003026

STDERR:
+ TIMESTAMP=20250525_003026
+ RESULT_DIR=/home/<USER>/balanced_task_sampling/results_trial_20250525_003026
+ '[' -n /home/<USER>/balanced_task_sampling/results_trial_20250525_003026/main_training_trial_26_20250525_003026.log ']'
+ LOG_FILE=/home/<USER>/balanced_task_sampling/results_trial_20250525_003026/main_training_trial_26_20250525_003026.log
++ dirname /home/<USER>/balanced_task_sampling/results_trial_20250525_003026/main_training_trial_26_20250525_003026.log
+ mkdir -p /home/<USER>/balanced_task_sampling/results_trial_20250525_003026
+ mkdir -p /home/<USER>/balanced_task_sampling/results_trial_20250525_003026
+ echo '===================== 训练配置 ====================='
+ echo 'GPU: 0'
+ tee -a /home/<USER>/balanced_task_sampling/results_trial_20250525_003026/main_training_trial_26_20250525_003026.log
+ echo '输出目录: /home/<USER>/balanced_task_sampling/results_trial_20250525_003026'
+ echo '日志文件: /home/<USER>/balanced_task_sampling/results_trial_20250525_003026/main_training_trial_26_20250525_003026.log'
+ echo ''
+ echo '--------- 优化参数 ---------'
+ echo '内部学习率: 0.347389'
+ echo '训练轮数: 50'
+ echo '每轮任务数: 30'
+ echo ''
+ echo '--------- 模型参数 ---------'
+ echo '使用类别权重: 圆锥角膜=6.88631981995826, 早期圆锥角膜=4.465037338622979'
+ echo '温度参数: 0.07136091213566449'
+ echo '硬样本挖掘比例: 0.5107925561204784'
+ echo '对比学习权重: 0.6330396760937711'
+ echo '=============================================
'
+ validate_params
+ local error_count=0
++ dirname /home/<USER>/balanced_task_sampling/results_trial_20250525_003026/main_training_trial_26_20250525_003026.log
++ dirname /home/<USER>/balanced_task_sampling/split_result/train_set.csv
+ for dir in "$RESULT_DIR" "$(dirname "$LOG_FILE")" "$(dirname "$TRAIN_CSV")"
+ mkdir -p /home/<USER>/balanced_task_sampling/results_trial_20250525_003026
+ for dir in "$RESULT_DIR" "$(dirname "$LOG_FILE")" "$(dirname "$TRAIN_CSV")"
+ mkdir -p /home/<USER>/balanced_task_sampling/results_trial_20250525_003026
+ for dir in "$RESULT_DIR" "$(dirname "$LOG_FILE")" "$(dirname "$TRAIN_CSV")"
+ mkdir -p /home/<USER>/balanced_task_sampling/split_result
+ for csv in "$TRAIN_CSV" "$VAL_CSV" "$TEST_CSV"
+ '[' '!' -f /home/<USER>/balanced_task_sampling/split_result/train_set.csv ']'
+ for csv in "$TRAIN_CSV" "$VAL_CSV" "$TEST_CSV"
+ '[' '!' -f /home/<USER>/balanced_task_sampling/split_result/val_set.csv ']'
+ for csv in "$TRAIN_CSV" "$VAL_CSV" "$TEST_CSV"
+ '[' '!' -f /home/<USER>/balanced_task_sampling/split_result/test_set.csv ']'
+ [[ 0.0002 =~ ^[0-9.]+$ ]]
+ [[ 0.347389 =~ ^[0-9.]+$ ]]
+ '[' 0 -gt 0 ']'
+ return 0
+ '[' false = true ']'
+ export CUDA_VISIBLE_DEVICES=0
+ CUDA_VISIBLE_DEVICES=0
+ export OMP_NUM_THREADS=1
+ OMP_NUM_THREADS=1
+ export MKL_NUM_THREADS=1
+ MKL_NUM_THREADS=1
+ export MPLBACKEND=Agg
+ MPLBACKEND=Agg
+ mkdir -p /home/<USER>/balanced_task_sampling/results_trial_20250525_003026
+ echo '创建目录: /home/<USER>/balanced_task_sampling/results_trial_20250525_003026'
+ ls -l /home/<USER>/balanced_task_sampling/results_trial_20250525_003026
+ PRETRAINED_ARGS=
+ '[' true = true ']'
+ PRETRAINED_ARGS=--pretrained
+ AUGMENT_ARGS=--no_augmentation
+ CONTRASTIVE_ARGS=
+ '[' true = true ']'
+ CONTRASTIVE_ARGS='--use_contrastive --contrastive_weight 0.6330396760937711 --temperature 0.07136091213566449 --hard_mining_ratio 0.5107925561204784 --early_normal_weight 1.6079647764558842 --kc_normal_weight 1.0'
+ NORMALIZE_ARGS=
+ '[' false = true ']'
+ SAMPLER_ARGS=
+ '[' true = true ']'
+ SAMPLER_ARGS=' --use_separate_test_sampler'
+ '[' true = true ']'
+ SAMPLER_ARGS=' --use_separate_test_sampler --use_balanced_task_sampler --kc_shot_multiplier 1.7777613501411187 --early_kc_shot_multiplier 1.2163331295195938'
+ LR_SCHEDULER_ARGS=
+ '[' true = true ']'
+ LR_SCHEDULER_ARGS='--use_lr_scheduler --scheduler_type plateau'
+ '[' plateau = onecycle ']'
+ '[' plateau = plateau ']'
+ LR_SCHEDULER_ARGS='--use_lr_scheduler --scheduler_type plateau --plateau_factor 0.5 --plateau_patience 2 --min_lr 1e-6'
+ BASE_CMD='python train_balanced_task_sampling.py   --train_csv /home/<USER>/balanced_task_sampling/split_result/train_set.csv   --val_csv /home/<USER>/balanced_task_sampling/split_result/val_set.csv   --test_csv /home/<USER>/balanced_task_sampling/split_result/test_set.csv   --epochs 50   --lr 0.0002   --weight_decay 5e-4   --feature_dim 512   --save_dir /home/<USER>/balanced_task_sampling/results_trial_20250525_003026   --proto_counts 3,6,3   --pretrained   --inner_lr 0.347389   --inner_steps 5   --n_way 3   --n_shot 5   --n_query 15   --tasks_per_epoch 30   --meta_batch_size 8   --no_augmentation   --mixup_alpha 0.0   --cutmix_prob 0.0   --num_workers 4   --augment_factor 1   --kc_augment_factor 1   --seed 42   --early_kc_weight 4.465037338622979   --kc_weight 6.88631981995826   --focal_gamma 1.3485276994969209   --val_frequency 1   --early_stopping 3   --dropout 0.5    --use_separate_test_sampler --use_balanced_task_sampler --kc_shot_multiplier 1.7777613501411187 --early_kc_shot_multiplier 1.2163331295195938   --use_contrastive --contrastive_weight 0.6330396760937711 --temperature 0.07136091213566449 --hard_mining_ratio 0.5107925561204784 --early_normal_weight 1.6079647764558842 --kc_normal_weight 1.0      --use_lr_scheduler --scheduler_type plateau --plateau_factor 0.5 --plateau_patience 2 --min_lr 1e-6'
+ echo 开始无数据增强的平衡任务采样训练...
+ echo '使用GPU: 0'
+ '[' direct = nohup ']'
+ echo 直接运行训练脚本...
+ eval python train_balanced_task_sampling.py --train_csv /home/<USER>/balanced_task_sampling/split_result/train_set.csv --val_csv /home/<USER>/balanced_task_sampling/split_result/val_set.csv --test_csv /home/<USER>/balanced_task_sampling/split_result/test_set.csv --epochs 50 --lr 0.0002 --weight_decay 5e-4 --feature_dim 512 --save_dir /home/<USER>/balanced_task_sampling/results_trial_20250525_003026 --proto_counts 3,6,3 --pretrained --inner_lr 0.347389 --inner_steps 5 --n_way 3 --n_shot 5 --n_query 15 --tasks_per_epoch 30 --meta_batch_size 8 --no_augmentation --mixup_alpha 0.0 --cutmix_prob 0.0 --num_workers 4 --augment_factor 1 --kc_augment_factor 1 --seed 42 --early_kc_weight 4.465037338622979 --kc_weight 6.88631981995826 --focal_gamma 1.3485276994969209 --val_frequency 1 --early_stopping 3 --dropout 0.5 --use_separate_test_sampler --use_balanced_task_sampler --kc_shot_multiplier 1.7777613501411187 --early_kc_shot_multiplier 1.2163331295195938 --use_contrastive --contrastive_weight 0.6330396760937711 --temperature 0.07136091213566449 --hard_mining_ratio 0.5107925561204784 --early_normal_weight 1.6079647764558842 --kc_normal_weight 1.0 --use_lr_scheduler --scheduler_type plateau --plateau_factor 0.5 --plateau_patience 2 --min_lr 1e-6
++ python train_balanced_task_sampling.py --train_csv /home/<USER>/balanced_task_sampling/split_result/train_set.csv --val_csv /home/<USER>/balanced_task_sampling/split_result/val_set.csv --test_csv /home/<USER>/balanced_task_sampling/split_result/test_set.csv --epochs 50 --lr 0.0002 --weight_decay 5e-4 --feature_dim 512 --save_dir /home/<USER>/balanced_task_sampling/results_trial_20250525_003026 --proto_counts 3,6,3 --pretrained --inner_lr 0.347389 --inner_steps 5 --n_way 3 --n_shot 5 --n_query 15 --tasks_per_epoch 30 --meta_batch_size 8 --no_augmentation --mixup_alpha 0.0 --cutmix_prob 0.0 --num_workers 4 --augment_factor 1 --kc_augment_factor 1 --seed 42 --early_kc_weight 4.465037338622979 --kc_weight 6.88631981995826 --focal_gamma 1.3485276994969209 --val_frequency 1 --early_stopping 3 --dropout 0.5 --use_separate_test_sampler --use_balanced_task_sampler --kc_shot_multiplier 1.7777613501411187 --early_kc_shot_multiplier 1.2163331295195938 --use_contrastive --contrastive_weight 0.6330396760937711 --temperature 0.07136091213566449 --hard_mining_ratio 0.5107925561204784 --early_normal_weight 1.6079647764558842 --kc_normal_weight 1.0 --use_lr_scheduler --scheduler_type plateau --plateau_factor 0.5 --plateau_patience 2 --min_lr 1e-6
/home/<USER>/anaconda3/envs/kc1/lib/python3.10/site-packages/torchvision/models/_utils.py:208: UserWarning: The parameter 'pretrained' is deprecated since 0.13 and may be removed in the future, please use 'weights' instead.
  warnings.warn(
/home/<USER>/anaconda3/envs/kc1/lib/python3.10/site-packages/torchvision/models/_utils.py:223: UserWarning: Arguments other than a weight enum or `None` for 'weights' are deprecated since 0.13 and may be removed in the future. The current behavior is equivalent to passing `weights=ResNet34_Weights.IMAGENET1K_V1`. You can also use `weights=ResNet34_Weights.DEFAULT` to get the most up-to-date weights.
  warnings.warn(msg)
/home/<USER>/anaconda3/envs/kc1/lib/python3.10/site-packages/torch/optim/lr_scheduler.py:62: UserWarning: The verbose parameter is deprecated. Please use get_last_lr() to access the learning rate.
  warnings.warn(

Training tasks:   0%|          | 0/30 [00:00<?, ?it/s]
Training tasks:   3%|▎         | 1/30 [00:07<03:40,  7.61s/it]
Training tasks:   7%|▋         | 2/30 [00:09<01:54,  4.09s/it]
Training tasks:  10%|█         | 3/30 [00:11<01:22,  3.05s/it]
Training tasks:  13%|█▎        | 4/30 [00:12<01:03,  2.45s/it]
Training tasks:  17%|█▋        | 5/30 [00:14<00:54,  2.19s/it]
Training tasks:  20%|██        | 6/30 [00:16<00:48,  2.03s/it]
Training tasks:  23%|██▎       | 7/30 [00:17<00:44,  1.95s/it]
Training tasks:  27%|██▋       | 8/30 [00:19<00:41,  1.87s/it]
Training tasks:  30%|███       | 9/30 [00:21<00:38,  1.82s/it]
Training tasks:  33%|███▎      | 10/30 [00:22<00:35,  1.77s/it]
Training tasks:  37%|███▋      | 11/30 [00:24<00:31,  1.68s/it]
Training tasks:  40%|████      | 12/30 [00:25<00:28,  1.60s/it]
Training tasks:  43%|████▎     | 13/30 [00:27<00:26,  1.56s/it]
Training tasks:  47%|████▋     | 14/30 [00:28<00:24,  1.53s/it]
Training tasks:  50%|█████     | 15/30 [00:30<00:22,  1.51s/it]
Training tasks:  53%|█████▎    | 16/30 [00:31<00:21,  1.51s/it]
Training tasks:  57%|█████▋    | 17/30 [00:33<00:20,  1.54s/it]
Training tasks:  60%|██████    | 18/30 [00:34<00:18,  1.57s/it]
Training tasks:  63%|██████▎   | 19/30 [00:36<00:17,  1.60s/it]
Training tasks:  67%|██████▋   | 20/30 [00:38<00:16,  1.65s/it]
Training tasks:  70%|███████   | 21/30 [00:39<00:14,  1.65s/it]
Training tasks:  73%|███████▎  | 22/30 [00:41<00:12,  1.57s/it]
Training tasks:  77%|███████▋  | 23/30 [00:43<00:11,  1.61s/it]
Training tasks:  80%|████████  | 24/30 [00:44<00:09,  1.61s/it]
Training tasks:  83%|████████▎ | 25/30 [00:46<00:08,  1.60s/it]
Training tasks:  87%|████████▋ | 26/30 [00:47<00:06,  1.58s/it]
Training tasks:  90%|█████████ | 27/30 [00:49<00:04,  1.60s/it]
Training tasks:  93%|█████████▎| 28/30 [00:50<00:03,  1.57s/it]
Training tasks:  97%|█████████▋| 29/30 [00:52<00:01,  1.59s/it]
Training tasks: 100%|██████████| 30/30 [00:54<00:00,  1.61s/it]
Training tasks: 100%|██████████| 30/30 [00:54<00:00,  1.81s/it]

Validating tasks:   0%|          | 0/10 [00:00<?, ?it/s]
Validating tasks:  10%|█         | 1/10 [00:00<00:02,  3.80it/s]
Validating tasks:  20%|██        | 2/10 [00:00<00:02,  3.96it/s]
Validating tasks:  30%|███       | 3/10 [00:00<00:01,  4.00it/s]
Validating tasks:  40%|████      | 4/10 [00:00<00:01,  4.20it/s]
Validating tasks:  50%|█████     | 5/10 [00:01<00:01,  4.30it/s]
Validating tasks:  60%|██████    | 6/10 [00:01<00:00,  4.52it/s]
Validating tasks:  70%|███████   | 7/10 [00:01<00:00,  4.72it/s]
Validating tasks:  80%|████████  | 8/10 [00:01<00:00,  4.88it/s]
Validating tasks:  90%|█████████ | 9/10 [00:01<00:00,  4.86it/s]
Validating tasks: 100%|██████████| 10/10 [00:02<00:00,  4.79it/s]
Validating tasks: 100%|██████████| 10/10 [00:02<00:00,  4.54it/s]

Training tasks:   0%|          | 0/30 [00:00<?, ?it/s]
Training tasks:   3%|▎         | 1/30 [00:01<00:48,  1.67s/it]
Training tasks:   7%|▋         | 2/30 [00:03<00:46,  1.64s/it]
Training tasks:  10%|█         | 3/30 [00:04<00:41,  1.54s/it]
Training tasks:  13%|█▎        | 4/30 [00:06<00:38,  1.49s/it]
Training tasks:  17%|█▋        | 5/30 [00:07<00:35,  1.40s/it]
Training tasks:  20%|██        | 6/30 [00:08<00:34,  1.44s/it]
Training tasks:  23%|██▎       | 7/30 [00:10<00:32,  1.43s/it]
Training tasks:  27%|██▋       | 8/30 [00:11<00:32,  1.48s/it]
Training tasks:  30%|███       | 9/30 [00:13<00:31,  1.50s/it]
Training tasks:  33%|███▎      | 10/30 [00:14<00:28,  1.43s/it]
Training tasks:  37%|███▋      | 11/30 [00:16<00:27,  1.46s/it]
Training tasks:  40%|████      | 12/30 [00:17<00:26,  1.49s/it]
Training tasks:  43%|████▎     | 13/30 [00:19<00:24,  1.45s/it]
Training tasks:  47%|████▋     | 14/30 [00:20<00:24,  1.53s/it]
Training tasks:  50%|█████     | 15/30 [00:22<00:22,  1.52s/it]
Training tasks:  53%|█████▎    | 16/30 [00:23<00:20,  1.50s/it]
Training tasks:  57%|█████▋    | 17/30 [00:25<00:19,  1.54s/it]
Training tasks:  60%|██████    | 18/30 [00:27<00:18,  1.55s/it]
Training tasks:  63%|██████▎   | 19/30 [00:28<00:17,  1.56s/it]
Training tasks:  67%|██████▋   | 20/30 [00:30<00:15,  1.57s/it]
Training tasks:  70%|███████   | 21/30 [00:31<00:14,  1.57s/it]
Training tasks:  73%|███████▎  | 22/30 [00:33<00:12,  1.58s/it]
Training tasks:  77%|███████▋  | 23/30 [00:35<00:11,  1.62s/it]
Training tasks:  80%|████████  | 24/30 [00:36<00:09,  1.57s/it]
Training tasks:  83%|████████▎ | 25/30 [00:38<00:08,  1.61s/it]
Training tasks:  87%|████████▋ | 26/30 [00:39<00:06,  1.60s/it]
Training tasks:  90%|█████████ | 27/30 [00:41<00:04,  1.56s/it]
Training tasks:  93%|█████████▎| 28/30 [00:42<00:03,  1.57s/it]
Training tasks:  97%|█████████▋| 29/30 [00:44<00:01,  1.61s/it]
Training tasks: 100%|██████████| 30/30 [00:45<00:00,  1.56s/it]
Training tasks: 100%|██████████| 30/30 [00:45<00:00,  1.53s/it]

Validating tasks:   0%|          | 0/10 [00:00<?, ?it/s]
Validating tasks:  10%|█         | 1/10 [00:00<00:02,  4.29it/s]
Validating tasks:  20%|██        | 2/10 [00:00<00:01,  4.76it/s]
Validating tasks:  30%|███       | 3/10 [00:00<00:01,  4.94it/s]
Validating tasks:  40%|████      | 4/10 [00:00<00:01,  5.15it/s]
Validating tasks:  50%|█████     | 5/10 [00:01<00:01,  4.99it/s]
Validating tasks:  60%|██████    | 6/10 [00:01<00:00,  5.07it/s]
Validating tasks:  70%|███████   | 7/10 [00:01<00:00,  5.23it/s]
Validating tasks:  80%|████████  | 8/10 [00:01<00:00,  5.47it/s]
Validating tasks:  90%|█████████ | 9/10 [00:01<00:00,  5.33it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  5.15it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  5.12it/s]

Training tasks:   0%|          | 0/30 [00:00<?, ?it/s]
Training tasks:   3%|▎         | 1/30 [00:01<00:49,  1.72s/it]
Training tasks:   7%|▋         | 2/30 [00:03<00:43,  1.56s/it]
Training tasks:  10%|█         | 3/30 [00:04<00:42,  1.56s/it]
Training tasks:  13%|█▎        | 4/30 [00:06<00:43,  1.66s/it]
Training tasks:  17%|█▋        | 5/30 [00:08<00:42,  1.68s/it]
Training tasks:  20%|██        | 6/30 [00:09<00:38,  1.60s/it]
Training tasks:  23%|██▎       | 7/30 [00:11<00:38,  1.66s/it]
Training tasks:  27%|██▋       | 8/30 [00:13<00:35,  1.63s/it]
Training tasks:  30%|███       | 9/30 [00:14<00:35,  1.67s/it]
Training tasks:  33%|███▎      | 10/30 [00:16<00:33,  1.68s/it]
Training tasks:  37%|███▋      | 11/30 [00:18<00:31,  1.64s/it]
Training tasks:  40%|████      | 12/30 [00:19<00:29,  1.64s/it]
Training tasks:  43%|████▎     | 13/30 [00:21<00:27,  1.64s/it]
Training tasks:  47%|████▋     | 14/30 [00:22<00:25,  1.62s/it]
Training tasks:  50%|█████     | 15/30 [00:24<00:23,  1.59s/it]
Training tasks:  53%|█████▎    | 16/30 [00:25<00:21,  1.56s/it]
Training tasks:  57%|█████▋    | 17/30 [00:27<00:20,  1.57s/it]
Training tasks:  60%|██████    | 18/30 [00:29<00:18,  1.58s/it]
Training tasks:  63%|██████▎   | 19/30 [00:30<00:17,  1.60s/it]
Training tasks:  67%|██████▋   | 20/30 [00:32<00:16,  1.65s/it]
Training tasks:  70%|███████   | 21/30 [00:34<00:15,  1.68s/it]
Training tasks:  73%|███████▎  | 22/30 [00:35<00:13,  1.69s/it]
Training tasks:  77%|███████▋  | 23/30 [00:37<00:11,  1.71s/it]
Training tasks:  80%|████████  | 24/30 [00:39<00:09,  1.61s/it]
Training tasks:  83%|████████▎ | 25/30 [00:40<00:07,  1.57s/it]
Training tasks:  87%|████████▋ | 26/30 [00:42<00:06,  1.59s/it]
Training tasks:  90%|█████████ | 27/30 [00:43<00:04,  1.59s/it]
Training tasks:  93%|█████████▎| 28/30 [00:45<00:03,  1.56s/it]
Training tasks:  97%|█████████▋| 29/30 [00:47<00:01,  1.60s/it]
Training tasks: 100%|██████████| 30/30 [00:48<00:00,  1.60s/it]
Training tasks: 100%|██████████| 30/30 [00:48<00:00,  1.62s/it]

Validating tasks:   0%|          | 0/10 [00:00<?, ?it/s]
Validating tasks:  10%|█         | 1/10 [00:00<00:02,  3.90it/s]
Validating tasks:  20%|██        | 2/10 [00:00<00:02,  3.84it/s]
Validating tasks:  30%|███       | 3/10 [00:00<00:01,  3.77it/s]
Validating tasks:  40%|████      | 4/10 [00:01<00:01,  3.73it/s]
Validating tasks:  50%|█████     | 5/10 [00:01<00:01,  3.77it/s]
Validating tasks:  60%|██████    | 6/10 [00:01<00:00,  4.18it/s]
Validating tasks:  70%|███████   | 7/10 [00:01<00:00,  4.25it/s]
Validating tasks:  80%|████████  | 8/10 [00:01<00:00,  4.43it/s]
Validating tasks:  90%|█████████ | 9/10 [00:02<00:00,  4.42it/s]
Validating tasks: 100%|██████████| 10/10 [00:02<00:00,  4.37it/s]
Validating tasks: 100%|██████████| 10/10 [00:02<00:00,  4.16it/s]

Training tasks:   0%|          | 0/30 [00:00<?, ?it/s]
Training tasks:   3%|▎         | 1/30 [00:01<00:43,  1.49s/it]
Training tasks:   7%|▋         | 2/30 [00:03<00:45,  1.62s/it]
Training tasks:  10%|█         | 3/30 [00:04<00:41,  1.55s/it]
Training tasks:  13%|█▎        | 4/30 [00:06<00:39,  1.51s/it]
Training tasks:  17%|█▋        | 5/30 [00:07<00:37,  1.48s/it]
Training tasks:  20%|██        | 6/30 [00:09<00:35,  1.48s/it]
Training tasks:  23%|██▎       | 7/30 [00:10<00:34,  1.49s/it]
Training tasks:  27%|██▋       | 8/30 [00:12<00:33,  1.53s/it]
Training tasks:  30%|███       | 9/30 [00:13<00:33,  1.59s/it]
Training tasks:  33%|███▎      | 10/30 [00:15<00:31,  1.58s/it]
Training tasks:  37%|███▋      | 11/30 [00:17<00:31,  1.66s/it]
Training tasks:  40%|████      | 12/30 [00:18<00:29,  1.65s/it]
Training tasks:  43%|████▎     | 13/30 [00:20<00:27,  1.64s/it]
Training tasks:  47%|████▋     | 14/30 [00:22<00:25,  1.61s/it]
Training tasks:  50%|█████     | 15/30 [00:23<00:24,  1.66s/it]
Training tasks:  53%|█████▎    | 16/30 [00:25<00:22,  1.59s/it]
Training tasks:  57%|█████▋    | 17/30 [00:26<00:20,  1.58s/it]
Training tasks:  60%|██████    | 18/30 [00:28<00:18,  1.51s/it]
Training tasks:  63%|██████▎   | 19/30 [00:29<00:16,  1.48s/it]
Training tasks:  67%|██████▋   | 20/30 [00:30<00:14,  1.44s/it]
Training tasks:  70%|███████   | 21/30 [00:32<00:13,  1.46s/it]
Training tasks:  73%|███████▎  | 22/30 [00:33<00:11,  1.45s/it]
Training tasks:  77%|███████▋  | 23/30 [00:35<00:10,  1.48s/it]
Training tasks:  80%|████████  | 24/30 [00:36<00:08,  1.48s/it]
Training tasks:  83%|████████▎ | 25/30 [00:38<00:07,  1.53s/it]
Training tasks:  87%|████████▋ | 26/30 [00:40<00:06,  1.52s/it]
Training tasks:  90%|█████████ | 27/30 [00:41<00:04,  1.52s/it]
Training tasks:  93%|█████████▎| 28/30 [00:43<00:03,  1.55s/it]
Training tasks:  97%|█████████▋| 29/30 [00:44<00:01,  1.55s/it]
Training tasks: 100%|██████████| 30/30 [00:46<00:00,  1.56s/it]
Training tasks: 100%|██████████| 30/30 [00:46<00:00,  1.54s/it]

Validating tasks:   0%|          | 0/10 [00:00<?, ?it/s]
Validating tasks:  10%|█         | 1/10 [00:00<00:01,  4.72it/s]
Validating tasks:  20%|██        | 2/10 [00:00<00:01,  4.73it/s]
Validating tasks:  30%|███       | 3/10 [00:00<00:01,  4.57it/s]
Validating tasks:  40%|████      | 4/10 [00:00<00:01,  4.45it/s]
Validating tasks:  50%|█████     | 5/10 [00:01<00:01,  4.58it/s]
Validating tasks:  60%|██████    | 6/10 [00:01<00:00,  4.68it/s]
Validating tasks:  70%|███████   | 7/10 [00:01<00:00,  4.72it/s]
Validating tasks:  80%|████████  | 8/10 [00:01<00:00,  4.85it/s]
Validating tasks:  90%|█████████ | 9/10 [00:01<00:00,  4.61it/s]
Validating tasks: 100%|██████████| 10/10 [00:02<00:00,  4.47it/s]
Validating tasks: 100%|██████████| 10/10 [00:02<00:00,  4.59it/s]

Training tasks:   0%|          | 0/30 [00:00<?, ?it/s]
Training tasks:   3%|▎         | 1/30 [00:01<00:48,  1.67s/it]
Training tasks:   7%|▋         | 2/30 [00:03<00:47,  1.69s/it]
Training tasks:  10%|█         | 3/30 [00:04<00:43,  1.62s/it]
Training tasks:  13%|█▎        | 4/30 [00:06<00:44,  1.69s/it]
Training tasks:  17%|█▋        | 5/30 [00:08<00:41,  1.66s/it]
Training tasks:  20%|██        | 6/30 [00:09<00:37,  1.55s/it]
Training tasks:  23%|██▎       | 7/30 [00:11<00:35,  1.56s/it]
Training tasks:  27%|██▋       | 8/30 [00:12<00:33,  1.51s/it]
Training tasks:  30%|███       | 9/30 [00:14<00:32,  1.55s/it]
Training tasks:  33%|███▎      | 10/30 [00:16<00:32,  1.60s/it]
Training tasks:  37%|███▋      | 11/30 [00:17<00:30,  1.61s/it]
Training tasks:  40%|████      | 12/30 [00:18<00:27,  1.53s/it]
Training tasks:  43%|████▎     | 13/30 [00:20<00:25,  1.51s/it]
Training tasks:  47%|████▋     | 14/30 [00:21<00:23,  1.45s/it]
Training tasks:  50%|█████     | 15/30 [00:23<00:20,  1.39s/it]
Training tasks:  53%|█████▎    | 16/30 [00:24<00:18,  1.32s/it]
Training tasks:  57%|█████▋    | 17/30 [00:25<00:16,  1.25s/it]
Training tasks:  60%|██████    | 18/30 [00:26<00:15,  1.31s/it]
Training tasks:  63%|██████▎   | 19/30 [00:28<00:14,  1.34s/it]
Training tasks:  67%|██████▋   | 20/30 [00:29<00:13,  1.33s/it]
Training tasks:  70%|███████   | 21/30 [00:30<00:12,  1.34s/it]
Training tasks:  73%|███████▎  | 22/30 [00:32<00:10,  1.36s/it]
Training tasks:  77%|███████▋  | 23/30 [00:33<00:09,  1.40s/it]
Training tasks:  80%|████████  | 24/30 [00:35<00:08,  1.38s/it]
Training tasks:  83%|████████▎ | 25/30 [00:36<00:07,  1.46s/it]
Training tasks:  87%|████████▋ | 26/30 [00:38<00:05,  1.49s/it]
Training tasks:  90%|█████████ | 27/30 [00:39<00:04,  1.54s/it]
Training tasks:  93%|█████████▎| 28/30 [00:41<00:03,  1.56s/it]
Training tasks:  97%|█████████▋| 29/30 [00:43<00:01,  1.62s/it]
Training tasks: 100%|██████████| 30/30 [00:44<00:00,  1.60s/it]
Training tasks: 100%|██████████| 30/30 [00:44<00:00,  1.49s/it]

Validating tasks:   0%|          | 0/10 [00:00<?, ?it/s]
Validating tasks:  10%|█         | 1/10 [00:00<00:01,  4.73it/s]
Validating tasks:  20%|██        | 2/10 [00:00<00:01,  4.52it/s]
Validating tasks:  30%|███       | 3/10 [00:00<00:01,  4.48it/s]
Validating tasks:  40%|████      | 4/10 [00:00<00:01,  4.29it/s]
Validating tasks:  50%|█████     | 5/10 [00:01<00:01,  4.33it/s]
Validating tasks:  60%|██████    | 6/10 [00:01<00:00,  4.46it/s]
Validating tasks:  70%|███████   | 7/10 [00:01<00:00,  4.24it/s]
Validating tasks:  80%|████████  | 8/10 [00:01<00:00,  4.25it/s]
Validating tasks:  90%|█████████ | 9/10 [00:02<00:00,  4.17it/s]
Validating tasks: 100%|██████████| 10/10 [00:02<00:00,  4.12it/s]
Validating tasks: 100%|██████████| 10/10 [00:02<00:00,  4.26it/s]

Testing tasks:   0%|          | 0/30 [00:00<?, ?it/s]
Testing tasks:   3%|▎         | 1/30 [00:00<00:12,  2.24it/s]
Testing tasks:   7%|▋         | 2/30 [00:00<00:10,  2.62it/s]
Testing tasks:  10%|█         | 3/30 [00:01<00:09,  2.85it/s]
Testing tasks:  13%|█▎        | 4/30 [00:01<00:08,  3.10it/s]
Testing tasks:  17%|█▋        | 5/30 [00:01<00:08,  3.05it/s]
Testing tasks:  20%|██        | 6/30 [00:02<00:07,  3.16it/s]
Testing tasks:  23%|██▎       | 7/30 [00:02<00:07,  3.23it/s]
Testing tasks:  27%|██▋       | 8/30 [00:02<00:06,  3.31it/s]
Testing tasks:  30%|███       | 9/30 [00:02<00:06,  3.29it/s]
Testing tasks:  33%|███▎      | 10/30 [00:03<00:06,  3.09it/s]
Testing tasks:  37%|███▋      | 11/30 [00:03<00:06,  2.99it/s]
Testing tasks:  40%|████      | 12/30 [00:03<00:05,  3.04it/s]
Testing tasks:  43%|████▎     | 13/30 [00:04<00:05,  3.13it/s]
Testing tasks:  47%|████▋     | 14/30 [00:04<00:05,  3.05it/s]
Testing tasks:  50%|█████     | 15/30 [00:04<00:04,  3.11it/s]
Testing tasks:  53%|█████▎    | 16/30 [00:05<00:04,  3.00it/s]
Testing tasks:  57%|█████▋    | 17/30 [00:05<00:04,  2.97it/s]
Testing tasks:  60%|██████    | 18/30 [00:05<00:04,  2.98it/s]
Testing tasks:  63%|██████▎   | 19/30 [00:06<00:03,  3.01it/s]
Testing tasks:  67%|██████▋   | 20/30 [00:06<00:03,  3.15it/s]
Testing tasks:  70%|███████   | 21/30 [00:06<00:02,  3.03it/s]
Testing tasks:  73%|███████▎  | 22/30 [00:07<00:02,  2.95it/s]
Testing tasks:  77%|███████▋  | 23/30 [00:07<00:02,  2.95it/s]
Testing tasks:  80%|████████  | 24/30 [00:07<00:01,  3.08it/s]
Testing tasks:  83%|████████▎ | 25/30 [00:08<00:01,  3.01it/s]
Testing tasks:  87%|████████▋ | 26/30 [00:08<00:01,  3.09it/s]
Testing tasks:  90%|█████████ | 27/30 [00:08<00:00,  3.22it/s]
Testing tasks:  93%|█████████▎| 28/30 [00:09<00:00,  3.37it/s]
Testing tasks:  97%|█████████▋| 29/30 [00:09<00:00,  3.28it/s]
Testing tasks: 100%|██████████| 30/30 [00:09<00:00,  3.36it/s]
Testing tasks: 100%|██████████| 30/30 [00:09<00:00,  3.10it/s]

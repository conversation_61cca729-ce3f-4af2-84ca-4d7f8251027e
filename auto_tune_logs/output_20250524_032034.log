STDOUT:
===================== 训练配置 =====================
GPU: 0
输出目录: /home/<USER>/balanced_task_sampling/results_trial_20250524_032034
日志文件: /home/<USER>/balanced_task_sampling/results_trial_20250524_032034/main_training_trial_71_20250524_032034.log

--------- 优化参数 ---------
内部学习率: 0.347389
训练轮数: 50
每轮任务数: 30

--------- 模型参数 ---------
使用类别权重: 圆锥角膜=6.15076043556148, 早期圆锥角膜=5.300585807826661
温度参数: 0.03090847571921763
硬样本挖掘比例: 0.6521343136848173
对比学习权重: 0.3539088319687521
=============================================

创建目录: /home/<USER>/balanced_task_sampling/results_trial_20250524_032034
总用量 4
-rw-rw-r-- 1 <USER> <GROUP> 656 5月  24 03:20 main_training_trial_71_20250524_032034.log
开始无数据增强的平衡任务采样训练...
使用GPU: 0
直接运行训练脚本...
成功导入增强版特征提取器
成功导入归一化特征提取器
成功导入增强版损失函数
成功导入增强版MAML模型
成功导入KC特定增强模块
成功导入增强版GPU数据增强模块
使用设备: cuda
禁用所有数据增强，直接加载原始数据集...
使用平衡任务采样器
类别 normal (标签 0): 164个样本
类别 e-kc (标签 1): 26个样本
类别 kc (标签 2): 133个样本
使用原型配置: [1, 2, 3]
使用增强版特征提取器，dropout率: 0.5
使用增强版MAML-ProtoNet模型，内循环学习率: 0.347389, 内循环步数: 5
使用ReduceLROnPlateau学习率调度器，因子: 0.5, 耐心值: 2
开始训练，共50个epoch
Epoch 1/50
当前学习率: 0.000200
使用类别权重: 正常=1.0, 早期圆锥角膜=5.300585807826661, 圆锥角膜=6.15076043556148
使用增强版对比损失函数，温度: 0.03090847571921763, 硬负样本挖掘比例: 0.6521343136848173, 早期圆锥角膜与正常样本对比权重: 1.4797741441668677, 圆锥角膜与正常样本对比权重: 1.0
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
警告: 没有足够的有效类别进行3-way任务采样，尝试调整参数...
类别样本数量: {0: 33, 1: 5, 2: 29}
调整后的参数: n_shot=2, n_query=3
类别 normal (标签 0): 33个样本
类别 e-kc (标签 1): 5个样本
类别 kc (标签 2): 29个样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
Epoch 1: Train Loss: 2.5641, Train Acc: 0.4311, Val Loss: 2.6128, Val Acc: 0.5111
保存最佳模型，验证准确率: 0.5111
Epoch 2/50
当前学习率: 0.000200
使用类别权重: 正常=1.0, 早期圆锥角膜=5.300585807826661, 圆锥角膜=6.15076043556148
使用增强版对比损失函数，温度: 0.03090847571921763, 硬负样本挖掘比例: 0.6521343136848173, 早期圆锥角膜与正常样本对比权重: 1.4797741441668677, 圆锥角膜与正常样本对比权重: 1.0
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
警告: 没有足够的有效类别进行3-way任务采样，尝试调整参数...
类别样本数量: {0: 33, 1: 5, 2: 29}
调整后的参数: n_shot=2, n_query=3
类别 normal (标签 0): 33个样本
类别 e-kc (标签 1): 5个样本
类别 kc (标签 2): 29个样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
Epoch 2: Train Loss: 2.3760, Train Acc: 0.6000, Val Loss: 2.4299, Val Acc: 0.6333
保存最佳模型，验证准确率: 0.6333
Epoch 3/50
当前学习率: 0.000200
使用类别权重: 正常=1.0, 早期圆锥角膜=5.300585807826661, 圆锥角膜=6.15076043556148
使用增强版对比损失函数，温度: 0.03090847571921763, 硬负样本挖掘比例: 0.6521343136848173, 早期圆锥角膜与正常样本对比权重: 1.4797741441668677, 圆锥角膜与正常样本对比权重: 1.0
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
警告: 没有足够的有效类别进行3-way任务采样，尝试调整参数...
类别样本数量: {0: 33, 1: 5, 2: 29}
调整后的参数: n_shot=2, n_query=3
类别 normal (标签 0): 33个样本
类别 e-kc (标签 1): 5个样本
类别 kc (标签 2): 29个样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
Epoch 3: Train Loss: 1.9847, Train Acc: 0.7563, Val Loss: 2.4297, Val Acc: 0.6000
验证准确率未提高，当前耐心值: 1/3
Epoch 4/50
当前学习率: 0.000200
使用类别权重: 正常=1.0, 早期圆锥角膜=5.300585807826661, 圆锥角膜=6.15076043556148
使用增强版对比损失函数，温度: 0.03090847571921763, 硬负样本挖掘比例: 0.6521343136848173, 早期圆锥角膜与正常样本对比权重: 1.4797741441668677, 圆锥角膜与正常样本对比权重: 1.0
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
警告: 没有足够的有效类别进行3-way任务采样，尝试调整参数...
类别样本数量: {0: 33, 1: 5, 2: 29}
调整后的参数: n_shot=2, n_query=3
类别 normal (标签 0): 33个样本
类别 e-kc (标签 1): 5个样本
类别 kc (标签 2): 29个样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
Epoch 4: Train Loss: 1.8018, Train Acc: 0.8119, Val Loss: 2.2861, Val Acc: 0.6222
验证准确率未提高，当前耐心值: 2/3
Epoch 5/50
当前学习率: 0.000200
使用类别权重: 正常=1.0, 早期圆锥角膜=5.300585807826661, 圆锥角膜=6.15076043556148
使用增强版对比损失函数，温度: 0.03090847571921763, 硬负样本挖掘比例: 0.6521343136848173, 早期圆锥角膜与正常样本对比权重: 1.4797741441668677, 圆锥角膜与正常样本对比权重: 1.0
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
警告: 没有足够的有效类别进行3-way任务采样，尝试调整参数...
类别样本数量: {0: 33, 1: 5, 2: 29}
调整后的参数: n_shot=2, n_query=3
类别 normal (标签 0): 33个样本
类别 e-kc (标签 1): 5个样本
类别 kc (标签 2): 29个样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
Epoch 5: Train Loss: 1.7270, Train Acc: 0.8637, Val Loss: 2.3822, Val Acc: 0.7000
保存最佳模型，验证准确率: 0.7000
Epoch 6/50
当前学习率: 0.000200
使用类别权重: 正常=1.0, 早期圆锥角膜=5.300585807826661, 圆锥角膜=6.15076043556148
使用增强版对比损失函数，温度: 0.03090847571921763, 硬负样本挖掘比例: 0.6521343136848173, 早期圆锥角膜与正常样本对比权重: 1.4797741441668677, 圆锥角膜与正常样本对比权重: 1.0
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
警告: 没有足够的有效类别进行3-way任务采样，尝试调整参数...
类别样本数量: {0: 33, 1: 5, 2: 29}
调整后的参数: n_shot=2, n_query=3
类别 normal (标签 0): 33个样本
类别 e-kc (标签 1): 5个样本
类别 kc (标签 2): 29个样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
Epoch 6: Train Loss: 1.6111, Train Acc: 0.8607, Val Loss: 1.9587, Val Acc: 0.8000
保存最佳模型，验证准确率: 0.8000
Epoch 7/50
当前学习率: 0.000200
使用类别权重: 正常=1.0, 早期圆锥角膜=5.300585807826661, 圆锥角膜=6.15076043556148
使用增强版对比损失函数，温度: 0.03090847571921763, 硬负样本挖掘比例: 0.6521343136848173, 早期圆锥角膜与正常样本对比权重: 1.4797741441668677, 圆锥角膜与正常样本对比权重: 1.0
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
警告: 没有足够的有效类别进行3-way任务采样，尝试调整参数...
类别样本数量: {0: 33, 1: 5, 2: 29}
调整后的参数: n_shot=2, n_query=3
类别 normal (标签 0): 33个样本
类别 e-kc (标签 1): 5个样本
类别 kc (标签 2): 29个样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
Epoch 7: Train Loss: 1.5595, Train Acc: 0.9230, Val Loss: 2.3639, Val Acc: 0.5778
验证准确率未提高，当前耐心值: 1/3
Epoch 8/50
当前学习率: 0.000200
使用类别权重: 正常=1.0, 早期圆锥角膜=5.300585807826661, 圆锥角膜=6.15076043556148
使用增强版对比损失函数，温度: 0.03090847571921763, 硬负样本挖掘比例: 0.6521343136848173, 早期圆锥角膜与正常样本对比权重: 1.4797741441668677, 圆锥角膜与正常样本对比权重: 1.0
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
警告: 没有足够的有效类别进行3-way任务采样，尝试调整参数...
类别样本数量: {0: 33, 1: 5, 2: 29}
调整后的参数: n_shot=2, n_query=3
类别 normal (标签 0): 33个样本
类别 e-kc (标签 1): 5个样本
类别 kc (标签 2): 29个样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
Epoch 8: Train Loss: 1.4484, Train Acc: 0.9163, Val Loss: 1.9979, Val Acc: 0.7333
验证准确率未提高，当前耐心值: 2/3
Epoch 9/50
当前学习率: 0.000200
使用类别权重: 正常=1.0, 早期圆锥角膜=5.300585807826661, 圆锥角膜=6.15076043556148
使用增强版对比损失函数，温度: 0.03090847571921763, 硬负样本挖掘比例: 0.6521343136848173, 早期圆锥角膜与正常样本对比权重: 1.4797741441668677, 圆锥角膜与正常样本对比权重: 1.0
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 5 个支持集样本
警告: 没有足够的有效类别进行3-way任务采样，尝试调整参数...
类别样本数量: {0: 33, 1: 5, 2: 29}
调整后的参数: n_shot=2, n_query=3
类别 normal (标签 0): 33个样本
类别 e-kc (标签 1): 5个样本
类别 kc (标签 2): 29个样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
Epoch 9: Train Loss: 1.3543, Train Acc: 0.9659, Val Loss: 2.3314, Val Acc: 0.5889
验证准确率未提高，当前耐心值: 3/3
早停触发，停止训练
警告: 没有足够的有效类别进行3-way任务采样，尝试调整参数...
类别样本数量: {0: 37, 1: 7, 2: 27}
调整后的参数: n_shot=3, n_query=4
类别 normal (标签 0): 37个样本
类别 e-kc (标签 1): 7个样本
类别 kc (标签 2): 27个样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本

测试结果:
总体准确率: 72.22%
类别准确率:
  kc: 65.00%
  e-kc: 75.00%
  normal: 76.67%
训练完成，结果已保存到 /home/<USER>/balanced_task_sampling/results_trial_20250524_032034

STDERR:
+ TIMESTAMP=20250524_032034
+ RESULT_DIR=/home/<USER>/balanced_task_sampling/results_trial_20250524_032034
+ '[' -n /home/<USER>/balanced_task_sampling/results_trial_20250524_032034/main_training_trial_71_20250524_032034.log ']'
+ LOG_FILE=/home/<USER>/balanced_task_sampling/results_trial_20250524_032034/main_training_trial_71_20250524_032034.log
++ dirname /home/<USER>/balanced_task_sampling/results_trial_20250524_032034/main_training_trial_71_20250524_032034.log
+ mkdir -p /home/<USER>/balanced_task_sampling/results_trial_20250524_032034
+ mkdir -p /home/<USER>/balanced_task_sampling/results_trial_20250524_032034
+ echo '===================== 训练配置 ====================='
+ echo 'GPU: 0'
+ echo '输出目录: /home/<USER>/balanced_task_sampling/results_trial_20250524_032034'
+ echo '日志文件: /home/<USER>/balanced_task_sampling/results_trial_20250524_032034/main_training_trial_71_20250524_032034.log'
+ echo ''
+ tee -a /home/<USER>/balanced_task_sampling/results_trial_20250524_032034/main_training_trial_71_20250524_032034.log
+ echo '--------- 优化参数 ---------'
+ echo '内部学习率: 0.347389'
+ echo '训练轮数: 50'
+ echo '每轮任务数: 30'
+ echo ''
+ echo '--------- 模型参数 ---------'
+ echo '使用类别权重: 圆锥角膜=6.15076043556148, 早期圆锥角膜=5.300585807826661'
+ echo '温度参数: 0.03090847571921763'
+ echo '硬样本挖掘比例: 0.6521343136848173'
+ echo '对比学习权重: 0.3539088319687521'
+ echo '=============================================
'
+ validate_params
+ local error_count=0
++ dirname /home/<USER>/balanced_task_sampling/results_trial_20250524_032034/main_training_trial_71_20250524_032034.log
++ dirname /home/<USER>/balanced_task_sampling/split_result/train_set.csv
+ for dir in "$RESULT_DIR" "$(dirname "$LOG_FILE")" "$(dirname "$TRAIN_CSV")"
+ mkdir -p /home/<USER>/balanced_task_sampling/results_trial_20250524_032034
+ for dir in "$RESULT_DIR" "$(dirname "$LOG_FILE")" "$(dirname "$TRAIN_CSV")"
+ mkdir -p /home/<USER>/balanced_task_sampling/results_trial_20250524_032034
+ for dir in "$RESULT_DIR" "$(dirname "$LOG_FILE")" "$(dirname "$TRAIN_CSV")"
+ mkdir -p /home/<USER>/balanced_task_sampling/split_result
+ for csv in "$TRAIN_CSV" "$VAL_CSV" "$TEST_CSV"
+ '[' '!' -f /home/<USER>/balanced_task_sampling/split_result/train_set.csv ']'
+ for csv in "$TRAIN_CSV" "$VAL_CSV" "$TEST_CSV"
+ '[' '!' -f /home/<USER>/balanced_task_sampling/split_result/val_set.csv ']'
+ for csv in "$TRAIN_CSV" "$VAL_CSV" "$TEST_CSV"
+ '[' '!' -f /home/<USER>/balanced_task_sampling/split_result/test_set.csv ']'
+ [[ 0.0002 =~ ^[0-9.]+$ ]]
+ [[ 0.347389 =~ ^[0-9.]+$ ]]
+ '[' 0 -gt 0 ']'
+ return 0
+ '[' false = true ']'
+ export CUDA_VISIBLE_DEVICES=0
+ CUDA_VISIBLE_DEVICES=0
+ export OMP_NUM_THREADS=1
+ OMP_NUM_THREADS=1
+ export MKL_NUM_THREADS=1
+ MKL_NUM_THREADS=1
+ export MPLBACKEND=Agg
+ MPLBACKEND=Agg
+ mkdir -p /home/<USER>/balanced_task_sampling/results_trial_20250524_032034
+ echo '创建目录: /home/<USER>/balanced_task_sampling/results_trial_20250524_032034'
+ ls -l /home/<USER>/balanced_task_sampling/results_trial_20250524_032034
+ PRETRAINED_ARGS=
+ '[' true = true ']'
+ PRETRAINED_ARGS=--pretrained
+ AUGMENT_ARGS=--no_augmentation
+ CONTRASTIVE_ARGS=
+ '[' true = true ']'
+ CONTRASTIVE_ARGS='--use_contrastive --contrastive_weight 0.3539088319687521 --temperature 0.03090847571921763 --hard_mining_ratio 0.6521343136848173 --early_normal_weight 1.4797741441668677 --kc_normal_weight 1.0'
+ NORMALIZE_ARGS=
+ '[' false = true ']'
+ SAMPLER_ARGS=
+ '[' true = true ']'
+ SAMPLER_ARGS=' --use_separate_test_sampler'
+ '[' true = true ']'
+ SAMPLER_ARGS=' --use_separate_test_sampler --use_balanced_task_sampler --kc_shot_multiplier 3.8356964317112165 --early_kc_shot_multiplier 1.020226765500817'
+ LR_SCHEDULER_ARGS=
+ '[' true = true ']'
+ LR_SCHEDULER_ARGS='--use_lr_scheduler --scheduler_type plateau'
+ '[' plateau = onecycle ']'
+ '[' plateau = plateau ']'
+ LR_SCHEDULER_ARGS='--use_lr_scheduler --scheduler_type plateau --plateau_factor 0.5 --plateau_patience 2 --min_lr 1e-6'
+ BASE_CMD='python train_balanced_task_sampling.py   --train_csv /home/<USER>/balanced_task_sampling/split_result/train_set.csv   --val_csv /home/<USER>/balanced_task_sampling/split_result/val_set.csv   --test_csv /home/<USER>/balanced_task_sampling/split_result/test_set.csv   --epochs 50   --lr 0.0002   --weight_decay 5e-4   --feature_dim 512   --save_dir /home/<USER>/balanced_task_sampling/results_trial_20250524_032034   --proto_counts 1,2,3   --pretrained   --inner_lr 0.347389   --inner_steps 5   --n_way 3   --n_shot 5   --n_query 15   --tasks_per_epoch 30   --meta_batch_size 8   --no_augmentation   --mixup_alpha 0.0   --cutmix_prob 0.0   --num_workers 4   --augment_factor 1   --kc_augment_factor 1   --seed 42   --early_kc_weight 5.300585807826661   --kc_weight 6.15076043556148   --focal_gamma 1.2116655516748096   --val_frequency 1   --early_stopping 3   --dropout 0.5    --use_separate_test_sampler --use_balanced_task_sampler --kc_shot_multiplier 3.8356964317112165 --early_kc_shot_multiplier 1.020226765500817   --use_contrastive --contrastive_weight 0.3539088319687521 --temperature 0.03090847571921763 --hard_mining_ratio 0.6521343136848173 --early_normal_weight 1.4797741441668677 --kc_normal_weight 1.0      --use_lr_scheduler --scheduler_type plateau --plateau_factor 0.5 --plateau_patience 2 --min_lr 1e-6'
+ echo 开始无数据增强的平衡任务采样训练...
+ echo '使用GPU: 0'
+ '[' direct = nohup ']'
+ echo 直接运行训练脚本...
+ eval python train_balanced_task_sampling.py --train_csv /home/<USER>/balanced_task_sampling/split_result/train_set.csv --val_csv /home/<USER>/balanced_task_sampling/split_result/val_set.csv --test_csv /home/<USER>/balanced_task_sampling/split_result/test_set.csv --epochs 50 --lr 0.0002 --weight_decay 5e-4 --feature_dim 512 --save_dir /home/<USER>/balanced_task_sampling/results_trial_20250524_032034 --proto_counts 1,2,3 --pretrained --inner_lr 0.347389 --inner_steps 5 --n_way 3 --n_shot 5 --n_query 15 --tasks_per_epoch 30 --meta_batch_size 8 --no_augmentation --mixup_alpha 0.0 --cutmix_prob 0.0 --num_workers 4 --augment_factor 1 --kc_augment_factor 1 --seed 42 --early_kc_weight 5.300585807826661 --kc_weight 6.15076043556148 --focal_gamma 1.2116655516748096 --val_frequency 1 --early_stopping 3 --dropout 0.5 --use_separate_test_sampler --use_balanced_task_sampler --kc_shot_multiplier 3.8356964317112165 --early_kc_shot_multiplier 1.020226765500817 --use_contrastive --contrastive_weight 0.3539088319687521 --temperature 0.03090847571921763 --hard_mining_ratio 0.6521343136848173 --early_normal_weight 1.4797741441668677 --kc_normal_weight 1.0 --use_lr_scheduler --scheduler_type plateau --plateau_factor 0.5 --plateau_patience 2 --min_lr 1e-6
++ python train_balanced_task_sampling.py --train_csv /home/<USER>/balanced_task_sampling/split_result/train_set.csv --val_csv /home/<USER>/balanced_task_sampling/split_result/val_set.csv --test_csv /home/<USER>/balanced_task_sampling/split_result/test_set.csv --epochs 50 --lr 0.0002 --weight_decay 5e-4 --feature_dim 512 --save_dir /home/<USER>/balanced_task_sampling/results_trial_20250524_032034 --proto_counts 1,2,3 --pretrained --inner_lr 0.347389 --inner_steps 5 --n_way 3 --n_shot 5 --n_query 15 --tasks_per_epoch 30 --meta_batch_size 8 --no_augmentation --mixup_alpha 0.0 --cutmix_prob 0.0 --num_workers 4 --augment_factor 1 --kc_augment_factor 1 --seed 42 --early_kc_weight 5.300585807826661 --kc_weight 6.15076043556148 --focal_gamma 1.2116655516748096 --val_frequency 1 --early_stopping 3 --dropout 0.5 --use_separate_test_sampler --use_balanced_task_sampler --kc_shot_multiplier 3.8356964317112165 --early_kc_shot_multiplier 1.020226765500817 --use_contrastive --contrastive_weight 0.3539088319687521 --temperature 0.03090847571921763 --hard_mining_ratio 0.6521343136848173 --early_normal_weight 1.4797741441668677 --kc_normal_weight 1.0 --use_lr_scheduler --scheduler_type plateau --plateau_factor 0.5 --plateau_patience 2 --min_lr 1e-6
/home/<USER>/anaconda3/envs/kc1/lib/python3.10/site-packages/torchvision/models/_utils.py:208: UserWarning: The parameter 'pretrained' is deprecated since 0.13 and may be removed in the future, please use 'weights' instead.
  warnings.warn(
/home/<USER>/anaconda3/envs/kc1/lib/python3.10/site-packages/torchvision/models/_utils.py:223: UserWarning: Arguments other than a weight enum or `None` for 'weights' are deprecated since 0.13 and may be removed in the future. The current behavior is equivalent to passing `weights=ResNet34_Weights.IMAGENET1K_V1`. You can also use `weights=ResNet34_Weights.DEFAULT` to get the most up-to-date weights.
  warnings.warn(msg)
/home/<USER>/anaconda3/envs/kc1/lib/python3.10/site-packages/torch/optim/lr_scheduler.py:62: UserWarning: The verbose parameter is deprecated. Please use get_last_lr() to access the learning rate.
  warnings.warn(

Training tasks:   0%|          | 0/30 [00:00<?, ?it/s]
Training tasks:   3%|▎         | 1/30 [00:04<02:24,  4.99s/it]
Training tasks:   7%|▋         | 2/30 [00:06<01:21,  2.90s/it]
Training tasks:  10%|█         | 3/30 [00:07<00:59,  2.21s/it]
Training tasks:  13%|█▎        | 4/30 [00:09<00:50,  1.93s/it]
Training tasks:  17%|█▋        | 5/30 [00:10<00:42,  1.69s/it]
Training tasks:  20%|██        | 6/30 [00:11<00:37,  1.56s/it]
Training tasks:  23%|██▎       | 7/30 [00:13<00:34,  1.49s/it]
Training tasks:  27%|██▋       | 8/30 [00:14<00:33,  1.52s/it]
Training tasks:  30%|███       | 9/30 [00:16<00:30,  1.47s/it]
Training tasks:  33%|███▎      | 10/30 [00:17<00:30,  1.51s/it]
Training tasks:  37%|███▋      | 11/30 [00:19<00:27,  1.46s/it]
Training tasks:  40%|████      | 12/30 [00:20<00:25,  1.41s/it]
Training tasks:  43%|████▎     | 13/30 [00:21<00:23,  1.38s/it]
Training tasks:  47%|████▋     | 14/30 [00:23<00:21,  1.35s/it]
Training tasks:  50%|█████     | 15/30 [00:24<00:21,  1.42s/it]
Training tasks:  53%|█████▎    | 16/30 [00:25<00:19,  1.39s/it]
Training tasks:  57%|█████▋    | 17/30 [00:27<00:18,  1.45s/it]
Training tasks:  60%|██████    | 18/30 [00:29<00:18,  1.51s/it]
Training tasks:  63%|██████▎   | 19/30 [00:30<00:15,  1.45s/it]
Training tasks:  67%|██████▋   | 20/30 [00:31<00:14,  1.42s/it]
Training tasks:  70%|███████   | 21/30 [00:33<00:13,  1.47s/it]
Training tasks:  73%|███████▎  | 22/30 [00:34<00:11,  1.44s/it]
Training tasks:  77%|███████▋  | 23/30 [00:36<00:09,  1.40s/it]
Training tasks:  80%|████████  | 24/30 [00:37<00:08,  1.40s/it]
Training tasks:  83%|████████▎ | 25/30 [00:38<00:06,  1.36s/it]
Training tasks:  87%|████████▋ | 26/30 [00:40<00:05,  1.37s/it]
Training tasks:  90%|█████████ | 27/30 [00:41<00:04,  1.36s/it]
Training tasks:  93%|█████████▎| 28/30 [00:42<00:02,  1.31s/it]
Training tasks:  97%|█████████▋| 29/30 [00:43<00:01,  1.28s/it]
Training tasks: 100%|██████████| 30/30 [00:44<00:00,  1.20s/it]
Training tasks: 100%|██████████| 30/30 [00:44<00:00,  1.50s/it]

Validating tasks:   0%|          | 0/10 [00:00<?, ?it/s]
Validating tasks:  10%|█         | 1/10 [00:00<00:01,  6.41it/s]
Validating tasks:  20%|██        | 2/10 [00:00<00:01,  7.09it/s]
Validating tasks:  30%|███       | 3/10 [00:00<00:00,  7.37it/s]
Validating tasks:  40%|████      | 4/10 [00:00<00:00,  7.45it/s]
Validating tasks:  50%|█████     | 5/10 [00:00<00:00,  7.44it/s]
Validating tasks:  60%|██████    | 6/10 [00:00<00:00,  6.71it/s]
Validating tasks:  70%|███████   | 7/10 [00:00<00:00,  6.92it/s]
Validating tasks:  80%|████████  | 8/10 [00:01<00:00,  7.10it/s]
Validating tasks:  90%|█████████ | 9/10 [00:01<00:00,  7.19it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  7.28it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  7.16it/s]

Training tasks:   0%|          | 0/30 [00:00<?, ?it/s]
Training tasks:   3%|▎         | 1/30 [00:01<00:30,  1.05s/it]
Training tasks:   7%|▋         | 2/30 [00:02<00:32,  1.16s/it]
Training tasks:  10%|█         | 3/30 [00:03<00:36,  1.34s/it]
Training tasks:  13%|█▎        | 4/30 [00:05<00:35,  1.37s/it]
Training tasks:  17%|█▋        | 5/30 [00:06<00:34,  1.37s/it]
Training tasks:  20%|██        | 6/30 [00:08<00:33,  1.40s/it]
Training tasks:  23%|██▎       | 7/30 [00:09<00:32,  1.40s/it]
Training tasks:  27%|██▋       | 8/30 [00:10<00:31,  1.43s/it]
Training tasks:  30%|███       | 9/30 [00:12<00:28,  1.38s/it]
Training tasks:  33%|███▎      | 10/30 [00:13<00:28,  1.40s/it]
Training tasks:  37%|███▋      | 11/30 [00:15<00:26,  1.37s/it]
Training tasks:  40%|████      | 12/30 [00:16<00:24,  1.37s/it]
Training tasks:  43%|████▎     | 13/30 [00:17<00:23,  1.37s/it]
Training tasks:  47%|████▋     | 14/30 [00:19<00:21,  1.35s/it]
Training tasks:  50%|█████     | 15/30 [00:20<00:20,  1.35s/it]
Training tasks:  53%|█████▎    | 16/30 [00:21<00:18,  1.35s/it]
Training tasks:  57%|█████▋    | 17/30 [00:23<00:17,  1.35s/it]
Training tasks:  60%|██████    | 18/30 [00:24<00:16,  1.36s/it]
Training tasks:  63%|██████▎   | 19/30 [00:25<00:14,  1.33s/it]
Training tasks:  67%|██████▋   | 20/30 [00:27<00:13,  1.34s/it]
Training tasks:  70%|███████   | 21/30 [00:28<00:12,  1.34s/it]
Training tasks:  73%|███████▎  | 22/30 [00:29<00:10,  1.37s/it]
Training tasks:  77%|███████▋  | 23/30 [00:31<00:09,  1.36s/it]
Training tasks:  80%|████████  | 24/30 [00:32<00:08,  1.38s/it]
Training tasks:  83%|████████▎ | 25/30 [00:34<00:07,  1.44s/it]
Training tasks:  87%|████████▋ | 26/30 [00:35<00:05,  1.40s/it]
Training tasks:  90%|█████████ | 27/30 [00:36<00:04,  1.39s/it]
Training tasks:  93%|█████████▎| 28/30 [00:38<00:02,  1.37s/it]
Training tasks:  97%|█████████▋| 29/30 [00:39<00:01,  1.35s/it]
Training tasks: 100%|██████████| 30/30 [00:40<00:00,  1.37s/it]
Training tasks: 100%|██████████| 30/30 [00:40<00:00,  1.36s/it]

Validating tasks:   0%|          | 0/10 [00:00<?, ?it/s]
Validating tasks:  10%|█         | 1/10 [00:00<00:01,  5.28it/s]
Validating tasks:  20%|██        | 2/10 [00:00<00:01,  5.00it/s]
Validating tasks:  30%|███       | 3/10 [00:00<00:01,  5.08it/s]
Validating tasks:  40%|████      | 4/10 [00:00<00:01,  5.17it/s]
Validating tasks:  50%|█████     | 5/10 [00:00<00:00,  5.10it/s]
Validating tasks:  60%|██████    | 6/10 [00:01<00:00,  5.08it/s]
Validating tasks:  70%|███████   | 7/10 [00:01<00:00,  4.96it/s]
Validating tasks:  80%|████████  | 8/10 [00:01<00:00,  4.92it/s]
Validating tasks:  90%|█████████ | 9/10 [00:01<00:00,  4.88it/s]
Validating tasks: 100%|██████████| 10/10 [00:02<00:00,  4.87it/s]
Validating tasks: 100%|██████████| 10/10 [00:02<00:00,  4.97it/s]

Training tasks:   0%|          | 0/30 [00:00<?, ?it/s]
Training tasks:   3%|▎         | 1/30 [00:01<00:42,  1.47s/it]
Training tasks:   7%|▋         | 2/30 [00:02<00:39,  1.41s/it]
Training tasks:  10%|█         | 3/30 [00:04<00:36,  1.35s/it]
Training tasks:  13%|█▎        | 4/30 [00:05<00:34,  1.34s/it]
Training tasks:  17%|█▋        | 5/30 [00:06<00:33,  1.32s/it]
Training tasks:  20%|██        | 6/30 [00:08<00:32,  1.37s/it]
Training tasks:  23%|██▎       | 7/30 [00:09<00:31,  1.35s/it]
Training tasks:  27%|██▋       | 8/30 [00:10<00:29,  1.34s/it]
Training tasks:  30%|███       | 9/30 [00:12<00:29,  1.39s/it]
Training tasks:  33%|███▎      | 10/30 [00:13<00:27,  1.37s/it]
Training tasks:  37%|███▋      | 11/30 [00:15<00:26,  1.37s/it]
Training tasks:  40%|████      | 12/30 [00:16<00:24,  1.36s/it]
Training tasks:  43%|████▎     | 13/30 [00:17<00:23,  1.40s/it]
Training tasks:  47%|████▋     | 14/30 [00:19<00:23,  1.44s/it]
Training tasks:  50%|█████     | 15/30 [00:20<00:21,  1.44s/it]
Training tasks:  53%|█████▎    | 16/30 [00:22<00:19,  1.42s/it]
Training tasks:  57%|█████▋    | 17/30 [00:23<00:18,  1.45s/it]
Training tasks:  60%|██████    | 18/30 [00:25<00:17,  1.48s/it]
Training tasks:  63%|██████▎   | 19/30 [00:26<00:16,  1.48s/it]
Training tasks:  67%|██████▋   | 20/30 [00:28<00:14,  1.47s/it]
Training tasks:  70%|███████   | 21/30 [00:29<00:13,  1.45s/it]
Training tasks:  73%|███████▎  | 22/30 [00:30<00:11,  1.39s/it]
Training tasks:  77%|███████▋  | 23/30 [00:32<00:09,  1.39s/it]
Training tasks:  80%|████████  | 24/30 [00:33<00:08,  1.37s/it]
Training tasks:  83%|████████▎ | 25/30 [00:34<00:06,  1.36s/it]
Training tasks:  87%|████████▋ | 26/30 [00:36<00:05,  1.43s/it]
Training tasks:  90%|█████████ | 27/30 [00:38<00:04,  1.52s/it]
Training tasks:  93%|█████████▎| 28/30 [00:39<00:03,  1.53s/it]
Training tasks:  97%|█████████▋| 29/30 [00:41<00:01,  1.58s/it]
Training tasks: 100%|██████████| 30/30 [00:42<00:00,  1.51s/it]
Training tasks: 100%|██████████| 30/30 [00:42<00:00,  1.43s/it]

Validating tasks:   0%|          | 0/10 [00:00<?, ?it/s]
Validating tasks:  10%|█         | 1/10 [00:00<00:01,  5.58it/s]
Validating tasks:  20%|██        | 2/10 [00:00<00:01,  5.54it/s]
Validating tasks:  30%|███       | 3/10 [00:00<00:01,  5.62it/s]
Validating tasks:  40%|████      | 4/10 [00:00<00:01,  5.57it/s]
Validating tasks:  50%|█████     | 5/10 [00:00<00:00,  5.51it/s]
Validating tasks:  60%|██████    | 6/10 [00:01<00:00,  5.31it/s]
Validating tasks:  70%|███████   | 7/10 [00:01<00:00,  5.28it/s]
Validating tasks:  80%|████████  | 8/10 [00:01<00:00,  5.31it/s]
Validating tasks:  90%|█████████ | 9/10 [00:01<00:00,  5.30it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  5.44it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  5.42it/s]

Training tasks:   0%|          | 0/30 [00:00<?, ?it/s]
Training tasks:   3%|▎         | 1/30 [00:01<00:38,  1.31s/it]
Training tasks:   7%|▋         | 2/30 [00:02<00:37,  1.33s/it]
Training tasks:  10%|█         | 3/30 [00:04<00:36,  1.36s/it]
Training tasks:  13%|█▎        | 4/30 [00:05<00:35,  1.37s/it]
Training tasks:  17%|█▋        | 5/30 [00:06<00:34,  1.38s/it]
Training tasks:  20%|██        | 6/30 [00:08<00:32,  1.37s/it]
Training tasks:  23%|██▎       | 7/30 [00:09<00:31,  1.35s/it]
Training tasks:  27%|██▋       | 8/30 [00:10<00:29,  1.34s/it]
Training tasks:  30%|███       | 9/30 [00:12<00:28,  1.35s/it]
Training tasks:  33%|███▎      | 10/30 [00:13<00:26,  1.33s/it]
Training tasks:  37%|███▋      | 11/30 [00:14<00:25,  1.34s/it]
Training tasks:  40%|████      | 12/30 [00:16<00:24,  1.35s/it]
Training tasks:  43%|████▎     | 13/30 [00:17<00:22,  1.34s/it]
Training tasks:  47%|████▋     | 14/30 [00:19<00:22,  1.42s/it]
Training tasks:  50%|█████     | 15/30 [00:20<00:21,  1.42s/it]
Training tasks:  53%|█████▎    | 16/30 [00:22<00:20,  1.48s/it]
Training tasks:  57%|█████▋    | 17/30 [00:23<00:18,  1.44s/it]
Training tasks:  60%|██████    | 18/30 [00:24<00:17,  1.43s/it]
Training tasks:  63%|██████▎   | 19/30 [00:26<00:16,  1.47s/it]
Training tasks:  67%|██████▋   | 20/30 [00:27<00:14,  1.42s/it]
Training tasks:  70%|███████   | 21/30 [00:29<00:12,  1.39s/it]
Training tasks:  73%|███████▎  | 22/30 [00:30<00:10,  1.35s/it]
Training tasks:  77%|███████▋  | 23/30 [00:31<00:09,  1.36s/it]
Training tasks:  80%|████████  | 24/30 [00:33<00:08,  1.34s/it]
Training tasks:  83%|████████▎ | 25/30 [00:34<00:06,  1.31s/it]
Training tasks:  87%|████████▋ | 26/30 [00:35<00:05,  1.31s/it]
Training tasks:  90%|█████████ | 27/30 [00:36<00:03,  1.32s/it]
Training tasks:  93%|█████████▎| 28/30 [00:38<00:02,  1.42s/it]
Training tasks:  97%|█████████▋| 29/30 [00:40<00:01,  1.42s/it]
Training tasks: 100%|██████████| 30/30 [00:41<00:00,  1.48s/it]
Training tasks: 100%|██████████| 30/30 [00:41<00:00,  1.39s/it]

Validating tasks:   0%|          | 0/10 [00:00<?, ?it/s]
Validating tasks:  10%|█         | 1/10 [00:00<00:01,  5.76it/s]
Validating tasks:  20%|██        | 2/10 [00:00<00:01,  5.65it/s]
Validating tasks:  30%|███       | 3/10 [00:00<00:01,  5.56it/s]
Validating tasks:  40%|████      | 4/10 [00:00<00:01,  5.16it/s]
Validating tasks:  50%|█████     | 5/10 [00:00<00:01,  4.90it/s]
Validating tasks:  60%|██████    | 6/10 [00:01<00:00,  4.68it/s]
Validating tasks:  70%|███████   | 7/10 [00:01<00:00,  4.46it/s]
Validating tasks:  80%|████████  | 8/10 [00:01<00:00,  4.67it/s]
Validating tasks:  90%|█████████ | 9/10 [00:01<00:00,  4.58it/s]
Validating tasks: 100%|██████████| 10/10 [00:02<00:00,  4.60it/s]
Validating tasks: 100%|██████████| 10/10 [00:02<00:00,  4.79it/s]

Training tasks:   0%|          | 0/30 [00:00<?, ?it/s]
Training tasks:   3%|▎         | 1/30 [00:01<00:47,  1.63s/it]
Training tasks:   7%|▋         | 2/30 [00:03<00:42,  1.52s/it]
Training tasks:  10%|█         | 3/30 [00:04<00:38,  1.41s/it]
Training tasks:  13%|█▎        | 4/30 [00:05<00:36,  1.40s/it]
Training tasks:  17%|█▋        | 5/30 [00:07<00:35,  1.43s/it]
Training tasks:  20%|██        | 6/30 [00:08<00:35,  1.48s/it]
Training tasks:  23%|██▎       | 7/30 [00:10<00:32,  1.43s/it]
Training tasks:  27%|██▋       | 8/30 [00:11<00:30,  1.39s/it]
Training tasks:  30%|███       | 9/30 [00:12<00:28,  1.34s/it]
Training tasks:  33%|███▎      | 10/30 [00:13<00:26,  1.33s/it]
Training tasks:  37%|███▋      | 11/30 [00:15<00:24,  1.30s/it]
Training tasks:  40%|████      | 12/30 [00:16<00:23,  1.28s/it]
Training tasks:  43%|████▎     | 13/30 [00:17<00:22,  1.30s/it]
Training tasks:  47%|████▋     | 14/30 [00:19<00:21,  1.32s/it]
Training tasks:  50%|█████     | 15/30 [00:20<00:20,  1.37s/it]
Training tasks:  53%|█████▎    | 16/30 [00:22<00:19,  1.42s/it]
Training tasks:  57%|█████▋    | 17/30 [00:23<00:18,  1.46s/it]
Training tasks:  60%|██████    | 18/30 [00:25<00:17,  1.43s/it]
Training tasks:  63%|██████▎   | 19/30 [00:26<00:15,  1.39s/it]
Training tasks:  67%|██████▋   | 20/30 [00:27<00:14,  1.41s/it]
Training tasks:  70%|███████   | 21/30 [00:29<00:13,  1.49s/it]
Training tasks:  73%|███████▎  | 22/30 [00:31<00:12,  1.51s/it]
Training tasks:  77%|███████▋  | 23/30 [00:32<00:10,  1.51s/it]
Training tasks:  80%|████████  | 24/30 [00:34<00:09,  1.54s/it]
Training tasks:  83%|████████▎ | 25/30 [00:35<00:07,  1.47s/it]
Training tasks:  87%|████████▋ | 26/30 [00:36<00:05,  1.40s/it]
Training tasks:  90%|█████████ | 27/30 [00:38<00:04,  1.39s/it]
Training tasks:  93%|█████████▎| 28/30 [00:39<00:02,  1.41s/it]
Training tasks:  97%|█████████▋| 29/30 [00:40<00:01,  1.38s/it]
Training tasks: 100%|██████████| 30/30 [00:42<00:00,  1.38s/it]
Training tasks: 100%|██████████| 30/30 [00:42<00:00,  1.41s/it]

Validating tasks:   0%|          | 0/10 [00:00<?, ?it/s]
Validating tasks:  10%|█         | 1/10 [00:00<00:01,  5.07it/s]
Validating tasks:  20%|██        | 2/10 [00:00<00:01,  5.04it/s]
Validating tasks:  30%|███       | 3/10 [00:00<00:01,  4.85it/s]
Validating tasks:  40%|████      | 4/10 [00:00<00:01,  4.92it/s]
Validating tasks:  50%|█████     | 5/10 [00:01<00:01,  4.71it/s]
Validating tasks:  60%|██████    | 6/10 [00:01<00:00,  4.69it/s]
Validating tasks:  70%|███████   | 7/10 [00:01<00:00,  4.69it/s]
Validating tasks:  80%|████████  | 8/10 [00:01<00:00,  4.70it/s]
Validating tasks:  90%|█████████ | 9/10 [00:01<00:00,  4.59it/s]
Validating tasks: 100%|██████████| 10/10 [00:02<00:00,  4.69it/s]
Validating tasks: 100%|██████████| 10/10 [00:02<00:00,  4.74it/s]

Training tasks:   0%|          | 0/30 [00:00<?, ?it/s]
Training tasks:   3%|▎         | 1/30 [00:01<00:43,  1.51s/it]
Training tasks:   7%|▋         | 2/30 [00:02<00:39,  1.40s/it]
Training tasks:  10%|█         | 3/30 [00:04<00:40,  1.50s/it]
Training tasks:  13%|█▎        | 4/30 [00:06<00:40,  1.55s/it]
Training tasks:  17%|█▋        | 5/30 [00:07<00:39,  1.58s/it]
Training tasks:  20%|██        | 6/30 [00:09<00:37,  1.55s/it]
Training tasks:  23%|██▎       | 7/30 [00:10<00:35,  1.53s/it]
Training tasks:  27%|██▋       | 8/30 [00:12<00:33,  1.51s/it]
Training tasks:  30%|███       | 9/30 [00:13<00:31,  1.48s/it]
Training tasks:  33%|███▎      | 10/30 [00:14<00:29,  1.45s/it]
Training tasks:  37%|███▋      | 11/30 [00:16<00:27,  1.45s/it]
Training tasks:  40%|████      | 12/30 [00:17<00:25,  1.43s/it]
Training tasks:  43%|████▎     | 13/30 [00:19<00:23,  1.41s/it]
Training tasks:  47%|████▋     | 14/30 [00:20<00:22,  1.43s/it]
Training tasks:  50%|█████     | 15/30 [00:22<00:21,  1.45s/it]
Training tasks:  53%|█████▎    | 16/30 [00:23<00:20,  1.47s/it]
Training tasks:  57%|█████▋    | 17/30 [00:24<00:18,  1.43s/it]
Training tasks:  60%|██████    | 18/30 [00:26<00:16,  1.40s/it]
Training tasks:  63%|██████▎   | 19/30 [00:27<00:15,  1.40s/it]
Training tasks:  67%|██████▋   | 20/30 [00:29<00:14,  1.41s/it]
Training tasks:  70%|███████   | 21/30 [00:30<00:13,  1.45s/it]
Training tasks:  73%|███████▎  | 22/30 [00:31<00:11,  1.40s/it]
Training tasks:  77%|███████▋  | 23/30 [00:33<00:10,  1.44s/it]
Training tasks:  80%|████████  | 24/30 [00:34<00:08,  1.43s/it]
Training tasks:  83%|████████▎ | 25/30 [00:36<00:06,  1.38s/it]
Training tasks:  87%|████████▋ | 26/30 [00:37<00:05,  1.37s/it]
Training tasks:  90%|█████████ | 27/30 [00:38<00:04,  1.35s/it]
Training tasks:  93%|█████████▎| 28/30 [00:40<00:02,  1.36s/it]
Training tasks:  97%|█████████▋| 29/30 [00:41<00:01,  1.37s/it]
Training tasks: 100%|██████████| 30/30 [00:42<00:00,  1.35s/it]
Training tasks: 100%|██████████| 30/30 [00:42<00:00,  1.43s/it]

Validating tasks:   0%|          | 0/10 [00:00<?, ?it/s]
Validating tasks:  10%|█         | 1/10 [00:00<00:01,  5.77it/s]
Validating tasks:  20%|██        | 2/10 [00:00<00:01,  5.94it/s]
Validating tasks:  30%|███       | 3/10 [00:00<00:01,  5.54it/s]
Validating tasks:  40%|████      | 4/10 [00:00<00:01,  5.13it/s]
Validating tasks:  50%|█████     | 5/10 [00:00<00:00,  5.08it/s]
Validating tasks:  60%|██████    | 6/10 [00:01<00:00,  5.21it/s]
Validating tasks:  70%|███████   | 7/10 [00:01<00:00,  5.20it/s]
Validating tasks:  80%|████████  | 8/10 [00:01<00:00,  5.03it/s]
Validating tasks:  90%|█████████ | 9/10 [00:01<00:00,  4.73it/s]
Validating tasks: 100%|██████████| 10/10 [00:02<00:00,  4.52it/s]
Validating tasks: 100%|██████████| 10/10 [00:02<00:00,  4.94it/s]

Training tasks:   0%|          | 0/30 [00:00<?, ?it/s]
Training tasks:   3%|▎         | 1/30 [00:01<00:41,  1.45s/it]
Training tasks:   7%|▋         | 2/30 [00:02<00:40,  1.45s/it]
Training tasks:  10%|█         | 3/30 [00:04<00:38,  1.43s/it]
Training tasks:  13%|█▎        | 4/30 [00:05<00:36,  1.42s/it]
Training tasks:  17%|█▋        | 5/30 [00:07<00:35,  1.44s/it]
Training tasks:  20%|██        | 6/30 [00:08<00:34,  1.42s/it]
Training tasks:  23%|██▎       | 7/30 [00:10<00:33,  1.45s/it]
Training tasks:  27%|██▋       | 8/30 [00:11<00:32,  1.49s/it]
Training tasks:  30%|███       | 9/30 [00:12<00:30,  1.44s/it]
Training tasks:  33%|███▎      | 10/30 [00:14<00:27,  1.38s/it]
Training tasks:  37%|███▋      | 11/30 [00:15<00:25,  1.35s/it]
Training tasks:  40%|████      | 12/30 [00:16<00:23,  1.33s/it]
Training tasks:  43%|████▎     | 13/30 [00:18<00:22,  1.34s/it]
Training tasks:  47%|████▋     | 14/30 [00:19<00:21,  1.33s/it]
Training tasks:  50%|█████     | 15/30 [00:20<00:20,  1.39s/it]
Training tasks:  53%|█████▎    | 16/30 [00:22<00:20,  1.43s/it]
Training tasks:  57%|█████▋    | 17/30 [00:23<00:18,  1.42s/it]
Training tasks:  60%|██████    | 18/30 [00:25<00:17,  1.42s/it]
Training tasks:  63%|██████▎   | 19/30 [00:26<00:15,  1.45s/it]
Training tasks:  67%|██████▋   | 20/30 [00:28<00:14,  1.48s/it]
Training tasks:  70%|███████   | 21/30 [00:29<00:13,  1.48s/it]
Training tasks:  73%|███████▎  | 22/30 [00:31<00:11,  1.48s/it]
Training tasks:  77%|███████▋  | 23/30 [00:32<00:10,  1.45s/it]
Training tasks:  80%|████████  | 24/30 [00:34<00:08,  1.40s/it]
Training tasks:  83%|████████▎ | 25/30 [00:35<00:06,  1.37s/it]
Training tasks:  87%|████████▋ | 26/30 [00:36<00:05,  1.36s/it]
Training tasks:  90%|█████████ | 27/30 [00:38<00:04,  1.44s/it]
Training tasks:  93%|█████████▎| 28/30 [00:39<00:02,  1.44s/it]
Training tasks:  97%|█████████▋| 29/30 [00:41<00:01,  1.40s/it]
Training tasks: 100%|██████████| 30/30 [00:42<00:00,  1.38s/it]
Training tasks: 100%|██████████| 30/30 [00:42<00:00,  1.41s/it]

Validating tasks:   0%|          | 0/10 [00:00<?, ?it/s]
Validating tasks:  10%|█         | 1/10 [00:00<00:02,  3.85it/s]
Validating tasks:  20%|██        | 2/10 [00:00<00:02,  3.65it/s]
Validating tasks:  30%|███       | 3/10 [00:00<00:01,  3.58it/s]
Validating tasks:  40%|████      | 4/10 [00:01<00:01,  3.56it/s]
Validating tasks:  50%|█████     | 5/10 [00:01<00:01,  3.58it/s]
Validating tasks:  60%|██████    | 6/10 [00:01<00:01,  3.87it/s]
Validating tasks:  70%|███████   | 7/10 [00:01<00:00,  4.00it/s]
Validating tasks:  80%|████████  | 8/10 [00:02<00:00,  4.07it/s]
Validating tasks:  90%|█████████ | 9/10 [00:02<00:00,  4.05it/s]
Validating tasks: 100%|██████████| 10/10 [00:02<00:00,  4.21it/s]
Validating tasks: 100%|██████████| 10/10 [00:02<00:00,  3.93it/s]

Training tasks:   0%|          | 0/30 [00:00<?, ?it/s]
Training tasks:   3%|▎         | 1/30 [00:01<00:39,  1.35s/it]
Training tasks:   7%|▋         | 2/30 [00:02<00:38,  1.36s/it]
Training tasks:  10%|█         | 3/30 [00:04<00:37,  1.39s/it]
Training tasks:  13%|█▎        | 4/30 [00:05<00:35,  1.36s/it]
Training tasks:  17%|█▋        | 5/30 [00:06<00:34,  1.37s/it]
Training tasks:  20%|██        | 6/30 [00:08<00:33,  1.39s/it]
Training tasks:  23%|██▎       | 7/30 [00:09<00:33,  1.45s/it]
Training tasks:  27%|██▋       | 8/30 [00:11<00:32,  1.49s/it]
Training tasks:  30%|███       | 9/30 [00:12<00:31,  1.50s/it]
Training tasks:  33%|███▎      | 10/30 [00:14<00:29,  1.49s/it]
Training tasks:  37%|███▋      | 11/30 [00:15<00:28,  1.48s/it]
Training tasks:  40%|████      | 12/30 [00:17<00:25,  1.43s/it]
Training tasks:  43%|████▎     | 13/30 [00:18<00:23,  1.38s/it]
Training tasks:  47%|████▋     | 14/30 [00:19<00:21,  1.33s/it]
Training tasks:  50%|█████     | 15/30 [00:21<00:20,  1.36s/it]
Training tasks:  53%|█████▎    | 16/30 [00:22<00:19,  1.40s/it]
Training tasks:  57%|█████▋    | 17/30 [00:24<00:18,  1.41s/it]
Training tasks:  60%|██████    | 18/30 [00:25<00:17,  1.43s/it]
Training tasks:  63%|██████▎   | 19/30 [00:27<00:16,  1.46s/it]
Training tasks:  67%|██████▋   | 20/30 [00:28<00:14,  1.44s/it]
Training tasks:  70%|███████   | 21/30 [00:29<00:12,  1.42s/it]
Training tasks:  73%|███████▎  | 22/30 [00:31<00:11,  1.40s/it]
Training tasks:  77%|███████▋  | 23/30 [00:32<00:10,  1.43s/it]
Training tasks:  80%|████████  | 24/30 [00:34<00:08,  1.45s/it]
Training tasks:  83%|████████▎ | 25/30 [00:35<00:07,  1.43s/it]
Training tasks:  87%|████████▋ | 26/30 [00:36<00:05,  1.44s/it]
Training tasks:  90%|█████████ | 27/30 [00:38<00:04,  1.52s/it]
Training tasks:  93%|█████████▎| 28/30 [00:40<00:03,  1.56s/it]
Training tasks:  97%|█████████▋| 29/30 [00:41<00:01,  1.56s/it]
Training tasks: 100%|██████████| 30/30 [00:43<00:00,  1.47s/it]
Training tasks: 100%|██████████| 30/30 [00:43<00:00,  1.44s/it]

Validating tasks:   0%|          | 0/10 [00:00<?, ?it/s]
Validating tasks:  10%|█         | 1/10 [00:00<00:01,  5.24it/s]
Validating tasks:  20%|██        | 2/10 [00:00<00:01,  5.20it/s]
Validating tasks:  30%|███       | 3/10 [00:00<00:01,  5.20it/s]
Validating tasks:  40%|████      | 4/10 [00:00<00:01,  5.25it/s]
Validating tasks:  50%|█████     | 5/10 [00:01<00:01,  4.83it/s]
Validating tasks:  60%|██████    | 6/10 [00:01<00:00,  5.06it/s]
Validating tasks:  70%|███████   | 7/10 [00:01<00:00,  4.89it/s]
Validating tasks:  80%|████████  | 8/10 [00:01<00:00,  4.96it/s]
Validating tasks:  90%|█████████ | 9/10 [00:01<00:00,  4.82it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  4.99it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  5.00it/s]

Training tasks:   0%|          | 0/30 [00:00<?, ?it/s]
Training tasks:   3%|▎         | 1/30 [00:01<00:44,  1.54s/it]
Training tasks:   7%|▋         | 2/30 [00:03<00:43,  1.55s/it]
Training tasks:  10%|█         | 3/30 [00:04<00:41,  1.55s/it]
Training tasks:  13%|█▎        | 4/30 [00:06<00:38,  1.48s/it]
Training tasks:  17%|█▋        | 5/30 [00:07<00:36,  1.45s/it]
Training tasks:  20%|██        | 6/30 [00:08<00:35,  1.48s/it]
Training tasks:  23%|██▎       | 7/30 [00:10<00:32,  1.41s/it]
Training tasks:  27%|██▋       | 8/30 [00:11<00:30,  1.37s/it]
Training tasks:  30%|███       | 9/30 [00:12<00:28,  1.35s/it]
Training tasks:  33%|███▎      | 10/30 [00:14<00:28,  1.40s/it]
Training tasks:  37%|███▋      | 11/30 [00:15<00:28,  1.48s/it]
Training tasks:  40%|████      | 12/30 [00:17<00:27,  1.50s/it]
Training tasks:  43%|████▎     | 13/30 [00:19<00:26,  1.54s/it]
Training tasks:  47%|████▋     | 14/30 [00:20<00:23,  1.46s/it]
Training tasks:  50%|█████     | 15/30 [00:21<00:21,  1.41s/it]
Training tasks:  53%|█████▎    | 16/30 [00:23<00:20,  1.47s/it]
Training tasks:  57%|█████▋    | 17/30 [00:24<00:19,  1.47s/it]
Training tasks:  60%|██████    | 18/30 [00:26<00:17,  1.47s/it]
Training tasks:  63%|██████▎   | 19/30 [00:27<00:16,  1.46s/it]
Training tasks:  67%|██████▋   | 20/30 [00:29<00:14,  1.46s/it]
Training tasks:  70%|███████   | 21/30 [00:30<00:13,  1.48s/it]
Training tasks:  73%|███████▎  | 22/30 [00:32<00:11,  1.50s/it]
Training tasks:  77%|███████▋  | 23/30 [00:33<00:10,  1.52s/it]
Training tasks:  80%|████████  | 24/30 [00:35<00:08,  1.44s/it]
Training tasks:  83%|████████▎ | 25/30 [00:36<00:07,  1.40s/it]
Training tasks:  87%|████████▋ | 26/30 [00:37<00:05,  1.43s/it]
Training tasks:  90%|█████████ | 27/30 [00:39<00:04,  1.44s/it]
Training tasks:  93%|█████████▎| 28/30 [00:40<00:02,  1.41s/it]
Training tasks:  97%|█████████▋| 29/30 [00:42<00:01,  1.38s/it]
Training tasks: 100%|██████████| 30/30 [00:43<00:00,  1.43s/it]
Training tasks: 100%|██████████| 30/30 [00:43<00:00,  1.45s/it]

Validating tasks:   0%|          | 0/10 [00:00<?, ?it/s]
Validating tasks:  10%|█         | 1/10 [00:00<00:01,  4.68it/s]
Validating tasks:  20%|██        | 2/10 [00:00<00:01,  4.73it/s]
Validating tasks:  30%|███       | 3/10 [00:00<00:01,  4.64it/s]
Validating tasks:  40%|████      | 4/10 [00:00<00:01,  4.67it/s]
Validating tasks:  50%|█████     | 5/10 [00:01<00:01,  4.69it/s]
Validating tasks:  60%|██████    | 6/10 [00:01<00:00,  4.38it/s]
Validating tasks:  70%|███████   | 7/10 [00:01<00:00,  4.11it/s]
Validating tasks:  80%|████████  | 8/10 [00:01<00:00,  4.07it/s]
Validating tasks:  90%|█████████ | 9/10 [00:02<00:00,  4.18it/s]
Validating tasks: 100%|██████████| 10/10 [00:02<00:00,  4.34it/s]
Validating tasks: 100%|██████████| 10/10 [00:02<00:00,  4.37it/s]

Testing tasks:   0%|          | 0/30 [00:00<?, ?it/s]
Testing tasks:   3%|▎         | 1/30 [00:00<00:08,  3.48it/s]
Testing tasks:   7%|▋         | 2/30 [00:00<00:07,  3.67it/s]
Testing tasks:  10%|█         | 3/30 [00:00<00:07,  3.77it/s]
Testing tasks:  13%|█▎        | 4/30 [00:01<00:07,  3.60it/s]
Testing tasks:  17%|█▋        | 5/30 [00:01<00:06,  3.72it/s]
Testing tasks:  20%|██        | 6/30 [00:01<00:06,  3.73it/s]
Testing tasks:  23%|██▎       | 7/30 [00:01<00:06,  3.78it/s]
Testing tasks:  27%|██▋       | 8/30 [00:02<00:05,  3.74it/s]
Testing tasks:  30%|███       | 9/30 [00:02<00:05,  3.80it/s]
Testing tasks:  33%|███▎      | 10/30 [00:02<00:05,  3.48it/s]
Testing tasks:  37%|███▋      | 11/30 [00:03<00:05,  3.59it/s]
Testing tasks:  40%|████      | 12/30 [00:03<00:04,  3.67it/s]
Testing tasks:  43%|████▎     | 13/30 [00:03<00:04,  3.77it/s]
Testing tasks:  47%|████▋     | 14/30 [00:03<00:04,  3.78it/s]
Testing tasks:  50%|█████     | 15/30 [00:04<00:03,  3.87it/s]
Testing tasks:  53%|█████▎    | 16/30 [00:04<00:03,  3.83it/s]
Testing tasks:  57%|█████▋    | 17/30 [00:04<00:03,  3.89it/s]
Testing tasks:  60%|██████    | 18/30 [00:04<00:03,  3.86it/s]
Testing tasks:  63%|██████▎   | 19/30 [00:05<00:02,  3.90it/s]
Testing tasks:  67%|██████▋   | 20/30 [00:05<00:02,  3.70it/s]
Testing tasks:  70%|███████   | 21/30 [00:05<00:02,  3.58it/s]
Testing tasks:  73%|███████▎  | 22/30 [00:05<00:02,  3.51it/s]
Testing tasks:  77%|███████▋  | 23/30 [00:06<00:02,  3.31it/s]
Testing tasks:  80%|████████  | 24/30 [00:06<00:01,  3.26it/s]
Testing tasks:  83%|████████▎ | 25/30 [00:06<00:01,  3.34it/s]
Testing tasks:  87%|████████▋ | 26/30 [00:07<00:01,  3.40it/s]
Testing tasks:  90%|█████████ | 27/30 [00:07<00:00,  3.41it/s]
Testing tasks:  93%|█████████▎| 28/30 [00:07<00:00,  3.40it/s]
Testing tasks:  97%|█████████▋| 29/30 [00:08<00:00,  3.45it/s]
Testing tasks: 100%|██████████| 30/30 [00:08<00:00,  3.39it/s]
Testing tasks: 100%|██████████| 30/30 [00:08<00:00,  3.59it/s]

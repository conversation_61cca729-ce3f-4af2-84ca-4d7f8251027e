STDOUT:
===================== 训练配置 =====================
GPU: 0
输出目录: /home/<USER>/balanced_task_sampling/results_trial_20250525_003524
日志文件: /home/<USER>/balanced_task_sampling/results_trial_20250525_003524/main_training_trial_27_20250525_003524.log

--------- 优化参数 ---------
内部学习率: 0.347389
训练轮数: 50
每轮任务数: 30

--------- 模型参数 ---------
使用类别权重: 圆锥角膜=5.403546817303161, 早期圆锥角膜=5.045557349137128
温度参数: 0.02509940375435208
硬样本挖掘比例: 0.45700162851906456
对比学习权重: 0.5247860058859002
=============================================

创建目录: /home/<USER>/balanced_task_sampling/results_trial_20250525_003524
总用量 4
-rw-rw-r-- 1 <USER> <GROUP> 658 5月  25 00:35 main_training_trial_27_20250525_003524.log
开始无数据增强的平衡任务采样训练...
使用GPU: 0
直接运行训练脚本...
成功导入增强版特征提取器
成功导入归一化特征提取器
成功导入增强版损失函数
成功导入增强版MAML模型
成功导入KC特定增强模块
成功导入增强版GPU数据增强模块
使用设备: cuda
禁用所有数据增强，直接加载原始数据集...
使用平衡任务采样器
类别 normal (标签 0): 164个样本
类别 e-kc (标签 1): 26个样本
类别 kc (标签 2): 133个样本
使用原型配置: [2, 5, 4]
使用增强版特征提取器，dropout率: 0.5
使用增强版MAML-ProtoNet模型，内循环学习率: 0.347389, 内循环步数: 5
使用ReduceLROnPlateau学习率调度器，因子: 0.5, 耐心值: 2
开始训练，共50个epoch
Epoch 1/50
当前学习率: 0.000200
使用类别权重: 正常=1.0, 早期圆锥角膜=5.045557349137128, 圆锥角膜=5.403546817303161
使用增强版对比损失函数，温度: 0.02509940375435208, 硬负样本挖掘比例: 0.45700162851906456, 早期圆锥角膜与正常样本对比权重: 1.1157319874532652, 圆锥角膜与正常样本对比权重: 1.0
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
警告: 没有足够的有效类别进行3-way任务采样，尝试调整参数...
类别样本数量: {0: 33, 1: 5, 2: 29}
调整后的参数: n_shot=2, n_query=3
类别 normal (标签 0): 33个样本
类别 e-kc (标签 1): 5个样本
类别 kc (标签 2): 29个样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
Epoch 1: Train Loss: 2.7731, Train Acc: 0.4607, Val Loss: 2.1862, Val Acc: 0.5444
保存最佳模型，验证准确率: 0.5444
Epoch 2/50
当前学习率: 0.000200
使用类别权重: 正常=1.0, 早期圆锥角膜=5.045557349137128, 圆锥角膜=5.403546817303161
使用增强版对比损失函数，温度: 0.02509940375435208, 硬负样本挖掘比例: 0.45700162851906456, 早期圆锥角膜与正常样本对比权重: 1.1157319874532652, 圆锥角膜与正常样本对比权重: 1.0
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
警告: 没有足够的有效类别进行3-way任务采样，尝试调整参数...
类别样本数量: {0: 33, 1: 5, 2: 29}
调整后的参数: n_shot=2, n_query=3
类别 normal (标签 0): 33个样本
类别 e-kc (标签 1): 5个样本
类别 kc (标签 2): 29个样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
Epoch 2: Train Loss: 2.5027, Train Acc: 0.5296, Val Loss: 2.1796, Val Acc: 0.6222
保存最佳模型，验证准确率: 0.6222
Epoch 3/50
当前学习率: 0.000200
使用类别权重: 正常=1.0, 早期圆锥角膜=5.045557349137128, 圆锥角膜=5.403546817303161
使用增强版对比损失函数，温度: 0.02509940375435208, 硬负样本挖掘比例: 0.45700162851906456, 早期圆锥角膜与正常样本对比权重: 1.1157319874532652, 圆锥角膜与正常样本对比权重: 1.0
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
警告: 没有足够的有效类别进行3-way任务采样，尝试调整参数...
类别样本数量: {0: 33, 1: 5, 2: 29}
调整后的参数: n_shot=2, n_query=3
类别 normal (标签 0): 33个样本
类别 e-kc (标签 1): 5个样本
类别 kc (标签 2): 29个样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
Epoch 3: Train Loss: 2.1150, Train Acc: 0.6637, Val Loss: 2.0362, Val Acc: 0.6222
保存最佳模型，验证准确率: 0.6222
Epoch 4/50
当前学习率: 0.000200
使用类别权重: 正常=1.0, 早期圆锥角膜=5.045557349137128, 圆锥角膜=5.403546817303161
使用增强版对比损失函数，温度: 0.02509940375435208, 硬负样本挖掘比例: 0.45700162851906456, 早期圆锥角膜与正常样本对比权重: 1.1157319874532652, 圆锥角膜与正常样本对比权重: 1.0
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
警告: 没有足够的有效类别进行3-way任务采样，尝试调整参数...
类别样本数量: {0: 33, 1: 5, 2: 29}
调整后的参数: n_shot=2, n_query=3
类别 normal (标签 0): 33个样本
类别 e-kc (标签 1): 5个样本
类别 kc (标签 2): 29个样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
Epoch 4: Train Loss: 1.9557, Train Acc: 0.7459, Val Loss: 2.1281, Val Acc: 0.6444
保存最佳模型，验证准确率: 0.6444
Epoch 5/50
当前学习率: 0.000200
使用类别权重: 正常=1.0, 早期圆锥角膜=5.045557349137128, 圆锥角膜=5.403546817303161
使用增强版对比损失函数，温度: 0.02509940375435208, 硬负样本挖掘比例: 0.45700162851906456, 早期圆锥角膜与正常样本对比权重: 1.1157319874532652, 圆锥角膜与正常样本对比权重: 1.0
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
警告: 没有足够的有效类别进行3-way任务采样，尝试调整参数...
类别样本数量: {0: 33, 1: 5, 2: 29}
调整后的参数: n_shot=2, n_query=3
类别 normal (标签 0): 33个样本
类别 e-kc (标签 1): 5个样本
类别 kc (标签 2): 29个样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
Epoch 5: Train Loss: 1.9573, Train Acc: 0.8037, Val Loss: 1.9811, Val Acc: 0.7333
保存最佳模型，验证准确率: 0.7333
Epoch 6/50
当前学习率: 0.000200
使用类别权重: 正常=1.0, 早期圆锥角膜=5.045557349137128, 圆锥角膜=5.403546817303161
使用增强版对比损失函数，温度: 0.02509940375435208, 硬负样本挖掘比例: 0.45700162851906456, 早期圆锥角膜与正常样本对比权重: 1.1157319874532652, 圆锥角膜与正常样本对比权重: 1.0
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
警告: 没有足够的有效类别进行3-way任务采样，尝试调整参数...
类别样本数量: {0: 33, 1: 5, 2: 29}
调整后的参数: n_shot=2, n_query=3
类别 normal (标签 0): 33个样本
类别 e-kc (标签 1): 5个样本
类别 kc (标签 2): 29个样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
Epoch 6: Train Loss: 1.8454, Train Acc: 0.8141, Val Loss: 2.1137, Val Acc: 0.6000
验证准确率未提高，当前耐心值: 1/3
Epoch 7/50
当前学习率: 0.000200
使用类别权重: 正常=1.0, 早期圆锥角膜=5.045557349137128, 圆锥角膜=5.403546817303161
使用增强版对比损失函数，温度: 0.02509940375435208, 硬负样本挖掘比例: 0.45700162851906456, 早期圆锥角膜与正常样本对比权重: 1.1157319874532652, 圆锥角膜与正常样本对比权重: 1.0
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
警告: 没有足够的有效类别进行3-way任务采样，尝试调整参数...
类别样本数量: {0: 33, 1: 5, 2: 29}
调整后的参数: n_shot=2, n_query=3
类别 normal (标签 0): 33个样本
类别 e-kc (标签 1): 5个样本
类别 kc (标签 2): 29个样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
Epoch 7: Train Loss: 1.8336, Train Acc: 0.8156, Val Loss: 2.2505, Val Acc: 0.4889
验证准确率未提高，当前耐心值: 2/3
Epoch 8/50
当前学习率: 0.000200
使用类别权重: 正常=1.0, 早期圆锥角膜=5.045557349137128, 圆锥角膜=5.403546817303161
使用增强版对比损失函数，温度: 0.02509940375435208, 硬负样本挖掘比例: 0.45700162851906456, 早期圆锥角膜与正常样本对比权重: 1.1157319874532652, 圆锥角膜与正常样本对比权重: 1.0
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 18 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
警告: 没有足够的有效类别进行3-way任务采样，尝试调整参数...
类别样本数量: {0: 33, 1: 5, 2: 29}
调整后的参数: n_shot=2, n_query=3
类别 normal (标签 0): 33个样本
类别 e-kc (标签 1): 5个样本
类别 kc (标签 2): 29个样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
Epoch 8: Train Loss: 1.6489, Train Acc: 0.8252, Val Loss: 2.2276, Val Acc: 0.5333
验证准确率未提高，当前耐心值: 3/3
早停触发，停止训练
警告: 没有足够的有效类别进行3-way任务采样，尝试调整参数...
类别样本数量: {0: 37, 1: 7, 2: 27}
调整后的参数: n_shot=3, n_query=4
类别 normal (标签 0): 37个样本
类别 e-kc (标签 1): 7个样本
类别 kc (标签 2): 27个样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本

测试结果:
总体准确率: 85.83%
类别准确率:
  kc: 60.83%
  e-kc: 100.00%
  normal: 96.67%
训练完成，结果已保存到 /home/<USER>/balanced_task_sampling/results_trial_20250525_003524

STDERR:
+ TIMESTAMP=20250525_003524
+ RESULT_DIR=/home/<USER>/balanced_task_sampling/results_trial_20250525_003524
+ '[' -n /home/<USER>/balanced_task_sampling/results_trial_20250525_003524/main_training_trial_27_20250525_003524.log ']'
+ LOG_FILE=/home/<USER>/balanced_task_sampling/results_trial_20250525_003524/main_training_trial_27_20250525_003524.log
++ dirname /home/<USER>/balanced_task_sampling/results_trial_20250525_003524/main_training_trial_27_20250525_003524.log
+ mkdir -p /home/<USER>/balanced_task_sampling/results_trial_20250525_003524
+ mkdir -p /home/<USER>/balanced_task_sampling/results_trial_20250525_003524
+ echo '===================== 训练配置 ====================='
+ echo 'GPU: 0'
+ echo '输出目录: /home/<USER>/balanced_task_sampling/results_trial_20250525_003524'
+ echo '日志文件: /home/<USER>/balanced_task_sampling/results_trial_20250525_003524/main_training_trial_27_20250525_003524.log'
+ tee -a /home/<USER>/balanced_task_sampling/results_trial_20250525_003524/main_training_trial_27_20250525_003524.log
+ echo ''
+ echo '--------- 优化参数 ---------'
+ echo '内部学习率: 0.347389'
+ echo '训练轮数: 50'
+ echo '每轮任务数: 30'
+ echo ''
+ echo '--------- 模型参数 ---------'
+ echo '使用类别权重: 圆锥角膜=5.403546817303161, 早期圆锥角膜=5.045557349137128'
+ echo '温度参数: 0.02509940375435208'
+ echo '硬样本挖掘比例: 0.45700162851906456'
+ echo '对比学习权重: 0.5247860058859002'
+ echo '=============================================
'
+ validate_params
+ local error_count=0
++ dirname /home/<USER>/balanced_task_sampling/results_trial_20250525_003524/main_training_trial_27_20250525_003524.log
++ dirname /home/<USER>/balanced_task_sampling/split_result/train_set.csv
+ for dir in "$RESULT_DIR" "$(dirname "$LOG_FILE")" "$(dirname "$TRAIN_CSV")"
+ mkdir -p /home/<USER>/balanced_task_sampling/results_trial_20250525_003524
+ for dir in "$RESULT_DIR" "$(dirname "$LOG_FILE")" "$(dirname "$TRAIN_CSV")"
+ mkdir -p /home/<USER>/balanced_task_sampling/results_trial_20250525_003524
+ for dir in "$RESULT_DIR" "$(dirname "$LOG_FILE")" "$(dirname "$TRAIN_CSV")"
+ mkdir -p /home/<USER>/balanced_task_sampling/split_result
+ for csv in "$TRAIN_CSV" "$VAL_CSV" "$TEST_CSV"
+ '[' '!' -f /home/<USER>/balanced_task_sampling/split_result/train_set.csv ']'
+ for csv in "$TRAIN_CSV" "$VAL_CSV" "$TEST_CSV"
+ '[' '!' -f /home/<USER>/balanced_task_sampling/split_result/val_set.csv ']'
+ for csv in "$TRAIN_CSV" "$VAL_CSV" "$TEST_CSV"
+ '[' '!' -f /home/<USER>/balanced_task_sampling/split_result/test_set.csv ']'
+ [[ 0.0002 =~ ^[0-9.]+$ ]]
+ [[ 0.347389 =~ ^[0-9.]+$ ]]
+ '[' 0 -gt 0 ']'
+ return 0
+ '[' false = true ']'
+ export CUDA_VISIBLE_DEVICES=0
+ CUDA_VISIBLE_DEVICES=0
+ export OMP_NUM_THREADS=1
+ OMP_NUM_THREADS=1
+ export MKL_NUM_THREADS=1
+ MKL_NUM_THREADS=1
+ export MPLBACKEND=Agg
+ MPLBACKEND=Agg
+ mkdir -p /home/<USER>/balanced_task_sampling/results_trial_20250525_003524
+ echo '创建目录: /home/<USER>/balanced_task_sampling/results_trial_20250525_003524'
+ ls -l /home/<USER>/balanced_task_sampling/results_trial_20250525_003524
+ PRETRAINED_ARGS=
+ '[' true = true ']'
+ PRETRAINED_ARGS=--pretrained
+ AUGMENT_ARGS=--no_augmentation
+ CONTRASTIVE_ARGS=
+ '[' true = true ']'
+ CONTRASTIVE_ARGS='--use_contrastive --contrastive_weight 0.5247860058859002 --temperature 0.02509940375435208 --hard_mining_ratio 0.45700162851906456 --early_normal_weight 1.1157319874532652 --kc_normal_weight 1.0'
+ NORMALIZE_ARGS=
+ '[' false = true ']'
+ SAMPLER_ARGS=
+ '[' true = true ']'
+ SAMPLER_ARGS=' --use_separate_test_sampler'
+ '[' true = true ']'
+ SAMPLER_ARGS=' --use_separate_test_sampler --use_balanced_task_sampler --kc_shot_multiplier 3.797512761692582 --early_kc_shot_multiplier 1.7208333805613525'
+ LR_SCHEDULER_ARGS=
+ '[' true = true ']'
+ LR_SCHEDULER_ARGS='--use_lr_scheduler --scheduler_type plateau'
+ '[' plateau = onecycle ']'
+ '[' plateau = plateau ']'
+ LR_SCHEDULER_ARGS='--use_lr_scheduler --scheduler_type plateau --plateau_factor 0.5 --plateau_patience 2 --min_lr 1e-6'
+ BASE_CMD='python train_balanced_task_sampling.py   --train_csv /home/<USER>/balanced_task_sampling/split_result/train_set.csv   --val_csv /home/<USER>/balanced_task_sampling/split_result/val_set.csv   --test_csv /home/<USER>/balanced_task_sampling/split_result/test_set.csv   --epochs 50   --lr 0.0002   --weight_decay 5e-4   --feature_dim 512   --save_dir /home/<USER>/balanced_task_sampling/results_trial_20250525_003524   --proto_counts 2,5,4   --pretrained   --inner_lr 0.347389   --inner_steps 5   --n_way 3   --n_shot 5   --n_query 15   --tasks_per_epoch 30   --meta_batch_size 8   --no_augmentation   --mixup_alpha 0.0   --cutmix_prob 0.0   --num_workers 4   --augment_factor 1   --kc_augment_factor 1   --seed 42   --early_kc_weight 5.045557349137128   --kc_weight 5.403546817303161   --focal_gamma 1.174251162185004   --val_frequency 1   --early_stopping 3   --dropout 0.5    --use_separate_test_sampler --use_balanced_task_sampler --kc_shot_multiplier 3.797512761692582 --early_kc_shot_multiplier 1.7208333805613525   --use_contrastive --contrastive_weight 0.5247860058859002 --temperature 0.02509940375435208 --hard_mining_ratio 0.45700162851906456 --early_normal_weight 1.1157319874532652 --kc_normal_weight 1.0      --use_lr_scheduler --scheduler_type plateau --plateau_factor 0.5 --plateau_patience 2 --min_lr 1e-6'
+ echo 开始无数据增强的平衡任务采样训练...
+ echo '使用GPU: 0'
+ '[' direct = nohup ']'
+ echo 直接运行训练脚本...
+ eval python train_balanced_task_sampling.py --train_csv /home/<USER>/balanced_task_sampling/split_result/train_set.csv --val_csv /home/<USER>/balanced_task_sampling/split_result/val_set.csv --test_csv /home/<USER>/balanced_task_sampling/split_result/test_set.csv --epochs 50 --lr 0.0002 --weight_decay 5e-4 --feature_dim 512 --save_dir /home/<USER>/balanced_task_sampling/results_trial_20250525_003524 --proto_counts 2,5,4 --pretrained --inner_lr 0.347389 --inner_steps 5 --n_way 3 --n_shot 5 --n_query 15 --tasks_per_epoch 30 --meta_batch_size 8 --no_augmentation --mixup_alpha 0.0 --cutmix_prob 0.0 --num_workers 4 --augment_factor 1 --kc_augment_factor 1 --seed 42 --early_kc_weight 5.045557349137128 --kc_weight 5.403546817303161 --focal_gamma 1.174251162185004 --val_frequency 1 --early_stopping 3 --dropout 0.5 --use_separate_test_sampler --use_balanced_task_sampler --kc_shot_multiplier 3.797512761692582 --early_kc_shot_multiplier 1.7208333805613525 --use_contrastive --contrastive_weight 0.5247860058859002 --temperature 0.02509940375435208 --hard_mining_ratio 0.45700162851906456 --early_normal_weight 1.1157319874532652 --kc_normal_weight 1.0 --use_lr_scheduler --scheduler_type plateau --plateau_factor 0.5 --plateau_patience 2 --min_lr 1e-6
++ python train_balanced_task_sampling.py --train_csv /home/<USER>/balanced_task_sampling/split_result/train_set.csv --val_csv /home/<USER>/balanced_task_sampling/split_result/val_set.csv --test_csv /home/<USER>/balanced_task_sampling/split_result/test_set.csv --epochs 50 --lr 0.0002 --weight_decay 5e-4 --feature_dim 512 --save_dir /home/<USER>/balanced_task_sampling/results_trial_20250525_003524 --proto_counts 2,5,4 --pretrained --inner_lr 0.347389 --inner_steps 5 --n_way 3 --n_shot 5 --n_query 15 --tasks_per_epoch 30 --meta_batch_size 8 --no_augmentation --mixup_alpha 0.0 --cutmix_prob 0.0 --num_workers 4 --augment_factor 1 --kc_augment_factor 1 --seed 42 --early_kc_weight 5.045557349137128 --kc_weight 5.403546817303161 --focal_gamma 1.174251162185004 --val_frequency 1 --early_stopping 3 --dropout 0.5 --use_separate_test_sampler --use_balanced_task_sampler --kc_shot_multiplier 3.797512761692582 --early_kc_shot_multiplier 1.7208333805613525 --use_contrastive --contrastive_weight 0.5247860058859002 --temperature 0.02509940375435208 --hard_mining_ratio 0.45700162851906456 --early_normal_weight 1.1157319874532652 --kc_normal_weight 1.0 --use_lr_scheduler --scheduler_type plateau --plateau_factor 0.5 --plateau_patience 2 --min_lr 1e-6
/home/<USER>/anaconda3/envs/kc1/lib/python3.10/site-packages/torchvision/models/_utils.py:208: UserWarning: The parameter 'pretrained' is deprecated since 0.13 and may be removed in the future, please use 'weights' instead.
  warnings.warn(
/home/<USER>/anaconda3/envs/kc1/lib/python3.10/site-packages/torchvision/models/_utils.py:223: UserWarning: Arguments other than a weight enum or `None` for 'weights' are deprecated since 0.13 and may be removed in the future. The current behavior is equivalent to passing `weights=ResNet34_Weights.IMAGENET1K_V1`. You can also use `weights=ResNet34_Weights.DEFAULT` to get the most up-to-date weights.
  warnings.warn(msg)
/home/<USER>/anaconda3/envs/kc1/lib/python3.10/site-packages/torch/optim/lr_scheduler.py:62: UserWarning: The verbose parameter is deprecated. Please use get_last_lr() to access the learning rate.
  warnings.warn(

Training tasks:   0%|          | 0/30 [00:00<?, ?it/s]
Training tasks:   3%|▎         | 1/30 [00:06<02:56,  6.07s/it]
Training tasks:   7%|▋         | 2/30 [00:07<01:34,  3.39s/it]
Training tasks:  10%|█         | 3/30 [00:09<01:07,  2.51s/it]
Training tasks:  13%|█▎        | 4/30 [00:10<00:56,  2.16s/it]
Training tasks:  17%|█▋        | 5/30 [00:12<00:50,  2.02s/it]
Training tasks:  20%|██        | 6/30 [00:14<00:46,  1.93s/it]
Training tasks:  23%|██▎       | 7/30 [00:15<00:42,  1.83s/it]
Training tasks:  27%|██▋       | 8/30 [00:17<00:39,  1.79s/it]
Training tasks:  30%|███       | 9/30 [00:19<00:37,  1.78s/it]
Training tasks:  33%|███▎      | 10/30 [00:21<00:35,  1.76s/it]
Training tasks:  37%|███▋      | 11/30 [00:22<00:33,  1.79s/it]
Training tasks:  40%|████      | 12/30 [00:24<00:32,  1.79s/it]
Training tasks:  43%|████▎     | 13/30 [00:26<00:30,  1.77s/it]
Training tasks:  47%|████▋     | 14/30 [00:28<00:27,  1.75s/it]
Training tasks:  50%|█████     | 15/30 [00:29<00:25,  1.73s/it]
Training tasks:  53%|█████▎    | 16/30 [00:31<00:25,  1.79s/it]
Training tasks:  57%|█████▋    | 17/30 [00:33<00:23,  1.79s/it]
Training tasks:  60%|██████    | 18/30 [00:35<00:21,  1.76s/it]
Training tasks:  63%|██████▎   | 19/30 [00:37<00:19,  1.78s/it]
Training tasks:  67%|██████▋   | 20/30 [00:38<00:18,  1.80s/it]
Training tasks:  70%|███████   | 21/30 [00:40<00:16,  1.80s/it]
Training tasks:  73%|███████▎  | 22/30 [00:42<00:14,  1.79s/it]
Training tasks:  77%|███████▋  | 23/30 [00:44<00:12,  1.84s/it]
Training tasks:  80%|████████  | 24/30 [00:46<00:11,  1.87s/it]
Training tasks:  83%|████████▎ | 25/30 [00:47<00:08,  1.77s/it]
Training tasks:  87%|████████▋ | 26/30 [00:49<00:07,  1.76s/it]
Training tasks:  90%|█████████ | 27/30 [00:51<00:05,  1.78s/it]
Training tasks:  93%|█████████▎| 28/30 [00:53<00:03,  1.80s/it]
Training tasks:  97%|█████████▋| 29/30 [00:54<00:01,  1.76s/it]
Training tasks: 100%|██████████| 30/30 [00:56<00:00,  1.80s/it]
Training tasks: 100%|██████████| 30/30 [00:56<00:00,  1.89s/it]

Validating tasks:   0%|          | 0/10 [00:00<?, ?it/s]
Validating tasks:  10%|█         | 1/10 [00:00<00:02,  3.27it/s]
Validating tasks:  20%|██        | 2/10 [00:00<00:02,  3.59it/s]
Validating tasks:  30%|███       | 3/10 [00:00<00:01,  3.99it/s]
Validating tasks:  40%|████      | 4/10 [00:01<00:01,  4.09it/s]
Validating tasks:  50%|█████     | 5/10 [00:01<00:01,  4.25it/s]
Validating tasks:  60%|██████    | 6/10 [00:01<00:00,  4.26it/s]
Validating tasks:  70%|███████   | 7/10 [00:01<00:00,  4.37it/s]
Validating tasks:  80%|████████  | 8/10 [00:01<00:00,  4.31it/s]
Validating tasks:  90%|█████████ | 9/10 [00:02<00:00,  4.33it/s]
Validating tasks: 100%|██████████| 10/10 [00:02<00:00,  4.18it/s]
Validating tasks: 100%|██████████| 10/10 [00:02<00:00,  4.15it/s]

Training tasks:   0%|          | 0/30 [00:00<?, ?it/s]
Training tasks:   3%|▎         | 1/30 [00:01<00:49,  1.70s/it]
Training tasks:   7%|▋         | 2/30 [00:03<00:47,  1.70s/it]
Training tasks:  10%|█         | 3/30 [00:05<00:49,  1.83s/it]
Training tasks:  13%|█▎        | 4/30 [00:07<00:48,  1.86s/it]
Training tasks:  17%|█▋        | 5/30 [00:09<00:48,  1.92s/it]
Training tasks:  20%|██        | 6/30 [00:11<00:45,  1.88s/it]
Training tasks:  23%|██▎       | 7/30 [00:12<00:42,  1.85s/it]
Training tasks:  27%|██▋       | 8/30 [00:14<00:40,  1.83s/it]
Training tasks:  30%|███       | 9/30 [00:16<00:39,  1.89s/it]
Training tasks:  33%|███▎      | 10/30 [00:18<00:38,  1.91s/it]
Training tasks:  37%|███▋      | 11/30 [00:20<00:35,  1.85s/it]
Training tasks:  40%|████      | 12/30 [00:22<00:33,  1.83s/it]
Training tasks:  43%|████▎     | 13/30 [00:24<00:31,  1.84s/it]
Training tasks:  47%|████▋     | 14/30 [00:25<00:29,  1.82s/it]
Training tasks:  50%|█████     | 15/30 [00:27<00:25,  1.71s/it]
Training tasks:  53%|█████▎    | 16/30 [00:28<00:24,  1.72s/it]
Training tasks:  57%|█████▋    | 17/30 [00:30<00:23,  1.79s/it]
Training tasks:  60%|██████    | 18/30 [00:32<00:22,  1.85s/it]
Training tasks:  63%|██████▎   | 19/30 [00:34<00:20,  1.89s/it]
Training tasks:  67%|██████▋   | 20/30 [00:36<00:19,  1.91s/it]
Training tasks:  70%|███████   | 21/30 [00:38<00:16,  1.87s/it]
Training tasks:  73%|███████▎  | 22/30 [00:40<00:14,  1.85s/it]
Training tasks:  77%|███████▋  | 23/30 [00:42<00:12,  1.85s/it]
Training tasks:  80%|████████  | 24/30 [00:43<00:10,  1.78s/it]
Training tasks:  83%|████████▎ | 25/30 [00:45<00:08,  1.78s/it]
Training tasks:  87%|████████▋ | 26/30 [00:47<00:07,  1.78s/it]
Training tasks:  90%|█████████ | 27/30 [00:49<00:05,  1.78s/it]
Training tasks:  93%|█████████▎| 28/30 [00:51<00:03,  1.81s/it]
Training tasks:  97%|█████████▋| 29/30 [00:53<00:01,  1.86s/it]
Training tasks: 100%|██████████| 30/30 [00:54<00:00,  1.84s/it]
Training tasks: 100%|██████████| 30/30 [00:54<00:00,  1.83s/it]

Validating tasks:   0%|          | 0/10 [00:00<?, ?it/s]
Validating tasks:  10%|█         | 1/10 [00:00<00:02,  3.65it/s]
Validating tasks:  20%|██        | 2/10 [00:00<00:02,  3.17it/s]
Validating tasks:  30%|███       | 3/10 [00:00<00:02,  3.28it/s]
Validating tasks:  40%|████      | 4/10 [00:01<00:01,  3.45it/s]
Validating tasks:  50%|█████     | 5/10 [00:01<00:01,  3.53it/s]
Validating tasks:  60%|██████    | 6/10 [00:01<00:01,  3.54it/s]
Validating tasks:  70%|███████   | 7/10 [00:02<00:00,  3.59it/s]
Validating tasks:  80%|████████  | 8/10 [00:02<00:00,  3.59it/s]
Validating tasks:  90%|█████████ | 9/10 [00:02<00:00,  3.74it/s]
Validating tasks: 100%|██████████| 10/10 [00:02<00:00,  3.64it/s]
Validating tasks: 100%|██████████| 10/10 [00:02<00:00,  3.55it/s]

Training tasks:   0%|          | 0/30 [00:00<?, ?it/s]
Training tasks:   3%|▎         | 1/30 [00:01<00:49,  1.72s/it]
Training tasks:   7%|▋         | 2/30 [00:03<00:47,  1.71s/it]
Training tasks:  10%|█         | 3/30 [00:04<00:43,  1.61s/it]
Training tasks:  13%|█▎        | 4/30 [00:06<00:44,  1.71s/it]
Training tasks:  17%|█▋        | 5/30 [00:08<00:45,  1.81s/it]
Training tasks:  20%|██        | 6/30 [00:10<00:42,  1.78s/it]
Training tasks:  23%|██▎       | 7/30 [00:12<00:40,  1.75s/it]
Training tasks:  27%|██▋       | 8/30 [00:13<00:36,  1.66s/it]
Training tasks:  30%|███       | 9/30 [00:15<00:36,  1.72s/it]
Training tasks:  33%|███▎      | 10/30 [00:17<00:33,  1.69s/it]
Training tasks:  37%|███▋      | 11/30 [00:18<00:31,  1.64s/it]
Training tasks:  40%|████      | 12/30 [00:20<00:28,  1.61s/it]
Training tasks:  43%|████▎     | 13/30 [00:22<00:28,  1.69s/it]
Training tasks:  47%|████▋     | 14/30 [00:23<00:27,  1.73s/it]
Training tasks:  50%|█████     | 15/30 [00:25<00:26,  1.74s/it]
Training tasks:  53%|█████▎    | 16/30 [00:27<00:24,  1.76s/it]
Training tasks:  57%|█████▋    | 17/30 [00:29<00:22,  1.70s/it]
Training tasks:  60%|██████    | 18/30 [00:30<00:21,  1.75s/it]
Training tasks:  63%|██████▎   | 19/30 [00:32<00:19,  1.76s/it]
Training tasks:  67%|██████▋   | 20/30 [00:34<00:18,  1.81s/it]
Training tasks:  70%|███████   | 21/30 [00:36<00:16,  1.83s/it]
Training tasks:  73%|███████▎  | 22/30 [00:38<00:14,  1.85s/it]
Training tasks:  77%|███████▋  | 23/30 [00:40<00:12,  1.81s/it]
Training tasks:  80%|████████  | 24/30 [00:41<00:10,  1.67s/it]
Training tasks:  83%|████████▎ | 25/30 [00:43<00:08,  1.73s/it]
Training tasks:  87%|████████▋ | 26/30 [00:45<00:06,  1.73s/it]
Training tasks:  90%|█████████ | 27/30 [00:46<00:05,  1.72s/it]
Training tasks:  93%|█████████▎| 28/30 [00:48<00:03,  1.64s/it]
Training tasks:  97%|█████████▋| 29/30 [00:49<00:01,  1.66s/it]
Training tasks: 100%|██████████| 30/30 [00:51<00:00,  1.65s/it]
Training tasks: 100%|██████████| 30/30 [00:51<00:00,  1.72s/it]

Validating tasks:   0%|          | 0/10 [00:00<?, ?it/s]
Validating tasks:  10%|█         | 1/10 [00:00<00:01,  4.62it/s]
Validating tasks:  20%|██        | 2/10 [00:00<00:01,  4.38it/s]
Validating tasks:  30%|███       | 3/10 [00:00<00:01,  4.43it/s]
Validating tasks:  40%|████      | 4/10 [00:00<00:01,  4.44it/s]
Validating tasks:  50%|█████     | 5/10 [00:01<00:01,  4.48it/s]
Validating tasks:  60%|██████    | 6/10 [00:01<00:00,  4.09it/s]
Validating tasks:  70%|███████   | 7/10 [00:01<00:00,  4.20it/s]
Validating tasks:  80%|████████  | 8/10 [00:01<00:00,  4.50it/s]
Validating tasks:  90%|█████████ | 9/10 [00:02<00:00,  4.53it/s]
Validating tasks: 100%|██████████| 10/10 [00:02<00:00,  3.98it/s]
Validating tasks: 100%|██████████| 10/10 [00:02<00:00,  4.24it/s]

Training tasks:   0%|          | 0/30 [00:00<?, ?it/s]
Training tasks:   3%|▎         | 1/30 [00:01<00:54,  1.86s/it]
Training tasks:   7%|▋         | 2/30 [00:03<00:49,  1.78s/it]
Training tasks:  10%|█         | 3/30 [00:05<00:45,  1.70s/it]
Training tasks:  13%|█▎        | 4/30 [00:06<00:44,  1.72s/it]
Training tasks:  17%|█▋        | 5/30 [00:08<00:43,  1.74s/it]
Training tasks:  20%|██        | 6/30 [00:10<00:41,  1.73s/it]
Training tasks:  23%|██▎       | 7/30 [00:11<00:38,  1.67s/it]
Training tasks:  27%|██▋       | 8/30 [00:13<00:37,  1.69s/it]
Training tasks:  30%|███       | 9/30 [00:15<00:36,  1.74s/it]
Training tasks:  33%|███▎      | 10/30 [00:17<00:34,  1.73s/it]
Training tasks:  37%|███▋      | 11/30 [00:18<00:31,  1.65s/it]
Training tasks:  40%|████      | 12/30 [00:19<00:27,  1.52s/it]
Training tasks:  43%|████▎     | 13/30 [00:21<00:27,  1.64s/it]
Training tasks:  47%|████▋     | 14/30 [00:23<00:26,  1.65s/it]
Training tasks:  50%|█████     | 15/30 [00:25<00:25,  1.68s/it]
Training tasks:  53%|█████▎    | 16/30 [00:26<00:23,  1.67s/it]
Training tasks:  57%|█████▋    | 17/30 [00:28<00:22,  1.70s/it]
Training tasks:  60%|██████    | 18/30 [00:30<00:20,  1.70s/it]
Training tasks:  63%|██████▎   | 19/30 [00:32<00:18,  1.67s/it]
Training tasks:  67%|██████▋   | 20/30 [00:33<00:15,  1.59s/it]
Training tasks:  70%|███████   | 21/30 [00:34<00:13,  1.48s/it]
Training tasks:  73%|███████▎  | 22/30 [00:36<00:12,  1.57s/it]
Training tasks:  77%|███████▋  | 23/30 [00:37<00:10,  1.56s/it]
Training tasks:  80%|████████  | 24/30 [00:39<00:09,  1.61s/it]
Training tasks:  83%|████████▎ | 25/30 [00:41<00:08,  1.66s/it]
Training tasks:  87%|████████▋ | 26/30 [00:43<00:06,  1.70s/it]
Training tasks:  90%|█████████ | 27/30 [00:44<00:04,  1.66s/it]
Training tasks:  93%|█████████▎| 28/30 [00:46<00:03,  1.63s/it]
Training tasks:  97%|█████████▋| 29/30 [00:48<00:01,  1.67s/it]
Training tasks: 100%|██████████| 30/30 [00:50<00:00,  1.82s/it]
Training tasks: 100%|██████████| 30/30 [00:50<00:00,  1.68s/it]

Validating tasks:   0%|          | 0/10 [00:00<?, ?it/s]
Validating tasks:  10%|█         | 1/10 [00:00<00:02,  4.21it/s]
Validating tasks:  20%|██        | 2/10 [00:00<00:01,  4.20it/s]
Validating tasks:  30%|███       | 3/10 [00:00<00:01,  3.92it/s]
Validating tasks:  40%|████      | 4/10 [00:01<00:01,  3.84it/s]
Validating tasks:  50%|█████     | 5/10 [00:01<00:01,  3.96it/s]
Validating tasks:  60%|██████    | 6/10 [00:01<00:00,  4.16it/s]
Validating tasks:  70%|███████   | 7/10 [00:01<00:00,  4.08it/s]
Validating tasks:  80%|████████  | 8/10 [00:01<00:00,  3.97it/s]
Validating tasks:  90%|█████████ | 9/10 [00:02<00:00,  3.71it/s]
Validating tasks: 100%|██████████| 10/10 [00:02<00:00,  3.60it/s]
Validating tasks: 100%|██████████| 10/10 [00:02<00:00,  3.85it/s]

Training tasks:   0%|          | 0/30 [00:00<?, ?it/s]
Training tasks:   3%|▎         | 1/30 [00:01<00:48,  1.67s/it]
Training tasks:   7%|▋         | 2/30 [00:03<00:48,  1.73s/it]
Training tasks:  10%|█         | 3/30 [00:05<00:48,  1.80s/it]
Training tasks:  13%|█▎        | 4/30 [00:07<00:45,  1.76s/it]
Training tasks:  17%|█▋        | 5/30 [00:08<00:45,  1.81s/it]
Training tasks:  20%|██        | 6/30 [00:10<00:41,  1.72s/it]
Training tasks:  23%|██▎       | 7/30 [00:11<00:37,  1.63s/it]
Training tasks:  27%|██▋       | 8/30 [00:13<00:35,  1.63s/it]
Training tasks:  30%|███       | 9/30 [00:15<00:34,  1.63s/it]
Training tasks:  33%|███▎      | 10/30 [00:16<00:31,  1.58s/it]
Training tasks:  37%|███▋      | 11/30 [00:18<00:30,  1.63s/it]
Training tasks:  40%|████      | 12/30 [00:20<00:30,  1.67s/it]
Training tasks:  43%|████▎     | 13/30 [00:21<00:28,  1.70s/it]
Training tasks:  47%|████▋     | 14/30 [00:23<00:26,  1.66s/it]
Training tasks:  50%|█████     | 15/30 [00:25<00:25,  1.67s/it]
Training tasks:  53%|█████▎    | 16/30 [00:26<00:23,  1.67s/it]
Training tasks:  57%|█████▋    | 17/30 [00:28<00:21,  1.68s/it]
Training tasks:  60%|██████    | 18/30 [00:30<00:20,  1.73s/it]
Training tasks:  63%|██████▎   | 19/30 [00:32<00:18,  1.71s/it]
Training tasks:  67%|██████▋   | 20/30 [00:33<00:17,  1.70s/it]
Training tasks:  70%|███████   | 21/30 [00:35<00:14,  1.63s/it]
Training tasks:  73%|███████▎  | 22/30 [00:36<00:12,  1.57s/it]
Training tasks:  77%|███████▋  | 23/30 [00:38<00:11,  1.59s/it]
Training tasks:  80%|████████  | 24/30 [00:39<00:09,  1.61s/it]
Training tasks:  83%|████████▎ | 25/30 [00:41<00:07,  1.58s/it]
Training tasks:  87%|████████▋ | 26/30 [00:42<00:06,  1.55s/it]
Training tasks:  90%|█████████ | 27/30 [00:44<00:04,  1.55s/it]
Training tasks:  93%|█████████▎| 28/30 [00:46<00:03,  1.59s/it]
Training tasks:  97%|█████████▋| 29/30 [00:47<00:01,  1.58s/it]
Training tasks: 100%|██████████| 30/30 [00:49<00:00,  1.64s/it]
Training tasks: 100%|██████████| 30/30 [00:49<00:00,  1.65s/it]

Validating tasks:   0%|          | 0/10 [00:00<?, ?it/s]
Validating tasks:  10%|█         | 1/10 [00:00<00:02,  3.94it/s]
Validating tasks:  20%|██        | 2/10 [00:00<00:01,  4.07it/s]
Validating tasks:  30%|███       | 3/10 [00:00<00:01,  4.05it/s]
Validating tasks:  40%|████      | 4/10 [00:01<00:01,  3.88it/s]
Validating tasks:  50%|█████     | 5/10 [00:01<00:01,  4.03it/s]
Validating tasks:  60%|██████    | 6/10 [00:01<00:00,  4.07it/s]
Validating tasks:  70%|███████   | 7/10 [00:01<00:00,  4.10it/s]
Validating tasks:  80%|████████  | 8/10 [00:01<00:00,  4.06it/s]
Validating tasks:  90%|█████████ | 9/10 [00:02<00:00,  4.03it/s]
Validating tasks: 100%|██████████| 10/10 [00:02<00:00,  3.97it/s]
Validating tasks: 100%|██████████| 10/10 [00:02<00:00,  4.01it/s]

Training tasks:   0%|          | 0/30 [00:00<?, ?it/s]
Training tasks:   3%|▎         | 1/30 [00:01<00:49,  1.70s/it]
Training tasks:   7%|▋         | 2/30 [00:03<00:50,  1.79s/it]
Training tasks:  10%|█         | 3/30 [00:05<00:50,  1.87s/it]
Training tasks:  13%|█▎        | 4/30 [00:07<00:47,  1.82s/it]
Training tasks:  17%|█▋        | 5/30 [00:08<00:44,  1.77s/it]
Training tasks:  20%|██        | 6/30 [00:10<00:42,  1.78s/it]
Training tasks:  23%|██▎       | 7/30 [00:12<00:40,  1.78s/it]
Training tasks:  27%|██▋       | 8/30 [00:14<00:38,  1.76s/it]
Training tasks:  30%|███       | 9/30 [00:15<00:34,  1.66s/it]
Training tasks:  33%|███▎      | 10/30 [00:17<00:33,  1.66s/it]
Training tasks:  37%|███▋      | 11/30 [00:19<00:32,  1.70s/it]
Training tasks:  40%|████      | 12/30 [00:20<00:30,  1.72s/it]
Training tasks:  43%|████▎     | 13/30 [00:22<00:29,  1.73s/it]
Training tasks:  47%|████▋     | 14/30 [00:24<00:27,  1.71s/it]
Training tasks:  50%|█████     | 15/30 [00:26<00:26,  1.74s/it]
Training tasks:  53%|█████▎    | 16/30 [00:28<00:25,  1.82s/it]
Training tasks:  57%|█████▋    | 17/30 [00:29<00:22,  1.73s/it]
Training tasks:  60%|██████    | 18/30 [00:31<00:19,  1.64s/it]
Training tasks:  63%|██████▎   | 19/30 [00:33<00:19,  1.74s/it]
Training tasks:  67%|██████▋   | 20/30 [00:34<00:17,  1.75s/it]
Training tasks:  70%|███████   | 21/30 [00:36<00:15,  1.72s/it]
Training tasks:  73%|███████▎  | 22/30 [00:38<00:13,  1.73s/it]
Training tasks:  77%|███████▋  | 23/30 [00:40<00:12,  1.78s/it]
Training tasks:  80%|████████  | 24/30 [00:41<00:10,  1.79s/it]
Training tasks:  83%|████████▎ | 25/30 [00:43<00:08,  1.76s/it]
Training tasks:  87%|████████▋ | 26/30 [00:45<00:06,  1.74s/it]
Training tasks:  90%|█████████ | 27/30 [00:47<00:05,  1.80s/it]
Training tasks:  93%|█████████▎| 28/30 [00:49<00:03,  1.83s/it]
Training tasks:  97%|█████████▋| 29/30 [00:50<00:01,  1.81s/it]
Training tasks: 100%|██████████| 30/30 [00:52<00:00,  1.70s/it]
Training tasks: 100%|██████████| 30/30 [00:52<00:00,  1.75s/it]

Validating tasks:   0%|          | 0/10 [00:00<?, ?it/s]
Validating tasks:  10%|█         | 1/10 [00:00<00:01,  5.01it/s]
Validating tasks:  20%|██        | 2/10 [00:00<00:01,  4.96it/s]
Validating tasks:  30%|███       | 3/10 [00:00<00:01,  4.79it/s]
Validating tasks:  40%|████      | 4/10 [00:00<00:01,  4.66it/s]
Validating tasks:  50%|█████     | 5/10 [00:01<00:01,  4.67it/s]
Validating tasks:  60%|██████    | 6/10 [00:01<00:00,  4.65it/s]
Validating tasks:  70%|███████   | 7/10 [00:01<00:00,  4.69it/s]
Validating tasks:  80%|████████  | 8/10 [00:01<00:00,  4.75it/s]
Validating tasks:  90%|█████████ | 9/10 [00:01<00:00,  4.77it/s]
Validating tasks: 100%|██████████| 10/10 [00:02<00:00,  4.68it/s]
Validating tasks: 100%|██████████| 10/10 [00:02<00:00,  4.72it/s]

Training tasks:   0%|          | 0/30 [00:00<?, ?it/s]
Training tasks:   3%|▎         | 1/30 [00:01<00:43,  1.50s/it]
Training tasks:   7%|▋         | 2/30 [00:03<00:46,  1.66s/it]
Training tasks:  10%|█         | 3/30 [00:04<00:44,  1.64s/it]
Training tasks:  13%|█▎        | 4/30 [00:06<00:44,  1.71s/it]
Training tasks:  17%|█▋        | 5/30 [00:08<00:44,  1.78s/it]
Training tasks:  20%|██        | 6/30 [00:10<00:44,  1.84s/it]
Training tasks:  23%|██▎       | 7/30 [00:12<00:40,  1.77s/it]
Training tasks:  27%|██▋       | 8/30 [00:13<00:38,  1.74s/it]
Training tasks:  30%|███       | 9/30 [00:15<00:36,  1.72s/it]
Training tasks:  33%|███▎      | 10/30 [00:17<00:35,  1.76s/it]
Training tasks:  37%|███▋      | 11/30 [00:19<00:32,  1.72s/it]
Training tasks:  40%|████      | 12/30 [00:20<00:31,  1.74s/it]
Training tasks:  43%|████▎     | 13/30 [00:22<00:28,  1.70s/it]
Training tasks:  47%|████▋     | 14/30 [00:24<00:26,  1.67s/it]
Training tasks:  50%|█████     | 15/30 [00:25<00:25,  1.68s/it]
Training tasks:  53%|█████▎    | 16/30 [00:27<00:24,  1.76s/it]
Training tasks:  57%|█████▋    | 17/30 [00:29<00:22,  1.75s/it]
Training tasks:  60%|██████    | 18/30 [00:31<00:21,  1.77s/it]
Training tasks:  63%|██████▎   | 19/30 [00:32<00:19,  1.73s/it]
Training tasks:  67%|██████▋   | 20/30 [00:34<00:16,  1.64s/it]
Training tasks:  70%|███████   | 21/30 [00:35<00:14,  1.63s/it]
Training tasks:  73%|███████▎  | 22/30 [00:37<00:13,  1.68s/it]
Training tasks:  77%|███████▋  | 23/30 [00:39<00:12,  1.75s/it]
Training tasks:  80%|████████  | 24/30 [00:41<00:10,  1.72s/it]
Training tasks:  83%|████████▎ | 25/30 [00:42<00:08,  1.65s/it]
Training tasks:  87%|████████▋ | 26/30 [00:44<00:06,  1.64s/it]
Training tasks:  90%|█████████ | 27/30 [00:46<00:05,  1.67s/it]
Training tasks:  93%|█████████▎| 28/30 [00:47<00:03,  1.64s/it]
Training tasks:  97%|█████████▋| 29/30 [00:49<00:01,  1.56s/it]
Training tasks: 100%|██████████| 30/30 [00:50<00:00,  1.50s/it]
Training tasks: 100%|██████████| 30/30 [00:50<00:00,  1.68s/it]

Validating tasks:   0%|          | 0/10 [00:00<?, ?it/s]
Validating tasks:  10%|█         | 1/10 [00:00<00:01,  4.97it/s]
Validating tasks:  20%|██        | 2/10 [00:00<00:01,  4.33it/s]
Validating tasks:  30%|███       | 3/10 [00:00<00:01,  4.08it/s]
Validating tasks:  40%|████      | 4/10 [00:00<00:01,  4.16it/s]
Validating tasks:  50%|█████     | 5/10 [00:01<00:01,  4.11it/s]
Validating tasks:  60%|██████    | 6/10 [00:01<00:00,  4.05it/s]
Validating tasks:  70%|███████   | 7/10 [00:01<00:00,  4.02it/s]
Validating tasks:  80%|████████  | 8/10 [00:01<00:00,  3.81it/s]
Validating tasks:  90%|█████████ | 9/10 [00:02<00:00,  3.76it/s]
Validating tasks: 100%|██████████| 10/10 [00:02<00:00,  3.83it/s]
Validating tasks: 100%|██████████| 10/10 [00:02<00:00,  3.97it/s]

Training tasks:   0%|          | 0/30 [00:00<?, ?it/s]
Training tasks:   3%|▎         | 1/30 [00:01<00:47,  1.62s/it]
Training tasks:   7%|▋         | 2/30 [00:03<00:48,  1.71s/it]
Training tasks:  10%|█         | 3/30 [00:05<00:47,  1.77s/it]
Training tasks:  13%|█▎        | 4/30 [00:07<00:46,  1.78s/it]
Training tasks:  17%|█▋        | 5/30 [00:08<00:44,  1.77s/it]
Training tasks:  20%|██        | 6/30 [00:10<00:43,  1.81s/it]
Training tasks:  23%|██▎       | 7/30 [00:12<00:41,  1.82s/it]
Training tasks:  27%|██▋       | 8/30 [00:14<00:39,  1.77s/it]
Training tasks:  30%|███       | 9/30 [00:16<00:38,  1.83s/it]
Training tasks:  33%|███▎      | 10/30 [00:17<00:34,  1.74s/it]
Training tasks:  37%|███▋      | 11/30 [00:19<00:30,  1.62s/it]
Training tasks:  40%|████      | 12/30 [00:20<00:29,  1.62s/it]
Training tasks:  43%|████▎     | 13/30 [00:22<00:26,  1.55s/it]
Training tasks:  47%|████▋     | 14/30 [00:23<00:24,  1.52s/it]
Training tasks:  50%|█████     | 15/30 [00:25<00:22,  1.52s/it]
Training tasks:  53%|█████▎    | 16/30 [00:26<00:21,  1.52s/it]
Training tasks:  57%|█████▋    | 17/30 [00:28<00:19,  1.52s/it]
Training tasks:  60%|██████    | 18/30 [00:29<00:18,  1.52s/it]
Training tasks:  63%|██████▎   | 19/30 [00:31<00:16,  1.49s/it]
Training tasks:  67%|██████▋   | 20/30 [00:32<00:14,  1.46s/it]
Training tasks:  70%|███████   | 21/30 [00:33<00:13,  1.48s/it]
Training tasks:  73%|███████▎  | 22/30 [00:35<00:12,  1.51s/it]
Training tasks:  77%|███████▋  | 23/30 [00:37<00:10,  1.56s/it]
Training tasks:  80%|████████  | 24/30 [00:38<00:09,  1.62s/it]
Training tasks:  83%|████████▎ | 25/30 [00:40<00:08,  1.66s/it]
Training tasks:  87%|████████▋ | 26/30 [00:42<00:06,  1.64s/it]
Training tasks:  90%|█████████ | 27/30 [00:43<00:04,  1.63s/it]
Training tasks:  93%|█████████▎| 28/30 [00:45<00:03,  1.63s/it]
Training tasks:  97%|█████████▋| 29/30 [00:47<00:01,  1.65s/it]
Training tasks: 100%|██████████| 30/30 [00:49<00:00,  1.69s/it]
Training tasks: 100%|██████████| 30/30 [00:49<00:00,  1.63s/it]

Validating tasks:   0%|          | 0/10 [00:00<?, ?it/s]
Validating tasks:  10%|█         | 1/10 [00:00<00:02,  3.64it/s]
Validating tasks:  20%|██        | 2/10 [00:00<00:02,  3.46it/s]
Validating tasks:  30%|███       | 3/10 [00:00<00:02,  3.37it/s]
Validating tasks:  40%|████      | 4/10 [00:01<00:01,  3.39it/s]
Validating tasks:  50%|█████     | 5/10 [00:01<00:01,  3.35it/s]
Validating tasks:  60%|██████    | 6/10 [00:01<00:01,  3.50it/s]
Validating tasks:  70%|███████   | 7/10 [00:01<00:00,  3.63it/s]
Validating tasks:  80%|████████  | 8/10 [00:02<00:00,  3.72it/s]
Validating tasks:  90%|█████████ | 9/10 [00:02<00:00,  3.95it/s]
Validating tasks: 100%|██████████| 10/10 [00:02<00:00,  4.01it/s]
Validating tasks: 100%|██████████| 10/10 [00:02<00:00,  3.70it/s]

Testing tasks:   0%|          | 0/30 [00:00<?, ?it/s]
Testing tasks:   3%|▎         | 1/30 [00:00<00:12,  2.34it/s]
Testing tasks:   7%|▋         | 2/30 [00:00<00:11,  2.49it/s]
Testing tasks:  10%|█         | 3/30 [00:01<00:10,  2.69it/s]
Testing tasks:  13%|█▎        | 4/30 [00:01<00:09,  2.72it/s]
Testing tasks:  17%|█▋        | 5/30 [00:01<00:09,  2.73it/s]
Testing tasks:  20%|██        | 6/30 [00:02<00:08,  2.68it/s]
Testing tasks:  23%|██▎       | 7/30 [00:02<00:08,  2.67it/s]
Testing tasks:  27%|██▋       | 8/30 [00:02<00:08,  2.71it/s]
Testing tasks:  30%|███       | 9/30 [00:03<00:07,  2.70it/s]
Testing tasks:  33%|███▎      | 10/30 [00:03<00:07,  2.73it/s]
Testing tasks:  37%|███▋      | 11/30 [00:04<00:06,  2.76it/s]
Testing tasks:  40%|████      | 12/30 [00:04<00:06,  2.85it/s]
Testing tasks:  43%|████▎     | 13/30 [00:04<00:05,  2.96it/s]
Testing tasks:  47%|████▋     | 14/30 [00:05<00:05,  2.77it/s]
Testing tasks:  50%|█████     | 15/30 [00:05<00:05,  2.56it/s]
Testing tasks:  53%|█████▎    | 16/30 [00:05<00:05,  2.57it/s]
Testing tasks:  57%|█████▋    | 17/30 [00:06<00:04,  2.60it/s]
Testing tasks:  60%|██████    | 18/30 [00:06<00:04,  2.66it/s]
Testing tasks:  63%|██████▎   | 19/30 [00:07<00:04,  2.74it/s]
Testing tasks:  67%|██████▋   | 20/30 [00:07<00:03,  2.89it/s]
Testing tasks:  70%|███████   | 21/30 [00:07<00:02,  3.02it/s]
Testing tasks:  73%|███████▎  | 22/30 [00:07<00:02,  3.10it/s]
Testing tasks:  77%|███████▋  | 23/30 [00:08<00:02,  3.05it/s]
Testing tasks:  80%|████████  | 24/30 [00:08<00:02,  2.88it/s]
Testing tasks:  83%|████████▎ | 25/30 [00:09<00:01,  2.92it/s]
Testing tasks:  87%|████████▋ | 26/30 [00:09<00:01,  2.91it/s]
Testing tasks:  90%|█████████ | 27/30 [00:09<00:01,  2.85it/s]
Testing tasks:  93%|█████████▎| 28/30 [00:10<00:00,  2.71it/s]
Testing tasks:  97%|█████████▋| 29/30 [00:10<00:00,  2.61it/s]
Testing tasks: 100%|██████████| 30/30 [00:10<00:00,  2.81it/s]
Testing tasks: 100%|██████████| 30/30 [00:10<00:00,  2.77it/s]

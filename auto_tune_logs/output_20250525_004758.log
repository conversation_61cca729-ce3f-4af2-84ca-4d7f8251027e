STDOUT:
===================== 训练配置 =====================
GPU: 0
输出目录: /home/<USER>/balanced_task_sampling/results_trial_20250525_004758
日志文件: /home/<USER>/balanced_task_sampling/results_trial_20250525_004758/main_training_trial_29_20250525_004758.log

--------- 优化参数 ---------
内部学习率: 0.347389
训练轮数: 50
每轮任务数: 30

--------- 模型参数 ---------
使用类别权重: 圆锥角膜=6.327520041382975, 早期圆锥角膜=6.231727555394775
温度参数: 0.09561272008326499
硬样本挖掘比例: 0.5424541611462215
对比学习权重: 0.3347902507554248
=============================================

创建目录: /home/<USER>/balanced_task_sampling/results_trial_20250525_004758
总用量 4
-rw-rw-r-- 1 <USER> <GROUP> 657 5月  25 00:47 main_training_trial_29_20250525_004758.log
开始无数据增强的平衡任务采样训练...
使用GPU: 0
直接运行训练脚本...
成功导入增强版特征提取器
成功导入归一化特征提取器
成功导入增强版损失函数
成功导入增强版MAML模型
成功导入KC特定增强模块
成功导入增强版GPU数据增强模块
使用设备: cuda
禁用所有数据增强，直接加载原始数据集...
使用平衡任务采样器
类别 normal (标签 0): 164个样本
类别 e-kc (标签 1): 26个样本
类别 kc (标签 2): 133个样本
使用原型配置: [4, 6, 3]
使用增强版特征提取器，dropout率: 0.5
使用增强版MAML-ProtoNet模型，内循环学习率: 0.347389, 内循环步数: 5
使用ReduceLROnPlateau学习率调度器，因子: 0.5, 耐心值: 2
开始训练，共50个epoch
Epoch 1/50
当前学习率: 0.000200
使用类别权重: 正常=1.0, 早期圆锥角膜=6.231727555394775, 圆锥角膜=6.327520041382975
使用增强版对比损失函数，温度: 0.09561272008326499, 硬负样本挖掘比例: 0.5424541611462215, 早期圆锥角膜与正常样本对比权重: 1.7843428622955229, 圆锥角膜与正常样本对比权重: 1.0
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
警告: 没有足够的有效类别进行3-way任务采样，尝试调整参数...
类别样本数量: {0: 33, 1: 5, 2: 29}
调整后的参数: n_shot=2, n_query=3
类别 normal (标签 0): 33个样本
类别 e-kc (标签 1): 5个样本
类别 kc (标签 2): 29个样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
Epoch 1: Train Loss: 1.9355, Train Acc: 0.7185, Val Loss: 2.0020, Val Acc: 0.5889
保存最佳模型，验证准确率: 0.5889
Epoch 2/50
当前学习率: 0.000200
使用类别权重: 正常=1.0, 早期圆锥角膜=6.231727555394775, 圆锥角膜=6.327520041382975
使用增强版对比损失函数，温度: 0.09561272008326499, 硬负样本挖掘比例: 0.5424541611462215, 早期圆锥角膜与正常样本对比权重: 1.7843428622955229, 圆锥角膜与正常样本对比权重: 1.0
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
警告: 没有足够的有效类别进行3-way任务采样，尝试调整参数...
类别样本数量: {0: 33, 1: 5, 2: 29}
调整后的参数: n_shot=2, n_query=3
类别 normal (标签 0): 33个样本
类别 e-kc (标签 1): 5个样本
类别 kc (标签 2): 29个样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
Epoch 2: Train Loss: 1.3442, Train Acc: 0.8904, Val Loss: 2.0205, Val Acc: 0.7000
保存最佳模型，验证准确率: 0.7000
Epoch 3/50
当前学习率: 0.000200
使用类别权重: 正常=1.0, 早期圆锥角膜=6.231727555394775, 圆锥角膜=6.327520041382975
使用增强版对比损失函数，温度: 0.09561272008326499, 硬负样本挖掘比例: 0.5424541611462215, 早期圆锥角膜与正常样本对比权重: 1.7843428622955229, 圆锥角膜与正常样本对比权重: 1.0
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
警告: 没有足够的有效类别进行3-way任务采样，尝试调整参数...
类别样本数量: {0: 33, 1: 5, 2: 29}
调整后的参数: n_shot=2, n_query=3
类别 normal (标签 0): 33个样本
类别 e-kc (标签 1): 5个样本
类别 kc (标签 2): 29个样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
Epoch 3: Train Loss: 1.1332, Train Acc: 0.9504, Val Loss: 2.0688, Val Acc: 0.6111
验证准确率未提高，当前耐心值: 1/3
Epoch 4/50
当前学习率: 0.000200
使用类别权重: 正常=1.0, 早期圆锥角膜=6.231727555394775, 圆锥角膜=6.327520041382975
使用增强版对比损失函数，温度: 0.09561272008326499, 硬负样本挖掘比例: 0.5424541611462215, 早期圆锥角膜与正常样本对比权重: 1.7843428622955229, 圆锥角膜与正常样本对比权重: 1.0
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
警告: 没有足够的有效类别进行3-way任务采样，尝试调整参数...
类别样本数量: {0: 33, 1: 5, 2: 29}
调整后的参数: n_shot=2, n_query=3
类别 normal (标签 0): 33个样本
类别 e-kc (标签 1): 5个样本
类别 kc (标签 2): 29个样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
Epoch 4: Train Loss: 1.0570, Train Acc: 0.9593, Val Loss: 2.1370, Val Acc: 0.5333
验证准确率未提高，当前耐心值: 2/3
Epoch 5/50
当前学习率: 0.000200
使用类别权重: 正常=1.0, 早期圆锥角膜=6.231727555394775, 圆锥角膜=6.327520041382975
使用增强版对比损失函数，温度: 0.09561272008326499, 硬负样本挖掘比例: 0.5424541611462215, 早期圆锥角膜与正常样本对比权重: 1.7843428622955229, 圆锥角膜与正常样本对比权重: 1.0
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 16 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
警告: 没有足够的有效类别进行3-way任务采样，尝试调整参数...
类别样本数量: {0: 33, 1: 5, 2: 29}
调整后的参数: n_shot=2, n_query=3
类别 normal (标签 0): 33个样本
类别 e-kc (标签 1): 5个样本
类别 kc (标签 2): 29个样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 6 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
Epoch 5: Train Loss: 0.9796, Train Acc: 0.9704, Val Loss: 2.0121, Val Acc: 0.6222
验证准确率未提高，当前耐心值: 3/3
早停触发，停止训练
警告: 没有足够的有效类别进行3-way任务采样，尝试调整参数...
类别样本数量: {0: 37, 1: 7, 2: 27}
调整后的参数: n_shot=3, n_query=4
类别 normal (标签 0): 37个样本
类别 e-kc (标签 1): 7个样本
类别 kc (标签 2): 27个样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 10 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 10 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 10 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 10 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 10 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 10 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 10 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 10 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 10 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 10 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 10 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 10 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 10 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 10 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 10 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 10 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 10 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 10 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 10 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 10 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 10 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 10 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 10 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 10 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 10 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 10 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 10 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 10 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 10 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 10 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本

测试结果:
总体准确率: 87.22%
类别准确率:
  kc: 75.83%
  e-kc: 85.83%
  normal: 100.00%
训练完成，结果已保存到 /home/<USER>/balanced_task_sampling/results_trial_20250525_004758

STDERR:
+ TIMESTAMP=20250525_004758
+ RESULT_DIR=/home/<USER>/balanced_task_sampling/results_trial_20250525_004758
+ '[' -n /home/<USER>/balanced_task_sampling/results_trial_20250525_004758/main_training_trial_29_20250525_004758.log ']'
+ LOG_FILE=/home/<USER>/balanced_task_sampling/results_trial_20250525_004758/main_training_trial_29_20250525_004758.log
++ dirname /home/<USER>/balanced_task_sampling/results_trial_20250525_004758/main_training_trial_29_20250525_004758.log
+ mkdir -p /home/<USER>/balanced_task_sampling/results_trial_20250525_004758
+ mkdir -p /home/<USER>/balanced_task_sampling/results_trial_20250525_004758
+ echo '===================== 训练配置 ====================='
+ echo 'GPU: 0'
+ echo '输出目录: /home/<USER>/balanced_task_sampling/results_trial_20250525_004758'
+ echo '日志文件: /home/<USER>/balanced_task_sampling/results_trial_20250525_004758/main_training_trial_29_20250525_004758.log'
+ echo ''
+ echo '--------- 优化参数 ---------'
+ echo '内部学习率: 0.347389'
+ echo '训练轮数: 50'
+ echo '每轮任务数: 30'
+ echo ''
+ echo '--------- 模型参数 ---------'
+ echo '使用类别权重: 圆锥角膜=6.327520041382975, 早期圆锥角膜=6.231727555394775'
+ tee -a /home/<USER>/balanced_task_sampling/results_trial_20250525_004758/main_training_trial_29_20250525_004758.log
+ echo '温度参数: 0.09561272008326499'
+ echo '硬样本挖掘比例: 0.5424541611462215'
+ echo '对比学习权重: 0.3347902507554248'
+ echo '=============================================
'
+ validate_params
+ local error_count=0
++ dirname /home/<USER>/balanced_task_sampling/results_trial_20250525_004758/main_training_trial_29_20250525_004758.log
++ dirname /home/<USER>/balanced_task_sampling/split_result/train_set.csv
+ for dir in "$RESULT_DIR" "$(dirname "$LOG_FILE")" "$(dirname "$TRAIN_CSV")"
+ mkdir -p /home/<USER>/balanced_task_sampling/results_trial_20250525_004758
+ for dir in "$RESULT_DIR" "$(dirname "$LOG_FILE")" "$(dirname "$TRAIN_CSV")"
+ mkdir -p /home/<USER>/balanced_task_sampling/results_trial_20250525_004758
+ for dir in "$RESULT_DIR" "$(dirname "$LOG_FILE")" "$(dirname "$TRAIN_CSV")"
+ mkdir -p /home/<USER>/balanced_task_sampling/split_result
+ for csv in "$TRAIN_CSV" "$VAL_CSV" "$TEST_CSV"
+ '[' '!' -f /home/<USER>/balanced_task_sampling/split_result/train_set.csv ']'
+ for csv in "$TRAIN_CSV" "$VAL_CSV" "$TEST_CSV"
+ '[' '!' -f /home/<USER>/balanced_task_sampling/split_result/val_set.csv ']'
+ for csv in "$TRAIN_CSV" "$VAL_CSV" "$TEST_CSV"
+ '[' '!' -f /home/<USER>/balanced_task_sampling/split_result/test_set.csv ']'
+ [[ 0.0002 =~ ^[0-9.]+$ ]]
+ [[ 0.347389 =~ ^[0-9.]+$ ]]
+ '[' 0 -gt 0 ']'
+ return 0
+ '[' false = true ']'
+ export CUDA_VISIBLE_DEVICES=0
+ CUDA_VISIBLE_DEVICES=0
+ export OMP_NUM_THREADS=1
+ OMP_NUM_THREADS=1
+ export MKL_NUM_THREADS=1
+ MKL_NUM_THREADS=1
+ export MPLBACKEND=Agg
+ MPLBACKEND=Agg
+ mkdir -p /home/<USER>/balanced_task_sampling/results_trial_20250525_004758
+ echo '创建目录: /home/<USER>/balanced_task_sampling/results_trial_20250525_004758'
+ ls -l /home/<USER>/balanced_task_sampling/results_trial_20250525_004758
+ PRETRAINED_ARGS=
+ '[' true = true ']'
+ PRETRAINED_ARGS=--pretrained
+ AUGMENT_ARGS=--no_augmentation
+ CONTRASTIVE_ARGS=
+ '[' true = true ']'
+ CONTRASTIVE_ARGS='--use_contrastive --contrastive_weight 0.3347902507554248 --temperature 0.09561272008326499 --hard_mining_ratio 0.5424541611462215 --early_normal_weight 1.7843428622955229 --kc_normal_weight 1.0'
+ NORMALIZE_ARGS=
+ '[' false = true ']'
+ SAMPLER_ARGS=
+ '[' true = true ']'
+ SAMPLER_ARGS=' --use_separate_test_sampler'
+ '[' true = true ']'
+ SAMPLER_ARGS=' --use_separate_test_sampler --use_balanced_task_sampler --kc_shot_multiplier 3.3447431450581537 --early_kc_shot_multiplier 1.6738496571577692'
+ LR_SCHEDULER_ARGS=
+ '[' true = true ']'
+ LR_SCHEDULER_ARGS='--use_lr_scheduler --scheduler_type plateau'
+ '[' plateau = onecycle ']'
+ '[' plateau = plateau ']'
+ LR_SCHEDULER_ARGS='--use_lr_scheduler --scheduler_type plateau --plateau_factor 0.5 --plateau_patience 2 --min_lr 1e-6'
+ BASE_CMD='python train_balanced_task_sampling.py   --train_csv /home/<USER>/balanced_task_sampling/split_result/train_set.csv   --val_csv /home/<USER>/balanced_task_sampling/split_result/val_set.csv   --test_csv /home/<USER>/balanced_task_sampling/split_result/test_set.csv   --epochs 50   --lr 0.0002   --weight_decay 5e-4   --feature_dim 512   --save_dir /home/<USER>/balanced_task_sampling/results_trial_20250525_004758   --proto_counts 4,6,3   --pretrained   --inner_lr 0.347389   --inner_steps 5   --n_way 3   --n_shot 5   --n_query 15   --tasks_per_epoch 30   --meta_batch_size 8   --no_augmentation   --mixup_alpha 0.0   --cutmix_prob 0.0   --num_workers 4   --augment_factor 1   --kc_augment_factor 1   --seed 42   --early_kc_weight 6.231727555394775   --kc_weight 6.327520041382975   --focal_gamma 1.7010125146769512   --val_frequency 1   --early_stopping 3   --dropout 0.5    --use_separate_test_sampler --use_balanced_task_sampler --kc_shot_multiplier 3.3447431450581537 --early_kc_shot_multiplier 1.6738496571577692   --use_contrastive --contrastive_weight 0.3347902507554248 --temperature 0.09561272008326499 --hard_mining_ratio 0.5424541611462215 --early_normal_weight 1.7843428622955229 --kc_normal_weight 1.0      --use_lr_scheduler --scheduler_type plateau --plateau_factor 0.5 --plateau_patience 2 --min_lr 1e-6'
+ echo 开始无数据增强的平衡任务采样训练...
+ echo '使用GPU: 0'
+ '[' direct = nohup ']'
+ echo 直接运行训练脚本...
+ eval python train_balanced_task_sampling.py --train_csv /home/<USER>/balanced_task_sampling/split_result/train_set.csv --val_csv /home/<USER>/balanced_task_sampling/split_result/val_set.csv --test_csv /home/<USER>/balanced_task_sampling/split_result/test_set.csv --epochs 50 --lr 0.0002 --weight_decay 5e-4 --feature_dim 512 --save_dir /home/<USER>/balanced_task_sampling/results_trial_20250525_004758 --proto_counts 4,6,3 --pretrained --inner_lr 0.347389 --inner_steps 5 --n_way 3 --n_shot 5 --n_query 15 --tasks_per_epoch 30 --meta_batch_size 8 --no_augmentation --mixup_alpha 0.0 --cutmix_prob 0.0 --num_workers 4 --augment_factor 1 --kc_augment_factor 1 --seed 42 --early_kc_weight 6.231727555394775 --kc_weight 6.327520041382975 --focal_gamma 1.7010125146769512 --val_frequency 1 --early_stopping 3 --dropout 0.5 --use_separate_test_sampler --use_balanced_task_sampler --kc_shot_multiplier 3.3447431450581537 --early_kc_shot_multiplier 1.6738496571577692 --use_contrastive --contrastive_weight 0.3347902507554248 --temperature 0.09561272008326499 --hard_mining_ratio 0.5424541611462215 --early_normal_weight 1.7843428622955229 --kc_normal_weight 1.0 --use_lr_scheduler --scheduler_type plateau --plateau_factor 0.5 --plateau_patience 2 --min_lr 1e-6
++ python train_balanced_task_sampling.py --train_csv /home/<USER>/balanced_task_sampling/split_result/train_set.csv --val_csv /home/<USER>/balanced_task_sampling/split_result/val_set.csv --test_csv /home/<USER>/balanced_task_sampling/split_result/test_set.csv --epochs 50 --lr 0.0002 --weight_decay 5e-4 --feature_dim 512 --save_dir /home/<USER>/balanced_task_sampling/results_trial_20250525_004758 --proto_counts 4,6,3 --pretrained --inner_lr 0.347389 --inner_steps 5 --n_way 3 --n_shot 5 --n_query 15 --tasks_per_epoch 30 --meta_batch_size 8 --no_augmentation --mixup_alpha 0.0 --cutmix_prob 0.0 --num_workers 4 --augment_factor 1 --kc_augment_factor 1 --seed 42 --early_kc_weight 6.231727555394775 --kc_weight 6.327520041382975 --focal_gamma 1.7010125146769512 --val_frequency 1 --early_stopping 3 --dropout 0.5 --use_separate_test_sampler --use_balanced_task_sampler --kc_shot_multiplier 3.3447431450581537 --early_kc_shot_multiplier 1.6738496571577692 --use_contrastive --contrastive_weight 0.3347902507554248 --temperature 0.09561272008326499 --hard_mining_ratio 0.5424541611462215 --early_normal_weight 1.7843428622955229 --kc_normal_weight 1.0 --use_lr_scheduler --scheduler_type plateau --plateau_factor 0.5 --plateau_patience 2 --min_lr 1e-6
/home/<USER>/anaconda3/envs/kc1/lib/python3.10/site-packages/torchvision/models/_utils.py:208: UserWarning: The parameter 'pretrained' is deprecated since 0.13 and may be removed in the future, please use 'weights' instead.
  warnings.warn(
/home/<USER>/anaconda3/envs/kc1/lib/python3.10/site-packages/torchvision/models/_utils.py:223: UserWarning: Arguments other than a weight enum or `None` for 'weights' are deprecated since 0.13 and may be removed in the future. The current behavior is equivalent to passing `weights=ResNet34_Weights.IMAGENET1K_V1`. You can also use `weights=ResNet34_Weights.DEFAULT` to get the most up-to-date weights.
  warnings.warn(msg)
/home/<USER>/anaconda3/envs/kc1/lib/python3.10/site-packages/torch/optim/lr_scheduler.py:62: UserWarning: The verbose parameter is deprecated. Please use get_last_lr() to access the learning rate.
  warnings.warn(

Training tasks:   0%|          | 0/30 [00:00<?, ?it/s]
Training tasks:   3%|▎         | 1/30 [00:07<03:25,  7.08s/it]
Training tasks:   7%|▋         | 2/30 [00:08<01:51,  4.00s/it]
Training tasks:  10%|█         | 3/30 [00:10<01:21,  3.01s/it]
Training tasks:  13%|█▎        | 4/30 [00:12<01:06,  2.55s/it]
Training tasks:  17%|█▋        | 5/30 [00:14<00:59,  2.38s/it]
Training tasks:  20%|██        | 6/30 [00:16<00:53,  2.22s/it]
Training tasks:  23%|██▎       | 7/30 [00:18<00:48,  2.09s/it]
Training tasks:  27%|██▋       | 8/30 [00:20<00:44,  2.01s/it]
Training tasks:  30%|███       | 9/30 [00:21<00:39,  1.88s/it]
Training tasks:  33%|███▎      | 10/30 [00:23<00:36,  1.85s/it]
Training tasks:  37%|███▋      | 11/30 [00:25<00:34,  1.84s/it]
Training tasks:  40%|████      | 12/30 [00:27<00:32,  1.82s/it]
Training tasks:  43%|████▎     | 13/30 [00:29<00:31,  1.87s/it]
Training tasks:  47%|████▋     | 14/30 [00:31<00:29,  1.85s/it]
Training tasks:  50%|█████     | 15/30 [00:32<00:27,  1.85s/it]
Training tasks:  53%|█████▎    | 16/30 [00:34<00:25,  1.80s/it]
Training tasks:  57%|█████▋    | 17/30 [00:36<00:23,  1.84s/it]
Training tasks:  60%|██████    | 18/30 [00:38<00:22,  1.86s/it]
Training tasks:  63%|██████▎   | 19/30 [00:40<00:20,  1.89s/it]
Training tasks:  67%|██████▋   | 20/30 [00:42<00:18,  1.88s/it]
Training tasks:  70%|███████   | 21/30 [00:44<00:17,  1.90s/it]
Training tasks:  73%|███████▎  | 22/30 [00:45<00:14,  1.86s/it]
Training tasks:  77%|███████▋  | 23/30 [00:47<00:12,  1.80s/it]
Training tasks:  80%|████████  | 24/30 [00:49<00:10,  1.81s/it]
Training tasks:  83%|████████▎ | 25/30 [00:51<00:08,  1.76s/it]
Training tasks:  87%|████████▋ | 26/30 [00:52<00:06,  1.72s/it]
Training tasks:  90%|█████████ | 27/30 [00:54<00:05,  1.74s/it]
Training tasks:  93%|█████████▎| 28/30 [00:56<00:03,  1.78s/it]
Training tasks:  97%|█████████▋| 29/30 [00:58<00:01,  1.79s/it]
Training tasks: 100%|██████████| 30/30 [00:59<00:00,  1.79s/it]
Training tasks: 100%|██████████| 30/30 [00:59<00:00,  2.00s/it]

Validating tasks:   0%|          | 0/10 [00:00<?, ?it/s]
Validating tasks:  10%|█         | 1/10 [00:00<00:03,  2.73it/s]
Validating tasks:  20%|██        | 2/10 [00:00<00:02,  3.10it/s]
Validating tasks:  30%|███       | 3/10 [00:00<00:02,  3.19it/s]
Validating tasks:  40%|████      | 4/10 [00:01<00:01,  3.18it/s]
Validating tasks:  50%|█████     | 5/10 [00:01<00:01,  3.33it/s]
Validating tasks:  60%|██████    | 6/10 [00:01<00:01,  3.59it/s]
Validating tasks:  70%|███████   | 7/10 [00:01<00:00,  3.90it/s]
Validating tasks:  80%|████████  | 8/10 [00:02<00:00,  4.14it/s]
Validating tasks:  90%|█████████ | 9/10 [00:02<00:00,  4.40it/s]
Validating tasks: 100%|██████████| 10/10 [00:02<00:00,  4.39it/s]
Validating tasks: 100%|██████████| 10/10 [00:02<00:00,  3.80it/s]

Training tasks:   0%|          | 0/30 [00:00<?, ?it/s]
Training tasks:   3%|▎         | 1/30 [00:01<00:52,  1.81s/it]
Training tasks:   7%|▋         | 2/30 [00:03<00:49,  1.78s/it]
Training tasks:  10%|█         | 3/30 [00:05<00:50,  1.87s/it]
Training tasks:  13%|█▎        | 4/30 [00:07<00:47,  1.84s/it]
Training tasks:  17%|█▋        | 5/30 [00:09<00:47,  1.88s/it]
Training tasks:  20%|██        | 6/30 [00:11<00:45,  1.89s/it]
Training tasks:  23%|██▎       | 7/30 [00:13<00:43,  1.89s/it]
Training tasks:  27%|██▋       | 8/30 [00:14<00:40,  1.84s/it]
Training tasks:  30%|███       | 9/30 [00:16<00:39,  1.87s/it]
Training tasks:  33%|███▎      | 10/30 [00:18<00:37,  1.89s/it]
Training tasks:  37%|███▋      | 11/30 [00:20<00:35,  1.85s/it]
Training tasks:  40%|████      | 12/30 [00:22<00:33,  1.85s/it]
Training tasks:  43%|████▎     | 13/30 [00:23<00:29,  1.74s/it]
Training tasks:  47%|████▋     | 14/30 [00:25<00:27,  1.71s/it]
Training tasks:  50%|█████     | 15/30 [00:27<00:25,  1.67s/it]
Training tasks:  53%|█████▎    | 16/30 [00:28<00:23,  1.69s/it]
Training tasks:  57%|█████▋    | 17/30 [00:30<00:22,  1.71s/it]
Training tasks:  60%|██████    | 18/30 [00:32<00:21,  1.75s/it]
Training tasks:  63%|██████▎   | 19/30 [00:34<00:19,  1.78s/it]
Training tasks:  67%|██████▋   | 20/30 [00:36<00:18,  1.84s/it]
Training tasks:  70%|███████   | 21/30 [00:38<00:17,  1.90s/it]
Training tasks:  73%|███████▎  | 22/30 [00:40<00:15,  1.92s/it]
Training tasks:  77%|███████▋  | 23/30 [00:41<00:12,  1.82s/it]
Training tasks:  80%|████████  | 24/30 [00:43<00:10,  1.75s/it]
Training tasks:  83%|████████▎ | 25/30 [00:45<00:08,  1.74s/it]
Training tasks:  87%|████████▋ | 26/30 [00:46<00:07,  1.78s/it]
Training tasks:  90%|█████████ | 27/30 [00:48<00:05,  1.79s/it]
Training tasks:  93%|█████████▎| 28/30 [00:50<00:03,  1.78s/it]
Training tasks:  97%|█████████▋| 29/30 [00:52<00:01,  1.74s/it]
Training tasks: 100%|██████████| 30/30 [00:53<00:00,  1.67s/it]
Training tasks: 100%|██████████| 30/30 [00:53<00:00,  1.79s/it]

Validating tasks:   0%|          | 0/10 [00:00<?, ?it/s]
Validating tasks:  10%|█         | 1/10 [00:00<00:01,  5.56it/s]
Validating tasks:  20%|██        | 2/10 [00:00<00:01,  5.15it/s]
Validating tasks:  30%|███       | 3/10 [00:00<00:01,  4.78it/s]
Validating tasks:  40%|████      | 4/10 [00:00<00:01,  4.70it/s]
Validating tasks:  50%|█████     | 5/10 [00:01<00:01,  4.23it/s]
Validating tasks:  60%|██████    | 6/10 [00:01<00:00,  4.29it/s]
Validating tasks:  70%|███████   | 7/10 [00:01<00:00,  4.10it/s]
Validating tasks:  80%|████████  | 8/10 [00:01<00:00,  4.12it/s]
Validating tasks:  90%|█████████ | 9/10 [00:02<00:00,  4.28it/s]
Validating tasks: 100%|██████████| 10/10 [00:02<00:00,  4.41it/s]
Validating tasks: 100%|██████████| 10/10 [00:02<00:00,  4.41it/s]

Training tasks:   0%|          | 0/30 [00:00<?, ?it/s]
Training tasks:   3%|▎         | 1/30 [00:01<00:56,  1.93s/it]
Training tasks:   7%|▋         | 2/30 [00:03<00:50,  1.82s/it]
Training tasks:  10%|█         | 3/30 [00:05<00:45,  1.70s/it]
Training tasks:  13%|█▎        | 4/30 [00:06<00:44,  1.70s/it]
Training tasks:  17%|█▋        | 5/30 [00:08<00:41,  1.66s/it]
Training tasks:  20%|██        | 6/30 [00:10<00:39,  1.65s/it]
Training tasks:  23%|██▎       | 7/30 [00:12<00:39,  1.73s/it]
Training tasks:  27%|██▋       | 8/30 [00:13<00:38,  1.77s/it]
Training tasks:  30%|███       | 9/30 [00:15<00:37,  1.78s/it]
Training tasks:  33%|███▎      | 10/30 [00:17<00:36,  1.83s/it]
Training tasks:  37%|███▋      | 11/30 [00:19<00:32,  1.72s/it]
Training tasks:  40%|████      | 12/30 [00:20<00:29,  1.62s/it]
Training tasks:  43%|████▎     | 13/30 [00:22<00:28,  1.65s/it]
Training tasks:  47%|████▋     | 14/30 [00:24<00:28,  1.76s/it]
Training tasks:  50%|█████     | 15/30 [00:25<00:26,  1.76s/it]
Training tasks:  53%|█████▎    | 16/30 [00:27<00:24,  1.78s/it]
Training tasks:  57%|█████▋    | 17/30 [00:29<00:22,  1.76s/it]
Training tasks:  60%|██████    | 18/30 [00:31<00:21,  1.78s/it]
Training tasks:  63%|██████▎   | 19/30 [00:32<00:19,  1.73s/it]
Training tasks:  67%|██████▋   | 20/30 [00:34<00:17,  1.80s/it]
Training tasks:  70%|███████   | 21/30 [00:36<00:15,  1.78s/it]
Training tasks:  73%|███████▎  | 22/30 [00:38<00:14,  1.76s/it]
Training tasks:  77%|███████▋  | 23/30 [00:39<00:11,  1.71s/it]
Training tasks:  80%|████████  | 24/30 [00:41<00:10,  1.73s/it]
Training tasks:  83%|████████▎ | 25/30 [00:43<00:08,  1.69s/it]
Training tasks:  87%|████████▋ | 26/30 [00:45<00:06,  1.73s/it]
Training tasks:  90%|█████████ | 27/30 [00:46<00:05,  1.68s/it]
Training tasks:  93%|█████████▎| 28/30 [00:48<00:03,  1.72s/it]
Training tasks:  97%|█████████▋| 29/30 [00:50<00:01,  1.75s/it]
Training tasks: 100%|██████████| 30/30 [00:52<00:00,  1.73s/it]
Training tasks: 100%|██████████| 30/30 [00:52<00:00,  1.73s/it]

Validating tasks:   0%|          | 0/10 [00:00<?, ?it/s]
Validating tasks:  10%|█         | 1/10 [00:00<00:02,  4.24it/s]
Validating tasks:  20%|██        | 2/10 [00:00<00:02,  3.74it/s]
Validating tasks:  30%|███       | 3/10 [00:00<00:02,  3.50it/s]
Validating tasks:  40%|████      | 4/10 [00:01<00:01,  3.66it/s]
Validating tasks:  50%|█████     | 5/10 [00:01<00:01,  4.06it/s]
Validating tasks:  60%|██████    | 6/10 [00:01<00:00,  4.29it/s]
Validating tasks:  70%|███████   | 7/10 [00:01<00:00,  4.40it/s]
Validating tasks:  80%|████████  | 8/10 [00:01<00:00,  4.45it/s]
Validating tasks:  90%|█████████ | 9/10 [00:02<00:00,  4.59it/s]
Validating tasks: 100%|██████████| 10/10 [00:02<00:00,  4.45it/s]
Validating tasks: 100%|██████████| 10/10 [00:02<00:00,  4.22it/s]

Training tasks:   0%|          | 0/30 [00:00<?, ?it/s]
Training tasks:   3%|▎         | 1/30 [00:01<00:40,  1.41s/it]
Training tasks:   7%|▋         | 2/30 [00:02<00:40,  1.44s/it]
Training tasks:  10%|█         | 3/30 [00:04<00:38,  1.43s/it]
Training tasks:  13%|█▎        | 4/30 [00:05<00:36,  1.42s/it]
Training tasks:  17%|█▋        | 5/30 [00:07<00:36,  1.47s/it]
Training tasks:  20%|██        | 6/30 [00:08<00:36,  1.52s/it]
Training tasks:  23%|██▎       | 7/30 [00:10<00:36,  1.60s/it]
Training tasks:  27%|██▋       | 8/30 [00:12<00:36,  1.67s/it]
Training tasks:  30%|███       | 9/30 [00:14<00:36,  1.73s/it]
Training tasks:  33%|███▎      | 10/30 [00:16<00:34,  1.74s/it]
Training tasks:  37%|███▋      | 11/30 [00:17<00:33,  1.77s/it]
Training tasks:  40%|████      | 12/30 [00:19<00:30,  1.71s/it]
Training tasks:  43%|████▎     | 13/30 [00:21<00:29,  1.71s/it]
Training tasks:  47%|████▋     | 14/30 [00:22<00:26,  1.67s/it]
Training tasks:  50%|█████     | 15/30 [00:24<00:23,  1.59s/it]
Training tasks:  53%|█████▎    | 16/30 [00:26<00:23,  1.67s/it]
Training tasks:  57%|█████▋    | 17/30 [00:27<00:22,  1.71s/it]
Training tasks:  60%|██████    | 18/30 [00:29<00:19,  1.65s/it]
Training tasks:  63%|██████▎   | 19/30 [00:30<00:18,  1.65s/it]
Training tasks:  67%|██████▋   | 20/30 [00:32<00:16,  1.64s/it]
Training tasks:  70%|███████   | 21/30 [00:34<00:14,  1.64s/it]
Training tasks:  73%|███████▎  | 22/30 [00:35<00:12,  1.54s/it]
Training tasks:  77%|███████▋  | 23/30 [00:36<00:10,  1.48s/it]
Training tasks:  80%|████████  | 24/30 [00:38<00:08,  1.43s/it]
Training tasks:  83%|████████▎ | 25/30 [00:39<00:07,  1.43s/it]
Training tasks:  87%|████████▋ | 26/30 [00:40<00:05,  1.38s/it]
Training tasks:  90%|█████████ | 27/30 [00:42<00:03,  1.32s/it]
Training tasks:  93%|█████████▎| 28/30 [00:43<00:02,  1.30s/it]
Training tasks:  97%|█████████▋| 29/30 [00:44<00:01,  1.33s/it]
Training tasks: 100%|██████████| 30/30 [00:46<00:00,  1.32s/it]
Training tasks: 100%|██████████| 30/30 [00:46<00:00,  1.53s/it]

Validating tasks:   0%|          | 0/10 [00:00<?, ?it/s]
Validating tasks:  10%|█         | 1/10 [00:00<00:01,  6.12it/s]
Validating tasks:  20%|██        | 2/10 [00:00<00:01,  6.25it/s]
Validating tasks:  30%|███       | 3/10 [00:00<00:01,  6.22it/s]
Validating tasks:  40%|████      | 4/10 [00:00<00:00,  6.13it/s]
Validating tasks:  50%|█████     | 5/10 [00:00<00:00,  6.11it/s]
Validating tasks:  60%|██████    | 6/10 [00:00<00:00,  6.08it/s]
Validating tasks:  70%|███████   | 7/10 [00:01<00:00,  6.10it/s]
Validating tasks:  80%|████████  | 8/10 [00:01<00:00,  6.06it/s]
Validating tasks:  90%|█████████ | 9/10 [00:01<00:00,  6.06it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  6.01it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  6.08it/s]

Training tasks:   0%|          | 0/30 [00:00<?, ?it/s]
Training tasks:   3%|▎         | 1/30 [00:01<00:39,  1.36s/it]
Training tasks:   7%|▋         | 2/30 [00:02<00:41,  1.49s/it]
Training tasks:  10%|█         | 3/30 [00:04<00:39,  1.45s/it]
Training tasks:  13%|█▎        | 4/30 [00:05<00:37,  1.45s/it]
Training tasks:  17%|█▋        | 5/30 [00:07<00:39,  1.57s/it]
Training tasks:  20%|██        | 6/30 [00:09<00:39,  1.65s/it]
Training tasks:  23%|██▎       | 7/30 [00:10<00:37,  1.63s/it]
Training tasks:  27%|██▋       | 8/30 [00:12<00:35,  1.63s/it]
Training tasks:  30%|███       | 9/30 [00:14<00:34,  1.63s/it]
Training tasks:  33%|███▎      | 10/30 [00:15<00:32,  1.63s/it]
Training tasks:  37%|███▋      | 11/30 [00:17<00:30,  1.62s/it]
Training tasks:  40%|████      | 12/30 [00:19<00:28,  1.61s/it]
Training tasks:  43%|████▎     | 13/30 [00:20<00:27,  1.64s/it]
Training tasks:  47%|████▋     | 14/30 [00:22<00:26,  1.67s/it]
Training tasks:  50%|█████     | 15/30 [00:24<00:24,  1.62s/it]
Training tasks:  53%|█████▎    | 16/30 [00:25<00:22,  1.58s/it]
Training tasks:  57%|█████▋    | 17/30 [00:27<00:21,  1.64s/it]
Training tasks:  60%|██████    | 18/30 [00:28<00:19,  1.63s/it]
Training tasks:  63%|██████▎   | 19/30 [00:30<00:17,  1.63s/it]
Training tasks:  67%|██████▋   | 20/30 [00:32<00:15,  1.59s/it]
Training tasks:  70%|███████   | 21/30 [00:33<00:14,  1.63s/it]
Training tasks:  73%|███████▎  | 22/30 [00:35<00:13,  1.63s/it]
Training tasks:  77%|███████▋  | 23/30 [00:37<00:11,  1.66s/it]
Training tasks:  80%|████████  | 24/30 [00:38<00:10,  1.72s/it]
Training tasks:  83%|████████▎ | 25/30 [00:40<00:08,  1.61s/it]
Training tasks:  87%|████████▋ | 26/30 [00:42<00:06,  1.67s/it]
Training tasks:  90%|█████████ | 27/30 [00:43<00:04,  1.67s/it]
Training tasks:  93%|█████████▎| 28/30 [00:45<00:03,  1.65s/it]
Training tasks:  97%|█████████▋| 29/30 [00:47<00:01,  1.66s/it]
Training tasks: 100%|██████████| 30/30 [00:48<00:00,  1.68s/it]
Training tasks: 100%|██████████| 30/30 [00:48<00:00,  1.63s/it]

Validating tasks:   0%|          | 0/10 [00:00<?, ?it/s]
Validating tasks:  10%|█         | 1/10 [00:00<00:01,  4.74it/s]
Validating tasks:  20%|██        | 2/10 [00:00<00:01,  4.63it/s]
Validating tasks:  30%|███       | 3/10 [00:00<00:01,  4.24it/s]
Validating tasks:  40%|████      | 4/10 [00:00<00:01,  4.05it/s]
Validating tasks:  50%|█████     | 5/10 [00:01<00:01,  3.98it/s]
Validating tasks:  60%|██████    | 6/10 [00:01<00:00,  4.07it/s]
Validating tasks:  70%|███████   | 7/10 [00:01<00:00,  4.02it/s]
Validating tasks:  80%|████████  | 8/10 [00:01<00:00,  3.92it/s]
Validating tasks:  90%|█████████ | 9/10 [00:02<00:00,  3.78it/s]
Validating tasks: 100%|██████████| 10/10 [00:02<00:00,  3.72it/s]
Validating tasks: 100%|██████████| 10/10 [00:02<00:00,  3.95it/s]

Testing tasks:   0%|          | 0/30 [00:00<?, ?it/s]
Testing tasks:   3%|▎         | 1/30 [00:00<00:11,  2.49it/s]
Testing tasks:   7%|▋         | 2/30 [00:00<00:10,  2.70it/s]
Testing tasks:  10%|█         | 3/30 [00:01<00:09,  2.79it/s]
Testing tasks:  13%|█▎        | 4/30 [00:01<00:09,  2.87it/s]
Testing tasks:  17%|█▋        | 5/30 [00:01<00:09,  2.77it/s]
Testing tasks:  20%|██        | 6/30 [00:02<00:08,  2.76it/s]
Testing tasks:  23%|██▎       | 7/30 [00:02<00:08,  2.73it/s]
Testing tasks:  27%|██▋       | 8/30 [00:02<00:08,  2.67it/s]
Testing tasks:  30%|███       | 9/30 [00:03<00:07,  2.71it/s]
Testing tasks:  33%|███▎      | 10/30 [00:03<00:07,  2.74it/s]
Testing tasks:  37%|███▋      | 11/30 [00:04<00:06,  2.78it/s]
Testing tasks:  40%|████      | 12/30 [00:04<00:06,  2.72it/s]
Testing tasks:  43%|████▎     | 13/30 [00:04<00:06,  2.71it/s]
Testing tasks:  47%|████▋     | 14/30 [00:05<00:05,  2.76it/s]
Testing tasks:  50%|█████     | 15/30 [00:05<00:05,  2.67it/s]
Testing tasks:  53%|█████▎    | 16/30 [00:05<00:05,  2.54it/s]
Testing tasks:  57%|█████▋    | 17/30 [00:06<00:05,  2.53it/s]
Testing tasks:  60%|██████    | 18/30 [00:06<00:04,  2.61it/s]
Testing tasks:  63%|██████▎   | 19/30 [00:06<00:03,  2.80it/s]
Testing tasks:  67%|██████▋   | 20/30 [00:07<00:03,  2.85it/s]
Testing tasks:  70%|███████   | 21/30 [00:07<00:03,  2.84it/s]
Testing tasks:  73%|███████▎  | 22/30 [00:08<00:02,  2.90it/s]
Testing tasks:  77%|███████▋  | 23/30 [00:08<00:02,  2.98it/s]
Testing tasks:  80%|████████  | 24/30 [00:08<00:02,  3.00it/s]
Testing tasks:  83%|████████▎ | 25/30 [00:09<00:01,  2.94it/s]
Testing tasks:  87%|████████▋ | 26/30 [00:09<00:01,  3.00it/s]
Testing tasks:  90%|█████████ | 27/30 [00:09<00:00,  3.07it/s]
Testing tasks:  93%|█████████▎| 28/30 [00:09<00:00,  3.13it/s]
Testing tasks:  97%|█████████▋| 29/30 [00:10<00:00,  3.09it/s]
Testing tasks: 100%|██████████| 30/30 [00:10<00:00,  3.08it/s]
Testing tasks: 100%|██████████| 30/30 [00:10<00:00,  2.83it/s]

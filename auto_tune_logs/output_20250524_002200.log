STDOUT:
===================== 训练配置 =====================
GPU: 0
输出目录: /home/<USER>/balanced_task_sampling/results_trial_20250524_002200
日志文件: /home/<USER>/balanced_task_sampling/results_trial_20250524_002200/main_training_trial_37_20250524_002200.log

--------- 优化参数 ---------
内部学习率: 0.347389
训练轮数: 50
每轮任务数: 30

--------- 模型参数 ---------
使用类别权重: 圆锥角膜=7.087643637934322, 早期圆锥角膜=6.585738354514965
温度参数: 0.03471062585435967
硬样本挖掘比例: 0.5206957940391875
对比学习权重: 0.5556355200422206
=============================================

创建目录: /home/<USER>/balanced_task_sampling/results_trial_20250524_002200
总用量 4
-rw-rw-r-- 1 <USER> <GROUP> 657 5月  24 00:22 main_training_trial_37_20250524_002200.log
开始无数据增强的平衡任务采样训练...
使用GPU: 0
直接运行训练脚本...
成功导入增强版特征提取器
成功导入归一化特征提取器
成功导入增强版损失函数
成功导入增强版MAML模型
成功导入KC特定增强模块
成功导入增强版GPU数据增强模块
使用设备: cuda
禁用所有数据增强，直接加载原始数据集...
使用平衡任务采样器
类别 normal (标签 0): 164个样本
类别 e-kc (标签 1): 26个样本
类别 kc (标签 2): 133个样本
使用原型配置: [1, 6, 3]
使用增强版特征提取器，dropout率: 0.5
使用增强版MAML-ProtoNet模型，内循环学习率: 0.347389, 内循环步数: 5
使用ReduceLROnPlateau学习率调度器，因子: 0.5, 耐心值: 2
开始训练，共50个epoch
Epoch 1/50
当前学习率: 0.000200
使用类别权重: 正常=1.0, 早期圆锥角膜=6.585738354514965, 圆锥角膜=7.087643637934322
使用增强版对比损失函数，温度: 0.03471062585435967, 硬负样本挖掘比例: 0.5206957940391875, 早期圆锥角膜与正常样本对比权重: 1.3152958314226866, 圆锥角膜与正常样本对比权重: 1.0
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 13 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 13 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 13 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 13 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 13 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 13 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 13 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 13 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 13 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 13 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 13 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 13 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 13 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 13 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 13 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 13 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 13 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 13 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 13 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 13 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 13 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 13 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 13 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 13 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 13 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 13 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 13 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 13 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 13 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 13 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
警告: 没有足够的有效类别进行3-way任务采样，尝试调整参数...
类别样本数量: {0: 33, 1: 5, 2: 29}
调整后的参数: n_shot=2, n_query=3
类别 normal (标签 0): 33个样本
类别 e-kc (标签 1): 5个样本
类别 kc (标签 2): 29个样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
Epoch 1: Train Loss: 2.6914, Train Acc: 0.4674, Val Loss: 2.1147, Val Acc: 0.5333
保存最佳模型，验证准确率: 0.5333
Epoch 2/50
当前学习率: 0.000200
使用类别权重: 正常=1.0, 早期圆锥角膜=6.585738354514965, 圆锥角膜=7.087643637934322
使用增强版对比损失函数，温度: 0.03471062585435967, 硬负样本挖掘比例: 0.5206957940391875, 早期圆锥角膜与正常样本对比权重: 1.3152958314226866, 圆锥角膜与正常样本对比权重: 1.0
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 13 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 13 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 13 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 13 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 13 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 13 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 13 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 13 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 13 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 13 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 13 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 13 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 13 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 13 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 13 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 13 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 13 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 13 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 13 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 13 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 13 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 13 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 13 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 13 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 13 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 13 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 13 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 13 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 13 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 13 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
警告: 没有足够的有效类别进行3-way任务采样，尝试调整参数...
类别样本数量: {0: 33, 1: 5, 2: 29}
调整后的参数: n_shot=2, n_query=3
类别 normal (标签 0): 33个样本
类别 e-kc (标签 1): 5个样本
类别 kc (标签 2): 29个样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
Epoch 2: Train Loss: 2.0880, Train Acc: 0.5844, Val Loss: 2.5747, Val Acc: 0.3556
验证准确率未提高，当前耐心值: 1/3
Epoch 3/50
当前学习率: 0.000200
使用类别权重: 正常=1.0, 早期圆锥角膜=6.585738354514965, 圆锥角膜=7.087643637934322
使用增强版对比损失函数，温度: 0.03471062585435967, 硬负样本挖掘比例: 0.5206957940391875, 早期圆锥角膜与正常样本对比权重: 1.3152958314226866, 圆锥角膜与正常样本对比权重: 1.0
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 13 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 13 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 13 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 13 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 13 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 13 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 13 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 13 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 13 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 13 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 13 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 13 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 13 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 13 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 13 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 13 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 13 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 13 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 13 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 13 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 13 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 13 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 13 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 13 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 13 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 13 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 13 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 13 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 13 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 13 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
警告: 没有足够的有效类别进行3-way任务采样，尝试调整参数...
类别样本数量: {0: 33, 1: 5, 2: 29}
调整后的参数: n_shot=2, n_query=3
类别 normal (标签 0): 33个样本
类别 e-kc (标签 1): 5个样本
类别 kc (标签 2): 29个样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
Epoch 3: Train Loss: 1.8662, Train Acc: 0.6933, Val Loss: 2.5388, Val Acc: 0.4889
验证准确率未提高，当前耐心值: 2/3
Epoch 4/50
当前学习率: 0.000200
使用类别权重: 正常=1.0, 早期圆锥角膜=6.585738354514965, 圆锥角膜=7.087643637934322
使用增强版对比损失函数，温度: 0.03471062585435967, 硬负样本挖掘比例: 0.5206957940391875, 早期圆锥角膜与正常样本对比权重: 1.3152958314226866, 圆锥角膜与正常样本对比权重: 1.0
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 13 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 13 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 13 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 13 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 13 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 13 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 13 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 13 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 13 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 13 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 13 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 13 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 13 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 13 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 13 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 13 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 13 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 13 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 13 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 13 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 13 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 13 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 13 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 13 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 13 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 13 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 13 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 13 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 13 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 13 个支持集样本
早期圆锥角膜类别使用 9 个支持集样本
警告: 没有足够的有效类别进行3-way任务采样，尝试调整参数...
类别样本数量: {0: 33, 1: 5, 2: 29}
调整后的参数: n_shot=2, n_query=3
类别 normal (标签 0): 33个样本
类别 e-kc (标签 1): 5个样本
类别 kc (标签 2): 29个样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
Epoch 4: Train Loss: 1.7650, Train Acc: 0.7304, Val Loss: 2.5241, Val Acc: 0.4333
验证准确率未提高，当前耐心值: 3/3
早停触发，停止训练
警告: 没有足够的有效类别进行3-way任务采样，尝试调整参数...
类别样本数量: {0: 37, 1: 7, 2: 27}
调整后的参数: n_shot=3, n_query=4
类别 normal (标签 0): 37个样本
类别 e-kc (标签 1): 7个样本
类别 kc (标签 2): 27个样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本

测试结果:
总体准确率: 56.67%
类别准确率:
  kc: 53.33%
  e-kc: 100.00%
  normal: 16.67%
训练完成，结果已保存到 /home/<USER>/balanced_task_sampling/results_trial_20250524_002200

STDERR:
+ TIMESTAMP=20250524_002200
+ RESULT_DIR=/home/<USER>/balanced_task_sampling/results_trial_20250524_002200
+ '[' -n /home/<USER>/balanced_task_sampling/results_trial_20250524_002200/main_training_trial_37_20250524_002200.log ']'
+ LOG_FILE=/home/<USER>/balanced_task_sampling/results_trial_20250524_002200/main_training_trial_37_20250524_002200.log
++ dirname /home/<USER>/balanced_task_sampling/results_trial_20250524_002200/main_training_trial_37_20250524_002200.log
+ mkdir -p /home/<USER>/balanced_task_sampling/results_trial_20250524_002200
+ mkdir -p /home/<USER>/balanced_task_sampling/results_trial_20250524_002200
+ echo '===================== 训练配置 ====================='
+ echo 'GPU: 0'
+ echo '输出目录: /home/<USER>/balanced_task_sampling/results_trial_20250524_002200'
+ echo '日志文件: /home/<USER>/balanced_task_sampling/results_trial_20250524_002200/main_training_trial_37_20250524_002200.log'
+ echo ''
+ echo '--------- 优化参数 ---------'
+ echo '内部学习率: 0.347389'
+ echo '训练轮数: 50'
+ echo '每轮任务数: 30'
+ echo ''
+ echo '--------- 模型参数 ---------'
+ echo '使用类别权重: 圆锥角膜=7.087643637934322, 早期圆锥角膜=6.585738354514965'
+ echo '温度参数: 0.03471062585435967'
+ tee -a /home/<USER>/balanced_task_sampling/results_trial_20250524_002200/main_training_trial_37_20250524_002200.log
+ echo '硬样本挖掘比例: 0.5206957940391875'
+ echo '对比学习权重: 0.5556355200422206'
+ echo '=============================================
'
+ validate_params
+ local error_count=0
++ dirname /home/<USER>/balanced_task_sampling/results_trial_20250524_002200/main_training_trial_37_20250524_002200.log
++ dirname /home/<USER>/balanced_task_sampling/split_result/train_set.csv
+ for dir in "$RESULT_DIR" "$(dirname "$LOG_FILE")" "$(dirname "$TRAIN_CSV")"
+ mkdir -p /home/<USER>/balanced_task_sampling/results_trial_20250524_002200
+ for dir in "$RESULT_DIR" "$(dirname "$LOG_FILE")" "$(dirname "$TRAIN_CSV")"
+ mkdir -p /home/<USER>/balanced_task_sampling/results_trial_20250524_002200
+ for dir in "$RESULT_DIR" "$(dirname "$LOG_FILE")" "$(dirname "$TRAIN_CSV")"
+ mkdir -p /home/<USER>/balanced_task_sampling/split_result
+ for csv in "$TRAIN_CSV" "$VAL_CSV" "$TEST_CSV"
+ '[' '!' -f /home/<USER>/balanced_task_sampling/split_result/train_set.csv ']'
+ for csv in "$TRAIN_CSV" "$VAL_CSV" "$TEST_CSV"
+ '[' '!' -f /home/<USER>/balanced_task_sampling/split_result/val_set.csv ']'
+ for csv in "$TRAIN_CSV" "$VAL_CSV" "$TEST_CSV"
+ '[' '!' -f /home/<USER>/balanced_task_sampling/split_result/test_set.csv ']'
+ [[ 0.0002 =~ ^[0-9.]+$ ]]
+ [[ 0.347389 =~ ^[0-9.]+$ ]]
+ '[' 0 -gt 0 ']'
+ return 0
+ '[' false = true ']'
+ export CUDA_VISIBLE_DEVICES=0
+ CUDA_VISIBLE_DEVICES=0
+ export OMP_NUM_THREADS=1
+ OMP_NUM_THREADS=1
+ export MKL_NUM_THREADS=1
+ MKL_NUM_THREADS=1
+ export MPLBACKEND=Agg
+ MPLBACKEND=Agg
+ mkdir -p /home/<USER>/balanced_task_sampling/results_trial_20250524_002200
+ echo '创建目录: /home/<USER>/balanced_task_sampling/results_trial_20250524_002200'
+ ls -l /home/<USER>/balanced_task_sampling/results_trial_20250524_002200
+ PRETRAINED_ARGS=
+ '[' true = true ']'
+ PRETRAINED_ARGS=--pretrained
+ AUGMENT_ARGS=--no_augmentation
+ CONTRASTIVE_ARGS=
+ '[' true = true ']'
+ CONTRASTIVE_ARGS='--use_contrastive --contrastive_weight 0.5556355200422206 --temperature 0.03471062585435967 --hard_mining_ratio 0.5206957940391875 --early_normal_weight 1.3152958314226866 --kc_normal_weight 1.0'
+ NORMALIZE_ARGS=
+ '[' false = true ']'
+ SAMPLER_ARGS=
+ '[' true = true ']'
+ SAMPLER_ARGS=' --use_separate_test_sampler'
+ '[' true = true ']'
+ SAMPLER_ARGS=' --use_separate_test_sampler --use_balanced_task_sampler --kc_shot_multiplier 2.7418975584719982 --early_kc_shot_multiplier 1.8483848880236557'
+ LR_SCHEDULER_ARGS=
+ '[' true = true ']'
+ LR_SCHEDULER_ARGS='--use_lr_scheduler --scheduler_type plateau'
+ '[' plateau = onecycle ']'
+ '[' plateau = plateau ']'
+ LR_SCHEDULER_ARGS='--use_lr_scheduler --scheduler_type plateau --plateau_factor 0.5 --plateau_patience 2 --min_lr 1e-6'
+ BASE_CMD='python train_balanced_task_sampling.py   --train_csv /home/<USER>/balanced_task_sampling/split_result/train_set.csv   --val_csv /home/<USER>/balanced_task_sampling/split_result/val_set.csv   --test_csv /home/<USER>/balanced_task_sampling/split_result/test_set.csv   --epochs 50   --lr 0.0002   --weight_decay 5e-4   --feature_dim 512   --save_dir /home/<USER>/balanced_task_sampling/results_trial_20250524_002200   --proto_counts 1,6,3   --pretrained   --inner_lr 0.347389   --inner_steps 5   --n_way 3   --n_shot 5   --n_query 15   --tasks_per_epoch 30   --meta_batch_size 8   --no_augmentation   --mixup_alpha 0.0   --cutmix_prob 0.0   --num_workers 4   --augment_factor 1   --kc_augment_factor 1   --seed 42   --early_kc_weight 6.585738354514965   --kc_weight 7.087643637934322   --focal_gamma 1.5017237734497955   --val_frequency 1   --early_stopping 3   --dropout 0.5    --use_separate_test_sampler --use_balanced_task_sampler --kc_shot_multiplier 2.7418975584719982 --early_kc_shot_multiplier 1.8483848880236557   --use_contrastive --contrastive_weight 0.5556355200422206 --temperature 0.03471062585435967 --hard_mining_ratio 0.5206957940391875 --early_normal_weight 1.3152958314226866 --kc_normal_weight 1.0      --use_lr_scheduler --scheduler_type plateau --plateau_factor 0.5 --plateau_patience 2 --min_lr 1e-6'
+ echo 开始无数据增强的平衡任务采样训练...
+ echo '使用GPU: 0'
+ '[' direct = nohup ']'
+ echo 直接运行训练脚本...
+ eval python train_balanced_task_sampling.py --train_csv /home/<USER>/balanced_task_sampling/split_result/train_set.csv --val_csv /home/<USER>/balanced_task_sampling/split_result/val_set.csv --test_csv /home/<USER>/balanced_task_sampling/split_result/test_set.csv --epochs 50 --lr 0.0002 --weight_decay 5e-4 --feature_dim 512 --save_dir /home/<USER>/balanced_task_sampling/results_trial_20250524_002200 --proto_counts 1,6,3 --pretrained --inner_lr 0.347389 --inner_steps 5 --n_way 3 --n_shot 5 --n_query 15 --tasks_per_epoch 30 --meta_batch_size 8 --no_augmentation --mixup_alpha 0.0 --cutmix_prob 0.0 --num_workers 4 --augment_factor 1 --kc_augment_factor 1 --seed 42 --early_kc_weight 6.585738354514965 --kc_weight 7.087643637934322 --focal_gamma 1.5017237734497955 --val_frequency 1 --early_stopping 3 --dropout 0.5 --use_separate_test_sampler --use_balanced_task_sampler --kc_shot_multiplier 2.7418975584719982 --early_kc_shot_multiplier 1.8483848880236557 --use_contrastive --contrastive_weight 0.5556355200422206 --temperature 0.03471062585435967 --hard_mining_ratio 0.5206957940391875 --early_normal_weight 1.3152958314226866 --kc_normal_weight 1.0 --use_lr_scheduler --scheduler_type plateau --plateau_factor 0.5 --plateau_patience 2 --min_lr 1e-6
++ python train_balanced_task_sampling.py --train_csv /home/<USER>/balanced_task_sampling/split_result/train_set.csv --val_csv /home/<USER>/balanced_task_sampling/split_result/val_set.csv --test_csv /home/<USER>/balanced_task_sampling/split_result/test_set.csv --epochs 50 --lr 0.0002 --weight_decay 5e-4 --feature_dim 512 --save_dir /home/<USER>/balanced_task_sampling/results_trial_20250524_002200 --proto_counts 1,6,3 --pretrained --inner_lr 0.347389 --inner_steps 5 --n_way 3 --n_shot 5 --n_query 15 --tasks_per_epoch 30 --meta_batch_size 8 --no_augmentation --mixup_alpha 0.0 --cutmix_prob 0.0 --num_workers 4 --augment_factor 1 --kc_augment_factor 1 --seed 42 --early_kc_weight 6.585738354514965 --kc_weight 7.087643637934322 --focal_gamma 1.5017237734497955 --val_frequency 1 --early_stopping 3 --dropout 0.5 --use_separate_test_sampler --use_balanced_task_sampler --kc_shot_multiplier 2.7418975584719982 --early_kc_shot_multiplier 1.8483848880236557 --use_contrastive --contrastive_weight 0.5556355200422206 --temperature 0.03471062585435967 --hard_mining_ratio 0.5206957940391875 --early_normal_weight 1.3152958314226866 --kc_normal_weight 1.0 --use_lr_scheduler --scheduler_type plateau --plateau_factor 0.5 --plateau_patience 2 --min_lr 1e-6
/home/<USER>/anaconda3/envs/kc1/lib/python3.10/site-packages/torchvision/models/_utils.py:208: UserWarning: The parameter 'pretrained' is deprecated since 0.13 and may be removed in the future, please use 'weights' instead.
  warnings.warn(
/home/<USER>/anaconda3/envs/kc1/lib/python3.10/site-packages/torchvision/models/_utils.py:223: UserWarning: Arguments other than a weight enum or `None` for 'weights' are deprecated since 0.13 and may be removed in the future. The current behavior is equivalent to passing `weights=ResNet34_Weights.IMAGENET1K_V1`. You can also use `weights=ResNet34_Weights.DEFAULT` to get the most up-to-date weights.
  warnings.warn(msg)
/home/<USER>/anaconda3/envs/kc1/lib/python3.10/site-packages/torch/optim/lr_scheduler.py:62: UserWarning: The verbose parameter is deprecated. Please use get_last_lr() to access the learning rate.
  warnings.warn(

Training tasks:   0%|          | 0/30 [00:00<?, ?it/s]
Training tasks:   3%|▎         | 1/30 [00:05<02:30,  5.18s/it]
Training tasks:   7%|▋         | 2/30 [00:06<01:25,  3.07s/it]
Training tasks:  10%|█         | 3/30 [00:08<01:04,  2.38s/it]
Training tasks:  13%|█▎        | 4/30 [00:09<00:52,  2.03s/it]
Training tasks:  17%|█▋        | 5/30 [00:11<00:46,  1.87s/it]
Training tasks:  20%|██        | 6/30 [00:12<00:42,  1.76s/it]
Training tasks:  23%|██▎       | 7/30 [00:14<00:37,  1.65s/it]
Training tasks:  27%|██▋       | 8/30 [00:15<00:33,  1.52s/it]
Training tasks:  30%|███       | 9/30 [00:16<00:29,  1.43s/it]
Training tasks:  33%|███▎      | 10/30 [00:18<00:27,  1.36s/it]
Training tasks:  37%|███▋      | 11/30 [00:19<00:25,  1.32s/it]
Training tasks:  40%|████      | 12/30 [00:20<00:23,  1.30s/it]
Training tasks:  43%|████▎     | 13/30 [00:21<00:21,  1.28s/it]
Training tasks:  47%|████▋     | 14/30 [00:23<00:20,  1.27s/it]
Training tasks:  50%|█████     | 15/30 [00:24<00:19,  1.27s/it]
Training tasks:  53%|█████▎    | 16/30 [00:25<00:18,  1.30s/it]
Training tasks:  57%|█████▋    | 17/30 [00:27<00:17,  1.34s/it]
Training tasks:  60%|██████    | 18/30 [00:28<00:16,  1.36s/it]
Training tasks:  63%|██████▎   | 19/30 [00:29<00:15,  1.39s/it]
Training tasks:  67%|██████▋   | 20/30 [00:31<00:13,  1.38s/it]
Training tasks:  70%|███████   | 21/30 [00:32<00:11,  1.32s/it]
Training tasks:  73%|███████▎  | 22/30 [00:33<00:10,  1.29s/it]
Training tasks:  77%|███████▋  | 23/30 [00:35<00:09,  1.29s/it]
Training tasks:  80%|████████  | 24/30 [00:36<00:07,  1.30s/it]
Training tasks:  83%|████████▎ | 25/30 [00:37<00:06,  1.32s/it]
Training tasks:  87%|████████▋ | 26/30 [00:39<00:05,  1.33s/it]
Training tasks:  90%|█████████ | 27/30 [00:40<00:04,  1.34s/it]
Training tasks:  93%|█████████▎| 28/30 [00:41<00:02,  1.37s/it]
Training tasks:  97%|█████████▋| 29/30 [00:43<00:01,  1.40s/it]
Training tasks: 100%|██████████| 30/30 [00:44<00:00,  1.45s/it]
Training tasks: 100%|██████████| 30/30 [00:44<00:00,  1.50s/it]

Validating tasks:   0%|          | 0/10 [00:00<?, ?it/s]
Validating tasks:  10%|█         | 1/10 [00:00<00:02,  3.88it/s]
Validating tasks:  20%|██        | 2/10 [00:00<00:01,  4.25it/s]
Validating tasks:  30%|███       | 3/10 [00:00<00:01,  4.49it/s]
Validating tasks:  40%|████      | 4/10 [00:00<00:01,  4.85it/s]
Validating tasks:  50%|█████     | 5/10 [00:01<00:00,  5.17it/s]
Validating tasks:  60%|██████    | 6/10 [00:01<00:00,  5.04it/s]
Validating tasks:  70%|███████   | 7/10 [00:01<00:00,  4.92it/s]
Validating tasks:  80%|████████  | 8/10 [00:01<00:00,  4.93it/s]
Validating tasks:  90%|█████████ | 9/10 [00:01<00:00,  4.84it/s]
Validating tasks: 100%|██████████| 10/10 [00:02<00:00,  5.02it/s]
Validating tasks: 100%|██████████| 10/10 [00:02<00:00,  4.86it/s]

Training tasks:   0%|          | 0/30 [00:00<?, ?it/s]
Training tasks:   3%|▎         | 1/30 [00:01<00:42,  1.47s/it]
Training tasks:   7%|▋         | 2/30 [00:02<00:40,  1.45s/it]
Training tasks:  10%|█         | 3/30 [00:04<00:38,  1.41s/it]
Training tasks:  13%|█▎        | 4/30 [00:05<00:38,  1.48s/it]
Training tasks:  17%|█▋        | 5/30 [00:07<00:35,  1.43s/it]
Training tasks:  20%|██        | 6/30 [00:08<00:33,  1.41s/it]
Training tasks:  23%|██▎       | 7/30 [00:09<00:32,  1.39s/it]
Training tasks:  27%|██▋       | 8/30 [00:11<00:30,  1.40s/it]
Training tasks:  30%|███       | 9/30 [00:12<00:29,  1.41s/it]
Training tasks:  33%|███▎      | 10/30 [00:14<00:28,  1.44s/it]
Training tasks:  37%|███▋      | 11/30 [00:15<00:27,  1.45s/it]
Training tasks:  40%|████      | 12/30 [00:17<00:26,  1.47s/it]
Training tasks:  43%|████▎     | 13/30 [00:18<00:24,  1.43s/it]
Training tasks:  47%|████▋     | 14/30 [00:20<00:22,  1.43s/it]
Training tasks:  50%|█████     | 15/30 [00:21<00:21,  1.41s/it]
Training tasks:  53%|█████▎    | 16/30 [00:22<00:19,  1.41s/it]
Training tasks:  57%|█████▋    | 17/30 [00:24<00:18,  1.39s/it]
Training tasks:  60%|██████    | 18/30 [00:25<00:17,  1.42s/it]
Training tasks:  63%|██████▎   | 19/30 [00:27<00:15,  1.45s/it]
Training tasks:  67%|██████▋   | 20/30 [00:28<00:14,  1.44s/it]
Training tasks:  70%|███████   | 21/30 [00:29<00:12,  1.37s/it]
Training tasks:  73%|███████▎  | 22/30 [00:31<00:10,  1.33s/it]
Training tasks:  77%|███████▋  | 23/30 [00:32<00:09,  1.37s/it]
Training tasks:  80%|████████  | 24/30 [00:33<00:08,  1.41s/it]
Training tasks:  83%|████████▎ | 25/30 [00:35<00:07,  1.44s/it]
Training tasks:  87%|████████▋ | 26/30 [00:36<00:05,  1.44s/it]
Training tasks:  90%|█████████ | 27/30 [00:38<00:04,  1.43s/it]
Training tasks:  93%|█████████▎| 28/30 [00:39<00:02,  1.37s/it]
Training tasks:  97%|█████████▋| 29/30 [00:40<00:01,  1.32s/it]
Training tasks: 100%|██████████| 30/30 [00:41<00:00,  1.29s/it]
Training tasks: 100%|██████████| 30/30 [00:41<00:00,  1.40s/it]

Validating tasks:   0%|          | 0/10 [00:00<?, ?it/s]
Validating tasks:  10%|█         | 1/10 [00:00<00:01,  5.28it/s]
Validating tasks:  20%|██        | 2/10 [00:00<00:01,  5.63it/s]
Validating tasks:  30%|███       | 3/10 [00:00<00:01,  5.47it/s]
Validating tasks:  40%|████      | 4/10 [00:00<00:01,  5.33it/s]
Validating tasks:  50%|█████     | 5/10 [00:00<00:00,  5.28it/s]
Validating tasks:  60%|██████    | 6/10 [00:01<00:00,  5.34it/s]
Validating tasks:  70%|███████   | 7/10 [00:01<00:00,  5.25it/s]
Validating tasks:  80%|████████  | 8/10 [00:01<00:00,  5.28it/s]
Validating tasks:  90%|█████████ | 9/10 [00:01<00:00,  5.16it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  5.20it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  5.27it/s]

Training tasks:   0%|          | 0/30 [00:00<?, ?it/s]
Training tasks:   3%|▎         | 1/30 [00:01<00:44,  1.54s/it]
Training tasks:   7%|▋         | 2/30 [00:03<00:43,  1.56s/it]
Training tasks:  10%|█         | 3/30 [00:04<00:40,  1.50s/it]
Training tasks:  13%|█▎        | 4/30 [00:06<00:38,  1.49s/it]
Training tasks:  17%|█▋        | 5/30 [00:07<00:37,  1.51s/it]
Training tasks:  20%|██        | 6/30 [00:08<00:35,  1.47s/it]
Training tasks:  23%|██▎       | 7/30 [00:10<00:33,  1.43s/it]
Training tasks:  27%|██▋       | 8/30 [00:11<00:31,  1.43s/it]
Training tasks:  30%|███       | 9/30 [00:13<00:29,  1.43s/it]
Training tasks:  33%|███▎      | 10/30 [00:14<00:28,  1.41s/it]
Training tasks:  37%|███▋      | 11/30 [00:16<00:27,  1.43s/it]
Training tasks:  40%|████      | 12/30 [00:17<00:25,  1.43s/it]
Training tasks:  43%|████▎     | 13/30 [00:18<00:24,  1.44s/it]
Training tasks:  47%|████▋     | 14/30 [00:20<00:22,  1.42s/it]
Training tasks:  50%|█████     | 15/30 [00:21<00:21,  1.44s/it]
Training tasks:  53%|█████▎    | 16/30 [00:23<00:20,  1.44s/it]
Training tasks:  57%|█████▋    | 17/30 [00:24<00:18,  1.43s/it]
Training tasks:  60%|██████    | 18/30 [00:25<00:17,  1.42s/it]
Training tasks:  63%|██████▎   | 19/30 [00:27<00:15,  1.40s/it]
Training tasks:  67%|██████▋   | 20/30 [00:28<00:13,  1.39s/it]
Training tasks:  70%|███████   | 21/30 [00:30<00:12,  1.37s/it]
Training tasks:  73%|███████▎  | 22/30 [00:31<00:10,  1.37s/it]
Training tasks:  77%|███████▋  | 23/30 [00:32<00:09,  1.37s/it]
Training tasks:  80%|████████  | 24/30 [00:34<00:08,  1.38s/it]
Training tasks:  83%|████████▎ | 25/30 [00:35<00:06,  1.38s/it]
Training tasks:  87%|████████▋ | 26/30 [00:36<00:05,  1.39s/it]
Training tasks:  90%|█████████ | 27/30 [00:38<00:04,  1.40s/it]
Training tasks:  93%|█████████▎| 28/30 [00:39<00:02,  1.29s/it]
Training tasks:  97%|█████████▋| 29/30 [00:40<00:01,  1.25s/it]
Training tasks: 100%|██████████| 30/30 [00:42<00:00,  1.34s/it]
Training tasks: 100%|██████████| 30/30 [00:42<00:00,  1.41s/it]

Validating tasks:   0%|          | 0/10 [00:00<?, ?it/s]
Validating tasks:  10%|█         | 1/10 [00:00<00:01,  5.04it/s]
Validating tasks:  20%|██        | 2/10 [00:00<00:01,  4.97it/s]
Validating tasks:  30%|███       | 3/10 [00:00<00:01,  5.01it/s]
Validating tasks:  40%|████      | 4/10 [00:00<00:01,  5.09it/s]
Validating tasks:  50%|█████     | 5/10 [00:00<00:00,  5.02it/s]
Validating tasks:  60%|██████    | 6/10 [00:01<00:00,  4.86it/s]
Validating tasks:  70%|███████   | 7/10 [00:01<00:00,  5.26it/s]
Validating tasks:  80%|████████  | 8/10 [00:01<00:00,  5.11it/s]
Validating tasks:  90%|█████████ | 9/10 [00:01<00:00,  5.16it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  5.16it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  5.09it/s]

Training tasks:   0%|          | 0/30 [00:00<?, ?it/s]
Training tasks:   3%|▎         | 1/30 [00:01<00:42,  1.48s/it]
Training tasks:   7%|▋         | 2/30 [00:02<00:39,  1.42s/it]
Training tasks:  10%|█         | 3/30 [00:04<00:38,  1.43s/it]
Training tasks:  13%|█▎        | 4/30 [00:05<00:35,  1.38s/it]
Training tasks:  17%|█▋        | 5/30 [00:06<00:32,  1.29s/it]
Training tasks:  20%|██        | 6/30 [00:07<00:29,  1.21s/it]
Training tasks:  23%|██▎       | 7/30 [00:09<00:29,  1.29s/it]
Training tasks:  27%|██▋       | 8/30 [00:10<00:29,  1.35s/it]
Training tasks:  30%|███       | 9/30 [00:12<00:29,  1.39s/it]
Training tasks:  33%|███▎      | 10/30 [00:13<00:28,  1.43s/it]
Training tasks:  37%|███▋      | 11/30 [00:15<00:27,  1.43s/it]
Training tasks:  40%|████      | 12/30 [00:16<00:26,  1.45s/it]
Training tasks:  43%|████▎     | 13/30 [00:18<00:24,  1.44s/it]
Training tasks:  47%|████▋     | 14/30 [00:19<00:22,  1.42s/it]
Training tasks:  50%|█████     | 15/30 [00:20<00:20,  1.39s/it]
Training tasks:  53%|█████▎    | 16/30 [00:22<00:19,  1.41s/it]
Training tasks:  57%|█████▋    | 17/30 [00:23<00:18,  1.40s/it]
Training tasks:  60%|██████    | 18/30 [00:24<00:16,  1.37s/it]
Training tasks:  63%|██████▎   | 19/30 [00:26<00:14,  1.36s/it]
Training tasks:  67%|██████▋   | 20/30 [00:27<00:13,  1.38s/it]
Training tasks:  70%|███████   | 21/30 [00:29<00:12,  1.38s/it]
Training tasks:  73%|███████▎  | 22/30 [00:30<00:11,  1.43s/it]
Training tasks:  77%|███████▋  | 23/30 [00:31<00:09,  1.33s/it]
Training tasks:  80%|████████  | 24/30 [00:33<00:08,  1.35s/it]
Training tasks:  83%|████████▎ | 25/30 [00:34<00:06,  1.36s/it]
Training tasks:  87%|████████▋ | 26/30 [00:35<00:05,  1.36s/it]
Training tasks:  90%|█████████ | 27/30 [00:37<00:04,  1.40s/it]
Training tasks:  93%|█████████▎| 28/30 [00:38<00:02,  1.42s/it]
Training tasks:  97%|█████████▋| 29/30 [00:40<00:01,  1.40s/it]
Training tasks: 100%|██████████| 30/30 [00:41<00:00,  1.39s/it]
Training tasks: 100%|██████████| 30/30 [00:41<00:00,  1.38s/it]

Validating tasks:   0%|          | 0/10 [00:00<?, ?it/s]
Validating tasks:  10%|█         | 1/10 [00:00<00:01,  8.23it/s]
Validating tasks:  20%|██        | 2/10 [00:00<00:01,  6.36it/s]
Validating tasks:  30%|███       | 3/10 [00:00<00:01,  5.91it/s]
Validating tasks:  40%|████      | 4/10 [00:00<00:01,  5.63it/s]
Validating tasks:  50%|█████     | 5/10 [00:00<00:00,  5.83it/s]
Validating tasks:  60%|██████    | 6/10 [00:01<00:00,  5.70it/s]
Validating tasks:  70%|███████   | 7/10 [00:01<00:00,  5.58it/s]
Validating tasks:  80%|████████  | 8/10 [00:01<00:00,  5.56it/s]
Validating tasks:  90%|█████████ | 9/10 [00:01<00:00,  5.41it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  5.33it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  5.62it/s]

Testing tasks:   0%|          | 0/30 [00:00<?, ?it/s]
Testing tasks:   3%|▎         | 1/30 [00:00<00:08,  3.44it/s]
Testing tasks:   7%|▋         | 2/30 [00:00<00:07,  3.51it/s]
Testing tasks:  10%|█         | 3/30 [00:00<00:07,  3.49it/s]
Testing tasks:  13%|█▎        | 4/30 [00:01<00:07,  3.49it/s]
Testing tasks:  17%|█▋        | 5/30 [00:01<00:06,  3.59it/s]
Testing tasks:  20%|██        | 6/30 [00:01<00:06,  3.67it/s]
Testing tasks:  23%|██▎       | 7/30 [00:01<00:06,  3.58it/s]
Testing tasks:  27%|██▋       | 8/30 [00:02<00:05,  3.67it/s]
Testing tasks:  30%|███       | 9/30 [00:02<00:05,  3.71it/s]
Testing tasks:  33%|███▎      | 10/30 [00:02<00:05,  3.72it/s]
Testing tasks:  37%|███▋      | 11/30 [00:03<00:05,  3.72it/s]
Testing tasks:  40%|████      | 12/30 [00:03<00:04,  3.66it/s]
Testing tasks:  43%|████▎     | 13/30 [00:03<00:04,  3.67it/s]
Testing tasks:  47%|████▋     | 14/30 [00:03<00:04,  3.67it/s]
Testing tasks:  50%|█████     | 15/30 [00:04<00:04,  3.63it/s]
Testing tasks:  53%|█████▎    | 16/30 [00:04<00:03,  3.57it/s]
Testing tasks:  57%|█████▋    | 17/30 [00:04<00:03,  3.55it/s]
Testing tasks:  60%|██████    | 18/30 [00:04<00:03,  3.53it/s]
Testing tasks:  63%|██████▎   | 19/30 [00:05<00:03,  3.53it/s]
Testing tasks:  67%|██████▋   | 20/30 [00:05<00:02,  3.46it/s]
Testing tasks:  70%|███████   | 21/30 [00:05<00:02,  3.49it/s]
Testing tasks:  73%|███████▎  | 22/30 [00:06<00:02,  3.53it/s]
Testing tasks:  77%|███████▋  | 23/30 [00:06<00:02,  3.48it/s]
Testing tasks:  80%|████████  | 24/30 [00:06<00:01,  3.74it/s]
Testing tasks:  83%|████████▎ | 25/30 [00:06<00:01,  3.76it/s]
Testing tasks:  87%|████████▋ | 26/30 [00:07<00:01,  3.87it/s]
Testing tasks:  90%|█████████ | 27/30 [00:07<00:00,  4.09it/s]
Testing tasks:  93%|█████████▎| 28/30 [00:07<00:00,  4.29it/s]
Testing tasks:  97%|█████████▋| 29/30 [00:07<00:00,  4.16it/s]
Testing tasks: 100%|██████████| 30/30 [00:08<00:00,  4.11it/s]
Testing tasks: 100%|██████████| 30/30 [00:08<00:00,  3.71it/s]

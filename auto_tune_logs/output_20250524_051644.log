STDOUT:
===================== 训练配置 =====================
GPU: 0
输出目录: /home/<USER>/balanced_task_sampling/results_trial_20250524_051644
日志文件: /home/<USER>/balanced_task_sampling/results_trial_20250524_051644/main_training_trial_94_20250524_051644.log

--------- 优化参数 ---------
内部学习率: 0.347389
训练轮数: 50
每轮任务数: 30

--------- 模型参数 ---------
使用类别权重: 圆锥角膜=6.006318184512451, 早期圆锥角膜=6.913861712699548
温度参数: 0.09455232625927315
硬样本挖掘比例: 0.48119203841747477
对比学习权重: 0.3255527903317712
=============================================

创建目录: /home/<USER>/balanced_task_sampling/results_trial_20250524_051644
总用量 4
-rw-rw-r-- 1 <USER> <GROUP> 658 5月  24 05:16 main_training_trial_94_20250524_051644.log
开始无数据增强的平衡任务采样训练...
使用GPU: 0
直接运行训练脚本...
成功导入增强版特征提取器
成功导入归一化特征提取器
成功导入增强版损失函数
成功导入增强版MAML模型
成功导入KC特定增强模块
成功导入增强版GPU数据增强模块
使用设备: cuda
禁用所有数据增强，直接加载原始数据集...
使用平衡任务采样器
类别 normal (标签 0): 164个样本
类别 e-kc (标签 1): 26个样本
类别 kc (标签 2): 133个样本
使用原型配置: [4, 6, 4]
使用增强版特征提取器，dropout率: 0.5
使用增强版MAML-ProtoNet模型，内循环学习率: 0.347389, 内循环步数: 5
使用ReduceLROnPlateau学习率调度器，因子: 0.5, 耐心值: 2
开始训练，共50个epoch
Epoch 1/50
当前学习率: 0.000200
使用类别权重: 正常=1.0, 早期圆锥角膜=6.913861712699548, 圆锥角膜=6.006318184512451
使用增强版对比损失函数，温度: 0.09455232625927315, 硬负样本挖掘比例: 0.48119203841747477, 早期圆锥角膜与正常样本对比权重: 1.111536341915452, 圆锥角膜与正常样本对比权重: 1.0
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
警告: 没有足够的有效类别进行3-way任务采样，尝试调整参数...
类别样本数量: {0: 33, 1: 5, 2: 29}
调整后的参数: n_shot=2, n_query=3
类别 normal (标签 0): 33个样本
类别 e-kc (标签 1): 5个样本
类别 kc (标签 2): 29个样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
Epoch 1: Train Loss: 2.6477, Train Acc: 0.8052, Val Loss: 2.9402, Val Acc: 0.5889
保存最佳模型，验证准确率: 0.5889
Epoch 2/50
当前学习率: 0.000200
使用类别权重: 正常=1.0, 早期圆锥角膜=6.913861712699548, 圆锥角膜=6.006318184512451
使用增强版对比损失函数，温度: 0.09455232625927315, 硬负样本挖掘比例: 0.48119203841747477, 早期圆锥角膜与正常样本对比权重: 1.111536341915452, 圆锥角膜与正常样本对比权重: 1.0
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
警告: 没有足够的有效类别进行3-way任务采样，尝试调整参数...
类别样本数量: {0: 33, 1: 5, 2: 29}
调整后的参数: n_shot=2, n_query=3
类别 normal (标签 0): 33个样本
类别 e-kc (标签 1): 5个样本
类别 kc (标签 2): 29个样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
Epoch 2: Train Loss: 2.0079, Train Acc: 0.8926, Val Loss: 2.8394, Val Acc: 0.6778
保存最佳模型，验证准确率: 0.6778
Epoch 3/50
当前学习率: 0.000200
使用类别权重: 正常=1.0, 早期圆锥角膜=6.913861712699548, 圆锥角膜=6.006318184512451
使用增强版对比损失函数，温度: 0.09455232625927315, 硬负样本挖掘比例: 0.48119203841747477, 早期圆锥角膜与正常样本对比权重: 1.111536341915452, 圆锥角膜与正常样本对比权重: 1.0
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
警告: 没有足够的有效类别进行3-way任务采样，尝试调整参数...
类别样本数量: {0: 33, 1: 5, 2: 29}
调整后的参数: n_shot=2, n_query=3
类别 normal (标签 0): 33个样本
类别 e-kc (标签 1): 5个样本
类别 kc (标签 2): 29个样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
Epoch 3: Train Loss: 1.8832, Train Acc: 0.9185, Val Loss: 3.0038, Val Acc: 0.4333
验证准确率未提高，当前耐心值: 1/3
Epoch 4/50
当前学习率: 0.000200
使用类别权重: 正常=1.0, 早期圆锥角膜=6.913861712699548, 圆锥角膜=6.006318184512451
使用增强版对比损失函数，温度: 0.09455232625927315, 硬负样本挖掘比例: 0.48119203841747477, 早期圆锥角膜与正常样本对比权重: 1.111536341915452, 圆锥角膜与正常样本对比权重: 1.0
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
警告: 没有足够的有效类别进行3-way任务采样，尝试调整参数...
类别样本数量: {0: 33, 1: 5, 2: 29}
调整后的参数: n_shot=2, n_query=3
类别 normal (标签 0): 33个样本
类别 e-kc (标签 1): 5个样本
类别 kc (标签 2): 29个样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
Epoch 4: Train Loss: 1.9038, Train Acc: 0.9185, Val Loss: 3.1589, Val Acc: 0.5111
验证准确率未提高，当前耐心值: 2/3
Epoch 5/50
当前学习率: 0.000200
使用类别权重: 正常=1.0, 早期圆锥角膜=6.913861712699548, 圆锥角膜=6.006318184512451
使用增强版对比损失函数，温度: 0.09455232625927315, 硬负样本挖掘比例: 0.48119203841747477, 早期圆锥角膜与正常样本对比权重: 1.111536341915452, 圆锥角膜与正常样本对比权重: 1.0
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
警告: 没有足够的有效类别进行3-way任务采样，尝试调整参数...
类别样本数量: {0: 33, 1: 5, 2: 29}
调整后的参数: n_shot=2, n_query=3
类别 normal (标签 0): 33个样本
类别 e-kc (标签 1): 5个样本
类别 kc (标签 2): 29个样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
Epoch 5: Train Loss: 1.8179, Train Acc: 0.9296, Val Loss: 2.6836, Val Acc: 0.7222
保存最佳模型，验证准确率: 0.7222
Epoch 6/50
当前学习率: 0.000200
使用类别权重: 正常=1.0, 早期圆锥角膜=6.913861712699548, 圆锥角膜=6.006318184512451
使用增强版对比损失函数，温度: 0.09455232625927315, 硬负样本挖掘比例: 0.48119203841747477, 早期圆锥角膜与正常样本对比权重: 1.111536341915452, 圆锥角膜与正常样本对比权重: 1.0
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
警告: 没有足够的有效类别进行3-way任务采样，尝试调整参数...
类别样本数量: {0: 33, 1: 5, 2: 29}
调整后的参数: n_shot=2, n_query=3
类别 normal (标签 0): 33个样本
类别 e-kc (标签 1): 5个样本
类别 kc (标签 2): 29个样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
Epoch 6: Train Loss: 1.7448, Train Acc: 0.9481, Val Loss: 2.5494, Val Acc: 0.7222
保存最佳模型，验证准确率: 0.7222
Epoch 7/50
当前学习率: 0.000200
使用类别权重: 正常=1.0, 早期圆锥角膜=6.913861712699548, 圆锥角膜=6.006318184512451
使用增强版对比损失函数，温度: 0.09455232625927315, 硬负样本挖掘比例: 0.48119203841747477, 早期圆锥角膜与正常样本对比权重: 1.111536341915452, 圆锥角膜与正常样本对比权重: 1.0
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
警告: 没有足够的有效类别进行3-way任务采样，尝试调整参数...
类别样本数量: {0: 33, 1: 5, 2: 29}
调整后的参数: n_shot=2, n_query=3
类别 normal (标签 0): 33个样本
类别 e-kc (标签 1): 5个样本
类别 kc (标签 2): 29个样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
Epoch 7: Train Loss: 1.6920, Train Acc: 0.9467, Val Loss: 3.0432, Val Acc: 0.6000
验证准确率未提高，当前耐心值: 1/3
Epoch 8/50
当前学习率: 0.000200
使用类别权重: 正常=1.0, 早期圆锥角膜=6.913861712699548, 圆锥角膜=6.006318184512451
使用增强版对比损失函数，温度: 0.09455232625927315, 硬负样本挖掘比例: 0.48119203841747477, 早期圆锥角膜与正常样本对比权重: 1.111536341915452, 圆锥角膜与正常样本对比权重: 1.0
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
警告: 没有足够的有效类别进行3-way任务采样，尝试调整参数...
类别样本数量: {0: 33, 1: 5, 2: 29}
调整后的参数: n_shot=2, n_query=3
类别 normal (标签 0): 33个样本
类别 e-kc (标签 1): 5个样本
类别 kc (标签 2): 29个样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
Epoch 8: Train Loss: 1.5548, Train Acc: 0.9630, Val Loss: 2.9606, Val Acc: 0.6444
验证准确率未提高，当前耐心值: 2/3
Epoch 9/50
当前学习率: 0.000100
使用类别权重: 正常=1.0, 早期圆锥角膜=6.913861712699548, 圆锥角膜=6.006318184512451
使用增强版对比损失函数，温度: 0.09455232625927315, 硬负样本挖掘比例: 0.48119203841747477, 早期圆锥角膜与正常样本对比权重: 1.111536341915452, 圆锥角膜与正常样本对比权重: 1.0
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 8 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
警告: 没有足够的有效类别进行3-way任务采样，尝试调整参数...
类别样本数量: {0: 33, 1: 5, 2: 29}
调整后的参数: n_shot=2, n_query=3
类别 normal (标签 0): 33个样本
类别 e-kc (标签 1): 5个样本
类别 kc (标签 2): 29个样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
Epoch 9: Train Loss: 1.4482, Train Acc: 0.9830, Val Loss: 3.1655, Val Acc: 0.5556
验证准确率未提高，当前耐心值: 3/3
早停触发，停止训练
警告: 没有足够的有效类别进行3-way任务采样，尝试调整参数...
类别样本数量: {0: 37, 1: 7, 2: 27}
调整后的参数: n_shot=3, n_query=4
类别 normal (标签 0): 37个样本
类别 e-kc (标签 1): 7个样本
类别 kc (标签 2): 27个样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 5 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本

测试结果:
总体准确率: 60.56%
类别准确率:
  kc: 42.50%
  e-kc: 57.50%
  normal: 81.67%
训练完成，结果已保存到 /home/<USER>/balanced_task_sampling/results_trial_20250524_051644

STDERR:
+ TIMESTAMP=20250524_051644
+ RESULT_DIR=/home/<USER>/balanced_task_sampling/results_trial_20250524_051644
+ '[' -n /home/<USER>/balanced_task_sampling/results_trial_20250524_051644/main_training_trial_94_20250524_051644.log ']'
+ LOG_FILE=/home/<USER>/balanced_task_sampling/results_trial_20250524_051644/main_training_trial_94_20250524_051644.log
++ dirname /home/<USER>/balanced_task_sampling/results_trial_20250524_051644/main_training_trial_94_20250524_051644.log
+ mkdir -p /home/<USER>/balanced_task_sampling/results_trial_20250524_051644
+ mkdir -p /home/<USER>/balanced_task_sampling/results_trial_20250524_051644
+ echo '===================== 训练配置 ====================='
+ echo 'GPU: 0'
+ echo '输出目录: /home/<USER>/balanced_task_sampling/results_trial_20250524_051644'
+ tee -a /home/<USER>/balanced_task_sampling/results_trial_20250524_051644/main_training_trial_94_20250524_051644.log
+ echo '日志文件: /home/<USER>/balanced_task_sampling/results_trial_20250524_051644/main_training_trial_94_20250524_051644.log'
+ echo ''
+ echo '--------- 优化参数 ---------'
+ echo '内部学习率: 0.347389'
+ echo '训练轮数: 50'
+ echo '每轮任务数: 30'
+ echo ''
+ echo '--------- 模型参数 ---------'
+ echo '使用类别权重: 圆锥角膜=6.006318184512451, 早期圆锥角膜=6.913861712699548'
+ echo '温度参数: 0.09455232625927315'
+ echo '硬样本挖掘比例: 0.48119203841747477'
+ echo '对比学习权重: 0.3255527903317712'
+ echo '=============================================
'
+ validate_params
+ local error_count=0
++ dirname /home/<USER>/balanced_task_sampling/results_trial_20250524_051644/main_training_trial_94_20250524_051644.log
++ dirname /home/<USER>/balanced_task_sampling/split_result/train_set.csv
+ for dir in "$RESULT_DIR" "$(dirname "$LOG_FILE")" "$(dirname "$TRAIN_CSV")"
+ mkdir -p /home/<USER>/balanced_task_sampling/results_trial_20250524_051644
+ for dir in "$RESULT_DIR" "$(dirname "$LOG_FILE")" "$(dirname "$TRAIN_CSV")"
+ mkdir -p /home/<USER>/balanced_task_sampling/results_trial_20250524_051644
+ for dir in "$RESULT_DIR" "$(dirname "$LOG_FILE")" "$(dirname "$TRAIN_CSV")"
+ mkdir -p /home/<USER>/balanced_task_sampling/split_result
+ for csv in "$TRAIN_CSV" "$VAL_CSV" "$TEST_CSV"
+ '[' '!' -f /home/<USER>/balanced_task_sampling/split_result/train_set.csv ']'
+ for csv in "$TRAIN_CSV" "$VAL_CSV" "$TEST_CSV"
+ '[' '!' -f /home/<USER>/balanced_task_sampling/split_result/val_set.csv ']'
+ for csv in "$TRAIN_CSV" "$VAL_CSV" "$TEST_CSV"
+ '[' '!' -f /home/<USER>/balanced_task_sampling/split_result/test_set.csv ']'
+ [[ 0.0002 =~ ^[0-9.]+$ ]]
+ [[ 0.347389 =~ ^[0-9.]+$ ]]
+ '[' 0 -gt 0 ']'
+ return 0
+ '[' false = true ']'
+ export CUDA_VISIBLE_DEVICES=0
+ CUDA_VISIBLE_DEVICES=0
+ export OMP_NUM_THREADS=1
+ OMP_NUM_THREADS=1
+ export MKL_NUM_THREADS=1
+ MKL_NUM_THREADS=1
+ export MPLBACKEND=Agg
+ MPLBACKEND=Agg
+ mkdir -p /home/<USER>/balanced_task_sampling/results_trial_20250524_051644
+ echo '创建目录: /home/<USER>/balanced_task_sampling/results_trial_20250524_051644'
+ ls -l /home/<USER>/balanced_task_sampling/results_trial_20250524_051644
+ PRETRAINED_ARGS=
+ '[' true = true ']'
+ PRETRAINED_ARGS=--pretrained
+ AUGMENT_ARGS=--no_augmentation
+ CONTRASTIVE_ARGS=
+ '[' true = true ']'
+ CONTRASTIVE_ARGS='--use_contrastive --contrastive_weight 0.3255527903317712 --temperature 0.09455232625927315 --hard_mining_ratio 0.48119203841747477 --early_normal_weight 1.111536341915452 --kc_normal_weight 1.0'
+ NORMALIZE_ARGS=
+ '[' false = true ']'
+ SAMPLER_ARGS=
+ '[' true = true ']'
+ SAMPLER_ARGS=' --use_separate_test_sampler'
+ '[' true = true ']'
+ SAMPLER_ARGS=' --use_separate_test_sampler --use_balanced_task_sampler --kc_shot_multiplier 1.7616889919291359 --early_kc_shot_multiplier 1.2950106152241732'
+ LR_SCHEDULER_ARGS=
+ '[' true = true ']'
+ LR_SCHEDULER_ARGS='--use_lr_scheduler --scheduler_type plateau'
+ '[' plateau = onecycle ']'
+ '[' plateau = plateau ']'
+ LR_SCHEDULER_ARGS='--use_lr_scheduler --scheduler_type plateau --plateau_factor 0.5 --plateau_patience 2 --min_lr 1e-6'
+ BASE_CMD='python train_balanced_task_sampling.py   --train_csv /home/<USER>/balanced_task_sampling/split_result/train_set.csv   --val_csv /home/<USER>/balanced_task_sampling/split_result/val_set.csv   --test_csv /home/<USER>/balanced_task_sampling/split_result/test_set.csv   --epochs 50   --lr 0.0002   --weight_decay 5e-4   --feature_dim 512   --save_dir /home/<USER>/balanced_task_sampling/results_trial_20250524_051644   --proto_counts 4,6,4   --pretrained   --inner_lr 0.347389   --inner_steps 5   --n_way 3   --n_shot 5   --n_query 15   --tasks_per_epoch 30   --meta_batch_size 8   --no_augmentation   --mixup_alpha 0.0   --cutmix_prob 0.0   --num_workers 4   --augment_factor 1   --kc_augment_factor 1   --seed 42   --early_kc_weight 6.913861712699548   --kc_weight 6.006318184512451   --focal_gamma 1.0031528270061931   --val_frequency 1   --early_stopping 3   --dropout 0.5    --use_separate_test_sampler --use_balanced_task_sampler --kc_shot_multiplier 1.7616889919291359 --early_kc_shot_multiplier 1.2950106152241732   --use_contrastive --contrastive_weight 0.3255527903317712 --temperature 0.09455232625927315 --hard_mining_ratio 0.48119203841747477 --early_normal_weight 1.111536341915452 --kc_normal_weight 1.0      --use_lr_scheduler --scheduler_type plateau --plateau_factor 0.5 --plateau_patience 2 --min_lr 1e-6'
+ echo 开始无数据增强的平衡任务采样训练...
+ echo '使用GPU: 0'
+ '[' direct = nohup ']'
+ echo 直接运行训练脚本...
+ eval python train_balanced_task_sampling.py --train_csv /home/<USER>/balanced_task_sampling/split_result/train_set.csv --val_csv /home/<USER>/balanced_task_sampling/split_result/val_set.csv --test_csv /home/<USER>/balanced_task_sampling/split_result/test_set.csv --epochs 50 --lr 0.0002 --weight_decay 5e-4 --feature_dim 512 --save_dir /home/<USER>/balanced_task_sampling/results_trial_20250524_051644 --proto_counts 4,6,4 --pretrained --inner_lr 0.347389 --inner_steps 5 --n_way 3 --n_shot 5 --n_query 15 --tasks_per_epoch 30 --meta_batch_size 8 --no_augmentation --mixup_alpha 0.0 --cutmix_prob 0.0 --num_workers 4 --augment_factor 1 --kc_augment_factor 1 --seed 42 --early_kc_weight 6.913861712699548 --kc_weight 6.006318184512451 --focal_gamma 1.0031528270061931 --val_frequency 1 --early_stopping 3 --dropout 0.5 --use_separate_test_sampler --use_balanced_task_sampler --kc_shot_multiplier 1.7616889919291359 --early_kc_shot_multiplier 1.2950106152241732 --use_contrastive --contrastive_weight 0.3255527903317712 --temperature 0.09455232625927315 --hard_mining_ratio 0.48119203841747477 --early_normal_weight 1.111536341915452 --kc_normal_weight 1.0 --use_lr_scheduler --scheduler_type plateau --plateau_factor 0.5 --plateau_patience 2 --min_lr 1e-6
++ python train_balanced_task_sampling.py --train_csv /home/<USER>/balanced_task_sampling/split_result/train_set.csv --val_csv /home/<USER>/balanced_task_sampling/split_result/val_set.csv --test_csv /home/<USER>/balanced_task_sampling/split_result/test_set.csv --epochs 50 --lr 0.0002 --weight_decay 5e-4 --feature_dim 512 --save_dir /home/<USER>/balanced_task_sampling/results_trial_20250524_051644 --proto_counts 4,6,4 --pretrained --inner_lr 0.347389 --inner_steps 5 --n_way 3 --n_shot 5 --n_query 15 --tasks_per_epoch 30 --meta_batch_size 8 --no_augmentation --mixup_alpha 0.0 --cutmix_prob 0.0 --num_workers 4 --augment_factor 1 --kc_augment_factor 1 --seed 42 --early_kc_weight 6.913861712699548 --kc_weight 6.006318184512451 --focal_gamma 1.0031528270061931 --val_frequency 1 --early_stopping 3 --dropout 0.5 --use_separate_test_sampler --use_balanced_task_sampler --kc_shot_multiplier 1.7616889919291359 --early_kc_shot_multiplier 1.2950106152241732 --use_contrastive --contrastive_weight 0.3255527903317712 --temperature 0.09455232625927315 --hard_mining_ratio 0.48119203841747477 --early_normal_weight 1.111536341915452 --kc_normal_weight 1.0 --use_lr_scheduler --scheduler_type plateau --plateau_factor 0.5 --plateau_patience 2 --min_lr 1e-6
/home/<USER>/anaconda3/envs/kc1/lib/python3.10/site-packages/torchvision/models/_utils.py:208: UserWarning: The parameter 'pretrained' is deprecated since 0.13 and may be removed in the future, please use 'weights' instead.
  warnings.warn(
/home/<USER>/anaconda3/envs/kc1/lib/python3.10/site-packages/torchvision/models/_utils.py:223: UserWarning: Arguments other than a weight enum or `None` for 'weights' are deprecated since 0.13 and may be removed in the future. The current behavior is equivalent to passing `weights=ResNet34_Weights.IMAGENET1K_V1`. You can also use `weights=ResNet34_Weights.DEFAULT` to get the most up-to-date weights.
  warnings.warn(msg)
/home/<USER>/anaconda3/envs/kc1/lib/python3.10/site-packages/torch/optim/lr_scheduler.py:62: UserWarning: The verbose parameter is deprecated. Please use get_last_lr() to access the learning rate.
  warnings.warn(

Training tasks:   0%|          | 0/30 [00:00<?, ?it/s]
Training tasks:   3%|▎         | 1/30 [00:05<02:48,  5.81s/it]
Training tasks:   7%|▋         | 2/30 [00:07<01:30,  3.24s/it]
Training tasks:  10%|█         | 3/30 [00:08<01:06,  2.45s/it]
Training tasks:  13%|█▎        | 4/30 [00:10<00:53,  2.05s/it]
Training tasks:  17%|█▋        | 5/30 [00:11<00:45,  1.80s/it]
Training tasks:  20%|██        | 6/30 [00:12<00:39,  1.63s/it]
Training tasks:  23%|██▎       | 7/30 [00:14<00:36,  1.57s/it]
Training tasks:  27%|██▋       | 8/30 [00:15<00:31,  1.44s/it]
Training tasks:  30%|███       | 9/30 [00:16<00:28,  1.36s/it]
Training tasks:  33%|███▎      | 10/30 [00:17<00:25,  1.30s/it]
Training tasks:  37%|███▋      | 11/30 [00:18<00:23,  1.25s/it]
Training tasks:  40%|████      | 12/30 [00:20<00:21,  1.21s/it]
Training tasks:  43%|████▎     | 13/30 [00:21<00:20,  1.19s/it]
Training tasks:  47%|████▋     | 14/30 [00:22<00:18,  1.17s/it]
Training tasks:  50%|█████     | 15/30 [00:23<00:17,  1.16s/it]
Training tasks:  53%|█████▎    | 16/30 [00:24<00:16,  1.18s/it]
Training tasks:  57%|█████▋    | 17/30 [00:26<00:16,  1.25s/it]
Training tasks:  60%|██████    | 18/30 [00:27<00:15,  1.31s/it]
Training tasks:  63%|██████▎   | 19/30 [00:28<00:14,  1.28s/it]
Training tasks:  67%|██████▋   | 20/30 [00:30<00:12,  1.29s/it]
Training tasks:  70%|███████   | 21/30 [00:31<00:11,  1.32s/it]
Training tasks:  73%|███████▎  | 22/30 [00:32<00:10,  1.31s/it]
Training tasks:  77%|███████▋  | 23/30 [00:34<00:09,  1.29s/it]
Training tasks:  80%|████████  | 24/30 [00:35<00:07,  1.30s/it]
Training tasks:  83%|████████▎ | 25/30 [00:36<00:06,  1.30s/it]
Training tasks:  87%|████████▋ | 26/30 [00:37<00:05,  1.31s/it]
Training tasks:  90%|█████████ | 27/30 [00:39<00:03,  1.30s/it]
Training tasks:  93%|█████████▎| 28/30 [00:40<00:02,  1.29s/it]
Training tasks:  97%|█████████▋| 29/30 [00:41<00:01,  1.25s/it]
Training tasks: 100%|██████████| 30/30 [00:42<00:00,  1.20s/it]
Training tasks: 100%|██████████| 30/30 [00:42<00:00,  1.42s/it]

Validating tasks:   0%|          | 0/10 [00:00<?, ?it/s]
Validating tasks:  10%|█         | 1/10 [00:00<00:01,  5.20it/s]
Validating tasks:  20%|██        | 2/10 [00:00<00:01,  5.96it/s]
Validating tasks:  30%|███       | 3/10 [00:00<00:01,  6.13it/s]
Validating tasks:  40%|████      | 4/10 [00:00<00:00,  6.31it/s]
Validating tasks:  50%|█████     | 5/10 [00:00<00:00,  6.41it/s]
Validating tasks:  60%|██████    | 6/10 [00:00<00:00,  6.58it/s]
Validating tasks:  70%|███████   | 7/10 [00:01<00:00,  6.50it/s]
Validating tasks:  80%|████████  | 8/10 [00:01<00:00,  6.65it/s]
Validating tasks:  90%|█████████ | 9/10 [00:01<00:00,  6.64it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  6.66it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  6.45it/s]

Training tasks:   0%|          | 0/30 [00:00<?, ?it/s]
Training tasks:   3%|▎         | 1/30 [00:01<00:34,  1.20s/it]
Training tasks:   7%|▋         | 2/30 [00:02<00:37,  1.33s/it]
Training tasks:  10%|█         | 3/30 [00:04<00:37,  1.40s/it]
Training tasks:  13%|█▎        | 4/30 [00:05<00:35,  1.38s/it]
Training tasks:  17%|█▋        | 5/30 [00:06<00:32,  1.31s/it]
Training tasks:  20%|██        | 6/30 [00:07<00:31,  1.32s/it]
Training tasks:  23%|██▎       | 7/30 [00:09<00:29,  1.30s/it]
Training tasks:  27%|██▋       | 8/30 [00:10<00:28,  1.28s/it]
Training tasks:  30%|███       | 9/30 [00:11<00:25,  1.23s/it]
Training tasks:  33%|███▎      | 10/30 [00:12<00:25,  1.27s/it]
Training tasks:  37%|███▋      | 11/30 [00:14<00:24,  1.30s/it]
Training tasks:  40%|████      | 12/30 [00:15<00:23,  1.28s/it]
Training tasks:  43%|████▎     | 13/30 [00:16<00:21,  1.25s/it]
Training tasks:  47%|████▋     | 14/30 [00:17<00:19,  1.21s/it]
Training tasks:  50%|█████     | 15/30 [00:19<00:18,  1.20s/it]
Training tasks:  53%|█████▎    | 16/30 [00:20<00:17,  1.22s/it]
Training tasks:  57%|█████▋    | 17/30 [00:21<00:15,  1.22s/it]
Training tasks:  60%|██████    | 18/30 [00:22<00:14,  1.24s/it]
Training tasks:  63%|██████▎   | 19/30 [00:24<00:13,  1.25s/it]
Training tasks:  67%|██████▋   | 20/30 [00:25<00:12,  1.25s/it]
Training tasks:  70%|███████   | 21/30 [00:26<00:10,  1.22s/it]
Training tasks:  73%|███████▎  | 22/30 [00:27<00:09,  1.23s/it]
Training tasks:  77%|███████▋  | 23/30 [00:29<00:09,  1.31s/it]
Training tasks:  80%|████████  | 24/30 [00:30<00:08,  1.37s/it]
Training tasks:  83%|████████▎ | 25/30 [00:32<00:06,  1.35s/it]
Training tasks:  87%|████████▋ | 26/30 [00:33<00:05,  1.30s/it]
Training tasks:  90%|█████████ | 27/30 [00:34<00:03,  1.25s/it]
Training tasks:  93%|█████████▎| 28/30 [00:35<00:02,  1.22s/it]
Training tasks:  97%|█████████▋| 29/30 [00:36<00:01,  1.20s/it]
Training tasks: 100%|██████████| 30/30 [00:37<00:00,  1.17s/it]
Training tasks: 100%|██████████| 30/30 [00:37<00:00,  1.26s/it]

Validating tasks:   0%|          | 0/10 [00:00<?, ?it/s]
Validating tasks:  10%|█         | 1/10 [00:00<00:01,  5.32it/s]
Validating tasks:  20%|██        | 2/10 [00:00<00:01,  5.47it/s]
Validating tasks:  30%|███       | 3/10 [00:00<00:01,  5.36it/s]
Validating tasks:  40%|████      | 4/10 [00:00<00:01,  5.23it/s]
Validating tasks:  50%|█████     | 5/10 [00:00<00:00,  5.24it/s]
Validating tasks:  60%|██████    | 6/10 [00:01<00:00,  5.26it/s]
Validating tasks:  70%|███████   | 7/10 [00:01<00:00,  5.00it/s]
Validating tasks:  80%|████████  | 8/10 [00:01<00:00,  5.21it/s]
Validating tasks:  90%|█████████ | 9/10 [00:01<00:00,  5.12it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  5.09it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  5.17it/s]

Training tasks:   0%|          | 0/30 [00:00<?, ?it/s]
Training tasks:   3%|▎         | 1/30 [00:01<00:36,  1.27s/it]
Training tasks:   7%|▋         | 2/30 [00:02<00:35,  1.25s/it]
Training tasks:  10%|█         | 3/30 [00:03<00:35,  1.31s/it]
Training tasks:  13%|█▎        | 4/30 [00:05<00:33,  1.30s/it]
Training tasks:  17%|█▋        | 5/30 [00:06<00:32,  1.31s/it]
Training tasks:  20%|██        | 6/30 [00:07<00:31,  1.32s/it]
Training tasks:  23%|██▎       | 7/30 [00:09<00:29,  1.30s/it]
Training tasks:  27%|██▋       | 8/30 [00:10<00:27,  1.25s/it]
Training tasks:  30%|███       | 9/30 [00:11<00:26,  1.27s/it]
Training tasks:  33%|███▎      | 10/30 [00:12<00:25,  1.29s/it]
Training tasks:  37%|███▋      | 11/30 [00:14<00:24,  1.28s/it]
Training tasks:  40%|████      | 12/30 [00:15<00:23,  1.31s/it]
Training tasks:  43%|████▎     | 13/30 [00:16<00:22,  1.32s/it]
Training tasks:  47%|████▋     | 14/30 [00:18<00:20,  1.29s/it]
Training tasks:  50%|█████     | 15/30 [00:19<00:18,  1.27s/it]
Training tasks:  53%|█████▎    | 16/30 [00:20<00:17,  1.27s/it]
Training tasks:  57%|█████▋    | 17/30 [00:21<00:15,  1.23s/it]
Training tasks:  60%|██████    | 18/30 [00:22<00:14,  1.20s/it]
Training tasks:  63%|██████▎   | 19/30 [00:24<00:13,  1.22s/it]
Training tasks:  67%|██████▋   | 20/30 [00:25<00:12,  1.26s/it]
Training tasks:  70%|███████   | 21/30 [00:26<00:11,  1.25s/it]
Training tasks:  73%|███████▎  | 22/30 [00:27<00:09,  1.22s/it]
Training tasks:  77%|███████▋  | 23/30 [00:28<00:08,  1.19s/it]
Training tasks:  80%|████████  | 24/30 [00:30<00:07,  1.22s/it]
Training tasks:  83%|████████▎ | 25/30 [00:31<00:06,  1.26s/it]
Training tasks:  87%|████████▋ | 26/30 [00:32<00:04,  1.23s/it]
Training tasks:  90%|█████████ | 27/30 [00:33<00:03,  1.20s/it]
Training tasks:  93%|█████████▎| 28/30 [00:35<00:02,  1.24s/it]
Training tasks:  97%|█████████▋| 29/30 [00:36<00:01,  1.28s/it]
Training tasks: 100%|██████████| 30/30 [00:37<00:00,  1.29s/it]
Training tasks: 100%|██████████| 30/30 [00:37<00:00,  1.26s/it]

Validating tasks:   0%|          | 0/10 [00:00<?, ?it/s]
Validating tasks:  10%|█         | 1/10 [00:00<00:01,  5.49it/s]
Validating tasks:  20%|██        | 2/10 [00:00<00:01,  5.70it/s]
Validating tasks:  30%|███       | 3/10 [00:00<00:01,  5.63it/s]
Validating tasks:  40%|████      | 4/10 [00:00<00:01,  5.83it/s]
Validating tasks:  50%|█████     | 5/10 [00:00<00:00,  5.81it/s]
Validating tasks:  60%|██████    | 6/10 [00:01<00:00,  5.82it/s]
Validating tasks:  70%|███████   | 7/10 [00:01<00:00,  5.86it/s]
Validating tasks:  80%|████████  | 8/10 [00:01<00:00,  5.85it/s]
Validating tasks:  90%|█████████ | 9/10 [00:01<00:00,  5.85it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  5.88it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  5.82it/s]

Training tasks:   0%|          | 0/30 [00:00<?, ?it/s]
Training tasks:   3%|▎         | 1/30 [00:01<00:37,  1.31s/it]
Training tasks:   7%|▋         | 2/30 [00:02<00:36,  1.30s/it]
Training tasks:  10%|█         | 3/30 [00:03<00:34,  1.28s/it]
Training tasks:  13%|█▎        | 4/30 [00:05<00:32,  1.26s/it]
Training tasks:  17%|█▋        | 5/30 [00:06<00:31,  1.27s/it]
Training tasks:  20%|██        | 6/30 [00:07<00:29,  1.24s/it]
Training tasks:  23%|██▎       | 7/30 [00:08<00:28,  1.24s/it]
Training tasks:  27%|██▋       | 8/30 [00:10<00:27,  1.23s/it]
Training tasks:  30%|███       | 9/30 [00:11<00:26,  1.25s/it]
Training tasks:  33%|███▎      | 10/30 [00:12<00:25,  1.27s/it]
Training tasks:  37%|███▋      | 11/30 [00:13<00:24,  1.29s/it]
Training tasks:  40%|████      | 12/30 [00:15<00:23,  1.32s/it]
Training tasks:  43%|████▎     | 13/30 [00:16<00:23,  1.36s/it]
Training tasks:  47%|████▋     | 14/30 [00:18<00:21,  1.35s/it]
Training tasks:  50%|█████     | 15/30 [00:19<00:20,  1.33s/it]
Training tasks:  53%|█████▎    | 16/30 [00:20<00:17,  1.28s/it]
Training tasks:  57%|█████▋    | 17/30 [00:21<00:16,  1.24s/it]
Training tasks:  60%|██████    | 18/30 [00:22<00:14,  1.22s/it]
Training tasks:  63%|██████▎   | 19/30 [00:24<00:13,  1.24s/it]
Training tasks:  67%|██████▋   | 20/30 [00:25<00:12,  1.22s/it]
Training tasks:  70%|███████   | 21/30 [00:26<00:11,  1.28s/it]
Training tasks:  73%|███████▎  | 22/30 [00:28<00:10,  1.31s/it]
Training tasks:  77%|███████▋  | 23/30 [00:29<00:09,  1.31s/it]
Training tasks:  80%|████████  | 24/30 [00:30<00:07,  1.30s/it]
Training tasks:  83%|████████▎ | 25/30 [00:32<00:06,  1.30s/it]
Training tasks:  87%|████████▋ | 26/30 [00:33<00:05,  1.32s/it]
Training tasks:  90%|█████████ | 27/30 [00:34<00:03,  1.30s/it]
Training tasks:  93%|█████████▎| 28/30 [00:36<00:02,  1.33s/it]
Training tasks:  97%|█████████▋| 29/30 [00:37<00:01,  1.32s/it]
Training tasks: 100%|██████████| 30/30 [00:38<00:00,  1.26s/it]
Training tasks: 100%|██████████| 30/30 [00:38<00:00,  1.28s/it]

Validating tasks:   0%|          | 0/10 [00:00<?, ?it/s]
Validating tasks:  10%|█         | 1/10 [00:00<00:01,  6.75it/s]
Validating tasks:  20%|██        | 2/10 [00:00<00:01,  6.76it/s]
Validating tasks:  30%|███       | 3/10 [00:00<00:01,  6.69it/s]
Validating tasks:  40%|████      | 4/10 [00:00<00:00,  6.55it/s]
Validating tasks:  50%|█████     | 5/10 [00:00<00:00,  6.46it/s]
Validating tasks:  60%|██████    | 6/10 [00:00<00:00,  6.54it/s]
Validating tasks:  70%|███████   | 7/10 [00:01<00:00,  6.61it/s]
Validating tasks:  80%|████████  | 8/10 [00:01<00:00,  6.62it/s]
Validating tasks:  90%|█████████ | 9/10 [00:01<00:00,  6.64it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  6.70it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  6.63it/s]

Training tasks:   0%|          | 0/30 [00:00<?, ?it/s]
Training tasks:   3%|▎         | 1/30 [00:01<00:31,  1.09s/it]
Training tasks:   7%|▋         | 2/30 [00:02<00:31,  1.12s/it]
Training tasks:  10%|█         | 3/30 [00:03<00:30,  1.14s/it]
Training tasks:  13%|█▎        | 4/30 [00:04<00:31,  1.21s/it]
Training tasks:  17%|█▋        | 5/30 [00:05<00:29,  1.19s/it]
Training tasks:  20%|██        | 6/30 [00:07<00:28,  1.21s/it]
Training tasks:  23%|██▎       | 7/30 [00:08<00:27,  1.20s/it]
Training tasks:  27%|██▋       | 8/30 [00:09<00:26,  1.19s/it]
Training tasks:  30%|███       | 9/30 [00:10<00:26,  1.29s/it]
Training tasks:  33%|███▎      | 10/30 [00:12<00:26,  1.31s/it]
Training tasks:  37%|███▋      | 11/30 [00:13<00:25,  1.32s/it]
Training tasks:  40%|████      | 12/30 [00:14<00:23,  1.29s/it]
Training tasks:  43%|████▎     | 13/30 [00:16<00:22,  1.35s/it]
Training tasks:  47%|████▋     | 14/30 [00:17<00:21,  1.32s/it]
Training tasks:  50%|█████     | 15/30 [00:19<00:20,  1.34s/it]
Training tasks:  53%|█████▎    | 16/30 [00:20<00:17,  1.28s/it]
Training tasks:  57%|█████▋    | 17/30 [00:21<00:16,  1.24s/it]
Training tasks:  60%|██████    | 18/30 [00:22<00:14,  1.24s/it]
Training tasks:  63%|██████▎   | 19/30 [00:23<00:13,  1.27s/it]
Training tasks:  67%|██████▋   | 20/30 [00:25<00:13,  1.30s/it]
Training tasks:  70%|███████   | 21/30 [00:26<00:11,  1.30s/it]
Training tasks:  73%|███████▎  | 22/30 [00:27<00:10,  1.28s/it]
Training tasks:  77%|███████▋  | 23/30 [00:29<00:09,  1.31s/it]
Training tasks:  80%|████████  | 24/30 [00:30<00:07,  1.29s/it]
Training tasks:  83%|████████▎ | 25/30 [00:31<00:06,  1.26s/it]
Training tasks:  87%|████████▋ | 26/30 [00:32<00:05,  1.27s/it]
Training tasks:  90%|█████████ | 27/30 [00:34<00:03,  1.26s/it]
Training tasks:  93%|█████████▎| 28/30 [00:35<00:02,  1.25s/it]
Training tasks:  97%|█████████▋| 29/30 [00:36<00:01,  1.25s/it]
Training tasks: 100%|██████████| 30/30 [00:37<00:00,  1.23s/it]
Training tasks: 100%|██████████| 30/30 [00:37<00:00,  1.26s/it]

Validating tasks:   0%|          | 0/10 [00:00<?, ?it/s]
Validating tasks:  10%|█         | 1/10 [00:00<00:01,  6.11it/s]
Validating tasks:  20%|██        | 2/10 [00:00<00:01,  5.70it/s]
Validating tasks:  30%|███       | 3/10 [00:00<00:01,  5.40it/s]
Validating tasks:  40%|████      | 4/10 [00:00<00:01,  5.08it/s]
Validating tasks:  50%|█████     | 5/10 [00:00<00:01,  4.89it/s]
Validating tasks:  60%|██████    | 6/10 [00:01<00:00,  5.11it/s]
Validating tasks:  70%|███████   | 7/10 [00:01<00:00,  5.37it/s]
Validating tasks:  80%|████████  | 8/10 [00:01<00:00,  5.20it/s]
Validating tasks:  90%|█████████ | 9/10 [00:01<00:00,  5.11it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  5.07it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  5.18it/s]

Training tasks:   0%|          | 0/30 [00:00<?, ?it/s]
Training tasks:   3%|▎         | 1/30 [00:01<00:33,  1.14s/it]
Training tasks:   7%|▋         | 2/30 [00:02<00:31,  1.13s/it]
Training tasks:  10%|█         | 3/30 [00:03<00:35,  1.30s/it]
Training tasks:  13%|█▎        | 4/30 [00:05<00:33,  1.28s/it]
Training tasks:  17%|█▋        | 5/30 [00:06<00:31,  1.27s/it]
Training tasks:  20%|██        | 6/30 [00:07<00:31,  1.31s/it]
Training tasks:  23%|██▎       | 7/30 [00:09<00:30,  1.34s/it]
Training tasks:  27%|██▋       | 8/30 [00:10<00:28,  1.28s/it]
Training tasks:  30%|███       | 9/30 [00:11<00:25,  1.23s/it]
Training tasks:  33%|███▎      | 10/30 [00:12<00:25,  1.25s/it]
Training tasks:  37%|███▋      | 11/30 [00:14<00:24,  1.30s/it]
Training tasks:  40%|████      | 12/30 [00:15<00:23,  1.33s/it]
Training tasks:  43%|████▎     | 13/30 [00:16<00:22,  1.31s/it]
Training tasks:  47%|████▋     | 14/30 [00:18<00:21,  1.33s/it]
Training tasks:  50%|█████     | 15/30 [00:19<00:19,  1.31s/it]
Training tasks:  53%|█████▎    | 16/30 [00:20<00:18,  1.29s/it]
Training tasks:  57%|█████▋    | 17/30 [00:21<00:16,  1.23s/it]
Training tasks:  60%|██████    | 18/30 [00:23<00:15,  1.26s/it]
Training tasks:  63%|██████▎   | 19/30 [00:24<00:14,  1.29s/it]
Training tasks:  67%|██████▋   | 20/30 [00:25<00:12,  1.28s/it]
Training tasks:  70%|███████   | 21/30 [00:26<00:11,  1.29s/it]
Training tasks:  73%|███████▎  | 22/30 [00:28<00:10,  1.32s/it]
Training tasks:  77%|███████▋  | 23/30 [00:29<00:09,  1.32s/it]
Training tasks:  80%|████████  | 24/30 [00:30<00:07,  1.27s/it]
Training tasks:  83%|████████▎ | 25/30 [00:32<00:06,  1.25s/it]
Training tasks:  87%|████████▋ | 26/30 [00:33<00:04,  1.22s/it]
Training tasks:  90%|█████████ | 27/30 [00:34<00:03,  1.22s/it]
Training tasks:  93%|█████████▎| 28/30 [00:35<00:02,  1.25s/it]
Training tasks:  97%|█████████▋| 29/30 [00:36<00:01,  1.24s/it]
Training tasks: 100%|██████████| 30/30 [00:38<00:00,  1.26s/it]
Training tasks: 100%|██████████| 30/30 [00:38<00:00,  1.28s/it]

Validating tasks:   0%|          | 0/10 [00:00<?, ?it/s]
Validating tasks:  10%|█         | 1/10 [00:00<00:01,  5.57it/s]
Validating tasks:  20%|██        | 2/10 [00:00<00:01,  5.48it/s]
Validating tasks:  30%|███       | 3/10 [00:00<00:01,  5.83it/s]
Validating tasks:  40%|████      | 4/10 [00:00<00:01,  5.81it/s]
Validating tasks:  50%|█████     | 5/10 [00:00<00:00,  5.72it/s]
Validating tasks:  60%|██████    | 6/10 [00:01<00:00,  5.70it/s]
Validating tasks:  70%|███████   | 7/10 [00:01<00:00,  5.77it/s]
Validating tasks:  80%|████████  | 8/10 [00:01<00:00,  5.92it/s]
Validating tasks:  90%|█████████ | 9/10 [00:01<00:00,  5.82it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  5.85it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  5.79it/s]

Training tasks:   0%|          | 0/30 [00:00<?, ?it/s]
Training tasks:   3%|▎         | 1/30 [00:01<00:38,  1.32s/it]
Training tasks:   7%|▋         | 2/30 [00:02<00:35,  1.26s/it]
Training tasks:  10%|█         | 3/30 [00:03<00:32,  1.19s/it]
Training tasks:  13%|█▎        | 4/30 [00:04<00:31,  1.21s/it]
Training tasks:  17%|█▋        | 5/30 [00:06<00:31,  1.27s/it]
Training tasks:  20%|██        | 6/30 [00:07<00:30,  1.27s/it]
Training tasks:  23%|██▎       | 7/30 [00:08<00:29,  1.28s/it]
Training tasks:  27%|██▋       | 8/30 [00:10<00:28,  1.30s/it]
Training tasks:  30%|███       | 9/30 [00:11<00:27,  1.32s/it]
Training tasks:  33%|███▎      | 10/30 [00:12<00:26,  1.34s/it]
Training tasks:  37%|███▋      | 11/30 [00:14<00:25,  1.33s/it]
Training tasks:  40%|████      | 12/30 [00:15<00:24,  1.34s/it]
Training tasks:  43%|████▎     | 13/30 [00:17<00:23,  1.41s/it]
Training tasks:  47%|████▋     | 14/30 [00:18<00:22,  1.41s/it]
Training tasks:  50%|█████     | 15/30 [00:19<00:19,  1.33s/it]
Training tasks:  53%|█████▎    | 16/30 [00:21<00:18,  1.33s/it]
Training tasks:  57%|█████▋    | 17/30 [00:22<00:17,  1.33s/it]
Training tasks:  60%|██████    | 18/30 [00:23<00:16,  1.33s/it]
Training tasks:  63%|██████▎   | 19/30 [00:25<00:14,  1.33s/it]
Training tasks:  67%|██████▋   | 20/30 [00:26<00:13,  1.33s/it]
Training tasks:  70%|███████   | 21/30 [00:27<00:11,  1.27s/it]
Training tasks:  73%|███████▎  | 22/30 [00:28<00:09,  1.23s/it]
Training tasks:  77%|███████▋  | 23/30 [00:29<00:08,  1.20s/it]
Training tasks:  80%|████████  | 24/30 [00:31<00:07,  1.23s/it]
Training tasks:  83%|████████▎ | 25/30 [00:32<00:06,  1.30s/it]
Training tasks:  87%|████████▋ | 26/30 [00:33<00:05,  1.31s/it]
Training tasks:  90%|█████████ | 27/30 [00:35<00:03,  1.32s/it]
Training tasks:  93%|█████████▎| 28/30 [00:36<00:02,  1.34s/it]
Training tasks:  97%|█████████▋| 29/30 [00:37<00:01,  1.32s/it]
Training tasks: 100%|██████████| 30/30 [00:39<00:00,  1.32s/it]
Training tasks: 100%|██████████| 30/30 [00:39<00:00,  1.31s/it]

Validating tasks:   0%|          | 0/10 [00:00<?, ?it/s]
Validating tasks:  10%|█         | 1/10 [00:00<00:01,  4.89it/s]
Validating tasks:  20%|██        | 2/10 [00:00<00:01,  5.01it/s]
Validating tasks:  30%|███       | 3/10 [00:00<00:01,  5.17it/s]
Validating tasks:  40%|████      | 4/10 [00:00<00:01,  5.15it/s]
Validating tasks:  50%|█████     | 5/10 [00:00<00:00,  5.10it/s]
Validating tasks:  60%|██████    | 6/10 [00:01<00:00,  5.11it/s]
Validating tasks:  70%|███████   | 7/10 [00:01<00:00,  5.13it/s]
Validating tasks:  80%|████████  | 8/10 [00:01<00:00,  5.40it/s]
Validating tasks:  90%|█████████ | 9/10 [00:01<00:00,  5.24it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  5.20it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  5.17it/s]

Training tasks:   0%|          | 0/30 [00:00<?, ?it/s]
Training tasks:   3%|▎         | 1/30 [00:01<00:40,  1.39s/it]
Training tasks:   7%|▋         | 2/30 [00:02<00:38,  1.38s/it]
Training tasks:  10%|█         | 3/30 [00:04<00:36,  1.34s/it]
Training tasks:  13%|█▎        | 4/30 [00:05<00:35,  1.35s/it]
Training tasks:  17%|█▋        | 5/30 [00:06<00:33,  1.36s/it]
Training tasks:  20%|██        | 6/30 [00:08<00:32,  1.37s/it]
Training tasks:  23%|██▎       | 7/30 [00:09<00:31,  1.38s/it]
Training tasks:  27%|██▋       | 8/30 [00:10<00:30,  1.38s/it]
Training tasks:  30%|███       | 9/30 [00:12<00:28,  1.35s/it]
Training tasks:  33%|███▎      | 10/30 [00:13<00:26,  1.32s/it]
Training tasks:  37%|███▋      | 11/30 [00:14<00:25,  1.34s/it]
Training tasks:  40%|████      | 12/30 [00:16<00:24,  1.35s/it]
Training tasks:  43%|████▎     | 13/30 [00:17<00:23,  1.36s/it]
Training tasks:  47%|████▋     | 14/30 [00:18<00:21,  1.35s/it]
Training tasks:  50%|█████     | 15/30 [00:20<00:19,  1.28s/it]
Training tasks:  53%|█████▎    | 16/30 [00:21<00:18,  1.29s/it]
Training tasks:  57%|█████▋    | 17/30 [00:22<00:16,  1.28s/it]
Training tasks:  60%|██████    | 18/30 [00:23<00:15,  1.26s/it]
Training tasks:  63%|██████▎   | 19/30 [00:25<00:14,  1.29s/it]
Training tasks:  67%|██████▋   | 20/30 [00:26<00:13,  1.31s/it]
Training tasks:  70%|███████   | 21/30 [00:27<00:11,  1.25s/it]
Training tasks:  73%|███████▎  | 22/30 [00:28<00:09,  1.22s/it]
Training tasks:  77%|███████▋  | 23/30 [00:30<00:09,  1.29s/it]
Training tasks:  80%|████████  | 24/30 [00:31<00:07,  1.29s/it]
Training tasks:  83%|████████▎ | 25/30 [00:32<00:06,  1.31s/it]
Training tasks:  87%|████████▋ | 26/30 [00:34<00:05,  1.33s/it]
Training tasks:  90%|█████████ | 27/30 [00:35<00:04,  1.35s/it]
Training tasks:  93%|█████████▎| 28/30 [00:37<00:02,  1.33s/it]
Training tasks:  97%|█████████▋| 29/30 [00:38<00:01,  1.32s/it]
Training tasks: 100%|██████████| 30/30 [00:39<00:00,  1.32s/it]
Training tasks: 100%|██████████| 30/30 [00:39<00:00,  1.32s/it]

Validating tasks:   0%|          | 0/10 [00:00<?, ?it/s]
Validating tasks:  10%|█         | 1/10 [00:00<00:01,  5.68it/s]
Validating tasks:  20%|██        | 2/10 [00:00<00:01,  6.14it/s]
Validating tasks:  30%|███       | 3/10 [00:00<00:01,  5.96it/s]
Validating tasks:  40%|████      | 4/10 [00:00<00:01,  5.98it/s]
Validating tasks:  50%|█████     | 5/10 [00:00<00:00,  6.27it/s]
Validating tasks:  60%|██████    | 6/10 [00:00<00:00,  6.32it/s]
Validating tasks:  70%|███████   | 7/10 [00:01<00:00,  6.44it/s]
Validating tasks:  80%|████████  | 8/10 [00:01<00:00,  6.45it/s]
Validating tasks:  90%|█████████ | 9/10 [00:01<00:00,  6.61it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  6.58it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  6.36it/s]

Training tasks:   0%|          | 0/30 [00:00<?, ?it/s]
Training tasks:   3%|▎         | 1/30 [00:01<00:32,  1.13s/it]
Training tasks:   7%|▋         | 2/30 [00:02<00:35,  1.25s/it]
Training tasks:  10%|█         | 3/30 [00:03<00:34,  1.28s/it]
Training tasks:  13%|█▎        | 4/30 [00:05<00:33,  1.29s/it]
Training tasks:  17%|█▋        | 5/30 [00:06<00:32,  1.30s/it]
Training tasks:  20%|██        | 6/30 [00:07<00:30,  1.28s/it]
Training tasks:  23%|██▎       | 7/30 [00:08<00:28,  1.23s/it]
Training tasks:  27%|██▋       | 8/30 [00:10<00:27,  1.23s/it]
Training tasks:  30%|███       | 9/30 [00:11<00:26,  1.24s/it]
Training tasks:  33%|███▎      | 10/30 [00:12<00:24,  1.24s/it]
Training tasks:  37%|███▋      | 11/30 [00:13<00:23,  1.26s/it]
Training tasks:  40%|████      | 12/30 [00:15<00:23,  1.28s/it]
Training tasks:  43%|████▎     | 13/30 [00:16<00:21,  1.24s/it]
Training tasks:  47%|████▋     | 14/30 [00:17<00:19,  1.24s/it]
Training tasks:  50%|█████     | 15/30 [00:18<00:18,  1.26s/it]
Training tasks:  53%|█████▎    | 16/30 [00:20<00:17,  1.27s/it]
Training tasks:  57%|█████▋    | 17/30 [00:21<00:16,  1.30s/it]
Training tasks:  60%|██████    | 18/30 [00:22<00:15,  1.28s/it]
Training tasks:  63%|██████▎   | 19/30 [00:24<00:14,  1.28s/it]
Training tasks:  67%|██████▋   | 20/30 [00:25<00:12,  1.29s/it]
Training tasks:  70%|███████   | 21/30 [00:26<00:11,  1.28s/it]
Training tasks:  73%|███████▎  | 22/30 [00:27<00:10,  1.25s/it]
Training tasks:  77%|███████▋  | 23/30 [00:29<00:08,  1.26s/it]
Training tasks:  80%|████████  | 24/30 [00:30<00:07,  1.27s/it]
Training tasks:  83%|████████▎ | 25/30 [00:31<00:06,  1.29s/it]
Training tasks:  87%|████████▋ | 26/30 [00:32<00:05,  1.28s/it]
Training tasks:  90%|█████████ | 27/30 [00:34<00:03,  1.29s/it]
Training tasks:  93%|█████████▎| 28/30 [00:35<00:02,  1.28s/it]
Training tasks:  97%|█████████▋| 29/30 [00:36<00:01,  1.29s/it]
Training tasks: 100%|██████████| 30/30 [00:38<00:00,  1.30s/it]
Training tasks: 100%|██████████| 30/30 [00:38<00:00,  1.27s/it]

Validating tasks:   0%|          | 0/10 [00:00<?, ?it/s]
Validating tasks:  10%|█         | 1/10 [00:00<00:01,  6.81it/s]
Validating tasks:  20%|██        | 2/10 [00:00<00:01,  6.20it/s]
Validating tasks:  30%|███       | 3/10 [00:00<00:01,  5.95it/s]
Validating tasks:  40%|████      | 4/10 [00:00<00:00,  6.10it/s]
Validating tasks:  50%|█████     | 5/10 [00:00<00:00,  6.04it/s]
Validating tasks:  60%|██████    | 6/10 [00:00<00:00,  5.98it/s]
Validating tasks:  70%|███████   | 7/10 [00:01<00:00,  6.10it/s]
Validating tasks:  80%|████████  | 8/10 [00:01<00:00,  6.13it/s]
Validating tasks:  90%|█████████ | 9/10 [00:01<00:00,  5.91it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  6.00it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  6.05it/s]

Testing tasks:   0%|          | 0/30 [00:00<?, ?it/s]
Testing tasks:   3%|▎         | 1/30 [00:00<00:08,  3.53it/s]
Testing tasks:   7%|▋         | 2/30 [00:00<00:06,  4.05it/s]
Testing tasks:  10%|█         | 3/30 [00:00<00:06,  4.43it/s]
Testing tasks:  13%|█▎        | 4/30 [00:00<00:05,  4.65it/s]
Testing tasks:  17%|█▋        | 5/30 [00:01<00:05,  4.80it/s]
Testing tasks:  20%|██        | 6/30 [00:01<00:04,  4.89it/s]
Testing tasks:  23%|██▎       | 7/30 [00:01<00:04,  4.89it/s]
Testing tasks:  27%|██▋       | 8/30 [00:01<00:04,  4.66it/s]
Testing tasks:  30%|███       | 9/30 [00:01<00:04,  4.51it/s]
Testing tasks:  33%|███▎      | 10/30 [00:02<00:04,  4.39it/s]
Testing tasks:  37%|███▋      | 11/30 [00:02<00:04,  4.37it/s]
Testing tasks:  40%|████      | 12/30 [00:02<00:04,  4.26it/s]
Testing tasks:  43%|████▎     | 13/30 [00:02<00:03,  4.27it/s]
Testing tasks:  47%|████▋     | 14/30 [00:03<00:03,  4.30it/s]
Testing tasks:  50%|█████     | 15/30 [00:03<00:03,  4.32it/s]
Testing tasks:  53%|█████▎    | 16/30 [00:03<00:03,  4.27it/s]
Testing tasks:  57%|█████▋    | 17/30 [00:03<00:03,  4.32it/s]
Testing tasks:  60%|██████    | 18/30 [00:04<00:02,  4.49it/s]
Testing tasks:  63%|██████▎   | 19/30 [00:04<00:02,  4.47it/s]
Testing tasks:  67%|██████▋   | 20/30 [00:04<00:02,  4.49it/s]
Testing tasks:  70%|███████   | 21/30 [00:04<00:01,  4.57it/s]
Testing tasks:  73%|███████▎  | 22/30 [00:04<00:01,  4.60it/s]
Testing tasks:  77%|███████▋  | 23/30 [00:05<00:01,  4.60it/s]
Testing tasks:  80%|████████  | 24/30 [00:05<00:01,  4.71it/s]
Testing tasks:  83%|████████▎ | 25/30 [00:05<00:01,  4.82it/s]
Testing tasks:  87%|████████▋ | 26/30 [00:05<00:00,  4.54it/s]
Testing tasks:  90%|█████████ | 27/30 [00:06<00:00,  4.31it/s]
Testing tasks:  93%|█████████▎| 28/30 [00:06<00:00,  4.20it/s]
Testing tasks:  97%|█████████▋| 29/30 [00:06<00:00,  4.13it/s]
Testing tasks: 100%|██████████| 30/30 [00:06<00:00,  4.21it/s]
Testing tasks: 100%|██████████| 30/30 [00:06<00:00,  4.42it/s]

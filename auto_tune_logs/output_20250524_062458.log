STDOUT:
===================== 训练配置 =====================
GPU: 0
输出目录: /home/<USER>/balanced_task_sampling/results_trial_20250524_062458
日志文件: /home/<USER>/balanced_task_sampling/results_trial_20250524_062458/main_training_trial_108_20250524_062458.log

--------- 优化参数 ---------
内部学习率: 0.347389
训练轮数: 50
每轮任务数: 30

--------- 模型参数 ---------
使用类别权重: 圆锥角膜=7.626070004892245, 早期圆锥角膜=5.115647374077351
温度参数: 0.022021020730573788
硬样本挖掘比例: 0.7651463918651594
对比学习权重: 0.6666204505949068
=============================================

创建目录: /home/<USER>/balanced_task_sampling/results_trial_20250524_062458
总用量 4
-rw-rw-r-- 1 <USER> <GROUP> 659 5月  24 06:24 main_training_trial_108_20250524_062458.log
开始无数据增强的平衡任务采样训练...
使用GPU: 0
直接运行训练脚本...
成功导入增强版特征提取器
成功导入归一化特征提取器
成功导入增强版损失函数
成功导入增强版MAML模型
成功导入KC特定增强模块
成功导入增强版GPU数据增强模块
使用设备: cuda
禁用所有数据增强，直接加载原始数据集...
使用平衡任务采样器
类别 normal (标签 0): 164个样本
类别 e-kc (标签 1): 26个样本
类别 kc (标签 2): 133个样本
使用原型配置: [4, 5, 4]
使用增强版特征提取器，dropout率: 0.5
使用增强版MAML-ProtoNet模型，内循环学习率: 0.347389, 内循环步数: 5
使用ReduceLROnPlateau学习率调度器，因子: 0.5, 耐心值: 2
开始训练，共50个epoch
Epoch 1/50
当前学习率: 0.000200
使用类别权重: 正常=1.0, 早期圆锥角膜=5.115647374077351, 圆锥角膜=7.626070004892245
使用增强版对比损失函数，温度: 0.022021020730573788, 硬负样本挖掘比例: 0.7651463918651594, 早期圆锥角膜与正常样本对比权重: 1.5398066557991035, 圆锥角膜与正常样本对比权重: 1.0
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
警告: 没有足够的有效类别进行3-way任务采样，尝试调整参数...
类别样本数量: {0: 33, 1: 5, 2: 29}
调整后的参数: n_shot=2, n_query=3
类别 normal (标签 0): 33个样本
类别 e-kc (标签 1): 5个样本
类别 kc (标签 2): 29个样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 4 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 4 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 4 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 4 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 4 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 4 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 4 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 4 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 4 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 4 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
Epoch 1: Train Loss: 3.7683, Train Acc: 0.5807, Val Loss: 2.4377, Val Acc: 0.8000
保存最佳模型，验证准确率: 0.8000
Epoch 2/50
当前学习率: 0.000200
使用类别权重: 正常=1.0, 早期圆锥角膜=5.115647374077351, 圆锥角膜=7.626070004892245
使用增强版对比损失函数，温度: 0.022021020730573788, 硬负样本挖掘比例: 0.7651463918651594, 早期圆锥角膜与正常样本对比权重: 1.5398066557991035, 圆锥角膜与正常样本对比权重: 1.0
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
警告: 没有足够的有效类别进行3-way任务采样，尝试调整参数...
类别样本数量: {0: 33, 1: 5, 2: 29}
调整后的参数: n_shot=2, n_query=3
类别 normal (标签 0): 33个样本
类别 e-kc (标签 1): 5个样本
类别 kc (标签 2): 29个样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 4 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 4 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 4 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 4 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 4 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 4 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 4 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 4 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 4 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 4 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
Epoch 2: Train Loss: 3.6988, Train Acc: 0.6000, Val Loss: 2.7795, Val Acc: 0.6333
验证准确率未提高，当前耐心值: 1/3
Epoch 3/50
当前学习率: 0.000200
使用类别权重: 正常=1.0, 早期圆锥角膜=5.115647374077351, 圆锥角膜=7.626070004892245
使用增强版对比损失函数，温度: 0.022021020730573788, 硬负样本挖掘比例: 0.7651463918651594, 早期圆锥角膜与正常样本对比权重: 1.5398066557991035, 圆锥角膜与正常样本对比权重: 1.0
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
警告: 没有足够的有效类别进行3-way任务采样，尝试调整参数...
类别样本数量: {0: 33, 1: 5, 2: 29}
调整后的参数: n_shot=2, n_query=3
类别 normal (标签 0): 33个样本
类别 e-kc (标签 1): 5个样本
类别 kc (标签 2): 29个样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 4 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 4 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 4 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 4 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 4 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 4 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 4 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 4 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 4 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 4 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
Epoch 3: Train Loss: 3.4323, Train Acc: 0.6807, Val Loss: 2.8923, Val Acc: 0.6000
验证准确率未提高，当前耐心值: 2/3
Epoch 4/50
当前学习率: 0.000200
使用类别权重: 正常=1.0, 早期圆锥角膜=5.115647374077351, 圆锥角膜=7.626070004892245
使用增强版对比损失函数，温度: 0.022021020730573788, 硬负样本挖掘比例: 0.7651463918651594, 早期圆锥角膜与正常样本对比权重: 1.5398066557991035, 圆锥角膜与正常样本对比权重: 1.0
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
警告: 没有足够的有效类别进行3-way任务采样，尝试调整参数...
类别样本数量: {0: 33, 1: 5, 2: 29}
调整后的参数: n_shot=2, n_query=3
类别 normal (标签 0): 33个样本
类别 e-kc (标签 1): 5个样本
类别 kc (标签 2): 29个样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 4 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 4 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 4 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 4 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 4 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 4 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 4 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 4 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 4 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 4 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
Epoch 4: Train Loss: 3.2217, Train Acc: 0.7385, Val Loss: 2.7881, Val Acc: 0.6333
验证准确率未提高，当前耐心值: 3/3
早停触发，停止训练
警告: 没有足够的有效类别进行3-way任务采样，尝试调整参数...
类别样本数量: {0: 37, 1: 7, 2: 27}
调整后的参数: n_shot=3, n_query=4
类别 normal (标签 0): 37个样本
类别 e-kc (标签 1): 7个样本
类别 kc (标签 2): 27个样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本

测试结果:
总体准确率: 76.67%
类别准确率:
  kc: 82.50%
  e-kc: 50.83%
  normal: 96.67%
训练完成，结果已保存到 /home/<USER>/balanced_task_sampling/results_trial_20250524_062458

STDERR:
+ TIMESTAMP=20250524_062458
+ RESULT_DIR=/home/<USER>/balanced_task_sampling/results_trial_20250524_062458
+ '[' -n /home/<USER>/balanced_task_sampling/results_trial_20250524_062458/main_training_trial_108_20250524_062458.log ']'
+ LOG_FILE=/home/<USER>/balanced_task_sampling/results_trial_20250524_062458/main_training_trial_108_20250524_062458.log
++ dirname /home/<USER>/balanced_task_sampling/results_trial_20250524_062458/main_training_trial_108_20250524_062458.log
+ mkdir -p /home/<USER>/balanced_task_sampling/results_trial_20250524_062458
+ mkdir -p /home/<USER>/balanced_task_sampling/results_trial_20250524_062458
+ echo '===================== 训练配置 ====================='
+ echo 'GPU: 0'
+ echo '输出目录: /home/<USER>/balanced_task_sampling/results_trial_20250524_062458'
+ echo '日志文件: /home/<USER>/balanced_task_sampling/results_trial_20250524_062458/main_training_trial_108_20250524_062458.log'
+ echo ''
+ echo '--------- 优化参数 ---------'
+ echo '内部学习率: 0.347389'
+ echo '训练轮数: 50'
+ echo '每轮任务数: 30'
+ echo ''
+ echo '--------- 模型参数 ---------'
+ echo '使用类别权重: 圆锥角膜=7.626070004892245, 早期圆锥角膜=5.115647374077351'
+ echo '温度参数: 0.022021020730573788'
+ echo '硬样本挖掘比例: 0.7651463918651594'
+ echo '对比学习权重: 0.6666204505949068'
+ echo '=============================================
'
+ tee -a /home/<USER>/balanced_task_sampling/results_trial_20250524_062458/main_training_trial_108_20250524_062458.log
+ validate_params
+ local error_count=0
++ dirname /home/<USER>/balanced_task_sampling/results_trial_20250524_062458/main_training_trial_108_20250524_062458.log
++ dirname /home/<USER>/balanced_task_sampling/split_result/train_set.csv
+ for dir in "$RESULT_DIR" "$(dirname "$LOG_FILE")" "$(dirname "$TRAIN_CSV")"
+ mkdir -p /home/<USER>/balanced_task_sampling/results_trial_20250524_062458
+ for dir in "$RESULT_DIR" "$(dirname "$LOG_FILE")" "$(dirname "$TRAIN_CSV")"
+ mkdir -p /home/<USER>/balanced_task_sampling/results_trial_20250524_062458
+ for dir in "$RESULT_DIR" "$(dirname "$LOG_FILE")" "$(dirname "$TRAIN_CSV")"
+ mkdir -p /home/<USER>/balanced_task_sampling/split_result
+ for csv in "$TRAIN_CSV" "$VAL_CSV" "$TEST_CSV"
+ '[' '!' -f /home/<USER>/balanced_task_sampling/split_result/train_set.csv ']'
+ for csv in "$TRAIN_CSV" "$VAL_CSV" "$TEST_CSV"
+ '[' '!' -f /home/<USER>/balanced_task_sampling/split_result/val_set.csv ']'
+ for csv in "$TRAIN_CSV" "$VAL_CSV" "$TEST_CSV"
+ '[' '!' -f /home/<USER>/balanced_task_sampling/split_result/test_set.csv ']'
+ [[ 0.0002 =~ ^[0-9.]+$ ]]
+ [[ 0.347389 =~ ^[0-9.]+$ ]]
+ '[' 0 -gt 0 ']'
+ return 0
+ '[' false = true ']'
+ export CUDA_VISIBLE_DEVICES=0
+ CUDA_VISIBLE_DEVICES=0
+ export OMP_NUM_THREADS=1
+ OMP_NUM_THREADS=1
+ export MKL_NUM_THREADS=1
+ MKL_NUM_THREADS=1
+ export MPLBACKEND=Agg
+ MPLBACKEND=Agg
+ mkdir -p /home/<USER>/balanced_task_sampling/results_trial_20250524_062458
+ echo '创建目录: /home/<USER>/balanced_task_sampling/results_trial_20250524_062458'
+ ls -l /home/<USER>/balanced_task_sampling/results_trial_20250524_062458
+ PRETRAINED_ARGS=
+ '[' true = true ']'
+ PRETRAINED_ARGS=--pretrained
+ AUGMENT_ARGS=--no_augmentation
+ CONTRASTIVE_ARGS=
+ '[' true = true ']'
+ CONTRASTIVE_ARGS='--use_contrastive --contrastive_weight 0.6666204505949068 --temperature 0.022021020730573788 --hard_mining_ratio 0.7651463918651594 --early_normal_weight 1.5398066557991035 --kc_normal_weight 1.0'
+ NORMALIZE_ARGS=
+ '[' false = true ']'
+ SAMPLER_ARGS=
+ '[' true = true ']'
+ SAMPLER_ARGS=' --use_separate_test_sampler'
+ '[' true = true ']'
+ SAMPLER_ARGS=' --use_separate_test_sampler --use_balanced_task_sampler --kc_shot_multiplier 2.3445830391695934 --early_kc_shot_multiplier 1.2459523943929156'
+ LR_SCHEDULER_ARGS=
+ '[' true = true ']'
+ LR_SCHEDULER_ARGS='--use_lr_scheduler --scheduler_type plateau'
+ '[' plateau = onecycle ']'
+ '[' plateau = plateau ']'
+ LR_SCHEDULER_ARGS='--use_lr_scheduler --scheduler_type plateau --plateau_factor 0.5 --plateau_patience 2 --min_lr 1e-6'
+ BASE_CMD='python train_balanced_task_sampling.py   --train_csv /home/<USER>/balanced_task_sampling/split_result/train_set.csv   --val_csv /home/<USER>/balanced_task_sampling/split_result/val_set.csv   --test_csv /home/<USER>/balanced_task_sampling/split_result/test_set.csv   --epochs 50   --lr 0.0002   --weight_decay 5e-4   --feature_dim 512   --save_dir /home/<USER>/balanced_task_sampling/results_trial_20250524_062458   --proto_counts 4,5,4   --pretrained   --inner_lr 0.347389   --inner_steps 5   --n_way 3   --n_shot 5   --n_query 15   --tasks_per_epoch 30   --meta_batch_size 8   --no_augmentation   --mixup_alpha 0.0   --cutmix_prob 0.0   --num_workers 4   --augment_factor 1   --kc_augment_factor 1   --seed 42   --early_kc_weight 5.115647374077351   --kc_weight 7.626070004892245   --focal_gamma 1.0843936919733927   --val_frequency 1   --early_stopping 3   --dropout 0.5    --use_separate_test_sampler --use_balanced_task_sampler --kc_shot_multiplier 2.3445830391695934 --early_kc_shot_multiplier 1.2459523943929156   --use_contrastive --contrastive_weight 0.6666204505949068 --temperature 0.022021020730573788 --hard_mining_ratio 0.7651463918651594 --early_normal_weight 1.5398066557991035 --kc_normal_weight 1.0      --use_lr_scheduler --scheduler_type plateau --plateau_factor 0.5 --plateau_patience 2 --min_lr 1e-6'
+ echo 开始无数据增强的平衡任务采样训练...
+ echo '使用GPU: 0'
+ '[' direct = nohup ']'
+ echo 直接运行训练脚本...
+ eval python train_balanced_task_sampling.py --train_csv /home/<USER>/balanced_task_sampling/split_result/train_set.csv --val_csv /home/<USER>/balanced_task_sampling/split_result/val_set.csv --test_csv /home/<USER>/balanced_task_sampling/split_result/test_set.csv --epochs 50 --lr 0.0002 --weight_decay 5e-4 --feature_dim 512 --save_dir /home/<USER>/balanced_task_sampling/results_trial_20250524_062458 --proto_counts 4,5,4 --pretrained --inner_lr 0.347389 --inner_steps 5 --n_way 3 --n_shot 5 --n_query 15 --tasks_per_epoch 30 --meta_batch_size 8 --no_augmentation --mixup_alpha 0.0 --cutmix_prob 0.0 --num_workers 4 --augment_factor 1 --kc_augment_factor 1 --seed 42 --early_kc_weight 5.115647374077351 --kc_weight 7.626070004892245 --focal_gamma 1.0843936919733927 --val_frequency 1 --early_stopping 3 --dropout 0.5 --use_separate_test_sampler --use_balanced_task_sampler --kc_shot_multiplier 2.3445830391695934 --early_kc_shot_multiplier 1.2459523943929156 --use_contrastive --contrastive_weight 0.6666204505949068 --temperature 0.022021020730573788 --hard_mining_ratio 0.7651463918651594 --early_normal_weight 1.5398066557991035 --kc_normal_weight 1.0 --use_lr_scheduler --scheduler_type plateau --plateau_factor 0.5 --plateau_patience 2 --min_lr 1e-6
++ python train_balanced_task_sampling.py --train_csv /home/<USER>/balanced_task_sampling/split_result/train_set.csv --val_csv /home/<USER>/balanced_task_sampling/split_result/val_set.csv --test_csv /home/<USER>/balanced_task_sampling/split_result/test_set.csv --epochs 50 --lr 0.0002 --weight_decay 5e-4 --feature_dim 512 --save_dir /home/<USER>/balanced_task_sampling/results_trial_20250524_062458 --proto_counts 4,5,4 --pretrained --inner_lr 0.347389 --inner_steps 5 --n_way 3 --n_shot 5 --n_query 15 --tasks_per_epoch 30 --meta_batch_size 8 --no_augmentation --mixup_alpha 0.0 --cutmix_prob 0.0 --num_workers 4 --augment_factor 1 --kc_augment_factor 1 --seed 42 --early_kc_weight 5.115647374077351 --kc_weight 7.626070004892245 --focal_gamma 1.0843936919733927 --val_frequency 1 --early_stopping 3 --dropout 0.5 --use_separate_test_sampler --use_balanced_task_sampler --kc_shot_multiplier 2.3445830391695934 --early_kc_shot_multiplier 1.2459523943929156 --use_contrastive --contrastive_weight 0.6666204505949068 --temperature 0.022021020730573788 --hard_mining_ratio 0.7651463918651594 --early_normal_weight 1.5398066557991035 --kc_normal_weight 1.0 --use_lr_scheduler --scheduler_type plateau --plateau_factor 0.5 --plateau_patience 2 --min_lr 1e-6
/home/<USER>/anaconda3/envs/kc1/lib/python3.10/site-packages/torchvision/models/_utils.py:208: UserWarning: The parameter 'pretrained' is deprecated since 0.13 and may be removed in the future, please use 'weights' instead.
  warnings.warn(
/home/<USER>/anaconda3/envs/kc1/lib/python3.10/site-packages/torchvision/models/_utils.py:223: UserWarning: Arguments other than a weight enum or `None` for 'weights' are deprecated since 0.13 and may be removed in the future. The current behavior is equivalent to passing `weights=ResNet34_Weights.IMAGENET1K_V1`. You can also use `weights=ResNet34_Weights.DEFAULT` to get the most up-to-date weights.
  warnings.warn(msg)
/home/<USER>/anaconda3/envs/kc1/lib/python3.10/site-packages/torch/optim/lr_scheduler.py:62: UserWarning: The verbose parameter is deprecated. Please use get_last_lr() to access the learning rate.
  warnings.warn(

Training tasks:   0%|          | 0/30 [00:00<?, ?it/s]
Training tasks:   3%|▎         | 1/30 [00:04<02:24,  4.97s/it]
Training tasks:   7%|▋         | 2/30 [00:06<01:18,  2.79s/it]
Training tasks:  10%|█         | 3/30 [00:07<00:55,  2.04s/it]
Training tasks:  13%|█▎        | 4/30 [00:08<00:44,  1.72s/it]
Training tasks:  17%|█▋        | 5/30 [00:09<00:39,  1.58s/it]
Training tasks:  20%|██        | 6/30 [00:11<00:36,  1.52s/it]
Training tasks:  23%|██▎       | 7/30 [00:12<00:34,  1.48s/it]
Training tasks:  27%|██▋       | 8/30 [00:14<00:31,  1.45s/it]
Training tasks:  30%|███       | 9/30 [00:15<00:29,  1.40s/it]
Training tasks:  33%|███▎      | 10/30 [00:16<00:26,  1.35s/it]
Training tasks:  37%|███▋      | 11/30 [00:17<00:25,  1.32s/it]
Training tasks:  40%|████      | 12/30 [00:19<00:22,  1.27s/it]
Training tasks:  43%|████▎     | 13/30 [00:20<00:22,  1.30s/it]
Training tasks:  47%|████▋     | 14/30 [00:21<00:20,  1.26s/it]
Training tasks:  50%|█████     | 15/30 [00:22<00:18,  1.24s/it]
Training tasks:  53%|█████▎    | 16/30 [00:23<00:16,  1.20s/it]
Training tasks:  57%|█████▋    | 17/30 [00:25<00:15,  1.20s/it]
Training tasks:  60%|██████    | 18/30 [00:26<00:14,  1.19s/it]
Training tasks:  63%|██████▎   | 19/30 [00:27<00:13,  1.21s/it]
Training tasks:  67%|██████▋   | 20/30 [00:28<00:11,  1.20s/it]
Training tasks:  70%|███████   | 21/30 [00:29<00:10,  1.19s/it]
Training tasks:  73%|███████▎  | 22/30 [00:30<00:09,  1.16s/it]
Training tasks:  77%|███████▋  | 23/30 [00:32<00:08,  1.18s/it]
Training tasks:  80%|████████  | 24/30 [00:33<00:07,  1.19s/it]
Training tasks:  83%|████████▎ | 25/30 [00:34<00:06,  1.20s/it]
Training tasks:  87%|████████▋ | 26/30 [00:35<00:04,  1.20s/it]
Training tasks:  90%|█████████ | 27/30 [00:37<00:03,  1.26s/it]
Training tasks:  93%|█████████▎| 28/30 [00:38<00:02,  1.29s/it]
Training tasks:  97%|█████████▋| 29/30 [00:39<00:01,  1.27s/it]
Training tasks: 100%|██████████| 30/30 [00:41<00:00,  1.28s/it]
Training tasks: 100%|██████████| 30/30 [00:41<00:00,  1.37s/it]

Validating tasks:   0%|          | 0/10 [00:00<?, ?it/s]
Validating tasks:  10%|█         | 1/10 [00:00<00:01,  4.57it/s]
Validating tasks:  20%|██        | 2/10 [00:00<00:01,  4.99it/s]
Validating tasks:  30%|███       | 3/10 [00:00<00:01,  5.43it/s]
Validating tasks:  40%|████      | 4/10 [00:00<00:01,  5.63it/s]
Validating tasks:  50%|█████     | 5/10 [00:00<00:00,  6.03it/s]
Validating tasks:  60%|██████    | 6/10 [00:01<00:00,  5.97it/s]
Validating tasks:  70%|███████   | 7/10 [00:01<00:00,  5.82it/s]
Validating tasks:  80%|████████  | 8/10 [00:01<00:00,  5.73it/s]
Validating tasks:  90%|█████████ | 9/10 [00:01<00:00,  5.71it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  5.71it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  5.66it/s]

Training tasks:   0%|          | 0/30 [00:00<?, ?it/s]
Training tasks:   3%|▎         | 1/30 [00:01<00:39,  1.37s/it]
Training tasks:   7%|▋         | 2/30 [00:02<00:35,  1.28s/it]
Training tasks:  10%|█         | 3/30 [00:03<00:33,  1.23s/it]
Training tasks:  13%|█▎        | 4/30 [00:04<00:31,  1.20s/it]
Training tasks:  17%|█▋        | 5/30 [00:06<00:30,  1.20s/it]
Training tasks:  20%|██        | 6/30 [00:07<00:28,  1.20s/it]
Training tasks:  23%|██▎       | 7/30 [00:08<00:27,  1.20s/it]
Training tasks:  27%|██▋       | 8/30 [00:09<00:25,  1.18s/it]
Training tasks:  30%|███       | 9/30 [00:10<00:24,  1.15s/it]
Training tasks:  33%|███▎      | 10/30 [00:11<00:22,  1.12s/it]
Training tasks:  37%|███▋      | 11/30 [00:12<00:21,  1.14s/it]
Training tasks:  40%|████      | 12/30 [00:14<00:20,  1.15s/it]
Training tasks:  43%|████▎     | 13/30 [00:15<00:19,  1.15s/it]
Training tasks:  47%|████▋     | 14/30 [00:16<00:18,  1.16s/it]
Training tasks:  50%|█████     | 15/30 [00:17<00:17,  1.17s/it]
Training tasks:  53%|█████▎    | 16/30 [00:18<00:17,  1.22s/it]
Training tasks:  57%|█████▋    | 17/30 [00:20<00:16,  1.27s/it]
Training tasks:  60%|██████    | 18/30 [00:21<00:15,  1.32s/it]
Training tasks:  63%|██████▎   | 19/30 [00:23<00:14,  1.32s/it]
Training tasks:  67%|██████▋   | 20/30 [00:24<00:13,  1.35s/it]
Training tasks:  70%|███████   | 21/30 [00:25<00:12,  1.36s/it]
Training tasks:  73%|███████▎  | 22/30 [00:27<00:10,  1.37s/it]
Training tasks:  77%|███████▋  | 23/30 [00:28<00:09,  1.37s/it]
Training tasks:  80%|████████  | 24/30 [00:29<00:07,  1.31s/it]
Training tasks:  83%|████████▎ | 25/30 [00:31<00:06,  1.28s/it]
Training tasks:  87%|████████▋ | 26/30 [00:32<00:04,  1.25s/it]
Training tasks:  90%|█████████ | 27/30 [00:33<00:03,  1.27s/it]
Training tasks:  93%|█████████▎| 28/30 [00:35<00:02,  1.32s/it]
Training tasks:  97%|█████████▋| 29/30 [00:36<00:01,  1.27s/it]
Training tasks: 100%|██████████| 30/30 [00:37<00:00,  1.23s/it]
Training tasks: 100%|██████████| 30/30 [00:37<00:00,  1.24s/it]

Validating tasks:   0%|          | 0/10 [00:00<?, ?it/s]
Validating tasks:  10%|█         | 1/10 [00:00<00:01,  6.08it/s]
Validating tasks:  20%|██        | 2/10 [00:00<00:01,  6.15it/s]
Validating tasks:  30%|███       | 3/10 [00:00<00:01,  6.11it/s]
Validating tasks:  40%|████      | 4/10 [00:00<00:00,  6.03it/s]
Validating tasks:  50%|█████     | 5/10 [00:00<00:00,  6.11it/s]
Validating tasks:  60%|██████    | 6/10 [00:00<00:00,  6.17it/s]
Validating tasks:  70%|███████   | 7/10 [00:01<00:00,  6.14it/s]
Validating tasks:  80%|████████  | 8/10 [00:01<00:00,  6.07it/s]
Validating tasks:  90%|█████████ | 9/10 [00:01<00:00,  6.17it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  6.21it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  6.14it/s]

Training tasks:   0%|          | 0/30 [00:00<?, ?it/s]
Training tasks:   3%|▎         | 1/30 [00:01<00:34,  1.20s/it]
Training tasks:   7%|▋         | 2/30 [00:02<00:34,  1.21s/it]
Training tasks:  10%|█         | 3/30 [00:03<00:34,  1.26s/it]
Training tasks:  13%|█▎        | 4/30 [00:04<00:32,  1.24s/it]
Training tasks:  17%|█▋        | 5/30 [00:05<00:29,  1.17s/it]
Training tasks:  20%|██        | 6/30 [00:07<00:29,  1.24s/it]
Training tasks:  23%|██▎       | 7/30 [00:08<00:29,  1.30s/it]
Training tasks:  27%|██▋       | 8/30 [00:09<00:27,  1.27s/it]
Training tasks:  30%|███       | 9/30 [00:11<00:25,  1.24s/it]
Training tasks:  33%|███▎      | 10/30 [00:12<00:24,  1.25s/it]
Training tasks:  37%|███▋      | 11/30 [00:13<00:24,  1.28s/it]
Training tasks:  40%|████      | 12/30 [00:15<00:23,  1.28s/it]
Training tasks:  43%|████▎     | 13/30 [00:16<00:21,  1.25s/it]
Training tasks:  47%|████▋     | 14/30 [00:17<00:19,  1.22s/it]
Training tasks:  50%|█████     | 15/30 [00:18<00:18,  1.24s/it]
Training tasks:  53%|█████▎    | 16/30 [00:19<00:16,  1.21s/it]
Training tasks:  57%|█████▋    | 17/30 [00:21<00:15,  1.21s/it]
Training tasks:  60%|██████    | 18/30 [00:22<00:14,  1.19s/it]
Training tasks:  63%|██████▎   | 19/30 [00:23<00:13,  1.19s/it]
Training tasks:  67%|██████▋   | 20/30 [00:24<00:12,  1.23s/it]
Training tasks:  70%|███████   | 21/30 [00:26<00:11,  1.26s/it]
Training tasks:  73%|███████▎  | 22/30 [00:27<00:09,  1.22s/it]
Training tasks:  77%|███████▋  | 23/30 [00:28<00:08,  1.20s/it]
Training tasks:  80%|████████  | 24/30 [00:29<00:06,  1.15s/it]
Training tasks:  83%|████████▎ | 25/30 [00:30<00:05,  1.11s/it]
Training tasks:  87%|████████▋ | 26/30 [00:31<00:04,  1.13s/it]
Training tasks:  90%|█████████ | 27/30 [00:32<00:03,  1.14s/it]
Training tasks:  93%|█████████▎| 28/30 [00:33<00:02,  1.13s/it]
Training tasks:  97%|█████████▋| 29/30 [00:34<00:01,  1.13s/it]
Training tasks: 100%|██████████| 30/30 [00:36<00:00,  1.15s/it]
Training tasks: 100%|██████████| 30/30 [00:36<00:00,  1.20s/it]

Validating tasks:   0%|          | 0/10 [00:00<?, ?it/s]
Validating tasks:  10%|█         | 1/10 [00:00<00:01,  4.54it/s]
Validating tasks:  20%|██        | 2/10 [00:00<00:01,  4.43it/s]
Validating tasks:  30%|███       | 3/10 [00:00<00:01,  5.42it/s]
Validating tasks:  40%|████      | 4/10 [00:00<00:01,  5.66it/s]
Validating tasks:  50%|█████     | 5/10 [00:00<00:00,  5.84it/s]
Validating tasks:  60%|██████    | 6/10 [00:01<00:00,  6.25it/s]
Validating tasks:  70%|███████   | 7/10 [00:01<00:00,  6.21it/s]
Validating tasks:  80%|████████  | 8/10 [00:01<00:00,  6.22it/s]
Validating tasks:  90%|█████████ | 9/10 [00:01<00:00,  6.20it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  6.20it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  5.89it/s]

Training tasks:   0%|          | 0/30 [00:00<?, ?it/s]
Training tasks:   3%|▎         | 1/30 [00:01<00:35,  1.23s/it]
Training tasks:   7%|▋         | 2/30 [00:02<00:33,  1.20s/it]
Training tasks:  10%|█         | 3/30 [00:03<00:29,  1.11s/it]
Training tasks:  13%|█▎        | 4/30 [00:04<00:28,  1.11s/it]
Training tasks:  17%|█▋        | 5/30 [00:05<00:29,  1.19s/it]
Training tasks:  20%|██        | 6/30 [00:07<00:30,  1.27s/it]
Training tasks:  23%|██▎       | 7/30 [00:08<00:28,  1.25s/it]
Training tasks:  27%|██▋       | 8/30 [00:09<00:26,  1.22s/it]
Training tasks:  30%|███       | 9/30 [00:10<00:25,  1.21s/it]
Training tasks:  33%|███▎      | 10/30 [00:11<00:23,  1.16s/it]
Training tasks:  37%|███▋      | 11/30 [00:13<00:22,  1.16s/it]
Training tasks:  40%|████      | 12/30 [00:14<00:23,  1.28s/it]
Training tasks:  43%|████▎     | 13/30 [00:15<00:22,  1.31s/it]
Training tasks:  47%|████▋     | 14/30 [00:17<00:20,  1.30s/it]
Training tasks:  50%|█████     | 15/30 [00:18<00:20,  1.33s/it]
Training tasks:  53%|█████▎    | 16/30 [00:20<00:18,  1.35s/it]
Training tasks:  57%|█████▋    | 17/30 [00:21<00:16,  1.30s/it]
Training tasks:  60%|██████    | 18/30 [00:22<00:15,  1.28s/it]
Training tasks:  63%|██████▎   | 19/30 [00:23<00:13,  1.26s/it]
Training tasks:  67%|██████▋   | 20/30 [00:24<00:11,  1.19s/it]
Training tasks:  70%|███████   | 21/30 [00:26<00:11,  1.27s/it]
Training tasks:  73%|███████▎  | 22/30 [00:27<00:10,  1.35s/it]
Training tasks:  77%|███████▋  | 23/30 [00:28<00:09,  1.30s/it]
Training tasks:  80%|████████  | 24/30 [00:30<00:07,  1.28s/it]
Training tasks:  83%|████████▎ | 25/30 [00:31<00:06,  1.31s/it]
Training tasks:  87%|████████▋ | 26/30 [00:32<00:05,  1.35s/it]
Training tasks:  90%|█████████ | 27/30 [00:34<00:04,  1.35s/it]
Training tasks:  93%|█████████▎| 28/30 [00:35<00:02,  1.30s/it]
Training tasks:  97%|█████████▋| 29/30 [00:36<00:01,  1.26s/it]
Training tasks: 100%|██████████| 30/30 [00:37<00:00,  1.24s/it]
Training tasks: 100%|██████████| 30/30 [00:37<00:00,  1.26s/it]

Validating tasks:   0%|          | 0/10 [00:00<?, ?it/s]
Validating tasks:  10%|█         | 1/10 [00:00<00:01,  6.56it/s]
Validating tasks:  20%|██        | 2/10 [00:00<00:01,  6.38it/s]
Validating tasks:  30%|███       | 3/10 [00:00<00:01,  6.31it/s]
Validating tasks:  40%|████      | 4/10 [00:00<00:00,  6.27it/s]
Validating tasks:  50%|█████     | 5/10 [00:00<00:00,  6.34it/s]
Validating tasks:  60%|██████    | 6/10 [00:00<00:00,  6.10it/s]
Validating tasks:  70%|███████   | 7/10 [00:01<00:00,  6.15it/s]
Validating tasks:  80%|████████  | 8/10 [00:01<00:00,  6.17it/s]
Validating tasks:  90%|█████████ | 9/10 [00:01<00:00,  6.16it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  6.20it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  6.22it/s]

Testing tasks:   0%|          | 0/30 [00:00<?, ?it/s]
Testing tasks:   3%|▎         | 1/30 [00:00<00:07,  3.94it/s]
Testing tasks:   7%|▋         | 2/30 [00:00<00:06,  4.41it/s]
Testing tasks:  10%|█         | 3/30 [00:00<00:06,  4.41it/s]
Testing tasks:  13%|█▎        | 4/30 [00:00<00:05,  4.36it/s]
Testing tasks:  17%|█▋        | 5/30 [00:01<00:05,  4.61it/s]
Testing tasks:  20%|██        | 6/30 [00:01<00:04,  4.86it/s]
Testing tasks:  23%|██▎       | 7/30 [00:01<00:04,  4.95it/s]
Testing tasks:  27%|██▋       | 8/30 [00:01<00:04,  4.82it/s]
Testing tasks:  30%|███       | 9/30 [00:01<00:04,  4.97it/s]
Testing tasks:  33%|███▎      | 10/30 [00:02<00:04,  4.78it/s]
Testing tasks:  37%|███▋      | 11/30 [00:02<00:04,  4.63it/s]
Testing tasks:  40%|████      | 12/30 [00:02<00:03,  4.63it/s]
Testing tasks:  43%|████▎     | 13/30 [00:02<00:03,  4.56it/s]
Testing tasks:  47%|████▋     | 14/30 [00:03<00:03,  4.52it/s]
Testing tasks:  50%|█████     | 15/30 [00:03<00:03,  4.60it/s]
Testing tasks:  53%|█████▎    | 16/30 [00:03<00:02,  4.79it/s]
Testing tasks:  57%|█████▋    | 17/30 [00:03<00:02,  4.98it/s]
Testing tasks:  60%|██████    | 18/30 [00:03<00:02,  5.07it/s]
Testing tasks:  63%|██████▎   | 19/30 [00:04<00:02,  4.74it/s]
Testing tasks:  67%|██████▋   | 20/30 [00:04<00:02,  4.61it/s]
Testing tasks:  70%|███████   | 21/30 [00:04<00:01,  4.53it/s]
Testing tasks:  73%|███████▎  | 22/30 [00:04<00:01,  4.42it/s]
Testing tasks:  77%|███████▋  | 23/30 [00:04<00:01,  4.37it/s]
Testing tasks:  80%|████████  | 24/30 [00:05<00:01,  4.37it/s]
Testing tasks:  83%|████████▎ | 25/30 [00:05<00:01,  4.41it/s]
Testing tasks:  87%|████████▋ | 26/30 [00:05<00:00,  4.40it/s]
Testing tasks:  90%|█████████ | 27/30 [00:05<00:00,  4.40it/s]
Testing tasks:  93%|█████████▎| 28/30 [00:06<00:00,  4.39it/s]
Testing tasks:  97%|█████████▋| 29/30 [00:06<00:00,  4.38it/s]
Testing tasks: 100%|██████████| 30/30 [00:06<00:00,  4.38it/s]
Testing tasks: 100%|██████████| 30/30 [00:06<00:00,  4.57it/s]

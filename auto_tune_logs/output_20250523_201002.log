STDOUT:
===================== 训练配置 =====================
GPU: 0
输出目录: /home/<USER>/balanced_task_sampling/results_trial_20250523_201002
日志文件: /home/<USER>/balanced_task_sampling/results_trial_20250523_201002/main_training_trial_30_20250523_201002.log

--------- 优化参数 ---------
内部学习率: 0.347389
训练轮数: 50
每轮任务数: 30

--------- 模型参数 ---------
使用类别权重: 圆锥角膜=4.245436046187983, 早期圆锥角膜=4.460078954446798
温度参数: 0.03220906130640179
硬样本挖掘比例: 0.45820489254023744
对比学习权重: 0.46819033114181163
=============================================

创建目录: /home/<USER>/balanced_task_sampling/results_trial_20250523_201002
总用量 4
-rw-rw-r-- 1 <USER> <GROUP> 659 5月  23 20:10 main_training_trial_30_20250523_201002.log
开始无数据增强的平衡任务采样训练...
使用GPU: 0
直接运行训练脚本...
成功导入增强版特征提取器
成功导入归一化特征提取器
成功导入增强版损失函数
成功导入增强版MAML模型
成功导入KC特定增强模块
成功导入增强版GPU数据增强模块
使用设备: cuda
禁用所有数据增强，直接加载原始数据集...
使用平衡任务采样器
类别 normal (标签 0): 164个样本
类别 e-kc (标签 1): 26个样本
类别 kc (标签 2): 133个样本
使用原型配置: [2, 4, 2]
使用增强版特征提取器，dropout率: 0.5
使用增强版MAML-ProtoNet模型，内循环学习率: 0.347389, 内循环步数: 5
使用ReduceLROnPlateau学习率调度器，因子: 0.5, 耐心值: 2
开始训练，共50个epoch
Epoch 1/50
当前学习率: 0.000200
使用类别权重: 正常=1.0, 早期圆锥角膜=4.460078954446798, 圆锥角膜=4.245436046187983
使用增强版对比损失函数，温度: 0.03220906130640179, 硬负样本挖掘比例: 0.45820489254023744, 早期圆锥角膜与正常样本对比权重: 1.3192227556282015, 圆锥角膜与正常样本对比权重: 1.0
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
警告: 没有足够的有效类别进行3-way任务采样，尝试调整参数...
类别样本数量: {0: 33, 1: 5, 2: 29}
调整后的参数: n_shot=2, n_query=3
类别 normal (标签 0): 33个样本
类别 e-kc (标签 1): 5个样本
类别 kc (标签 2): 29个样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
Epoch 1: Train Loss: 1.4346, Train Acc: 0.4541, Val Loss: 1.0245, Val Acc: 0.5556
保存最佳模型，验证准确率: 0.5556
Epoch 2/50
当前学习率: 0.000200
使用类别权重: 正常=1.0, 早期圆锥角膜=4.460078954446798, 圆锥角膜=4.245436046187983
使用增强版对比损失函数，温度: 0.03220906130640179, 硬负样本挖掘比例: 0.45820489254023744, 早期圆锥角膜与正常样本对比权重: 1.3192227556282015, 圆锥角膜与正常样本对比权重: 1.0
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
警告: 没有足够的有效类别进行3-way任务采样，尝试调整参数...
类别样本数量: {0: 33, 1: 5, 2: 29}
调整后的参数: n_shot=2, n_query=3
类别 normal (标签 0): 33个样本
类别 e-kc (标签 1): 5个样本
类别 kc (标签 2): 29个样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
Epoch 2: Train Loss: 1.1698, Train Acc: 0.6348, Val Loss: 1.0483, Val Acc: 0.5000
验证准确率未提高，当前耐心值: 1/3
Epoch 3/50
当前学习率: 0.000200
使用类别权重: 正常=1.0, 早期圆锥角膜=4.460078954446798, 圆锥角膜=4.245436046187983
使用增强版对比损失函数，温度: 0.03220906130640179, 硬负样本挖掘比例: 0.45820489254023744, 早期圆锥角膜与正常样本对比权重: 1.3192227556282015, 圆锥角膜与正常样本对比权重: 1.0
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
警告: 没有足够的有效类别进行3-way任务采样，尝试调整参数...
类别样本数量: {0: 33, 1: 5, 2: 29}
调整后的参数: n_shot=2, n_query=3
类别 normal (标签 0): 33个样本
类别 e-kc (标签 1): 5个样本
类别 kc (标签 2): 29个样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
Epoch 3: Train Loss: 0.9922, Train Acc: 0.7593, Val Loss: 1.1362, Val Acc: 0.5222
验证准确率未提高，当前耐心值: 2/3
Epoch 4/50
当前学习率: 0.000200
使用类别权重: 正常=1.0, 早期圆锥角膜=4.460078954446798, 圆锥角膜=4.245436046187983
使用增强版对比损失函数，温度: 0.03220906130640179, 硬负样本挖掘比例: 0.45820489254023744, 早期圆锥角膜与正常样本对比权重: 1.3192227556282015, 圆锥角膜与正常样本对比权重: 1.0
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
警告: 没有足够的有效类别进行3-way任务采样，尝试调整参数...
类别样本数量: {0: 33, 1: 5, 2: 29}
调整后的参数: n_shot=2, n_query=3
类别 normal (标签 0): 33个样本
类别 e-kc (标签 1): 5个样本
类别 kc (标签 2): 29个样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
Epoch 4: Train Loss: 0.8401, Train Acc: 0.8170, Val Loss: 1.0532, Val Acc: 0.6444
保存最佳模型，验证准确率: 0.6444
Epoch 5/50
当前学习率: 0.000200
使用类别权重: 正常=1.0, 早期圆锥角膜=4.460078954446798, 圆锥角膜=4.245436046187983
使用增强版对比损失函数，温度: 0.03220906130640179, 硬负样本挖掘比例: 0.45820489254023744, 早期圆锥角膜与正常样本对比权重: 1.3192227556282015, 圆锥角膜与正常样本对比权重: 1.0
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
警告: 没有足够的有效类别进行3-way任务采样，尝试调整参数...
类别样本数量: {0: 33, 1: 5, 2: 29}
调整后的参数: n_shot=2, n_query=3
类别 normal (标签 0): 33个样本
类别 e-kc (标签 1): 5个样本
类别 kc (标签 2): 29个样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
Epoch 5: Train Loss: 0.7269, Train Acc: 0.8800, Val Loss: 0.9137, Val Acc: 0.6667
保存最佳模型，验证准确率: 0.6667
Epoch 6/50
当前学习率: 0.000200
使用类别权重: 正常=1.0, 早期圆锥角膜=4.460078954446798, 圆锥角膜=4.245436046187983
使用增强版对比损失函数，温度: 0.03220906130640179, 硬负样本挖掘比例: 0.45820489254023744, 早期圆锥角膜与正常样本对比权重: 1.3192227556282015, 圆锥角膜与正常样本对比权重: 1.0
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
警告: 没有足够的有效类别进行3-way任务采样，尝试调整参数...
类别样本数量: {0: 33, 1: 5, 2: 29}
调整后的参数: n_shot=2, n_query=3
类别 normal (标签 0): 33个样本
类别 e-kc (标签 1): 5个样本
类别 kc (标签 2): 29个样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
Epoch 6: Train Loss: 0.7041, Train Acc: 0.8600, Val Loss: 0.8753, Val Acc: 0.6556
验证准确率未提高，当前耐心值: 1/3
Epoch 7/50
当前学习率: 0.000200
使用类别权重: 正常=1.0, 早期圆锥角膜=4.460078954446798, 圆锥角膜=4.245436046187983
使用增强版对比损失函数，温度: 0.03220906130640179, 硬负样本挖掘比例: 0.45820489254023744, 早期圆锥角膜与正常样本对比权重: 1.3192227556282015, 圆锥角膜与正常样本对比权重: 1.0
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
警告: 没有足够的有效类别进行3-way任务采样，尝试调整参数...
类别样本数量: {0: 33, 1: 5, 2: 29}
调整后的参数: n_shot=2, n_query=3
类别 normal (标签 0): 33个样本
类别 e-kc (标签 1): 5个样本
类别 kc (标签 2): 29个样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
Epoch 7: Train Loss: 0.6333, Train Acc: 0.8800, Val Loss: 0.9659, Val Acc: 0.6667
验证准确率未提高，当前耐心值: 2/3
Epoch 8/50
当前学习率: 0.000200
使用类别权重: 正常=1.0, 早期圆锥角膜=4.460078954446798, 圆锥角膜=4.245436046187983
使用增强版对比损失函数，温度: 0.03220906130640179, 硬负样本挖掘比例: 0.45820489254023744, 早期圆锥角膜与正常样本对比权重: 1.3192227556282015, 圆锥角膜与正常样本对比权重: 1.0
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 8 个支持集样本
警告: 没有足够的有效类别进行3-way任务采样，尝试调整参数...
类别样本数量: {0: 33, 1: 5, 2: 29}
调整后的参数: n_shot=2, n_query=3
类别 normal (标签 0): 33个样本
类别 e-kc (标签 1): 5个样本
类别 kc (标签 2): 29个样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 3 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
Epoch 8: Train Loss: 0.7413, Train Acc: 0.8193, Val Loss: 0.8480, Val Acc: 0.6444
验证准确率未提高，当前耐心值: 3/3
早停触发，停止训练
警告: 没有足够的有效类别进行3-way任务采样，尝试调整参数...
类别样本数量: {0: 37, 1: 7, 2: 27}
调整后的参数: n_shot=3, n_query=4
类别 normal (标签 0): 37个样本
类别 e-kc (标签 1): 7个样本
类别 kc (标签 2): 27个样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 4 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 4 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 4 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 4 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 4 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 4 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 4 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 4 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 4 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 4 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 4 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 4 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 4 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 4 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 4 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 4 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 4 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 4 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 4 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 4 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 4 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 4 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 4 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 4 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 4 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 4 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 4 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 4 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 4 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 4 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本

测试结果:
总体准确率: 79.17%
类别准确率:
  kc: 54.17%
  e-kc: 83.33%
  normal: 100.00%
训练完成，结果已保存到 /home/<USER>/balanced_task_sampling/results_trial_20250523_201002

STDERR:
+ TIMESTAMP=20250523_201002
+ RESULT_DIR=/home/<USER>/balanced_task_sampling/results_trial_20250523_201002
+ '[' -n /home/<USER>/balanced_task_sampling/results_trial_20250523_201002/main_training_trial_30_20250523_201002.log ']'
+ LOG_FILE=/home/<USER>/balanced_task_sampling/results_trial_20250523_201002/main_training_trial_30_20250523_201002.log
++ dirname /home/<USER>/balanced_task_sampling/results_trial_20250523_201002/main_training_trial_30_20250523_201002.log
+ mkdir -p /home/<USER>/balanced_task_sampling/results_trial_20250523_201002
+ mkdir -p /home/<USER>/balanced_task_sampling/results_trial_20250523_201002
+ echo '===================== 训练配置 ====================='
+ echo 'GPU: 0'
+ echo '输出目录: /home/<USER>/balanced_task_sampling/results_trial_20250523_201002'
+ echo '日志文件: /home/<USER>/balanced_task_sampling/results_trial_20250523_201002/main_training_trial_30_20250523_201002.log'
+ echo ''
+ echo '--------- 优化参数 ---------'
+ echo '内部学习率: 0.347389'
+ echo '训练轮数: 50'
+ echo '每轮任务数: 30'
+ echo ''
+ echo '--------- 模型参数 ---------'
+ echo '使用类别权重: 圆锥角膜=4.245436046187983, 早期圆锥角膜=4.460078954446798'
+ echo '温度参数: 0.03220906130640179'
+ echo '硬样本挖掘比例: 0.45820489254023744'
+ echo '对比学习权重: 0.46819033114181163'
+ echo '=============================================
'
+ tee -a /home/<USER>/balanced_task_sampling/results_trial_20250523_201002/main_training_trial_30_20250523_201002.log
+ validate_params
+ local error_count=0
++ dirname /home/<USER>/balanced_task_sampling/results_trial_20250523_201002/main_training_trial_30_20250523_201002.log
++ dirname /home/<USER>/balanced_task_sampling/split_result/train_set.csv
+ for dir in "$RESULT_DIR" "$(dirname "$LOG_FILE")" "$(dirname "$TRAIN_CSV")"
+ mkdir -p /home/<USER>/balanced_task_sampling/results_trial_20250523_201002
+ for dir in "$RESULT_DIR" "$(dirname "$LOG_FILE")" "$(dirname "$TRAIN_CSV")"
+ mkdir -p /home/<USER>/balanced_task_sampling/results_trial_20250523_201002
+ for dir in "$RESULT_DIR" "$(dirname "$LOG_FILE")" "$(dirname "$TRAIN_CSV")"
+ mkdir -p /home/<USER>/balanced_task_sampling/split_result
+ for csv in "$TRAIN_CSV" "$VAL_CSV" "$TEST_CSV"
+ '[' '!' -f /home/<USER>/balanced_task_sampling/split_result/train_set.csv ']'
+ for csv in "$TRAIN_CSV" "$VAL_CSV" "$TEST_CSV"
+ '[' '!' -f /home/<USER>/balanced_task_sampling/split_result/val_set.csv ']'
+ for csv in "$TRAIN_CSV" "$VAL_CSV" "$TEST_CSV"
+ '[' '!' -f /home/<USER>/balanced_task_sampling/split_result/test_set.csv ']'
+ [[ 0.0002 =~ ^[0-9.]+$ ]]
+ [[ 0.347389 =~ ^[0-9.]+$ ]]
+ '[' 0 -gt 0 ']'
+ return 0
+ '[' false = true ']'
+ export CUDA_VISIBLE_DEVICES=0
+ CUDA_VISIBLE_DEVICES=0
+ export OMP_NUM_THREADS=1
+ OMP_NUM_THREADS=1
+ export MKL_NUM_THREADS=1
+ MKL_NUM_THREADS=1
+ export MPLBACKEND=Agg
+ MPLBACKEND=Agg
+ mkdir -p /home/<USER>/balanced_task_sampling/results_trial_20250523_201002
+ echo '创建目录: /home/<USER>/balanced_task_sampling/results_trial_20250523_201002'
+ ls -l /home/<USER>/balanced_task_sampling/results_trial_20250523_201002
+ PRETRAINED_ARGS=
+ '[' true = true ']'
+ PRETRAINED_ARGS=--pretrained
+ AUGMENT_ARGS=--no_augmentation
+ CONTRASTIVE_ARGS=
+ '[' true = true ']'
+ CONTRASTIVE_ARGS='--use_contrastive --contrastive_weight 0.46819033114181163 --temperature 0.03220906130640179 --hard_mining_ratio 0.45820489254023744 --early_normal_weight 1.3192227556282015 --kc_normal_weight 1.0'
+ NORMALIZE_ARGS=
+ '[' false = true ']'
+ SAMPLER_ARGS=
+ '[' true = true ']'
+ SAMPLER_ARGS=' --use_separate_test_sampler'
+ '[' true = true ']'
+ SAMPLER_ARGS=' --use_separate_test_sampler --use_balanced_task_sampler --kc_shot_multiplier 1.5046210862166594 --early_kc_shot_multiplier 1.6305190885916117'
+ LR_SCHEDULER_ARGS=
+ '[' true = true ']'
+ LR_SCHEDULER_ARGS='--use_lr_scheduler --scheduler_type plateau'
+ '[' plateau = onecycle ']'
+ '[' plateau = plateau ']'
+ LR_SCHEDULER_ARGS='--use_lr_scheduler --scheduler_type plateau --plateau_factor 0.5 --plateau_patience 2 --min_lr 1e-6'
+ BASE_CMD='python train_balanced_task_sampling.py   --train_csv /home/<USER>/balanced_task_sampling/split_result/train_set.csv   --val_csv /home/<USER>/balanced_task_sampling/split_result/val_set.csv   --test_csv /home/<USER>/balanced_task_sampling/split_result/test_set.csv   --epochs 50   --lr 0.0002   --weight_decay 5e-4   --feature_dim 512   --save_dir /home/<USER>/balanced_task_sampling/results_trial_20250523_201002   --proto_counts 2,4,2   --pretrained   --inner_lr 0.347389   --inner_steps 5   --n_way 3   --n_shot 5   --n_query 15   --tasks_per_epoch 30   --meta_batch_size 8   --no_augmentation   --mixup_alpha 0.0   --cutmix_prob 0.0   --num_workers 4   --augment_factor 1   --kc_augment_factor 1   --seed 42   --early_kc_weight 4.460078954446798   --kc_weight 4.245436046187983   --focal_gamma 2.611361363037405   --val_frequency 1   --early_stopping 3   --dropout 0.5    --use_separate_test_sampler --use_balanced_task_sampler --kc_shot_multiplier 1.5046210862166594 --early_kc_shot_multiplier 1.6305190885916117   --use_contrastive --contrastive_weight 0.46819033114181163 --temperature 0.03220906130640179 --hard_mining_ratio 0.45820489254023744 --early_normal_weight 1.3192227556282015 --kc_normal_weight 1.0      --use_lr_scheduler --scheduler_type plateau --plateau_factor 0.5 --plateau_patience 2 --min_lr 1e-6'
+ echo 开始无数据增强的平衡任务采样训练...
+ echo '使用GPU: 0'
+ '[' direct = nohup ']'
+ echo 直接运行训练脚本...
+ eval python train_balanced_task_sampling.py --train_csv /home/<USER>/balanced_task_sampling/split_result/train_set.csv --val_csv /home/<USER>/balanced_task_sampling/split_result/val_set.csv --test_csv /home/<USER>/balanced_task_sampling/split_result/test_set.csv --epochs 50 --lr 0.0002 --weight_decay 5e-4 --feature_dim 512 --save_dir /home/<USER>/balanced_task_sampling/results_trial_20250523_201002 --proto_counts 2,4,2 --pretrained --inner_lr 0.347389 --inner_steps 5 --n_way 3 --n_shot 5 --n_query 15 --tasks_per_epoch 30 --meta_batch_size 8 --no_augmentation --mixup_alpha 0.0 --cutmix_prob 0.0 --num_workers 4 --augment_factor 1 --kc_augment_factor 1 --seed 42 --early_kc_weight 4.460078954446798 --kc_weight 4.245436046187983 --focal_gamma 2.611361363037405 --val_frequency 1 --early_stopping 3 --dropout 0.5 --use_separate_test_sampler --use_balanced_task_sampler --kc_shot_multiplier 1.5046210862166594 --early_kc_shot_multiplier 1.6305190885916117 --use_contrastive --contrastive_weight 0.46819033114181163 --temperature 0.03220906130640179 --hard_mining_ratio 0.45820489254023744 --early_normal_weight 1.3192227556282015 --kc_normal_weight 1.0 --use_lr_scheduler --scheduler_type plateau --plateau_factor 0.5 --plateau_patience 2 --min_lr 1e-6
++ python train_balanced_task_sampling.py --train_csv /home/<USER>/balanced_task_sampling/split_result/train_set.csv --val_csv /home/<USER>/balanced_task_sampling/split_result/val_set.csv --test_csv /home/<USER>/balanced_task_sampling/split_result/test_set.csv --epochs 50 --lr 0.0002 --weight_decay 5e-4 --feature_dim 512 --save_dir /home/<USER>/balanced_task_sampling/results_trial_20250523_201002 --proto_counts 2,4,2 --pretrained --inner_lr 0.347389 --inner_steps 5 --n_way 3 --n_shot 5 --n_query 15 --tasks_per_epoch 30 --meta_batch_size 8 --no_augmentation --mixup_alpha 0.0 --cutmix_prob 0.0 --num_workers 4 --augment_factor 1 --kc_augment_factor 1 --seed 42 --early_kc_weight 4.460078954446798 --kc_weight 4.245436046187983 --focal_gamma 2.611361363037405 --val_frequency 1 --early_stopping 3 --dropout 0.5 --use_separate_test_sampler --use_balanced_task_sampler --kc_shot_multiplier 1.5046210862166594 --early_kc_shot_multiplier 1.6305190885916117 --use_contrastive --contrastive_weight 0.46819033114181163 --temperature 0.03220906130640179 --hard_mining_ratio 0.45820489254023744 --early_normal_weight 1.3192227556282015 --kc_normal_weight 1.0 --use_lr_scheduler --scheduler_type plateau --plateau_factor 0.5 --plateau_patience 2 --min_lr 1e-6
/home/<USER>/anaconda3/envs/kc1/lib/python3.10/site-packages/torchvision/models/_utils.py:208: UserWarning: The parameter 'pretrained' is deprecated since 0.13 and may be removed in the future, please use 'weights' instead.
  warnings.warn(
/home/<USER>/anaconda3/envs/kc1/lib/python3.10/site-packages/torchvision/models/_utils.py:223: UserWarning: Arguments other than a weight enum or `None` for 'weights' are deprecated since 0.13 and may be removed in the future. The current behavior is equivalent to passing `weights=ResNet34_Weights.IMAGENET1K_V1`. You can also use `weights=ResNet34_Weights.DEFAULT` to get the most up-to-date weights.
  warnings.warn(msg)
/home/<USER>/anaconda3/envs/kc1/lib/python3.10/site-packages/torch/optim/lr_scheduler.py:62: UserWarning: The verbose parameter is deprecated. Please use get_last_lr() to access the learning rate.
  warnings.warn(

Training tasks:   0%|          | 0/30 [00:00<?, ?it/s]
Training tasks:   3%|▎         | 1/30 [00:04<02:20,  4.85s/it]
Training tasks:   7%|▋         | 2/30 [00:06<01:18,  2.81s/it]
Training tasks:  10%|█         | 3/30 [00:07<00:56,  2.09s/it]
Training tasks:  13%|█▎        | 4/30 [00:08<00:44,  1.72s/it]
Training tasks:  17%|█▋        | 5/30 [00:09<00:37,  1.51s/it]
Training tasks:  20%|██        | 6/30 [00:10<00:32,  1.37s/it]
Training tasks:  23%|██▎       | 7/30 [00:11<00:29,  1.29s/it]
Training tasks:  27%|██▋       | 8/30 [00:13<00:27,  1.23s/it]
Training tasks:  30%|███       | 9/30 [00:14<00:25,  1.20s/it]
Training tasks:  33%|███▎      | 10/30 [00:15<00:24,  1.23s/it]
Training tasks:  37%|███▋      | 11/30 [00:16<00:22,  1.20s/it]
Training tasks:  40%|████      | 12/30 [00:17<00:22,  1.23s/it]
Training tasks:  43%|████▎     | 13/30 [00:19<00:21,  1.26s/it]
Training tasks:  47%|████▋     | 14/30 [00:20<00:19,  1.22s/it]
Training tasks:  50%|█████     | 15/30 [00:21<00:17,  1.20s/it]
Training tasks:  53%|█████▎    | 16/30 [00:22<00:16,  1.17s/it]
Training tasks:  57%|█████▋    | 17/30 [00:23<00:14,  1.12s/it]
Training tasks:  60%|██████    | 18/30 [00:25<00:14,  1.19s/it]
Training tasks:  63%|██████▎   | 19/30 [00:26<00:13,  1.23s/it]
Training tasks:  67%|██████▋   | 20/30 [00:27<00:12,  1.27s/it]
Training tasks:  70%|███████   | 21/30 [00:28<00:11,  1.26s/it]
Training tasks:  73%|███████▎  | 22/30 [00:30<00:09,  1.20s/it]
Training tasks:  77%|███████▋  | 23/30 [00:31<00:08,  1.17s/it]
Training tasks:  80%|████████  | 24/30 [00:32<00:06,  1.15s/it]
Training tasks:  83%|████████▎ | 25/30 [00:33<00:05,  1.13s/it]
Training tasks:  87%|████████▋ | 26/30 [00:34<00:04,  1.20s/it]
Training tasks:  90%|█████████ | 27/30 [00:35<00:03,  1.19s/it]
Training tasks:  93%|█████████▎| 28/30 [00:36<00:02,  1.18s/it]
Training tasks:  97%|█████████▋| 29/30 [00:38<00:01,  1.16s/it]
Training tasks: 100%|██████████| 30/30 [00:39<00:00,  1.14s/it]
Training tasks: 100%|██████████| 30/30 [00:39<00:00,  1.31s/it]

Validating tasks:   0%|          | 0/10 [00:00<?, ?it/s]
Validating tasks:  10%|█         | 1/10 [00:00<00:01,  4.57it/s]
Validating tasks:  20%|██        | 2/10 [00:00<00:01,  5.35it/s]
Validating tasks:  30%|███       | 3/10 [00:00<00:01,  5.35it/s]
Validating tasks:  40%|████      | 4/10 [00:00<00:01,  5.37it/s]
Validating tasks:  50%|█████     | 5/10 [00:00<00:00,  5.34it/s]
Validating tasks:  60%|██████    | 6/10 [00:01<00:00,  5.43it/s]
Validating tasks:  70%|███████   | 7/10 [00:01<00:00,  6.21it/s]
Validating tasks:  80%|████████  | 8/10 [00:01<00:00,  6.81it/s]
Validating tasks:  90%|█████████ | 9/10 [00:01<00:00,  7.01it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  6.98it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  6.13it/s]

Training tasks:   0%|          | 0/30 [00:00<?, ?it/s]
Training tasks:   3%|▎         | 1/30 [00:01<00:30,  1.04s/it]
Training tasks:   7%|▋         | 2/30 [00:01<00:27,  1.01it/s]
Training tasks:  10%|█         | 3/30 [00:03<00:28,  1.05s/it]
Training tasks:  13%|█▎        | 4/30 [00:04<00:28,  1.08s/it]
Training tasks:  17%|█▋        | 5/30 [00:05<00:27,  1.09s/it]
Training tasks:  20%|██        | 6/30 [00:06<00:26,  1.09s/it]
Training tasks:  23%|██▎       | 7/30 [00:07<00:25,  1.10s/it]
Training tasks:  27%|██▋       | 8/30 [00:08<00:26,  1.19s/it]
Training tasks:  30%|███       | 9/30 [00:10<00:25,  1.20s/it]
Training tasks:  33%|███▎      | 10/30 [00:11<00:23,  1.17s/it]
Training tasks:  37%|███▋      | 11/30 [00:12<00:23,  1.23s/it]
Training tasks:  40%|████      | 12/30 [00:14<00:23,  1.28s/it]
Training tasks:  43%|████▎     | 13/30 [00:15<00:21,  1.26s/it]
Training tasks:  47%|████▋     | 14/30 [00:16<00:19,  1.20s/it]
Training tasks:  50%|█████     | 15/30 [00:17<00:17,  1.17s/it]
Training tasks:  53%|█████▎    | 16/30 [00:18<00:16,  1.14s/it]
Training tasks:  57%|█████▋    | 17/30 [00:19<00:14,  1.13s/it]
Training tasks:  60%|██████    | 18/30 [00:20<00:13,  1.11s/it]
Training tasks:  63%|██████▎   | 19/30 [00:21<00:12,  1.12s/it]
Training tasks:  67%|██████▋   | 20/30 [00:22<00:11,  1.12s/it]
Training tasks:  70%|███████   | 21/30 [00:24<00:10,  1.12s/it]
Training tasks:  73%|███████▎  | 22/30 [00:25<00:09,  1.15s/it]
Training tasks:  77%|███████▋  | 23/30 [00:26<00:08,  1.15s/it]
Training tasks:  80%|████████  | 24/30 [00:27<00:06,  1.10s/it]
Training tasks:  83%|████████▎ | 25/30 [00:28<00:05,  1.06s/it]
Training tasks:  87%|████████▋ | 26/30 [00:29<00:04,  1.08s/it]
Training tasks:  90%|█████████ | 27/30 [00:30<00:03,  1.08s/it]
Training tasks:  93%|█████████▎| 28/30 [00:31<00:02,  1.12s/it]
Training tasks:  97%|█████████▋| 29/30 [00:32<00:01,  1.14s/it]
Training tasks: 100%|██████████| 30/30 [00:34<00:00,  1.19s/it]
Training tasks: 100%|██████████| 30/30 [00:34<00:00,  1.14s/it]

Validating tasks:   0%|          | 0/10 [00:00<?, ?it/s]
Validating tasks:  10%|█         | 1/10 [00:00<00:01,  6.76it/s]
Validating tasks:  20%|██        | 2/10 [00:00<00:01,  6.94it/s]
Validating tasks:  30%|███       | 3/10 [00:00<00:00,  7.73it/s]
Validating tasks:  40%|████      | 4/10 [00:00<00:00,  8.11it/s]
Validating tasks:  50%|█████     | 5/10 [00:00<00:00,  8.38it/s]
Validating tasks:  60%|██████    | 6/10 [00:00<00:00,  8.51it/s]
Validating tasks:  70%|███████   | 7/10 [00:00<00:00,  8.68it/s]
Validating tasks:  80%|████████  | 8/10 [00:00<00:00,  8.51it/s]
Validating tasks:  90%|█████████ | 9/10 [00:01<00:00,  8.59it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  8.48it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  8.26it/s]

Training tasks:   0%|          | 0/30 [00:00<?, ?it/s]
Training tasks:   3%|▎         | 1/30 [00:01<00:38,  1.33s/it]
Training tasks:   7%|▋         | 2/30 [00:02<00:38,  1.37s/it]
Training tasks:  10%|█         | 3/30 [00:04<00:36,  1.36s/it]
Training tasks:  13%|█▎        | 4/30 [00:05<00:32,  1.25s/it]
Training tasks:  17%|█▋        | 5/30 [00:06<00:30,  1.21s/it]
Training tasks:  20%|██        | 6/30 [00:07<00:29,  1.22s/it]
Training tasks:  23%|██▎       | 7/30 [00:08<00:29,  1.26s/it]
Training tasks:  27%|██▋       | 8/30 [00:10<00:27,  1.24s/it]
Training tasks:  30%|███       | 9/30 [00:11<00:25,  1.19s/it]
Training tasks:  33%|███▎      | 10/30 [00:12<00:23,  1.17s/it]
Training tasks:  37%|███▋      | 11/30 [00:13<00:21,  1.14s/it]
Training tasks:  40%|████      | 12/30 [00:14<00:20,  1.13s/it]
Training tasks:  43%|████▎     | 13/30 [00:15<00:19,  1.12s/it]
Training tasks:  47%|████▋     | 14/30 [00:16<00:17,  1.12s/it]
Training tasks:  50%|█████     | 15/30 [00:17<00:16,  1.12s/it]
Training tasks:  53%|█████▎    | 16/30 [00:18<00:15,  1.11s/it]
Training tasks:  57%|█████▋    | 17/30 [00:19<00:14,  1.11s/it]
Training tasks:  60%|██████    | 18/30 [00:21<00:13,  1.10s/it]
Training tasks:  63%|██████▎   | 19/30 [00:22<00:12,  1.10s/it]
Training tasks:  67%|██████▋   | 20/30 [00:23<00:10,  1.08s/it]
Training tasks:  70%|███████   | 21/30 [00:24<00:09,  1.08s/it]
Training tasks:  73%|███████▎  | 22/30 [00:25<00:08,  1.08s/it]
Training tasks:  77%|███████▋  | 23/30 [00:26<00:08,  1.16s/it]
Training tasks:  80%|████████  | 24/30 [00:28<00:07,  1.22s/it]
Training tasks:  83%|████████▎ | 25/30 [00:29<00:06,  1.20s/it]
Training tasks:  87%|████████▋ | 26/30 [00:30<00:04,  1.18s/it]
Training tasks:  90%|█████████ | 27/30 [00:31<00:03,  1.15s/it]
Training tasks:  93%|█████████▎| 28/30 [00:32<00:02,  1.14s/it]
Training tasks:  97%|█████████▋| 29/30 [00:33<00:01,  1.12s/it]
Training tasks: 100%|██████████| 30/30 [00:34<00:00,  1.12s/it]
Training tasks: 100%|██████████| 30/30 [00:34<00:00,  1.16s/it]

Validating tasks:   0%|          | 0/10 [00:00<?, ?it/s]
Validating tasks:  10%|█         | 1/10 [00:00<00:01,  6.84it/s]
Validating tasks:  20%|██        | 2/10 [00:00<00:01,  6.75it/s]
Validating tasks:  30%|███       | 3/10 [00:00<00:01,  6.86it/s]
Validating tasks:  40%|████      | 4/10 [00:00<00:00,  6.95it/s]
Validating tasks:  50%|█████     | 5/10 [00:00<00:00,  6.94it/s]
Validating tasks:  60%|██████    | 6/10 [00:00<00:00,  7.00it/s]
Validating tasks:  70%|███████   | 7/10 [00:01<00:00,  6.97it/s]
Validating tasks:  80%|████████  | 8/10 [00:01<00:00,  6.98it/s]
Validating tasks:  90%|█████████ | 9/10 [00:01<00:00,  6.90it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  6.96it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  6.94it/s]

Training tasks:   0%|          | 0/30 [00:00<?, ?it/s]
Training tasks:   3%|▎         | 1/30 [00:01<00:33,  1.16s/it]
Training tasks:   7%|▋         | 2/30 [00:02<00:32,  1.18s/it]
Training tasks:  10%|█         | 3/30 [00:03<00:32,  1.19s/it]
Training tasks:  13%|█▎        | 4/30 [00:04<00:30,  1.17s/it]
Training tasks:  17%|█▋        | 5/30 [00:05<00:29,  1.17s/it]
Training tasks:  20%|██        | 6/30 [00:06<00:27,  1.16s/it]
Training tasks:  23%|██▎       | 7/30 [00:08<00:26,  1.15s/it]
Training tasks:  27%|██▋       | 8/30 [00:09<00:25,  1.14s/it]
Training tasks:  30%|███       | 9/30 [00:10<00:23,  1.13s/it]
Training tasks:  33%|███▎      | 10/30 [00:11<00:23,  1.18s/it]
Training tasks:  37%|███▋      | 11/30 [00:12<00:22,  1.16s/it]
Training tasks:  40%|████      | 12/30 [00:14<00:21,  1.19s/it]
Training tasks:  43%|████▎     | 13/30 [00:15<00:19,  1.17s/it]
Training tasks:  47%|████▋     | 14/30 [00:16<00:18,  1.15s/it]
Training tasks:  50%|█████     | 15/30 [00:17<00:16,  1.11s/it]
Training tasks:  53%|█████▎    | 16/30 [00:18<00:14,  1.05s/it]
Training tasks:  57%|█████▋    | 17/30 [00:19<00:13,  1.01s/it]
Training tasks:  60%|██████    | 18/30 [00:20<00:12,  1.04s/it]
Training tasks:  63%|██████▎   | 19/30 [00:21<00:12,  1.13s/it]
Training tasks:  67%|██████▋   | 20/30 [00:22<00:11,  1.12s/it]
Training tasks:  70%|███████   | 21/30 [00:23<00:10,  1.12s/it]
Training tasks:  73%|███████▎  | 22/30 [00:24<00:08,  1.12s/it]
Training tasks:  77%|███████▋  | 23/30 [00:25<00:07,  1.11s/it]
Training tasks:  80%|████████  | 24/30 [00:27<00:06,  1.10s/it]
Training tasks:  83%|████████▎ | 25/30 [00:28<00:05,  1.11s/it]
Training tasks:  87%|████████▋ | 26/30 [00:29<00:04,  1.10s/it]
Training tasks:  90%|█████████ | 27/30 [00:30<00:03,  1.10s/it]
Training tasks:  93%|█████████▎| 28/30 [00:31<00:02,  1.10s/it]
Training tasks:  97%|█████████▋| 29/30 [00:32<00:01,  1.17s/it]
Training tasks: 100%|██████████| 30/30 [00:34<00:00,  1.22s/it]
Training tasks: 100%|██████████| 30/30 [00:34<00:00,  1.14s/it]

Validating tasks:   0%|          | 0/10 [00:00<?, ?it/s]
Validating tasks:  10%|█         | 1/10 [00:00<00:01,  7.17it/s]
Validating tasks:  20%|██        | 2/10 [00:00<00:01,  7.04it/s]
Validating tasks:  30%|███       | 3/10 [00:00<00:00,  7.06it/s]
Validating tasks:  40%|████      | 4/10 [00:00<00:00,  7.03it/s]
Validating tasks:  50%|█████     | 5/10 [00:00<00:00,  7.08it/s]
Validating tasks:  60%|██████    | 6/10 [00:00<00:00,  7.04it/s]
Validating tasks:  70%|███████   | 7/10 [00:00<00:00,  7.06it/s]
Validating tasks:  80%|████████  | 8/10 [00:01<00:00,  7.00it/s]
Validating tasks:  90%|█████████ | 9/10 [00:01<00:00,  6.95it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  6.94it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  7.00it/s]

Training tasks:   0%|          | 0/30 [00:00<?, ?it/s]
Training tasks:   3%|▎         | 1/30 [00:01<00:33,  1.15s/it]
Training tasks:   7%|▋         | 2/30 [00:02<00:31,  1.13s/it]
Training tasks:  10%|█         | 3/30 [00:03<00:30,  1.12s/it]
Training tasks:  13%|█▎        | 4/30 [00:04<00:28,  1.11s/it]
Training tasks:  17%|█▋        | 5/30 [00:05<00:27,  1.08s/it]
Training tasks:  20%|██        | 6/30 [00:06<00:26,  1.09s/it]
Training tasks:  23%|██▎       | 7/30 [00:07<00:25,  1.09s/it]
Training tasks:  27%|██▋       | 8/30 [00:08<00:24,  1.09s/it]
Training tasks:  30%|███       | 9/30 [00:09<00:22,  1.09s/it]
Training tasks:  33%|███▎      | 10/30 [00:10<00:21,  1.09s/it]
Training tasks:  37%|███▋      | 11/30 [00:12<00:20,  1.10s/it]
Training tasks:  40%|████      | 12/30 [00:13<00:19,  1.11s/it]
Training tasks:  43%|████▎     | 13/30 [00:14<00:18,  1.12s/it]
Training tasks:  47%|████▋     | 14/30 [00:15<00:17,  1.11s/it]
Training tasks:  50%|█████     | 15/30 [00:16<00:16,  1.12s/it]
Training tasks:  53%|█████▎    | 16/30 [00:17<00:15,  1.11s/it]
Training tasks:  57%|█████▋    | 17/30 [00:18<00:14,  1.11s/it]
Training tasks:  60%|██████    | 18/30 [00:19<00:13,  1.10s/it]
Training tasks:  63%|██████▎   | 19/30 [00:20<00:11,  1.03s/it]
Training tasks:  67%|██████▋   | 20/30 [00:21<00:09,  1.02it/s]
Training tasks:  70%|███████   | 21/30 [00:22<00:09,  1.02s/it]
Training tasks:  73%|███████▎  | 22/30 [00:23<00:08,  1.08s/it]
Training tasks:  77%|███████▋  | 23/30 [00:25<00:07,  1.10s/it]
Training tasks:  80%|████████  | 24/30 [00:26<00:06,  1.11s/it]
Training tasks:  83%|████████▎ | 25/30 [00:27<00:05,  1.11s/it]
Training tasks:  87%|████████▋ | 26/30 [00:28<00:04,  1.13s/it]
Training tasks:  90%|█████████ | 27/30 [00:29<00:03,  1.14s/it]
Training tasks:  93%|█████████▎| 28/30 [00:30<00:02,  1.15s/it]
Training tasks:  97%|█████████▋| 29/30 [00:32<00:01,  1.15s/it]
Training tasks: 100%|██████████| 30/30 [00:33<00:00,  1.15s/it]
Training tasks: 100%|██████████| 30/30 [00:33<00:00,  1.11s/it]

Validating tasks:   0%|          | 0/10 [00:00<?, ?it/s]
Validating tasks:  10%|█         | 1/10 [00:00<00:01,  6.57it/s]
Validating tasks:  20%|██        | 2/10 [00:00<00:01,  6.48it/s]
Validating tasks:  30%|███       | 3/10 [00:00<00:01,  6.50it/s]
Validating tasks:  40%|████      | 4/10 [00:00<00:00,  6.67it/s]
Validating tasks:  50%|█████     | 5/10 [00:00<00:00,  6.57it/s]
Validating tasks:  60%|██████    | 6/10 [00:00<00:00,  6.53it/s]
Validating tasks:  70%|███████   | 7/10 [00:01<00:00,  6.54it/s]
Validating tasks:  80%|████████  | 8/10 [00:01<00:00,  6.50it/s]
Validating tasks:  90%|█████████ | 9/10 [00:01<00:00,  6.45it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  6.42it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  6.49it/s]

Training tasks:   0%|          | 0/30 [00:00<?, ?it/s]
Training tasks:   3%|▎         | 1/30 [00:01<00:35,  1.23s/it]
Training tasks:   7%|▋         | 2/30 [00:02<00:33,  1.19s/it]
Training tasks:  10%|█         | 3/30 [00:03<00:31,  1.15s/it]
Training tasks:  13%|█▎        | 4/30 [00:04<00:30,  1.17s/it]
Training tasks:  17%|█▋        | 5/30 [00:05<00:29,  1.17s/it]
Training tasks:  20%|██        | 6/30 [00:06<00:27,  1.15s/it]
Training tasks:  23%|██▎       | 7/30 [00:08<00:26,  1.14s/it]
Training tasks:  27%|██▋       | 8/30 [00:09<00:25,  1.15s/it]
Training tasks:  30%|███       | 9/30 [00:10<00:23,  1.13s/it]
Training tasks:  33%|███▎      | 10/30 [00:11<00:22,  1.14s/it]
Training tasks:  37%|███▋      | 11/30 [00:12<00:21,  1.15s/it]
Training tasks:  40%|████      | 12/30 [00:13<00:20,  1.15s/it]
Training tasks:  43%|████▎     | 13/30 [00:15<00:19,  1.15s/it]
Training tasks:  47%|████▋     | 14/30 [00:16<00:18,  1.14s/it]
Training tasks:  50%|█████     | 15/30 [00:17<00:16,  1.11s/it]
Training tasks:  53%|█████▎    | 16/30 [00:18<00:15,  1.11s/it]
Training tasks:  57%|█████▋    | 17/30 [00:19<00:14,  1.11s/it]
Training tasks:  60%|██████    | 18/30 [00:20<00:13,  1.11s/it]
Training tasks:  63%|██████▎   | 19/30 [00:21<00:12,  1.15s/it]
Training tasks:  67%|██████▋   | 20/30 [00:22<00:11,  1.15s/it]
Training tasks:  70%|███████   | 21/30 [00:23<00:10,  1.13s/it]
Training tasks:  73%|███████▎  | 22/30 [00:25<00:09,  1.13s/it]
Training tasks:  77%|███████▋  | 23/30 [00:26<00:07,  1.14s/it]
Training tasks:  80%|████████  | 24/30 [00:27<00:06,  1.13s/it]
Training tasks:  83%|████████▎ | 25/30 [00:28<00:05,  1.14s/it]
Training tasks:  87%|████████▋ | 26/30 [00:29<00:04,  1.14s/it]
Training tasks:  90%|█████████ | 27/30 [00:30<00:03,  1.13s/it]
Training tasks:  93%|█████████▎| 28/30 [00:31<00:02,  1.12s/it]
Training tasks:  97%|█████████▋| 29/30 [00:33<00:01,  1.12s/it]
Training tasks: 100%|██████████| 30/30 [00:34<00:00,  1.12s/it]
Training tasks: 100%|██████████| 30/30 [00:34<00:00,  1.14s/it]

Validating tasks:   0%|          | 0/10 [00:00<?, ?it/s]
Validating tasks:  10%|█         | 1/10 [00:00<00:01,  6.96it/s]
Validating tasks:  20%|██        | 2/10 [00:00<00:01,  6.95it/s]
Validating tasks:  30%|███       | 3/10 [00:00<00:01,  6.87it/s]
Validating tasks:  40%|████      | 4/10 [00:00<00:00,  6.80it/s]
Validating tasks:  50%|█████     | 5/10 [00:00<00:00,  6.85it/s]
Validating tasks:  60%|██████    | 6/10 [00:00<00:00,  6.89it/s]
Validating tasks:  70%|███████   | 7/10 [00:01<00:00,  6.87it/s]
Validating tasks:  80%|████████  | 8/10 [00:01<00:00,  6.86it/s]
Validating tasks:  90%|█████████ | 9/10 [00:01<00:00,  6.83it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  6.82it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  6.85it/s]

Training tasks:   0%|          | 0/30 [00:00<?, ?it/s]
Training tasks:   3%|▎         | 1/30 [00:01<00:32,  1.11s/it]
Training tasks:   7%|▋         | 2/30 [00:02<00:31,  1.12s/it]
Training tasks:  10%|█         | 3/30 [00:03<00:30,  1.12s/it]
Training tasks:  13%|█▎        | 4/30 [00:04<00:29,  1.12s/it]
Training tasks:  17%|█▋        | 5/30 [00:05<00:27,  1.09s/it]
Training tasks:  20%|██        | 6/30 [00:06<00:26,  1.09s/it]
Training tasks:  23%|██▎       | 7/30 [00:07<00:25,  1.09s/it]
Training tasks:  27%|██▋       | 8/30 [00:08<00:24,  1.10s/it]
Training tasks:  30%|███       | 9/30 [00:09<00:23,  1.11s/it]
Training tasks:  33%|███▎      | 10/30 [00:11<00:24,  1.21s/it]
Training tasks:  37%|███▋      | 11/30 [00:12<00:23,  1.26s/it]
Training tasks:  40%|████      | 12/30 [00:14<00:23,  1.31s/it]
Training tasks:  43%|████▎     | 13/30 [00:15<00:22,  1.34s/it]
Training tasks:  47%|████▋     | 14/30 [00:16<00:21,  1.33s/it]
Training tasks:  50%|█████     | 15/30 [00:18<00:19,  1.30s/it]
Training tasks:  53%|█████▎    | 16/30 [00:19<00:17,  1.23s/it]
Training tasks:  57%|█████▋    | 17/30 [00:20<00:15,  1.19s/it]
Training tasks:  60%|██████    | 18/30 [00:21<00:14,  1.24s/it]
Training tasks:  63%|██████▎   | 19/30 [00:22<00:13,  1.19s/it]
Training tasks:  67%|██████▋   | 20/30 [00:23<00:11,  1.18s/it]
Training tasks:  70%|███████   | 21/30 [00:24<00:10,  1.15s/it]
Training tasks:  73%|███████▎  | 22/30 [00:26<00:09,  1.14s/it]
Training tasks:  77%|███████▋  | 23/30 [00:27<00:07,  1.13s/it]
Training tasks:  80%|████████  | 24/30 [00:28<00:06,  1.12s/it]
Training tasks:  83%|████████▎ | 25/30 [00:29<00:05,  1.08s/it]
Training tasks:  87%|████████▋ | 26/30 [00:30<00:04,  1.12s/it]
Training tasks:  90%|█████████ | 27/30 [00:31<00:03,  1.16s/it]
Training tasks:  93%|█████████▎| 28/30 [00:32<00:02,  1.14s/it]
Training tasks:  97%|█████████▋| 29/30 [00:33<00:01,  1.13s/it]
Training tasks: 100%|██████████| 30/30 [00:35<00:00,  1.12s/it]
Training tasks: 100%|██████████| 30/30 [00:35<00:00,  1.17s/it]

Validating tasks:   0%|          | 0/10 [00:00<?, ?it/s]
Validating tasks:  10%|█         | 1/10 [00:00<00:01,  7.25it/s]
Validating tasks:  20%|██        | 2/10 [00:00<00:01,  7.19it/s]
Validating tasks:  30%|███       | 3/10 [00:00<00:00,  7.16it/s]
Validating tasks:  40%|████      | 4/10 [00:00<00:00,  7.17it/s]
Validating tasks:  50%|█████     | 5/10 [00:00<00:00,  7.18it/s]
Validating tasks:  60%|██████    | 6/10 [00:00<00:00,  7.16it/s]
Validating tasks:  70%|███████   | 7/10 [00:00<00:00,  7.15it/s]
Validating tasks:  80%|████████  | 8/10 [00:01<00:00,  7.17it/s]
Validating tasks:  90%|█████████ | 9/10 [00:01<00:00,  7.17it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  7.15it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  7.16it/s]

Training tasks:   0%|          | 0/30 [00:00<?, ?it/s]
Training tasks:   3%|▎         | 1/30 [00:01<00:37,  1.28s/it]
Training tasks:   7%|▋         | 2/30 [00:02<00:35,  1.28s/it]
Training tasks:  10%|█         | 3/30 [00:03<00:32,  1.19s/it]
Training tasks:  13%|█▎        | 4/30 [00:04<00:30,  1.16s/it]
Training tasks:  17%|█▋        | 5/30 [00:05<00:28,  1.14s/it]
Training tasks:  20%|██        | 6/30 [00:06<00:26,  1.12s/it]
Training tasks:  23%|██▎       | 7/30 [00:08<00:25,  1.11s/it]
Training tasks:  27%|██▋       | 8/30 [00:09<00:26,  1.18s/it]
Training tasks:  30%|███       | 9/30 [00:10<00:25,  1.21s/it]
Training tasks:  33%|███▎      | 10/30 [00:11<00:24,  1.23s/it]
Training tasks:  37%|███▋      | 11/30 [00:13<00:22,  1.20s/it]
Training tasks:  40%|████      | 12/30 [00:14<00:21,  1.17s/it]
Training tasks:  43%|████▎     | 13/30 [00:15<00:19,  1.16s/it]
Training tasks:  47%|████▋     | 14/30 [00:16<00:18,  1.14s/it]
Training tasks:  50%|█████     | 15/30 [00:17<00:17,  1.13s/it]
Training tasks:  53%|█████▎    | 16/30 [00:18<00:15,  1.13s/it]
Training tasks:  57%|█████▋    | 17/30 [00:19<00:14,  1.12s/it]
Training tasks:  60%|██████    | 18/30 [00:20<00:13,  1.12s/it]
Training tasks:  63%|██████▎   | 19/30 [00:21<00:12,  1.12s/it]
Training tasks:  67%|██████▋   | 20/30 [00:23<00:11,  1.12s/it]
Training tasks:  70%|███████   | 21/30 [00:24<00:10,  1.13s/it]
Training tasks:  73%|███████▎  | 22/30 [00:25<00:09,  1.13s/it]
Training tasks:  77%|███████▋  | 23/30 [00:26<00:07,  1.13s/it]
Training tasks:  80%|████████  | 24/30 [00:27<00:06,  1.12s/it]
Training tasks:  83%|████████▎ | 25/30 [00:28<00:05,  1.04s/it]
Training tasks:  87%|████████▋ | 26/30 [00:29<00:03,  1.01it/s]
Training tasks:  90%|█████████ | 27/30 [00:30<00:02,  1.05it/s]
Training tasks:  93%|█████████▎| 28/30 [00:31<00:01,  1.08it/s]
Training tasks:  97%|█████████▋| 29/30 [00:31<00:00,  1.10it/s]
Training tasks: 100%|██████████| 30/30 [00:32<00:00,  1.11it/s]
Training tasks: 100%|██████████| 30/30 [00:32<00:00,  1.09s/it]

Validating tasks:   0%|          | 0/10 [00:00<?, ?it/s]
Validating tasks:  10%|█         | 1/10 [00:00<00:01,  6.25it/s]
Validating tasks:  20%|██        | 2/10 [00:00<00:01,  6.12it/s]
Validating tasks:  30%|███       | 3/10 [00:00<00:01,  5.79it/s]
Validating tasks:  40%|████      | 4/10 [00:00<00:01,  5.70it/s]
Validating tasks:  50%|█████     | 5/10 [00:00<00:00,  5.63it/s]
Validating tasks:  60%|██████    | 6/10 [00:01<00:00,  5.55it/s]
Validating tasks:  70%|███████   | 7/10 [00:01<00:00,  5.50it/s]
Validating tasks:  80%|████████  | 8/10 [00:01<00:00,  5.45it/s]
Validating tasks:  90%|█████████ | 9/10 [00:01<00:00,  5.46it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  5.54it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  5.60it/s]

Testing tasks:   0%|          | 0/30 [00:00<?, ?it/s]
Testing tasks:   3%|▎         | 1/30 [00:00<00:08,  3.44it/s]
Testing tasks:   7%|▋         | 2/30 [00:00<00:07,  3.71it/s]
Testing tasks:  10%|█         | 3/30 [00:00<00:06,  3.89it/s]
Testing tasks:  13%|█▎        | 4/30 [00:01<00:06,  3.99it/s]
Testing tasks:  17%|█▋        | 5/30 [00:01<00:06,  3.98it/s]
Testing tasks:  20%|██        | 6/30 [00:01<00:06,  4.00it/s]
Testing tasks:  23%|██▎       | 7/30 [00:01<00:05,  4.06it/s]
Testing tasks:  27%|██▋       | 8/30 [00:02<00:05,  4.07it/s]
Testing tasks:  30%|███       | 9/30 [00:02<00:05,  4.12it/s]
Testing tasks:  33%|███▎      | 10/30 [00:02<00:04,  4.19it/s]
Testing tasks:  37%|███▋      | 11/30 [00:02<00:04,  4.15it/s]
Testing tasks:  40%|████      | 12/30 [00:02<00:04,  4.12it/s]
Testing tasks:  43%|████▎     | 13/30 [00:03<00:04,  4.05it/s]
Testing tasks:  47%|████▋     | 14/30 [00:03<00:03,  4.09it/s]
Testing tasks:  50%|█████     | 15/30 [00:03<00:03,  4.20it/s]
Testing tasks:  53%|█████▎    | 16/30 [00:03<00:03,  4.46it/s]
Testing tasks:  57%|█████▋    | 17/30 [00:04<00:02,  4.69it/s]
Testing tasks:  60%|██████    | 18/30 [00:04<00:02,  4.86it/s]
Testing tasks:  63%|██████▎   | 19/30 [00:04<00:02,  4.97it/s]
Testing tasks:  67%|██████▋   | 20/30 [00:04<00:01,  5.06it/s]
Testing tasks:  70%|███████   | 21/30 [00:04<00:01,  5.14it/s]
Testing tasks:  73%|███████▎  | 22/30 [00:05<00:01,  5.18it/s]
Testing tasks:  77%|███████▋  | 23/30 [00:05<00:01,  5.21it/s]
Testing tasks:  80%|████████  | 24/30 [00:05<00:01,  5.27it/s]
Testing tasks:  83%|████████▎ | 25/30 [00:05<00:00,  5.28it/s]
Testing tasks:  87%|████████▋ | 26/30 [00:05<00:00,  5.30it/s]
Testing tasks:  90%|█████████ | 27/30 [00:05<00:00,  5.33it/s]
Testing tasks:  93%|█████████▎| 28/30 [00:06<00:00,  5.32it/s]
Testing tasks:  97%|█████████▋| 29/30 [00:06<00:00,  5.32it/s]
Testing tasks: 100%|██████████| 30/30 [00:06<00:00,  5.34it/s]
Testing tasks: 100%|██████████| 30/30 [00:06<00:00,  4.61it/s]

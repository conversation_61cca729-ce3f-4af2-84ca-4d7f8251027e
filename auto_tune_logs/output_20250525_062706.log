STDOUT:
===================== 训练配置 =====================
GPU: 0
输出目录: /home/<USER>/balanced_task_sampling/results_trial_20250525_062706
日志文件: /home/<USER>/balanced_task_sampling/results_trial_20250525_062706/main_training_trial_111_20250525_062706.log

--------- 优化参数 ---------
内部学习率: 0.347389
训练轮数: 50
每轮任务数: 30

--------- 模型参数 ---------
使用类别权重: 圆锥角膜=6.524695570574489, 早期圆锥角膜=5.599487055089051
温度参数: 0.0342810396912673
硬样本挖掘比例: 0.5957940967082633
对比学习权重: 0.6537879700046927
=============================================

创建目录: /home/<USER>/balanced_task_sampling/results_trial_20250525_062706
总用量 4
-rw-rw-r-- 1 <USER> <GROUP> 657 5月  25 06:27 main_training_trial_111_20250525_062706.log
开始无数据增强的平衡任务采样训练...
使用GPU: 0
直接运行训练脚本...
成功导入增强版特征提取器
成功导入归一化特征提取器
成功导入增强版损失函数
成功导入增强版MAML模型
成功导入KC特定增强模块
成功导入增强版GPU数据增强模块
使用设备: cuda
禁用所有数据增强，直接加载原始数据集...
使用平衡任务采样器
类别 normal (标签 0): 164个样本
类别 e-kc (标签 1): 26个样本
类别 kc (标签 2): 133个样本
使用原型配置: [3, 6, 4]
使用增强版特征提取器，dropout率: 0.5
使用增强版MAML-ProtoNet模型，内循环学习率: 0.347389, 内循环步数: 5
使用ReduceLROnPlateau学习率调度器，因子: 0.5, 耐心值: 2
开始训练，共50个epoch
Epoch 1/50
当前学习率: 0.000200
使用类别权重: 正常=1.0, 早期圆锥角膜=5.599487055089051, 圆锥角膜=6.524695570574489
使用增强版对比损失函数，温度: 0.0342810396912673, 硬负样本挖掘比例: 0.5957940967082633, 早期圆锥角膜与正常样本对比权重: 1.1206155104066204, 圆锥角膜与正常样本对比权重: 1.0
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
警告: 没有足够的有效类别进行3-way任务采样，尝试调整参数...
类别样本数量: {0: 33, 1: 5, 2: 29}
调整后的参数: n_shot=2, n_query=3
类别 normal (标签 0): 33个样本
类别 e-kc (标签 1): 5个样本
类别 kc (标签 2): 29个样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
Epoch 1: Train Loss: 2.7784, Train Acc: 0.4881, Val Loss: 2.3825, Val Acc: 0.5000
保存最佳模型，验证准确率: 0.5000
Epoch 2/50
当前学习率: 0.000200
使用类别权重: 正常=1.0, 早期圆锥角膜=5.599487055089051, 圆锥角膜=6.524695570574489
使用增强版对比损失函数，温度: 0.0342810396912673, 硬负样本挖掘比例: 0.5957940967082633, 早期圆锥角膜与正常样本对比权重: 1.1206155104066204, 圆锥角膜与正常样本对比权重: 1.0
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
警告: 没有足够的有效类别进行3-way任务采样，尝试调整参数...
类别样本数量: {0: 33, 1: 5, 2: 29}
调整后的参数: n_shot=2, n_query=3
类别 normal (标签 0): 33个样本
类别 e-kc (标签 1): 5个样本
类别 kc (标签 2): 29个样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
Epoch 2: Train Loss: 2.2782, Train Acc: 0.6519, Val Loss: 2.1691, Val Acc: 0.6111
保存最佳模型，验证准确率: 0.6111
Epoch 3/50
当前学习率: 0.000200
使用类别权重: 正常=1.0, 早期圆锥角膜=5.599487055089051, 圆锥角膜=6.524695570574489
使用增强版对比损失函数，温度: 0.0342810396912673, 硬负样本挖掘比例: 0.5957940967082633, 早期圆锥角膜与正常样本对比权重: 1.1206155104066204, 圆锥角膜与正常样本对比权重: 1.0
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
警告: 没有足够的有效类别进行3-way任务采样，尝试调整参数...
类别样本数量: {0: 33, 1: 5, 2: 29}
调整后的参数: n_shot=2, n_query=3
类别 normal (标签 0): 33个样本
类别 e-kc (标签 1): 5个样本
类别 kc (标签 2): 29个样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
Epoch 3: Train Loss: 2.0141, Train Acc: 0.8030, Val Loss: 2.2917, Val Acc: 0.5778
验证准确率未提高，当前耐心值: 1/3
Epoch 4/50
当前学习率: 0.000200
使用类别权重: 正常=1.0, 早期圆锥角膜=5.599487055089051, 圆锥角膜=6.524695570574489
使用增强版对比损失函数，温度: 0.0342810396912673, 硬负样本挖掘比例: 0.5957940967082633, 早期圆锥角膜与正常样本对比权重: 1.1206155104066204, 圆锥角膜与正常样本对比权重: 1.0
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
警告: 没有足够的有效类别进行3-way任务采样，尝试调整参数...
类别样本数量: {0: 33, 1: 5, 2: 29}
调整后的参数: n_shot=2, n_query=3
类别 normal (标签 0): 33个样本
类别 e-kc (标签 1): 5个样本
类别 kc (标签 2): 29个样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
Epoch 4: Train Loss: 2.0584, Train Acc: 0.7919, Val Loss: 2.5103, Val Acc: 0.4778
验证准确率未提高，当前耐心值: 2/3
Epoch 5/50
当前学习率: 0.000200
使用类别权重: 正常=1.0, 早期圆锥角膜=5.599487055089051, 圆锥角膜=6.524695570574489
使用增强版对比损失函数，温度: 0.0342810396912673, 硬负样本挖掘比例: 0.5957940967082633, 早期圆锥角膜与正常样本对比权重: 1.1206155104066204, 圆锥角膜与正常样本对比权重: 1.0
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
警告: 没有足够的有效类别进行3-way任务采样，尝试调整参数...
类别样本数量: {0: 33, 1: 5, 2: 29}
调整后的参数: n_shot=2, n_query=3
类别 normal (标签 0): 33个样本
类别 e-kc (标签 1): 5个样本
类别 kc (标签 2): 29个样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
Epoch 5: Train Loss: 2.0239, Train Acc: 0.7963, Val Loss: 2.0619, Val Acc: 0.6778
保存最佳模型，验证准确率: 0.6778
Epoch 6/50
当前学习率: 0.000200
使用类别权重: 正常=1.0, 早期圆锥角膜=5.599487055089051, 圆锥角膜=6.524695570574489
使用增强版对比损失函数，温度: 0.0342810396912673, 硬负样本挖掘比例: 0.5957940967082633, 早期圆锥角膜与正常样本对比权重: 1.1206155104066204, 圆锥角膜与正常样本对比权重: 1.0
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
警告: 没有足够的有效类别进行3-way任务采样，尝试调整参数...
类别样本数量: {0: 33, 1: 5, 2: 29}
调整后的参数: n_shot=2, n_query=3
类别 normal (标签 0): 33个样本
类别 e-kc (标签 1): 5个样本
类别 kc (标签 2): 29个样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
Epoch 6: Train Loss: 1.8222, Train Acc: 0.8044, Val Loss: 2.2060, Val Acc: 0.5889
验证准确率未提高，当前耐心值: 1/3
Epoch 7/50
当前学习率: 0.000200
使用类别权重: 正常=1.0, 早期圆锥角膜=5.599487055089051, 圆锥角膜=6.524695570574489
使用增强版对比损失函数，温度: 0.0342810396912673, 硬负样本挖掘比例: 0.5957940967082633, 早期圆锥角膜与正常样本对比权重: 1.1206155104066204, 圆锥角膜与正常样本对比权重: 1.0
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
警告: 没有足够的有效类别进行3-way任务采样，尝试调整参数...
类别样本数量: {0: 33, 1: 5, 2: 29}
调整后的参数: n_shot=2, n_query=3
类别 normal (标签 0): 33个样本
类别 e-kc (标签 1): 5个样本
类别 kc (标签 2): 29个样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
Epoch 7: Train Loss: 1.6633, Train Acc: 0.8570, Val Loss: 2.2146, Val Acc: 0.6111
验证准确率未提高，当前耐心值: 2/3
Epoch 8/50
当前学习率: 0.000200
使用类别权重: 正常=1.0, 早期圆锥角膜=5.599487055089051, 圆锥角膜=6.524695570574489
使用增强版对比损失函数，温度: 0.0342810396912673, 硬负样本挖掘比例: 0.5957940967082633, 早期圆锥角膜与正常样本对比权重: 1.1206155104066204, 圆锥角膜与正常样本对比权重: 1.0
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 19 个支持集样本
早期圆锥角膜类别使用 6 个支持集样本
警告: 没有足够的有效类别进行3-way任务采样，尝试调整参数...
类别样本数量: {0: 33, 1: 5, 2: 29}
调整后的参数: n_shot=2, n_query=3
类别 normal (标签 0): 33个样本
类别 e-kc (标签 1): 5个样本
类别 kc (标签 2): 29个样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 7 个支持集样本
早期圆锥角膜类别使用 2 个支持集样本
Epoch 8: Train Loss: 1.5460, Train Acc: 0.9119, Val Loss: 2.1920, Val Acc: 0.5778
验证准确率未提高，当前耐心值: 3/3
早停触发，停止训练
警告: 没有足够的有效类别进行3-way任务采样，尝试调整参数...
类别样本数量: {0: 37, 1: 7, 2: 27}
调整后的参数: n_shot=3, n_query=4
类别 normal (标签 0): 37个样本
类别 e-kc (标签 1): 7个样本
类别 kc (标签 2): 27个样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本
任务类别: ['kc', 'e-kc', 'normal']
圆锥角膜类别使用 11 个支持集样本
早期圆锥角膜类别使用 3 个支持集样本

测试结果:
总体准确率: 73.33%
类别准确率:
  kc: 52.50%
  e-kc: 80.83%
  normal: 86.67%
训练完成，结果已保存到 /home/<USER>/balanced_task_sampling/results_trial_20250525_062706

STDERR:
+ TIMESTAMP=20250525_062706
+ RESULT_DIR=/home/<USER>/balanced_task_sampling/results_trial_20250525_062706
+ '[' -n /home/<USER>/balanced_task_sampling/results_trial_20250525_062706/main_training_trial_111_20250525_062706.log ']'
+ LOG_FILE=/home/<USER>/balanced_task_sampling/results_trial_20250525_062706/main_training_trial_111_20250525_062706.log
++ dirname /home/<USER>/balanced_task_sampling/results_trial_20250525_062706/main_training_trial_111_20250525_062706.log
+ mkdir -p /home/<USER>/balanced_task_sampling/results_trial_20250525_062706
+ mkdir -p /home/<USER>/balanced_task_sampling/results_trial_20250525_062706
+ echo '===================== 训练配置 ====================='
+ echo 'GPU: 0'
+ echo '输出目录: /home/<USER>/balanced_task_sampling/results_trial_20250525_062706'
+ echo '日志文件: /home/<USER>/balanced_task_sampling/results_trial_20250525_062706/main_training_trial_111_20250525_062706.log'
+ echo ''
+ echo '--------- 优化参数 ---------'
+ echo '内部学习率: 0.347389'
+ echo '训练轮数: 50'
+ echo '每轮任务数: 30'
+ echo ''
+ echo '--------- 模型参数 ---------'
+ echo '使用类别权重: 圆锥角膜=6.524695570574489, 早期圆锥角膜=5.599487055089051'
+ echo '温度参数: 0.0342810396912673'
+ echo '硬样本挖掘比例: 0.5957940967082633'
+ echo '对比学习权重: 0.6537879700046927'
+ echo '=============================================
'
+ tee -a /home/<USER>/balanced_task_sampling/results_trial_20250525_062706/main_training_trial_111_20250525_062706.log
+ validate_params
+ local error_count=0
++ dirname /home/<USER>/balanced_task_sampling/results_trial_20250525_062706/main_training_trial_111_20250525_062706.log
++ dirname /home/<USER>/balanced_task_sampling/split_result/train_set.csv
+ for dir in "$RESULT_DIR" "$(dirname "$LOG_FILE")" "$(dirname "$TRAIN_CSV")"
+ mkdir -p /home/<USER>/balanced_task_sampling/results_trial_20250525_062706
+ for dir in "$RESULT_DIR" "$(dirname "$LOG_FILE")" "$(dirname "$TRAIN_CSV")"
+ mkdir -p /home/<USER>/balanced_task_sampling/results_trial_20250525_062706
+ for dir in "$RESULT_DIR" "$(dirname "$LOG_FILE")" "$(dirname "$TRAIN_CSV")"
+ mkdir -p /home/<USER>/balanced_task_sampling/split_result
+ for csv in "$TRAIN_CSV" "$VAL_CSV" "$TEST_CSV"
+ '[' '!' -f /home/<USER>/balanced_task_sampling/split_result/train_set.csv ']'
+ for csv in "$TRAIN_CSV" "$VAL_CSV" "$TEST_CSV"
+ '[' '!' -f /home/<USER>/balanced_task_sampling/split_result/val_set.csv ']'
+ for csv in "$TRAIN_CSV" "$VAL_CSV" "$TEST_CSV"
+ '[' '!' -f /home/<USER>/balanced_task_sampling/split_result/test_set.csv ']'
+ [[ 0.0002 =~ ^[0-9.]+$ ]]
+ [[ 0.347389 =~ ^[0-9.]+$ ]]
+ '[' 0 -gt 0 ']'
+ return 0
+ '[' false = true ']'
+ export CUDA_VISIBLE_DEVICES=0
+ CUDA_VISIBLE_DEVICES=0
+ export OMP_NUM_THREADS=1
+ OMP_NUM_THREADS=1
+ export MKL_NUM_THREADS=1
+ MKL_NUM_THREADS=1
+ export MPLBACKEND=Agg
+ MPLBACKEND=Agg
+ mkdir -p /home/<USER>/balanced_task_sampling/results_trial_20250525_062706
+ echo '创建目录: /home/<USER>/balanced_task_sampling/results_trial_20250525_062706'
+ ls -l /home/<USER>/balanced_task_sampling/results_trial_20250525_062706
+ PRETRAINED_ARGS=
+ '[' true = true ']'
+ PRETRAINED_ARGS=--pretrained
+ AUGMENT_ARGS=--no_augmentation
+ CONTRASTIVE_ARGS=
+ '[' true = true ']'
+ CONTRASTIVE_ARGS='--use_contrastive --contrastive_weight 0.6537879700046927 --temperature 0.0342810396912673 --hard_mining_ratio 0.5957940967082633 --early_normal_weight 1.1206155104066204 --kc_normal_weight 1.0'
+ NORMALIZE_ARGS=
+ '[' false = true ']'
+ SAMPLER_ARGS=
+ '[' true = true ']'
+ SAMPLER_ARGS=' --use_separate_test_sampler'
+ '[' true = true ']'
+ SAMPLER_ARGS=' --use_separate_test_sampler --use_balanced_task_sampler --kc_shot_multiplier 3.9139520661369103 --early_kc_shot_multiplier 1.3513373035441116'
+ LR_SCHEDULER_ARGS=
+ '[' true = true ']'
+ LR_SCHEDULER_ARGS='--use_lr_scheduler --scheduler_type plateau'
+ '[' plateau = onecycle ']'
+ '[' plateau = plateau ']'
+ LR_SCHEDULER_ARGS='--use_lr_scheduler --scheduler_type plateau --plateau_factor 0.5 --plateau_patience 2 --min_lr 1e-6'
+ BASE_CMD='python train_balanced_task_sampling.py   --train_csv /home/<USER>/balanced_task_sampling/split_result/train_set.csv   --val_csv /home/<USER>/balanced_task_sampling/split_result/val_set.csv   --test_csv /home/<USER>/balanced_task_sampling/split_result/test_set.csv   --epochs 50   --lr 0.0002   --weight_decay 5e-4   --feature_dim 512   --save_dir /home/<USER>/balanced_task_sampling/results_trial_20250525_062706   --proto_counts 3,6,4   --pretrained   --inner_lr 0.347389   --inner_steps 5   --n_way 3   --n_shot 5   --n_query 15   --tasks_per_epoch 30   --meta_batch_size 8   --no_augmentation   --mixup_alpha 0.0   --cutmix_prob 0.0   --num_workers 4   --augment_factor 1   --kc_augment_factor 1   --seed 42   --early_kc_weight 5.599487055089051   --kc_weight 6.524695570574489   --focal_gamma 1.3988557929829566   --val_frequency 1   --early_stopping 3   --dropout 0.5    --use_separate_test_sampler --use_balanced_task_sampler --kc_shot_multiplier 3.9139520661369103 --early_kc_shot_multiplier 1.3513373035441116   --use_contrastive --contrastive_weight 0.6537879700046927 --temperature 0.0342810396912673 --hard_mining_ratio 0.5957940967082633 --early_normal_weight 1.1206155104066204 --kc_normal_weight 1.0      --use_lr_scheduler --scheduler_type plateau --plateau_factor 0.5 --plateau_patience 2 --min_lr 1e-6'
+ echo 开始无数据增强的平衡任务采样训练...
+ echo '使用GPU: 0'
+ '[' direct = nohup ']'
+ echo 直接运行训练脚本...
+ eval python train_balanced_task_sampling.py --train_csv /home/<USER>/balanced_task_sampling/split_result/train_set.csv --val_csv /home/<USER>/balanced_task_sampling/split_result/val_set.csv --test_csv /home/<USER>/balanced_task_sampling/split_result/test_set.csv --epochs 50 --lr 0.0002 --weight_decay 5e-4 --feature_dim 512 --save_dir /home/<USER>/balanced_task_sampling/results_trial_20250525_062706 --proto_counts 3,6,4 --pretrained --inner_lr 0.347389 --inner_steps 5 --n_way 3 --n_shot 5 --n_query 15 --tasks_per_epoch 30 --meta_batch_size 8 --no_augmentation --mixup_alpha 0.0 --cutmix_prob 0.0 --num_workers 4 --augment_factor 1 --kc_augment_factor 1 --seed 42 --early_kc_weight 5.599487055089051 --kc_weight 6.524695570574489 --focal_gamma 1.3988557929829566 --val_frequency 1 --early_stopping 3 --dropout 0.5 --use_separate_test_sampler --use_balanced_task_sampler --kc_shot_multiplier 3.9139520661369103 --early_kc_shot_multiplier 1.3513373035441116 --use_contrastive --contrastive_weight 0.6537879700046927 --temperature 0.0342810396912673 --hard_mining_ratio 0.5957940967082633 --early_normal_weight 1.1206155104066204 --kc_normal_weight 1.0 --use_lr_scheduler --scheduler_type plateau --plateau_factor 0.5 --plateau_patience 2 --min_lr 1e-6
++ python train_balanced_task_sampling.py --train_csv /home/<USER>/balanced_task_sampling/split_result/train_set.csv --val_csv /home/<USER>/balanced_task_sampling/split_result/val_set.csv --test_csv /home/<USER>/balanced_task_sampling/split_result/test_set.csv --epochs 50 --lr 0.0002 --weight_decay 5e-4 --feature_dim 512 --save_dir /home/<USER>/balanced_task_sampling/results_trial_20250525_062706 --proto_counts 3,6,4 --pretrained --inner_lr 0.347389 --inner_steps 5 --n_way 3 --n_shot 5 --n_query 15 --tasks_per_epoch 30 --meta_batch_size 8 --no_augmentation --mixup_alpha 0.0 --cutmix_prob 0.0 --num_workers 4 --augment_factor 1 --kc_augment_factor 1 --seed 42 --early_kc_weight 5.599487055089051 --kc_weight 6.524695570574489 --focal_gamma 1.3988557929829566 --val_frequency 1 --early_stopping 3 --dropout 0.5 --use_separate_test_sampler --use_balanced_task_sampler --kc_shot_multiplier 3.9139520661369103 --early_kc_shot_multiplier 1.3513373035441116 --use_contrastive --contrastive_weight 0.6537879700046927 --temperature 0.0342810396912673 --hard_mining_ratio 0.5957940967082633 --early_normal_weight 1.1206155104066204 --kc_normal_weight 1.0 --use_lr_scheduler --scheduler_type plateau --plateau_factor 0.5 --plateau_patience 2 --min_lr 1e-6
/home/<USER>/anaconda3/envs/kc1/lib/python3.10/site-packages/torchvision/models/_utils.py:208: UserWarning: The parameter 'pretrained' is deprecated since 0.13 and may be removed in the future, please use 'weights' instead.
  warnings.warn(
/home/<USER>/anaconda3/envs/kc1/lib/python3.10/site-packages/torchvision/models/_utils.py:223: UserWarning: Arguments other than a weight enum or `None` for 'weights' are deprecated since 0.13 and may be removed in the future. The current behavior is equivalent to passing `weights=ResNet34_Weights.IMAGENET1K_V1`. You can also use `weights=ResNet34_Weights.DEFAULT` to get the most up-to-date weights.
  warnings.warn(msg)
/home/<USER>/anaconda3/envs/kc1/lib/python3.10/site-packages/torch/optim/lr_scheduler.py:62: UserWarning: The verbose parameter is deprecated. Please use get_last_lr() to access the learning rate.
  warnings.warn(

Training tasks:   0%|          | 0/30 [00:00<?, ?it/s]
Training tasks:   3%|▎         | 1/30 [00:03<01:41,  3.48s/it]
Training tasks:   7%|▋         | 2/30 [00:04<00:57,  2.05s/it]
Training tasks:  10%|█         | 3/30 [00:05<00:42,  1.59s/it]
Training tasks:  13%|█▎        | 4/30 [00:06<00:35,  1.36s/it]
Training tasks:  17%|█▋        | 5/30 [00:07<00:30,  1.23s/it]
Training tasks:  20%|██        | 6/30 [00:08<00:27,  1.16s/it]
Training tasks:  23%|██▎       | 7/30 [00:09<00:25,  1.11s/it]
Training tasks:  27%|██▋       | 8/30 [00:10<00:23,  1.07s/it]
Training tasks:  30%|███       | 9/30 [00:11<00:21,  1.04s/it]
Training tasks:  33%|███▎      | 10/30 [00:12<00:20,  1.03s/it]
Training tasks:  37%|███▋      | 11/30 [00:13<00:19,  1.02s/it]
Training tasks:  40%|████      | 12/30 [00:14<00:18,  1.01s/it]
Training tasks:  43%|████▎     | 13/30 [00:15<00:17,  1.01s/it]
Training tasks:  47%|████▋     | 14/30 [00:16<00:16,  1.02s/it]
Training tasks:  50%|█████     | 15/30 [00:17<00:15,  1.01s/it]
Training tasks:  53%|█████▎    | 16/30 [00:18<00:14,  1.00s/it]
Training tasks:  57%|█████▋    | 17/30 [00:19<00:12,  1.00it/s]
Training tasks:  60%|██████    | 18/30 [00:20<00:11,  1.01it/s]
Training tasks:  63%|██████▎   | 19/30 [00:21<00:10,  1.01it/s]
Training tasks:  67%|██████▋   | 20/30 [00:22<00:09,  1.01it/s]
Training tasks:  70%|███████   | 21/30 [00:23<00:08,  1.00it/s]
Training tasks:  73%|███████▎  | 22/30 [00:24<00:07,  1.00it/s]
Training tasks:  77%|███████▋  | 23/30 [00:25<00:07,  1.01s/it]
Training tasks:  80%|████████  | 24/30 [00:26<00:06,  1.01s/it]
Training tasks:  83%|████████▎ | 25/30 [00:27<00:05,  1.01s/it]
Training tasks:  87%|████████▋ | 26/30 [00:28<00:04,  1.00s/it]
Training tasks:  90%|█████████ | 27/30 [00:29<00:02,  1.00it/s]
Training tasks:  93%|█████████▎| 28/30 [00:30<00:01,  1.01it/s]
Training tasks:  97%|█████████▋| 29/30 [00:31<00:00,  1.01it/s]
Training tasks: 100%|██████████| 30/30 [00:32<00:00,  1.00it/s]
Training tasks: 100%|██████████| 30/30 [00:32<00:00,  1.08s/it]

Validating tasks:   0%|          | 0/10 [00:00<?, ?it/s]
Validating tasks:  10%|█         | 1/10 [00:00<00:01,  6.15it/s]
Validating tasks:  20%|██        | 2/10 [00:00<00:01,  6.78it/s]
Validating tasks:  30%|███       | 3/10 [00:00<00:00,  7.15it/s]
Validating tasks:  40%|████      | 4/10 [00:00<00:00,  7.32it/s]
Validating tasks:  50%|█████     | 5/10 [00:00<00:00,  7.44it/s]
Validating tasks:  60%|██████    | 6/10 [00:00<00:00,  7.50it/s]
Validating tasks:  70%|███████   | 7/10 [00:00<00:00,  7.55it/s]
Validating tasks:  80%|████████  | 8/10 [00:01<00:00,  7.56it/s]
Validating tasks:  90%|█████████ | 9/10 [00:01<00:00,  7.57it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  7.58it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  7.41it/s]

Training tasks:   0%|          | 0/30 [00:00<?, ?it/s]
Training tasks:   3%|▎         | 1/30 [00:01<00:30,  1.05s/it]
Training tasks:   7%|▋         | 2/30 [00:02<00:28,  1.03s/it]
Training tasks:  10%|█         | 3/30 [00:03<00:27,  1.01s/it]
Training tasks:  13%|█▎        | 4/30 [00:04<00:26,  1.00s/it]
Training tasks:  17%|█▋        | 5/30 [00:05<00:25,  1.00s/it]
Training tasks:  20%|██        | 6/30 [00:06<00:23,  1.01it/s]
Training tasks:  23%|██▎       | 7/30 [00:07<00:22,  1.01it/s]
Training tasks:  27%|██▋       | 8/30 [00:08<00:21,  1.01it/s]
Training tasks:  30%|███       | 9/30 [00:09<00:21,  1.01s/it]
Training tasks:  33%|███▎      | 10/30 [00:10<00:20,  1.00s/it]
Training tasks:  37%|███▋      | 11/30 [00:11<00:19,  1.01s/it]
Training tasks:  40%|████      | 12/30 [00:12<00:18,  1.01s/it]
Training tasks:  43%|████▎     | 13/30 [00:13<00:16,  1.00it/s]
Training tasks:  47%|████▋     | 14/30 [00:14<00:15,  1.01it/s]
Training tasks:  50%|█████     | 15/30 [00:14<00:14,  1.01it/s]
Training tasks:  53%|█████▎    | 16/30 [00:15<00:13,  1.02it/s]
Training tasks:  57%|█████▋    | 17/30 [00:16<00:12,  1.02it/s]
Training tasks:  60%|██████    | 18/30 [00:17<00:11,  1.02it/s]
Training tasks:  63%|██████▎   | 19/30 [00:18<00:10,  1.01it/s]
Training tasks:  67%|██████▋   | 20/30 [00:19<00:09,  1.01it/s]
Training tasks:  70%|███████   | 21/30 [00:20<00:08,  1.01it/s]
Training tasks:  73%|███████▎  | 22/30 [00:21<00:07,  1.01it/s]
Training tasks:  77%|███████▋  | 23/30 [00:22<00:06,  1.01it/s]
Training tasks:  80%|████████  | 24/30 [00:23<00:05,  1.02it/s]
Training tasks:  83%|████████▎ | 25/30 [00:24<00:04,  1.02it/s]
Training tasks:  87%|████████▋ | 26/30 [00:25<00:03,  1.01it/s]
Training tasks:  90%|█████████ | 27/30 [00:26<00:02,  1.02it/s]
Training tasks:  93%|█████████▎| 28/30 [00:27<00:01,  1.02it/s]
Training tasks:  97%|█████████▋| 29/30 [00:28<00:00,  1.01it/s]
Training tasks: 100%|██████████| 30/30 [00:29<00:00,  1.01it/s]
Training tasks: 100%|██████████| 30/30 [00:29<00:00,  1.01it/s]

Validating tasks:   0%|          | 0/10 [00:00<?, ?it/s]
Validating tasks:  10%|█         | 1/10 [00:00<00:01,  7.64it/s]
Validating tasks:  20%|██        | 2/10 [00:00<00:01,  7.24it/s]
Validating tasks:  30%|███       | 3/10 [00:00<00:00,  7.39it/s]
Validating tasks:  40%|████      | 4/10 [00:00<00:00,  7.47it/s]
Validating tasks:  50%|█████     | 5/10 [00:00<00:00,  7.43it/s]
Validating tasks:  60%|██████    | 6/10 [00:00<00:00,  7.47it/s]
Validating tasks:  70%|███████   | 7/10 [00:00<00:00,  7.50it/s]
Validating tasks:  80%|████████  | 8/10 [00:01<00:00,  7.54it/s]
Validating tasks:  90%|█████████ | 9/10 [00:01<00:00,  7.43it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  7.46it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  7.45it/s]

Training tasks:   0%|          | 0/30 [00:00<?, ?it/s]
Training tasks:   3%|▎         | 1/30 [00:01<00:29,  1.02s/it]
Training tasks:   7%|▋         | 2/30 [00:02<00:28,  1.02s/it]
Training tasks:  10%|█         | 3/30 [00:03<00:27,  1.00s/it]
Training tasks:  13%|█▎        | 4/30 [00:04<00:26,  1.00s/it]
Training tasks:  17%|█▋        | 5/30 [00:04<00:24,  1.01it/s]
Training tasks:  20%|██        | 6/30 [00:06<00:24,  1.00s/it]
Training tasks:  23%|██▎       | 7/30 [00:07<00:22,  1.00it/s]
Training tasks:  27%|██▋       | 8/30 [00:07<00:21,  1.01it/s]
Training tasks:  30%|███       | 9/30 [00:08<00:20,  1.01it/s]
Training tasks:  33%|███▎      | 10/30 [00:09<00:19,  1.01it/s]
Training tasks:  37%|███▋      | 11/30 [00:10<00:18,  1.01it/s]
Training tasks:  40%|████      | 12/30 [00:11<00:17,  1.01it/s]
Training tasks:  43%|████▎     | 13/30 [00:12<00:16,  1.00it/s]
Training tasks:  47%|████▋     | 14/30 [00:13<00:15,  1.01it/s]
Training tasks:  50%|█████     | 15/30 [00:14<00:14,  1.00it/s]
Training tasks:  53%|█████▎    | 16/30 [00:15<00:13,  1.00it/s]
Training tasks:  57%|█████▋    | 17/30 [00:16<00:12,  1.01it/s]
Training tasks:  60%|██████    | 18/30 [00:17<00:11,  1.01it/s]
Training tasks:  63%|██████▎   | 19/30 [00:18<00:10,  1.01it/s]
Training tasks:  67%|██████▋   | 20/30 [00:19<00:09,  1.00it/s]
Training tasks:  70%|███████   | 21/30 [00:20<00:08,  1.00it/s]
Training tasks:  73%|███████▎  | 22/30 [00:21<00:07,  1.00it/s]
Training tasks:  77%|███████▋  | 23/30 [00:22<00:06,  1.01it/s]
Training tasks:  80%|████████  | 24/30 [00:23<00:05,  1.01it/s]
Training tasks:  83%|████████▎ | 25/30 [00:24<00:04,  1.02it/s]
Training tasks:  87%|████████▋ | 26/30 [00:25<00:03,  1.01it/s]
Training tasks:  90%|█████████ | 27/30 [00:26<00:02,  1.01it/s]
Training tasks:  93%|█████████▎| 28/30 [00:27<00:01,  1.01it/s]
Training tasks:  97%|█████████▋| 29/30 [00:28<00:00,  1.01it/s]
Training tasks: 100%|██████████| 30/30 [00:29<00:00,  1.00it/s]
Training tasks: 100%|██████████| 30/30 [00:29<00:00,  1.01it/s]

Validating tasks:   0%|          | 0/10 [00:00<?, ?it/s]
Validating tasks:  10%|█         | 1/10 [00:00<00:01,  7.64it/s]
Validating tasks:  20%|██        | 2/10 [00:00<00:01,  7.79it/s]
Validating tasks:  30%|███       | 3/10 [00:00<00:00,  7.70it/s]
Validating tasks:  40%|████      | 4/10 [00:00<00:00,  7.76it/s]
Validating tasks:  50%|█████     | 5/10 [00:00<00:00,  7.69it/s]
Validating tasks:  60%|██████    | 6/10 [00:00<00:00,  7.73it/s]
Validating tasks:  70%|███████   | 7/10 [00:00<00:00,  7.67it/s]
Validating tasks:  80%|████████  | 8/10 [00:01<00:00,  7.71it/s]
Validating tasks:  90%|█████████ | 9/10 [00:01<00:00,  7.68it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  7.76it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  7.72it/s]

Training tasks:   0%|          | 0/30 [00:00<?, ?it/s]
Training tasks:   3%|▎         | 1/30 [00:01<00:29,  1.01s/it]
Training tasks:   7%|▋         | 2/30 [00:02<00:28,  1.00s/it]
Training tasks:  10%|█         | 3/30 [00:02<00:26,  1.01it/s]
Training tasks:  13%|█▎        | 4/30 [00:03<00:25,  1.02it/s]
Training tasks:  17%|█▋        | 5/30 [00:04<00:24,  1.01it/s]
Training tasks:  20%|██        | 6/30 [00:05<00:23,  1.01it/s]
Training tasks:  23%|██▎       | 7/30 [00:06<00:22,  1.01it/s]
Training tasks:  27%|██▋       | 8/30 [00:07<00:21,  1.02it/s]
Training tasks:  30%|███       | 9/30 [00:08<00:20,  1.01it/s]
Training tasks:  33%|███▎      | 10/30 [00:09<00:19,  1.01it/s]
Training tasks:  37%|███▋      | 11/30 [00:10<00:18,  1.02it/s]
Training tasks:  40%|████      | 12/30 [00:11<00:17,  1.02it/s]
Training tasks:  43%|████▎     | 13/30 [00:12<00:16,  1.02it/s]
Training tasks:  47%|████▋     | 14/30 [00:13<00:15,  1.02it/s]
Training tasks:  50%|█████     | 15/30 [00:14<00:14,  1.01it/s]
Training tasks:  53%|█████▎    | 16/30 [00:15<00:13,  1.01it/s]
Training tasks:  57%|█████▋    | 17/30 [00:16<00:12,  1.01it/s]
Training tasks:  60%|██████    | 18/30 [00:17<00:11,  1.01it/s]
Training tasks:  63%|██████▎   | 19/30 [00:18<00:10,  1.01it/s]
Training tasks:  67%|██████▋   | 20/30 [00:19<00:09,  1.02it/s]
Training tasks:  70%|███████   | 21/30 [00:20<00:08,  1.02it/s]
Training tasks:  73%|███████▎  | 22/30 [00:21<00:07,  1.02it/s]
Training tasks:  77%|███████▋  | 23/30 [00:22<00:06,  1.02it/s]
Training tasks:  80%|████████  | 24/30 [00:23<00:05,  1.01it/s]
Training tasks:  83%|████████▎ | 25/30 [00:24<00:04,  1.02it/s]
Training tasks:  87%|████████▋ | 26/30 [00:25<00:03,  1.00it/s]
Training tasks:  90%|█████████ | 27/30 [00:26<00:03,  1.02s/it]
Training tasks:  93%|█████████▎| 28/30 [00:27<00:02,  1.02s/it]
Training tasks:  97%|█████████▋| 29/30 [00:28<00:01,  1.02s/it]
Training tasks: 100%|██████████| 30/30 [00:29<00:00,  1.01s/it]
Training tasks: 100%|██████████| 30/30 [00:29<00:00,  1.01it/s]

Validating tasks:   0%|          | 0/10 [00:00<?, ?it/s]
Validating tasks:  10%|█         | 1/10 [00:00<00:01,  7.52it/s]
Validating tasks:  20%|██        | 2/10 [00:00<00:01,  7.54it/s]
Validating tasks:  30%|███       | 3/10 [00:00<00:00,  7.55it/s]
Validating tasks:  40%|████      | 4/10 [00:00<00:00,  7.55it/s]
Validating tasks:  50%|█████     | 5/10 [00:00<00:00,  7.56it/s]
Validating tasks:  60%|██████    | 6/10 [00:00<00:00,  7.56it/s]
Validating tasks:  70%|███████   | 7/10 [00:00<00:00,  7.56it/s]
Validating tasks:  80%|████████  | 8/10 [00:01<00:00,  7.57it/s]
Validating tasks:  90%|█████████ | 9/10 [00:01<00:00,  7.58it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  7.58it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  7.56it/s]

Training tasks:   0%|          | 0/30 [00:00<?, ?it/s]
Training tasks:   3%|▎         | 1/30 [00:01<00:29,  1.03s/it]
Training tasks:   7%|▋         | 2/30 [00:02<00:28,  1.03s/it]
Training tasks:  10%|█         | 3/30 [00:03<00:27,  1.02s/it]
Training tasks:  13%|█▎        | 4/30 [00:04<00:26,  1.02s/it]
Training tasks:  17%|█▋        | 5/30 [00:05<00:25,  1.01s/it]
Training tasks:  20%|██        | 6/30 [00:06<00:24,  1.02s/it]
Training tasks:  23%|██▎       | 7/30 [00:07<00:23,  1.02s/it]
Training tasks:  27%|██▋       | 8/30 [00:08<00:22,  1.02s/it]
Training tasks:  30%|███       | 9/30 [00:09<00:21,  1.02s/it]
Training tasks:  33%|███▎      | 10/30 [00:10<00:20,  1.02s/it]
Training tasks:  37%|███▋      | 11/30 [00:11<00:19,  1.03s/it]
Training tasks:  40%|████      | 12/30 [00:12<00:18,  1.02s/it]
Training tasks:  43%|████▎     | 13/30 [00:13<00:17,  1.02s/it]
Training tasks:  47%|████▋     | 14/30 [00:14<00:16,  1.01s/it]
Training tasks:  50%|█████     | 15/30 [00:15<00:15,  1.01s/it]
Training tasks:  53%|█████▎    | 16/30 [00:16<00:14,  1.00s/it]
Training tasks:  57%|█████▋    | 17/30 [00:17<00:13,  1.01s/it]
Training tasks:  60%|██████    | 18/30 [00:18<00:12,  1.01s/it]
Training tasks:  63%|██████▎   | 19/30 [00:19<00:11,  1.01s/it]
Training tasks:  67%|██████▋   | 20/30 [00:20<00:10,  1.00s/it]
Training tasks:  70%|███████   | 21/30 [00:21<00:09,  1.00s/it]
Training tasks:  73%|███████▎  | 22/30 [00:22<00:07,  1.00it/s]
Training tasks:  77%|███████▋  | 23/30 [00:23<00:07,  1.00s/it]
Training tasks:  80%|████████  | 24/30 [00:24<00:06,  1.01s/it]
Training tasks:  83%|████████▎ | 25/30 [00:25<00:05,  1.02s/it]
Training tasks:  87%|████████▋ | 26/30 [00:26<00:04,  1.01s/it]
Training tasks:  90%|█████████ | 27/30 [00:27<00:03,  1.01s/it]
Training tasks:  93%|█████████▎| 28/30 [00:28<00:02,  1.01s/it]
Training tasks:  97%|█████████▋| 29/30 [00:29<00:01,  1.01s/it]
Training tasks: 100%|██████████| 30/30 [00:30<00:00,  1.01s/it]
Training tasks: 100%|██████████| 30/30 [00:30<00:00,  1.01s/it]

Validating tasks:   0%|          | 0/10 [00:00<?, ?it/s]
Validating tasks:  10%|█         | 1/10 [00:00<00:01,  7.65it/s]
Validating tasks:  20%|██        | 2/10 [00:00<00:01,  7.55it/s]
Validating tasks:  30%|███       | 3/10 [00:00<00:00,  7.60it/s]
Validating tasks:  40%|████      | 4/10 [00:00<00:00,  7.54it/s]
Validating tasks:  50%|█████     | 5/10 [00:00<00:00,  7.57it/s]
Validating tasks:  60%|██████    | 6/10 [00:00<00:00,  7.53it/s]
Validating tasks:  70%|███████   | 7/10 [00:00<00:00,  7.56it/s]
Validating tasks:  80%|████████  | 8/10 [00:01<00:00,  7.54it/s]
Validating tasks:  90%|█████████ | 9/10 [00:01<00:00,  7.57it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  7.54it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  7.55it/s]

Training tasks:   0%|          | 0/30 [00:00<?, ?it/s]
Training tasks:   3%|▎         | 1/30 [00:01<00:30,  1.04s/it]
Training tasks:   7%|▋         | 2/30 [00:02<00:28,  1.02s/it]
Training tasks:  10%|█         | 3/30 [00:03<00:27,  1.03s/it]
Training tasks:  13%|█▎        | 4/30 [00:04<00:26,  1.02s/it]
Training tasks:  17%|█▋        | 5/30 [00:05<00:25,  1.01s/it]
Training tasks:  20%|██        | 6/30 [00:06<00:24,  1.00s/it]
Training tasks:  23%|██▎       | 7/30 [00:07<00:23,  1.00s/it]
Training tasks:  27%|██▋       | 8/30 [00:08<00:21,  1.01it/s]
Training tasks:  30%|███       | 9/30 [00:09<00:20,  1.01it/s]
Training tasks:  33%|███▎      | 10/30 [00:10<00:19,  1.01it/s]
Training tasks:  37%|███▋      | 11/30 [00:11<00:18,  1.00it/s]
Training tasks:  40%|████      | 12/30 [00:12<00:17,  1.00it/s]
Training tasks:  43%|████▎     | 13/30 [00:12<00:16,  1.01it/s]
Training tasks:  47%|████▋     | 14/30 [00:13<00:15,  1.01it/s]
Training tasks:  50%|█████     | 15/30 [00:14<00:14,  1.02it/s]
Training tasks:  53%|█████▎    | 16/30 [00:15<00:13,  1.02it/s]
Training tasks:  57%|█████▋    | 17/30 [00:16<00:12,  1.02it/s]
Training tasks:  60%|██████    | 18/30 [00:17<00:11,  1.01it/s]
Training tasks:  63%|██████▎   | 19/30 [00:18<00:10,  1.01it/s]
Training tasks:  67%|██████▋   | 20/30 [00:19<00:09,  1.01it/s]
Training tasks:  70%|███████   | 21/30 [00:20<00:08,  1.01it/s]
Training tasks:  73%|███████▎  | 22/30 [00:21<00:07,  1.02it/s]
Training tasks:  77%|███████▋  | 23/30 [00:22<00:06,  1.02it/s]
Training tasks:  80%|████████  | 24/30 [00:23<00:05,  1.02it/s]
Training tasks:  83%|████████▎ | 25/30 [00:24<00:04,  1.01it/s]
Training tasks:  87%|████████▋ | 26/30 [00:25<00:03,  1.01it/s]
Training tasks:  90%|█████████ | 27/30 [00:26<00:02,  1.01it/s]
Training tasks:  93%|█████████▎| 28/30 [00:27<00:01,  1.01it/s]
Training tasks:  97%|█████████▋| 29/30 [00:28<00:00,  1.02it/s]
Training tasks: 100%|██████████| 30/30 [00:29<00:00,  1.01it/s]
Training tasks: 100%|██████████| 30/30 [00:29<00:00,  1.01it/s]

Validating tasks:   0%|          | 0/10 [00:00<?, ?it/s]
Validating tasks:  10%|█         | 1/10 [00:00<00:01,  8.02it/s]
Validating tasks:  20%|██        | 2/10 [00:00<00:01,  7.89it/s]
Validating tasks:  30%|███       | 3/10 [00:00<00:00,  7.93it/s]
Validating tasks:  40%|████      | 4/10 [00:00<00:00,  7.85it/s]
Validating tasks:  50%|█████     | 5/10 [00:00<00:00,  7.88it/s]
Validating tasks:  60%|██████    | 6/10 [00:00<00:00,  7.85it/s]
Validating tasks:  70%|███████   | 7/10 [00:00<00:00,  7.88it/s]
Validating tasks:  80%|████████  | 8/10 [00:01<00:00,  7.85it/s]
Validating tasks:  90%|█████████ | 9/10 [00:01<00:00,  7.89it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  7.85it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  7.87it/s]

Training tasks:   0%|          | 0/30 [00:00<?, ?it/s]
Training tasks:   3%|▎         | 1/30 [00:01<00:29,  1.00s/it]
Training tasks:   7%|▋         | 2/30 [00:02<00:27,  1.00it/s]
Training tasks:  10%|█         | 3/30 [00:03<00:27,  1.02s/it]
Training tasks:  13%|█▎        | 4/30 [00:04<00:26,  1.01s/it]
Training tasks:  17%|█▋        | 5/30 [00:05<00:24,  1.00it/s]
Training tasks:  20%|██        | 6/30 [00:05<00:23,  1.01it/s]
Training tasks:  23%|██▎       | 7/30 [00:06<00:22,  1.01it/s]
Training tasks:  27%|██▋       | 8/30 [00:07<00:21,  1.00it/s]
Training tasks:  30%|███       | 9/30 [00:08<00:20,  1.01it/s]
Training tasks:  33%|███▎      | 10/30 [00:09<00:19,  1.01it/s]
Training tasks:  37%|███▋      | 11/30 [00:10<00:18,  1.01it/s]
Training tasks:  40%|████      | 12/30 [00:11<00:17,  1.00it/s]
Training tasks:  43%|████▎     | 13/30 [00:12<00:17,  1.00s/it]
Training tasks:  47%|████▋     | 14/30 [00:13<00:15,  1.00it/s]
Training tasks:  50%|█████     | 15/30 [00:14<00:14,  1.01it/s]
Training tasks:  53%|█████▎    | 16/30 [00:15<00:13,  1.01it/s]
Training tasks:  57%|█████▋    | 17/30 [00:16<00:12,  1.02it/s]
Training tasks:  60%|██████    | 18/30 [00:17<00:11,  1.02it/s]
Training tasks:  63%|██████▎   | 19/30 [00:18<00:10,  1.02it/s]
Training tasks:  67%|██████▋   | 20/30 [00:19<00:09,  1.02it/s]
Training tasks:  70%|███████   | 21/30 [00:20<00:08,  1.02it/s]
Training tasks:  73%|███████▎  | 22/30 [00:21<00:07,  1.01it/s]
Training tasks:  77%|███████▋  | 23/30 [00:22<00:06,  1.01it/s]
Training tasks:  80%|████████  | 24/30 [00:23<00:05,  1.02it/s]
Training tasks:  83%|████████▎ | 25/30 [00:24<00:04,  1.02it/s]
Training tasks:  87%|████████▋ | 26/30 [00:25<00:03,  1.02it/s]
Training tasks:  90%|█████████ | 27/30 [00:26<00:02,  1.02it/s]
Training tasks:  93%|█████████▎| 28/30 [00:27<00:01,  1.02it/s]
Training tasks:  97%|█████████▋| 29/30 [00:28<00:00,  1.02it/s]
Training tasks: 100%|██████████| 30/30 [00:29<00:00,  1.02it/s]
Training tasks: 100%|██████████| 30/30 [00:29<00:00,  1.01it/s]

Validating tasks:   0%|          | 0/10 [00:00<?, ?it/s]
Validating tasks:  10%|█         | 1/10 [00:00<00:01,  7.70it/s]
Validating tasks:  20%|██        | 2/10 [00:00<00:01,  7.72it/s]
Validating tasks:  30%|███       | 3/10 [00:00<00:00,  7.83it/s]
Validating tasks:  40%|████      | 4/10 [00:00<00:00,  7.82it/s]
Validating tasks:  50%|█████     | 5/10 [00:00<00:00,  7.87it/s]
Validating tasks:  60%|██████    | 6/10 [00:00<00:00,  7.83it/s]
Validating tasks:  70%|███████   | 7/10 [00:00<00:00,  7.87it/s]
Validating tasks:  80%|████████  | 8/10 [00:01<00:00,  7.89it/s]
Validating tasks:  90%|█████████ | 9/10 [00:01<00:00,  7.86it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  7.88it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  7.85it/s]

Training tasks:   0%|          | 0/30 [00:00<?, ?it/s]
Training tasks:   3%|▎         | 1/30 [00:00<00:28,  1.01it/s]
Training tasks:   7%|▋         | 2/30 [00:01<00:27,  1.00it/s]
Training tasks:  10%|█         | 3/30 [00:02<00:26,  1.01it/s]
Training tasks:  13%|█▎        | 4/30 [00:03<00:26,  1.00s/it]
Training tasks:  17%|█▋        | 5/30 [00:04<00:24,  1.00it/s]
Training tasks:  20%|██        | 6/30 [00:05<00:23,  1.01it/s]
Training tasks:  23%|██▎       | 7/30 [00:06<00:22,  1.01it/s]
Training tasks:  27%|██▋       | 8/30 [00:07<00:21,  1.01it/s]
Training tasks:  30%|███       | 9/30 [00:08<00:20,  1.02it/s]
Training tasks:  33%|███▎      | 10/30 [00:09<00:19,  1.02it/s]
Training tasks:  37%|███▋      | 11/30 [00:10<00:18,  1.01it/s]
Training tasks:  40%|████      | 12/30 [00:11<00:17,  1.01it/s]
Training tasks:  43%|████▎     | 13/30 [00:12<00:16,  1.01it/s]
Training tasks:  47%|████▋     | 14/30 [00:13<00:15,  1.02it/s]
Training tasks:  50%|█████     | 15/30 [00:14<00:14,  1.02it/s]
Training tasks:  53%|█████▎    | 16/30 [00:15<00:13,  1.02it/s]
Training tasks:  57%|█████▋    | 17/30 [00:16<00:12,  1.02it/s]
Training tasks:  60%|██████    | 18/30 [00:17<00:11,  1.02it/s]
Training tasks:  63%|██████▎   | 19/30 [00:18<00:10,  1.02it/s]
Training tasks:  67%|██████▋   | 20/30 [00:19<00:09,  1.02it/s]
Training tasks:  70%|███████   | 21/30 [00:20<00:08,  1.02it/s]
Training tasks:  73%|███████▎  | 22/30 [00:21<00:07,  1.02it/s]
Training tasks:  77%|███████▋  | 23/30 [00:22<00:06,  1.02it/s]
Training tasks:  80%|████████  | 24/30 [00:23<00:05,  1.03it/s]
Training tasks:  83%|████████▎ | 25/30 [00:24<00:04,  1.02it/s]
Training tasks:  87%|████████▋ | 26/30 [00:25<00:03,  1.02it/s]
Training tasks:  90%|█████████ | 27/30 [00:26<00:02,  1.02it/s]
Training tasks:  93%|█████████▎| 28/30 [00:27<00:01,  1.02it/s]
Training tasks:  97%|█████████▋| 29/30 [00:28<00:00,  1.02it/s]
Training tasks: 100%|██████████| 30/30 [00:29<00:00,  1.02it/s]
Training tasks: 100%|██████████| 30/30 [00:29<00:00,  1.02it/s]

Validating tasks:   0%|          | 0/10 [00:00<?, ?it/s]
Validating tasks:  10%|█         | 1/10 [00:00<00:01,  7.86it/s]
Validating tasks:  20%|██        | 2/10 [00:00<00:01,  7.93it/s]
Validating tasks:  30%|███       | 3/10 [00:00<00:00,  7.85it/s]
Validating tasks:  40%|████      | 4/10 [00:00<00:00,  7.90it/s]
Validating tasks:  50%|█████     | 5/10 [00:00<00:00,  7.85it/s]
Validating tasks:  60%|██████    | 6/10 [00:00<00:00,  7.88it/s]
Validating tasks:  70%|███████   | 7/10 [00:00<00:00,  7.85it/s]
Validating tasks:  80%|████████  | 8/10 [00:01<00:00,  7.89it/s]
Validating tasks:  90%|█████████ | 9/10 [00:01<00:00,  7.85it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  7.89it/s]
Validating tasks: 100%|██████████| 10/10 [00:01<00:00,  7.88it/s]

Testing tasks:   0%|          | 0/30 [00:00<?, ?it/s]
Testing tasks:   3%|▎         | 1/30 [00:00<00:06,  4.74it/s]
Testing tasks:   7%|▋         | 2/30 [00:00<00:05,  5.05it/s]
Testing tasks:  10%|█         | 3/30 [00:00<00:05,  5.19it/s]
Testing tasks:  13%|█▎        | 4/30 [00:00<00:04,  5.25it/s]
Testing tasks:  17%|█▋        | 5/30 [00:00<00:04,  5.29it/s]
Testing tasks:  20%|██        | 6/30 [00:01<00:04,  5.31it/s]
Testing tasks:  23%|██▎       | 7/30 [00:01<00:04,  5.33it/s]
Testing tasks:  27%|██▋       | 8/30 [00:01<00:04,  5.34it/s]
Testing tasks:  30%|███       | 9/30 [00:01<00:03,  5.34it/s]
Testing tasks:  33%|███▎      | 10/30 [00:01<00:03,  5.35it/s]
Testing tasks:  37%|███▋      | 11/30 [00:02<00:03,  5.35it/s]
Testing tasks:  40%|████      | 12/30 [00:02<00:03,  5.35it/s]
Testing tasks:  43%|████▎     | 13/30 [00:02<00:03,  5.34it/s]
Testing tasks:  47%|████▋     | 14/30 [00:02<00:02,  5.35it/s]
Testing tasks:  50%|█████     | 15/30 [00:02<00:02,  5.34it/s]
Testing tasks:  53%|█████▎    | 16/30 [00:03<00:02,  5.35it/s]
Testing tasks:  57%|█████▋    | 17/30 [00:03<00:02,  5.35it/s]
Testing tasks:  60%|██████    | 18/30 [00:03<00:02,  5.35it/s]
Testing tasks:  63%|██████▎   | 19/30 [00:03<00:02,  5.36it/s]
Testing tasks:  67%|██████▋   | 20/30 [00:03<00:01,  5.36it/s]
Testing tasks:  70%|███████   | 21/30 [00:03<00:01,  5.36it/s]
Testing tasks:  73%|███████▎  | 22/30 [00:04<00:01,  5.36it/s]
Testing tasks:  77%|███████▋  | 23/30 [00:04<00:01,  5.36it/s]
Testing tasks:  80%|████████  | 24/30 [00:04<00:01,  5.35it/s]
Testing tasks:  83%|████████▎ | 25/30 [00:04<00:00,  5.35it/s]
Testing tasks:  87%|████████▋ | 26/30 [00:04<00:00,  5.36it/s]
Testing tasks:  90%|█████████ | 27/30 [00:05<00:00,  5.36it/s]
Testing tasks:  93%|█████████▎| 28/30 [00:05<00:00,  5.37it/s]
Testing tasks:  97%|█████████▋| 29/30 [00:05<00:00,  5.36it/s]
Testing tasks: 100%|██████████| 30/30 [00:05<00:00,  5.37it/s]
Testing tasks: 100%|██████████| 30/30 [00:05<00:00,  5.33it/s]
